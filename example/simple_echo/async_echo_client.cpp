// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <gflags/gflags.h>

#include "byterpc/builder.h"
#include "byterpc/callback.h"
#include "byterpc/controller.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/io_buf.h"
#include "byterpc/util/logging.h"
#include "echo.pb.h"

DEFINE_uint32(protocol, 2, "1: PROTOCOL_BAIDU_STD; 2: PROTOCOL_BYTE_STD");
DEFINE_string(remote_addr, "0.0.0.0:18888", "Remote server address");
DEFINE_uint32(depth, 1, "Number of inflight rpc");
DEFINE_uint64(rpc_num, 100000, "Number of requests per thread");
DEFINE_bool(use_polling_mode, true, "use polling mode or event-driven mode");

class RpcCall {
public:
    RpcCall() : _stopped(false), _count(0), _inflight(0) {
        if (FLAGS_protocol == 1) {
            _protocol = byterpc::PROTOCOL_BAIDU_STD;
        } else if (FLAGS_protocol == 2) {
            _protocol = byterpc::PROTOCOL_BYTE_STD;
        } else {
            BYTERPC_LOG(FATAL) << "Unknown protocol";
        }
        byterpc::Builder::ChannelOptions options;
        options._trans_type = byterpc::TYPE_KERNEL_TCP;
        _channel = _builder.BuildChannel(FLAGS_remote_addr, options);
        _stub = new example::EchoService_Stub(_channel.get());
    }

    ~RpcCall() {
        delete _stub;
    }

    bool Stopped() {
        return _stopped;
    }

    void Run() {
        for (int i = 0; i < FLAGS_depth; ++i) {
            IssueRPC();
        }
    }

    void IssueRPC() {
        byterpc::Controller* cntl = _builder.CreateSessionController(_protocol);
        cntl->SetLogId(_count);

        byterpc::IOBuf buf;
        buf.append("it is an attachment");
        cntl->InstallOutgoingAttachment(buf);

        example::EchoRequest req;
        req.set_message("hello world");
        example::EchoResponse* resp = new example::EchoResponse();
        google::protobuf::Closure* done =
            byterpc::NewCallback(this, &RpcCall::HandleResponse, cntl, resp);

        ++_inflight;
        ++_count;
        _stub->Echo(cntl, &req, resp, done);
    }

    void HandleResponse(byterpc::Controller* cntl, example::EchoResponse* resp) {
        --_inflight;
        if (cntl->Failed()) {
            BYTERPC_LOG(WARNING) << "RPC call failed, error code: " << cntl->ErrorCode()
                                 << ", error text: " << cntl->ErrorText()
                                 << " rpc_id = " << cntl->LogId();
        } else {
            std::string msg = resp->message();
            std::string att;
            if (cntl->HasIncomingAttachment()) {
                byterpc::IOBuf resp_attach;
                cntl->MoveIncomingAttachment(&resp_attach);
                resp_attach.copy_to(&att, resp_attach.size(), 0);
            }

            BYTERPC_LOG(INFO) << "Received response[log_id=" << cntl->LogId() << "] from "
                              << cntl->remote_side() << ": " << msg << " (attached=" << att << ")";
        }
        delete resp;

        if (_count < FLAGS_rpc_num) {
            // send next rpc
            IssueRPC();
        } else if (_inflight == 0) {
            _stopped = true;
            if (!FLAGS_use_polling_mode) {
                byterpc::ExecCtx::QuitLoop();
            }
        }
    }

private:
    bool _stopped;
    uint64_t _count;
    uint64_t _inflight;
    byterpc::ProtocolType _protocol;
    byterpc::Builder _builder;
    std::shared_ptr<byterpc::Builder::Channel> _channel;
    example::EchoService_Stub* _stub;
};

int main(int argc, char* argv[]) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);

    // Byterpc global init, call once in the process
    byterpc::InitOptions init_opt(false, false, "byterpc_example");
    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to init byterpc";
        return -1;
    }

    // Byterpc thread init, call once in each thread
    byterpc::loop_type_t loop_type =
        FLAGS_use_polling_mode ? byterpc::LOOP_IF_POSSIBLE : byterpc::LOOP_UNTIL_QUIT;
    byterpc::ExecCtx ctx(loop_type);

    RpcCall call;
    call.Run();

    // Start loop
    if (FLAGS_use_polling_mode) {
        while (!call.Stopped()) {
            byterpc::ExecCtx::LoopOnce();
        }
    } else {
        byterpc::ExecCtx::LoopUntilQuit();
    }

    return 0;
}
