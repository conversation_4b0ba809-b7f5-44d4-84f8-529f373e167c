set(lib_name bytestore)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:storage/bytestore.git
  # commit id for branch cfs-dancedn
  GIT_TAG e3b556fe70898eb1c4589139adb0fdbc8637c332
  GIT_SUBMODULES ""
  GIT_SHALLOW TRUE
  GIT_SUBMODULE_UPDATE ""
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DBUILD_TESTING=OFF
    -DBUILD_FIO=OFF
    -DENABLE_PROFILER=ON
    -DBUILD_GF_COMPLETE=ON
    -DBUILD_JERASURE=ON
    -DBYTESTORE_USE_RDMA=ON
    -DBYTESTORE_USE_BRPC_RDMA=OFF
    -DCHUNKSERVER_USE_SPDK=OFF
    -DBYTESTORE_USE_PMEM=OFF
    -DENABLE_GCOV=OFF
    -DBYTESTORE_ENABLE_ASAN=OFF
    -DBYTESTORE_DISABLE_BYTERPC_TOOLS=true
    -DCMAKE_BUILD_TYPE=RelWithDebInfo
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
  )