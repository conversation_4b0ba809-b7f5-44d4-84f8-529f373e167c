set(lib_name bzip2)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND
    <NAME_EMAIL>:storage/bzip2.git ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
  UPDATE_COMMAND
    git checkout 6a8690fc8d26c815e798c588f796eabe9d684cf0
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE TRUE
  CONFIGURE_COMMAND ""
  BUILD_COMMAND
    ${common_configure_envs}
    make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND ${CMAKE_COMMAND}
    -DINSTALL_DIR=${CMAKE_INSTALL_PREFIX}
    -DSOURCE_DIR=${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
    -DBUILD_DIR=${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
    -P ${CMAKE_SOURCE_DIR}/cmakes/${lib_name}-install.cmake
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)
