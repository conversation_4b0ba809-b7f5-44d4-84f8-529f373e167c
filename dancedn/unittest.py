#!/usr/bin/python3
# -*- coding: UTF-8 -*-
# Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

import sys
import os
import threading
import queue
import time
import argparse
import textwrap
import shutil

# initialized by argparse
kNumWorkers = 0
kWorkerTimeout = 0
kRepeat = 0
kPath = ""
kMode = ""

kPollTimeout = 5
beginTime = {}
endTime = {}
results = {}
sysresults = {}
cmdMap = {}
indexMap = {}
memLeakReport = {}
gtestReport = {}
timeoutInfo = {}
cmd = []
sysOut = True

tcmallocIgnore = ["dancedn_tos_store_test", "dancedn_hdfs_http_service_test"]

# Create workers for callable function with arguments.
def createWorkers(callMethod, argList, callback=None):
    workers = []
    for arg in argList:
        if isinstance(arg, tuple):
            workers.append(Worker(callMethod, arg[0], arg[1], callback=callback))
        else:
            workers.append(Worker(callMethod, [arg], None, callback=callback))
    return workers

# All workers have been scheduled and processed.
class Finished(Exception):
    pass

# Worker thread in the background.
class WorkerThread(threading.Thread):
    def __init__(self, workerQueue, outputQueue, **kwds):
        threading.Thread.__init__(self, **kwds)
        self.setDaemon(1)
        self._workerQueue = workerQueue
        self._outputQueue = outputQueue
        self.event = threading.Event()
        self.start()

    def run(self):
        # Worker queue is processed until exits.
        while True:
            if self.event.isSet():
                break
            try:
                worker = self._workerQueue.get(True, kPollTimeout)
            except queue.Empty:
                continue
            if self.event.isSet():
                self._workerQueue.put(worker)
                break
            try:
                output = worker.callMethod(*worker.args, **worker.kwds)
                self._outputQueue.put((worker, output))
            except:
                worker.exception = True
                self._outputQueue.put((worker, sys.exc_info()))

# Define a worker which can be executed by callMethod.
class Worker:
    def __init__(self, callMethod, args=None, kwds=None, workerID=None, callback=None):
        if workerID is None:
            self.workerID = id(self)
        else:
            self.workerID = hash(workerID)
        self.exception = False
        self.callback = callback
        self.callMethod = callMethod
        self.args = args or []
        self.kwds = kwds or {}

# Worker thread scheduler, provided by adding and join workers.
class WorkerScheduler:
    def __init__(self, kNumWorkers):
        self._workerQueue = queue.Queue()
        self._outputQueue = queue.Queue()
        self._workers = []
        self._workerMap = {}
        self.addWorkerThread(kNumWorkers)

    def addWorkerThread(self, kNumWorkers):
        for i in range(kNumWorkers):
            self._workers.append(WorkerThread(self._workerQueue, self._outputQueue))

    def joinWorkers(self):
        for i in range(len(self._workers)):
            worker = self._workers.pop()
            worker.event.set()
            worker.join()

    def addWorker(self, worker):
        self._workerQueue.put(worker, True, None)
        self._workerMap[worker.workerID] = worker

    def schedule(self):
        while True:
            if not self._workerMap:
                raise Finished
            try:
                # Get next results
                worker, output = self._outputQueue.get_nowait()
                if worker.callback and not worker.exception:
                    worker.callback(worker, output)
                del self._workerMap[worker.workerID]
            except queue.Empty:
                break

    def killTimeoutWorkers(self):
        for w in self._workerMap:
            j = indexMap[w]
            if j in beginTime and not j in endTime and time.time() > beginTime[j] + kWorkerTimeout:
                command = "pid=`ps -ef|grep \"" + cmdMap[w] + \
                          "\"|grep -v \"sh -c \"|grep -v grep|grep -v gdb|awk '{print $2}'`; " + \
                          "if [ \"$pid\" ];then echo \"KILLED " + cmdMap[w] + "\"; " + \
                          "ulimit -c unlimited && gcore -o core_" + os.path.basename(cmdMap[w]) + " $pid; " + \
                          "pkill -9 -f " + cmdMap[w] + "; fi"
                endTime[j] = time.time()
                timeoutInfo[j] = True
                os.system(command)

if __name__ == '__main__':
    def runTest(index):
        # Run in the current working directory.
        test_name = os.path.basename(cmd[index])
        supp_path = f"{os.path.dirname(os.path.abspath(__file__))}/valgrind_ignore.supp" # hard code now
        if kMode == "valgrind" and not os.path.exists(supp_path):
            print("cannot find suppression file")
            sys.exit(1)

        if kMode == "normal" or test_name in tcmallocIgnore:
            command = "%s --gtest_repeat=%d > test_report/%s.report 2>&1" % (
                cmd[index], kRepeat, test_name)
        elif kMode == "valgrind":
            command="valgrind --num-callers=20 --fullpath-after=. --read-inline-info=yes \
                    --leak-check=full --show-leak-kinds=definite,indirect --track-origins=yes \
                    --errors-for-leak-kinds=definite,indirect --suppressions=%s \
                    %s --gtest_repeat=%d > test_report/%s.report 2>&1" % (
                supp_path, cmd[index], kRepeat, test_name)
        elif kMode == "tcmalloc":
            command="env HEAPCHECK=normal HEAP_CHECK_DUMP_DIRECTORY=test_report \
                    %s --gtest_repeat=%d > test_report/%s.report 2>&1 || true" % (
                cmd[index], kRepeat, test_name)
        else:
            sys.exit(1)

        timeoutInfo[index] = False
        beginTime[index] = time.time()
        output = os.system(command)
        endTime[index] = time.time()
        results[index] = output >> 8
        sysresults[index] = output & 255
        return output

    def outputResult(worker, output):
        print("Done %s status: %d" % (cmdMap[worker.workerID], output))

    class CustomHelpFormatter(argparse.RawDescriptionHelpFormatter, argparse.ArgumentDefaultsHelpFormatter):
        def __init__(self, *args, **kwargs):
            kwargs["max_help_position"] = 40
            kwargs["width"] = 100
            super().__init__(*args, **kwargs)
    
    parser = argparse.ArgumentParser(
        epilog=textwrap.dedent('''
        examples:
          %(prog)s: Run all hdfs tests without valgrind
          %(prog)s --workers 1: Run all hdfs tests with one worker
          %(prog)s --mode tcmalloc -t600: Run all hdfs tests with tcmalloc memcheck and timeout 600s
          %(prog)s block_sender chunkserver_store: Run dancedn_block_sender_test and dancedn_chunkserver_store_test
          '''),
        formatter_class=CustomHelpFormatter)
    
    parser.add_argument('files', type=str, default=['all'], nargs='*', help='files needed to run unit tests')
    parser.add_argument('-w', '--workers', type=int, default=16, help='workers for running tests')
    parser.add_argument('-t', '--timeout', type=int, default=300, help='worker timeout')
    parser.add_argument('-r', '--repeat', type=int, default=1, help='repeat times')
    default_path_list = [
        f"{os.path.dirname(os.path.abspath(__file__))}/build/src/cloudfs",
        f"{os.path.dirname(os.path.abspath(__file__))}/build/src/hdfs",
        f"{os.path.dirname(os.path.abspath(__file__))}/build/src/common"
    ]
    parser.add_argument('-p', '--path', type=str,
        default=",".join(default_path_list),
        help='paths to find tests')
    parser.add_argument('-m', '--mode', type=str, default="normal", choices=['normal', 'valgrind', 'tcmalloc'], help='mode to run the tests')
    args = parser.parse_args()

    kNumWorkers = args.workers
    kWorkerTimeout = args.timeout
    kRepeat = args.repeat
    kPath = args.path
    kMode = args.mode

    def find_test_programs():
        results = []
        path_list = kPath.split(",")
        for path in path_list:
            if len(args.files) == 1 and args.files[0] == 'all':
                if not os.path.exists(path):
                    continue
                for filename in os.listdir(path):
                    if filename.startswith('dancedn_') and filename.endswith('_test'):
                        results += [path + "/" + filename]
            else:
                absolute_filename = f"{path}/dancedn_{file}_test"
                if os.path.exists(absolute_filename):
                    results = [absolute_filename]
        return results

    os.system("mkdir -p test_report")
    os.system("echo && echo \"********** Start Run **********\" && echo")
    cmd = find_test_programs()
    print(f"Found {len(cmd)} test binaries.")
    max_parallelism = min(len(cmd), int(os.popen("cat /proc/cpuinfo |grep processor -c").read()))
    if kNumWorkers <= 0:
        kNumWorkers = max_parallelism
    else:
        kNumWorkers = min(max_parallelism, kNumWorkers)

    indexes = [i for i in range(len(cmd))]
    workers = createWorkers(runTest, indexes, outputResult)

    # Create a pool of worker threads.
    scheduler = WorkerScheduler(kNumWorkers)

    i = 0
    for w in workers:
        scheduler.addWorker(w)
        cmdMap[w.workerID] = cmd[i]
        indexMap[w.workerID] = i
        i += 1

    interrupt = False
    i = 0
    while True:
        try:
            time.sleep(1)
            scheduler.schedule()
            if i % 10 == 0:
                scheduler.killTimeoutWorkers()
            i += 1
        except KeyboardInterrupt:
            print("***** Interrupted! *****")
            interrupt = True
            break
        except Finished:
            break

    print("\n******************** Summary ********************\n")
    # Print all error reports.
    for i in range(len(cmd)):
        test_name = os.path.basename(cmd[i])
        command0 = 'grep -v "PASS\|^\[201\|^$" test_report/%s.report|grep "\[  FAILED  \]"' % test_name
        gtestReport[i] = os.system(command0)
        if timeoutInfo[i]:
            continue
        if kMode == "valgrind":
            command1 = 'grep "definitely lost: 0 bytes" test_report/%s.report' % test_name
            command2 = 'grep "indirectly lost: 0 bytes" test_report/%s.report' % test_name
            memLeakReport[i] = os.system(command1 + " && " + command2)
        elif kMode == "tcmalloc" and test_name not in tcmallocIgnore:
            command = 'grep -q \'No leaks found for check "_main_"\' test_report/%s.report' % test_name
            memLeakReport[i] = os.system(command)
        else:
            memLeakReport[i] = 0

    # Print the summaries.
    for i in range(len(cmd)):
        duration = endTime[i] - beginTime[i]
        if sysresults[i] == 2:
            # Interrupted.
            outString = "\033[0;31mINTERRUPTED\033[0m"
            sysOut = False
        elif gtestReport[i] == 0:
            # Error report found.
            outString = "\033[0;31mFAIL\033[0m"
            sysOut = False
        elif timeoutInfo[i]:
            # Binary is timeout
            outString = "\033[0;31mTIMEOUT\033[0m"
            sysOut = False
        elif results[i] == 11 or results[i] == 139:
            # Process Core dump.
            outString = "\033[0;31mCORE\033[0m"
            sysOut = False
        elif results[i] == 6 or results[i] == 134:
            # Process Abort.
            outString = "\033[0;31mABORT\033[0m"
            sysOut = False
        elif results[i] != 0:
            # Process NOT return 0.
            outString = "\033[0;31mBINARY\033[0m"
            sysOut = False
        elif memLeakReport[i] != 0:
            # Memory leak.
            outString = "\033[0;31mMEM_LEAK\033[0m"
            sysOut = False
        else:
            outString = "\033[0;32mPASS\033[0m"
        print("%s:   %s   %.2f seconds" % (cmd[i], outString, duration))
    print(f"Run {len(cmd)} tests. See more details in dir test_report/")
    if sysOut == True:
        sys.exit(0)
    else:
        sys.exit(1)
