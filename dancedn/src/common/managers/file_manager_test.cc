// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#include "common/managers/file_manager.h"

#include "byte/base/closure.h"
#include "byte/io/local_filesystem.h"
#include "byte/string/format/print.h"
#include "common/utils/exceptions.h"
#include "concurrent/count_down_latch.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "lavakv/options.h"

namespace bds::dancedn {
class FileLockManagerTest : public ::testing::Test {
 public:
  void SetUp() override {
    file_lock_manager_ = new FileLockManager(32);
  }
  void TearDown() override {
    delete file_lock_manager_;
  }

 public:
  FileLockManager* file_lock_manager_;
};

TEST_F(FileLockManagerTest, GetOrCreate) {
  FileLock* lock1 = file_lock_manager_->GetOrCreate("file1");
  FileLock* lock2 = file_lock_manager_->GetOrCreate("file1");
  FileLock* lock3 = file_lock_manager_->GetOrCreate("file3");
  EXPECT_EQ(lock1, lock2);
  EXPECT_EQ(lock1->ref_, 2);
  EXPECT_NE(lock1, lock3);
  EXPECT_EQ(lock3->ref_, 1);

  file_lock_manager_->Release("file1", lock1);
  EXPECT_EQ(lock1->ref_, 1);
  file_lock_manager_->Release("file1", lock2);
}

}  // namespace bds::dancedn
