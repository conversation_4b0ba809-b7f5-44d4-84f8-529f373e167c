// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#include "common/managers/file_manager.h"

#include <memory>
#include <string>
#include <vector>

#include "base/hash.h"
#include "byte/include/byte_log.h"
#include "common/utils/exceptions.h"
#include "concurrent/rwlock.h"
#include "gflags/gflags_declare.h"
#include "kvstore/kv_store.h"
#include "string/algorithm.h"
#include "system/timestamp.h"

DECLARE_uint32(dancedn_file_manager_bucket_number);

namespace bds::dancedn {

FileLockTable::FileLockTable() : table_lock_(), file_locks_() {}
FileLockTable::~FileLockTable() {
  for (auto&& lock : file_locks_) {
    delete lock.second->lock;
    delete lock.second;
  }
}

FileLock* FileLockTable::GetOrCreate(const std::string& path) {
  byte::SpinLock::Locker guard(&table_lock_);
  auto&& iter = file_locks_.find(path);
  if (iter == file_locks_.end()) {
    // TODO(lijiye.2021): take lock from lock pool
    FileLock* lock = new FileLock();
    lock->lock = new byte::RwLock();
    lock->ref_ = 1;
    file_locks_.emplace(path, lock);
    return lock;
  } else {
    iter->second->ref_++;
    return iter->second;
  }
}

void FileLockTable::Release(const std::string& path, FileLock* lock) {
  byte::SpinLock::Locker guard(&table_lock_);
  lock->ref_--;
  if (lock->ref_ == 0) {
    auto&& iter = file_locks_.find(path);
    BYTE_ASSERT(iter != file_locks_.end())
        << " path not found in file lock table, path: " << path;
    BYTE_ASSERT(iter->second == lock);
    delete lock->lock;
    delete lock;
    file_locks_.erase(iter);
  }
}

FileLockManager::FileLockManager(uint32_t bucket_number)
    : bucket_number_(bucket_number), file_lock_tables_() {
  file_lock_tables_.resize(bucket_number);
  for (int i = 0; i < bucket_number; i++) {
    file_lock_tables_[i] = std::make_unique<FileLockTable>();
  }
}

FileLockManager::~FileLockManager() {}

FileLock* FileLockManager::GetOrCreate(const std::string& path) {
  uint32_t hash = byte::XXHash(path.c_str(), path.size());
  uint32_t bucket_id = hash % bucket_number_;
  return file_lock_tables_[bucket_id]->GetOrCreate(path);
}

void FileLockManager::Release(const std::string& path, FileLock* lock) {
  uint32_t hash = byte::XXHash(path.c_str(), path.size());
  uint32_t bucket_id = hash % bucket_number_;
  file_lock_tables_[bucket_id]->Release(path, lock);
}

FileManager::FileManager(cacheengine::KVStore* kv_store,
                         const std::string& file_engine_name)
    : kv_store_(kv_store),
      file_engine_name_(file_engine_name),
      file_lock_manager_(std::make_unique<FileLockManager>(
          FLAGS_dancedn_file_manager_bucket_number)) {}

FileManager::~FileManager() {}

void FileManager::CreateFileAsync(
    File& file, uint32_t disk_id,
    Closure<void, exceptions::Exception>* callback) {
  auto&& stub = kv_store_->GetLavakvStub(disk_id);
  auto&& db = stub.db_;
  auto&& engine = stub.engine_handles_.at(file_engine_name_);

  std::string normalized_path;
  exceptions::Exception e = Normalize(file.name_, &normalized_path);
  if (!e.OK()) {
    LOG(ERROR) << "normalize path failed, path: " << file.name_;
    callback->Run(e);
    return;
  }
  file.ctime_ = byte::GetCurrentTimeInUs();
  file.mtime_ = file.ctime_;
  FileLock* lock = file_lock_manager_->GetOrCreate(file.name_);
  lock->lock->WriterLock();
}

exceptions::Exception FileManager::Normalize(const std::string& path,
                                             std::string* normalized_path) {
  if (path.empty()) {
    LOG(ERROR) << "path is empty ";
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 "path is empty");
  }

  if (path[0] != '/') {
    LOG(ERROR) << "path must start with /, path: " << path;
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 "path must start with /");
  }
  // POSIX allows one or two initial slashes, but treats three or more
  // as single slash.
  if (byte::StringStartsWith(path, "//") &&
      !byte::StringStartsWith(path, "///")) {
    LOG(ERROR) << "path must not have two initial slashes, path: " << path;
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 "path must not have two initial slashes");
  }

  std::vector<std::string> comps;
  byte::SplitStringKeepEmpty(path, '/', &comps);

  std::vector<std::string> new_comps;
  new_comps.resize(comps.size());
  for (auto i = comps.begin(); i != comps.end(); ++i) {
    const std::string& comp = *i;
    if (comp.empty() || comp == ".") {
      continue;
    }
    if (comp != "..") {
      new_comps.push_back(comp);
    } else if (!new_comps.empty()) {
      new_comps.pop_back();
    } else {
      LOG(ERROR) << "path has invalid component, path: " << path;
      return exceptions::Exception(exceptions::kIllegalArgumentException,
                                   "path has invalid component");
    }
  }

  std::string new_path = "/" + byte::JoinStrings(comps, "/");
  *normalized_path = new_path;
  return exceptions::Exception();
}

std::string FileManager::FileToProtoStr(const File& file) {
  return "";
}

}  // namespace bds::dancedn
