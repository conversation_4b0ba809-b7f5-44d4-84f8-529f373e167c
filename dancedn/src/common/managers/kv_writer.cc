// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#include "common/managers/kv_writer.h"

#include <cassert>
#include <cstdint>
#include <list>
#include <memory>

#include "byte/include/assert.h"
#include "byte/string/format/print.h"
#include "byte/thread/async_thread.h"
#include "byte/thread/base_thread.h"
#include "byte/thread/detail/thread_types.h"
#include "byte/thread/waiter.h"
#include "common/utils/exceptions.h"
#include "concurrent/mutex.h"
#include "lavakv/db.h"
#include "lavakv/options.h"
#include "lavakv/write_batch.h"
#include "system/timestamp.h"
#include "terarkdb/options.h"
#include "terarkdb/write_batch.h"

DECLARE_uint32(dancedn_kv_write_batch_max_size);

namespace bds::dancedn {

KVDiskWriter ::KVDiskWriter(uint32_t disk_id, cacheengine::KVStore* kv_store)
    : stop_(false),
      disk_id_(disk_id),
      kv_store_(kv_store),
      mutex_(),
      cond_(&mutex_) {
  flush_thread_.Start(std::bind(&KVDiskWriter::Flush, this));
}
KVDiskWriter::~KVDiskWriter() {
  stop_ = true;
  {
    byte::MutexLocker guard(&mutex_);
    cond_.Signal();
  }
  flush_thread_.Join();
}

void KVDiskWriter::WriteAsync(KVData* data) {
  data_queue_.Push(data);
  {
    byte::MutexLocker guard(&mutex_);
    cond_.Signal();
  }
}

void KVDiskWriter::Flush() {
  uint64_t flush_interval_us = 100;
  auto&& stub = kv_store_->GetLavakvStub(disk_id_);
  auto&& db = stub.db_;
  while (!stop_) {
    terarkdb::lavakv::LavaWriteBatch batch;
    uint32_t count = 0;
    std::list<KVData*> data_list;
    while (count < FLAGS_dancedn_kv_write_batch_max_size) {
      KVData* data = nullptr;
      data_queue_.Pop(&data);
      if (data == nullptr) {
        break;
      }
      batch.Put(stub.engine_handles_.at(data->engine_), data->key_.c_str(),
                data->value_.c_str());
      data_list.emplace_back(data);
      count++;
    }
    if (data_list.empty()) {
      {
        byte::MutexLocker guard(&mutex_);
        cond_.Wait();
      }
      continue;
    }
    terarkdb::WriteOptions options;
    auto status = db->Write(options, &batch);
    if (!status.ok()) {
      std::string err_msg = status.ToString();
      LOG(ERROR) << "flush kv_data to disk " << disk_id_
                 << " failed, status: " << err_msg;
      for (auto& data : data_list) {
        data->done_->Run(
            exceptions::Exception(exceptions::kIOException, err_msg), data);
      }
    } else {
      for (auto& data : data_list) {
        data->done_->Run(exceptions::Exception(), data);
      }
    }
  }
}

KVWriter::KVWriter(const std::set<uint32_t>& disk_ids,
                   cacheengine::KVStore* kv_store) {
  for (auto& disk_id : disk_ids) {
    disk_writers_.emplace(disk_id,
                          std::make_unique<KVDiskWriter>(disk_id, kv_store));
  }
}
KVWriter::~KVWriter() {}

void KVWriter::WriteAsync(KVData* data) {
  auto it = disk_writers_.find(data->disk_id_);
  if (it == disk_writers_.end()) {
    data->done_->Run(exceptions::Exception(exceptions::kDiskNotFound), data);
    return;
  }
  it->second->WriteAsync(data);
}

}  // namespace bds::dancedn
