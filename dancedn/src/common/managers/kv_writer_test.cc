// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#include "common/managers/kv_writer.h"

#include "byte/base/closure.h"
#include "byte/io/local_filesystem.h"
#include "byte/string/format/print.h"
#include "common/utils/exceptions.h"
#include "concurrent/count_down_latch.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "lavakv/options.h"

namespace bds::dancedn {
class KVWriterTest : public ::testing::Test {
 public:
  void SetUp() override {
    char buffer[4096];
    auto len = readlink("/proc/self/exe", buffer, sizeof(buffer) - 1);
    ASSERT_GE(len, 0);
    buffer[len] = '\0';
    std::string::size_type pos = std::string(buffer).find_last_of("/");
    binary_dir_ = std::string(buffer).substr(0, pos);
    work_dir_ =
        byte::StringPrint("%s/%s", binary_dir_.c_str(), "kv_writer_test");
    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    local_fs.DeleteDir(work_dir_, delete_options);
    ASSERT_TRUE(local_fs.CreateDir(work_dir_, byte::CreateOptions()).ok());
    ASSERT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk1", byte::CreateOptions()).ok());
    ASSERT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk2", byte::CreateOptions()).ok());
    ASSERT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk3", byte::CreateOptions()).ok());
    ASSERT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk1/lavakv", byte::CreateOptions())
            .ok());
    ASSERT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk2/lavakv", byte::CreateOptions())
            .ok());
    ASSERT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk3/lavakv", byte::CreateOptions())
            .ok());
    cacheengine::KVStore::InitOptions options;
    options.location_options_[1] = work_dir_ + "/disk1/lavakv";
    options.location_options_[2] = work_dir_ + "/disk2/lavakv";
    options.location_options_[3] = work_dir_ + "/disk3/lavakv";

    options.engine_options_ = {
        {"hash", terarkdb::lavakv::LavaEngineType::kHashEngine},
        {"sorted", terarkdb::lavakv::LavaEngineType::kSortedEngine}};
    kv_store_ = new cacheengine::KVStore();
    ASSERT_EQ(kv_store_->Init(options), 0);
    ASSERT_EQ(kv_store_->Start(), 0);

    kv_writer_ = new KVWriter({1, 2, 3}, kv_store_);
  }
  void TearDown() override {
    kv_store_->Stop();
    delete kv_writer_;
    delete kv_store_;
  }

 public:
  cacheengine::KVStore* kv_store_;
  std::string binary_dir_;
  std::string work_dir_;
  KVWriter* kv_writer_;
};

static void WriteCallback(byte::CountDownLatch* latch,
                          exceptions::Exception* ret_e, exceptions::Exception e,
                          KVData* data) {
  *ret_e = e;
  latch->CountDown();
}

TEST_F(KVWriterTest, write) {
  KVData data1;
  data1.key_ = "key";
  data1.value_ = "value";
  data1.disk_id_ = 0;
  data1.engine_ = "hash";
  byte::CountDownLatch latch1(1);
  exceptions::Exception ret_e;
  data1.done_ = NewClosure(&WriteCallback, &latch1, &ret_e);
  kv_writer_->WriteAsync(&data1);
  latch1.Wait();
  EXPECT_EQ(ret_e.GetE(), exceptions::kDiskNotFound);

  KVData data2;
  data2.key_ = "key2";
  data2.value_ = "value2";
  data2.disk_id_ = 1;
  data2.engine_ = "hash";
  byte::CountDownLatch latch2(1);
  exceptions::Exception ret_e2;
  data2.done_ = NewClosure(&WriteCallback, &latch2, &ret_e2);

  KVData data3;
  data3.key_ = "key3";
  data3.value_ = "value3";
  data3.disk_id_ = 2;
  data3.engine_ = "hash";
  byte::CountDownLatch latch3(1);
  exceptions::Exception ret_e3;
  data3.done_ = NewClosure(&WriteCallback, &latch3, &ret_e3);

  KVData data4;
  data4.key_ = "key3";
  data4.value_ = "value4";
  data4.disk_id_ = 2;
  data4.engine_ = "hash";
  byte::CountDownLatch latch4(1);
  exceptions::Exception ret_e4;
  data4.done_ = NewClosure(&WriteCallback, &latch4, &ret_e4);

  kv_writer_->WriteAsync(&data2);
  kv_writer_->WriteAsync(&data3);
  kv_writer_->WriteAsync(&data4);
  latch2.Wait();
  latch3.Wait();
  latch4.Wait();
  EXPECT_TRUE(ret_e4.OK());

  auto&& db2 = kv_store_->GetLavakvStub(1).db_;
  auto&& engine2 = kv_store_->GetLavakvStub(1).engine_handles_.at("hash");
  std::string value2;
  auto status =
      db2->Get(terarkdb::lavakv::LavaReadOptions(), engine2, "key2", &value2);
  EXPECT_TRUE(status.ok());
  EXPECT_EQ(value2, "value2");
  std::string value3;
  auto&& db3 = kv_store_->GetLavakvStub(2).db_;
  auto&& engine3 = kv_store_->GetLavakvStub(2).engine_handles_.at("hash");
  status =
      db3->Get(terarkdb::lavakv::LavaReadOptions(), engine3, "key3", &value3);
  EXPECT_TRUE(status.ok());
  EXPECT_EQ(value3, "value4");
}

}  // namespace bds::dancedn
