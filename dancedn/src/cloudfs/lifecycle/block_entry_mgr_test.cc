// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/lifecycle/block_entry_mgr.h"

#include <gmock/gmock-more-actions.h>

#include <cstdint>
#include <deque>
#include <memory>
#include <string>
#include <vector>

#include "base/closure.h"
#include "byte_log/byte_log_impl.h"
#include "cloudfs/cfs/storage_class.h"
#include "cloudfs/directory_scanner.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/finalized_replica.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_disk.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/chunkserver/disk_usage.h"
#include "cloudfs/store/chunkserver/partial_block.h"
#include "cloudfs/store/chunkserver/partial_block_locks.h"
#include "encoding/int128.h"
#include "gmock/gmock-actions.h"
#include "gmock/gmock-cardinalities.h"
#include "gmock/gmock-generated-actions.h"
#include "gmock/gmock-spec-builders.h"
#include "gtest/gtest.h"
#include "include/byte_log.h"
#include "io/local_filesystem.h"
#include "string/format/print.h"

DECLARE_double(bytestore_cfs_evict_threthold_percentage);
DECLARE_double(bytestore_cfs_min_retained_warm_percentage);
DECLARE_int64(bytestore_cfs_min_retained_warm_cnt);
DECLARE_int64(bytestore_cfs_evict_threthold_cnt);

namespace bds::dancedn::cloudfs {

class BlockEntryMgrTests : public ::testing::Test {
 public:
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
    disk1_ = new MockDisk();
    disk2_ = new MockDisk();
    dn_ = new MockDataNode();
    store_ = new MockChunkserverStore();
    upload_tos_mgr_ = new MockUploadTosMgr();
    std::set<uint32_t> diskids = {1, 2};
    disk_usage_ = std::make_shared<DiskUsage>(diskids);
    tos_managed_ns_info_ = new NameSpaceInfo(
        tos_managed_ns_id_, cluster_id_, tos_managed_bpid_,
        tos_managed_create_time_, build_version_, software_version_, fs_id_,
        tos_managed_ns_id_, NamespaceType::TOS_MANAGED);
    tos_local_ns_info_ = new NameSpaceInfo(
        tos_local_ns_id_, cluster_id_, tos_local_bpid_, tos_local_create_time_,
        build_version_, software_version_, fs_id_, tos_local_ns_id_,
        NamespaceType::TOS_LOCAL);
    EXPECT_CALL(*disk1_, GetDiskId())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*disk2_, GetDiskId())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(2));
    block_entry_mgr_ = new BlockEntryMgr(dn_, store_, diskids, disk_usage_);
  }
  void TearDown() override {
    delete block_entry_mgr_;
    delete upload_tos_mgr_;
    delete store_;
    delete dn_;
    delete disk1_;
    delete disk2_;
    delete tos_managed_ns_info_;
    delete tos_local_ns_info_;
  }
  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    std::cerr << "Shut down logging system" << std::endl;
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  MockDisk* disk1_;
  MockDisk* disk2_;
  MockDataNode* dn_;
  MockChunkserverStore* store_;
  MockUploadTosMgr* upload_tos_mgr_;
  std::shared_ptr<DiskUsage> disk_usage_;
  BlockEntryMgr* block_entry_mgr_;
  NameSpaceInfo* tos_local_ns_info_;
  NameSpaceInfo* tos_managed_ns_info_;
  std::string cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
  uint64_t tos_managed_create_time_ = 1546064706395;
  uint64_t tos_local_create_time_ = 1546064706393;
  std::string build_version_ = "2.6.0";
  std::string software_version_ = "2.6.3";
  uint64_t fs_id_ = 4013797767642215140;
  uint64_t tos_managed_ns_id_ = 3845026443837452998;
  uint64_t tos_local_ns_id_ = 3845026443837452999;
  std::string tos_managed_bpid_ = byte::StringPrint(
      "BP-%llu-%llu", tos_managed_ns_id_, tos_managed_create_time_);
  std::string tos_local_bpid_ = byte::StringPrint(
      "BP-%llu-%llu", tos_local_ns_id_, tos_local_create_time_);
};

TEST_F(BlockEntryMgrTests, AddAndRemove) {
  byte::concurrent::HashTable<EntryId, std::shared_ptr<BaseEntry>, EntryIdHash>&
      entries = block_entry_mgr_->TestGetBlockEntries();
  std::shared_ptr<ReplicaInfo> replica1 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 1, 210ULL * 1024 * 1024 * 1024, 1, true,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  std::shared_ptr<ReplicaInfo> replica2 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 2, 210ULL * 1024 * 1024 * 1024, 1, true,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  PartialBlockLocks locks;
  std::shared_ptr<BlockV2> block = std::make_shared<BlockV2>(
      &locks, uint128_t_pod{1, 3}, 3, 1024, 1, false, DEFAULT_STORAGE_CLASS,
      NamespaceType::TOS_MANAGED);
  std::shared_ptr<PartialBlock> partial_block1 =
      std::make_shared<PartialBlock>(&locks, block, 0);
  std::shared_ptr<PartialBlock> partial_block2 =
      std::make_shared<PartialBlock>(&locks, block, 1);

  block_entry_mgr_->AddBlock(nullptr, replica1);
  block_entry_mgr_->AddPartialBlock(nullptr, partial_block1);
  EXPECT_EQ(entries.Size(), 2);
  block_entry_mgr_->AddBlock(nullptr, replica1);
  block_entry_mgr_->AddPartialBlock(nullptr, partial_block1);
  EXPECT_EQ(entries.Size(), 2);
  block_entry_mgr_->AddBlock(nullptr, replica2);
  block_entry_mgr_->AddPartialBlock(nullptr, partial_block2);
  EXPECT_EQ(entries.Size(), 4);
  block_entry_mgr_->RemoveBlock(replica2);
  block_entry_mgr_->RemovePartialBlock(partial_block2);
  EXPECT_EQ(entries.Size(), 2);
  block_entry_mgr_->RemoveBlock(replica2);
  block_entry_mgr_->RemovePartialBlock(partial_block2);
  EXPECT_EQ(entries.Size(), 2);
}

TEST_F(BlockEntryMgrTests, ScanForUsage) {
  double origin_threshold = FLAGS_bytestore_cfs_evict_threthold_percentage;
  double origin_warm_pecentage =
      FLAGS_bytestore_cfs_min_retained_warm_percentage;
  FLAGS_bytestore_cfs_evict_threthold_percentage = 0.4;
  FLAGS_bytestore_cfs_min_retained_warm_percentage = 0.1;
  EXPECT_CALL(*dn_, GetNamespaceInfo(tos_managed_bpid_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(tos_managed_ns_info_));
  EXPECT_CALL(*dn_, GetNamespaceInfo(tos_local_bpid_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(tos_local_ns_info_));
  EXPECT_CALL(*store_, GetDisk(1))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(disk1_));
  EXPECT_CALL(*disk1_, GetTotalSizeInBytes(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::DoAll(
          ::testing::SetArgPointee<0>(500ULL * 1024 * 1024 * 1024),
          ::testing::Return(bytestore::BYTESTORE_OK)));

  EXPECT_CALL(*disk1_, GetDiskId())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(1));
  EXPECT_CALL(*disk1_, GetDiskStatus())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(bytestore::DiskStatus::DISK_NORMAL));
  EXPECT_CALL(*disk1_, GetUsedSizeInBytes(::testing::_))
      .Times(2)
      .WillOnce(::testing::DoAll(
          ::testing::SetArgPointee<0>(0ULL * 1024 * 1024 * 1024),
          ::testing::Return(bytestore::BYTESTORE_OK)))
      .WillOnce(::testing::DoAll(
          ::testing::SetArgPointee<0>(360ULL * 1024 * 1024 * 1024),
          ::testing::Return(bytestore::BYTESTORE_OK)));

  block_entry_mgr_->TestScanForUsage(1);

  std::shared_ptr<ReplicaInfo> replica1 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 1, 210ULL * 1024 * 1024 * 1024, 1, true,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  replica1->SetStorageClass(StorageClass::HOT);
  std::shared_ptr<ReplicaInfo> replica2 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 2, 10ULL * 1024 * 1024 * 1024, 1, false,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  replica2->SetStorageClass(StorageClass::HOT);
  std::shared_ptr<ReplicaInfo> replica3 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 3, 100ULL * 1024 * 1024 * 1024, 1, false,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  replica3->SetStorageClass(StorageClass::WARM);
  std::shared_ptr<ReplicaInfo> replica4 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 4, 40ULL * 1024 * 1024 * 1024, 1, false,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  EXPECT_CALL(*store_, GetReplica(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Invoke(
          [replica1, replica2, replica3, replica4](
              const std::string& bpid,
              uint64_t block_id) -> std::shared_ptr<ReplicaInfo> {
            if (block_id == 1ULL) {
              return replica1;
            } else if (block_id == 2ULL) {
              return replica2;
            } else if (block_id == 3ULL) {
              return replica3;
            } else if (block_id == 4ULL) {
              return replica4;
            }
            return nullptr;
          }));
  replica4->SetStorageClass(StorageClass::WARM);
  disk_usage_->IncReplica(replica1);
  block_entry_mgr_->AddBlock(store_, replica1);
  disk_usage_->IncReplica(replica2);
  block_entry_mgr_->AddBlock(store_, replica2);
  disk_usage_->IncReplica(replica3);
  block_entry_mgr_->AddBlock(store_, replica3);
  disk_usage_->IncReplica(replica4);
  block_entry_mgr_->AddBlock(store_, replica4);
  EXPECT_CALL(*store_, SubmitInvalidateReplicaBG(::testing::_, ::testing::_,
                                                 ::testing::_, ::testing::_,
                                                 ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Invoke([&](const std::string& bpid, uint64_t block_id,
                                bool is_evict, bytestore::IOPriority priority,
                                Closure<void, exceptions::Exception>* cb) {
            EXPECT_EQ(block_id, 3);
            block_entry_mgr_->RemoveBlock(replica3);
            disk_usage_->DecReplica(replica3);
          }))
      .WillOnce(
          ::testing::Invoke([&](const std::string& bpid, uint64_t block_id,
                                bool is_evict, bytestore::IOPriority priority,
                                Closure<void, exceptions::Exception>* cb) {
            EXPECT_EQ(block_id, 2);
            block_entry_mgr_->RemoveBlock(replica2);
            disk_usage_->DecReplica(replica2);
          }));
  block_entry_mgr_->TestScanForUsage(1);
  FLAGS_bytestore_cfs_evict_threthold_percentage = origin_threshold;
  FLAGS_bytestore_cfs_min_retained_warm_percentage = origin_warm_pecentage;
  FLAGS_bytestore_cfs_min_retained_warm_percentage = 0.1;
}

TEST_F(BlockEntryMgrTests, ScanForNumber) {
  int64_t origin_threshold = FLAGS_bytestore_cfs_evict_threthold_cnt;
  int64_t origin_warm_threshold = FLAGS_bytestore_cfs_min_retained_warm_cnt;
  FLAGS_bytestore_cfs_evict_threthold_cnt = 1;
  FLAGS_bytestore_cfs_min_retained_warm_cnt = 1;
  EXPECT_CALL(*dn_, GetNamespaceInfo(tos_managed_bpid_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(tos_managed_ns_info_));
  EXPECT_CALL(*dn_, GetNamespaceInfo(tos_local_bpid_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(tos_local_ns_info_));
  EXPECT_CALL(*store_, GetDisk(1))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(disk1_));
  EXPECT_CALL(*disk1_, GetTotalSizeInBytes(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::DoAll(
          ::testing::SetArgPointee<0>(500ULL * 1024 * 1024 * 1024),
          ::testing::Return(bytestore::BYTESTORE_OK)));

  EXPECT_CALL(*disk1_, GetDiskId())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(1));
  EXPECT_CALL(*disk1_, GetDiskStatus())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(bytestore::DiskStatus::DISK_NORMAL));
  // EXPECT_CALL(*disk1_, GetUsedSizeInBytes(::testing::_))
  //     .Times(2)
  //     .WillOnce(::testing::DoAll(::testing::SetArgPointee<0>(0ULL * 1024 *
  //     1024 * 1024),
  //                                ::testing::Return(bytestore::BYTESTORE_OK)))
  //     .WillOnce(::testing::DoAll(::testing::SetArgPointee<0>(360ULL * 1024 *
  //     1024 * 1024),
  //                                ::testing::Return(bytestore::BYTESTORE_OK)));

  block_entry_mgr_->TestScanForNumber(1);

  std::shared_ptr<ReplicaInfo> replica1 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 1, 210ULL * 1024 * 1024 * 1024, 1, true,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  replica1->SetStorageClass(StorageClass::HOT);
  std::shared_ptr<ReplicaInfo> replica2 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 2, 10ULL * 1024 * 1024 * 1024, 1, false,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  replica2->SetStorageClass(StorageClass::HOT);
  std::shared_ptr<ReplicaInfo> replica3 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 3, 100ULL * 1024 * 1024 * 1024, 1, false,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  replica3->SetStorageClass(StorageClass::WARM);
  std::shared_ptr<ReplicaInfo> replica4 = std::make_shared<FinalizedReplica>(
      tos_managed_bpid_, 4, 40ULL * 1024 * 1024 * 1024, 1, false,
      "storage_uuid", false, true, static_cast<int>(NamespaceType::TOS_MANAGED),
      false, 1);
  EXPECT_CALL(*store_, GetReplica(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Invoke(
          [replica1, replica2, replica3, replica4](
              const std::string& bpid,
              uint64_t block_id) -> std::shared_ptr<ReplicaInfo> {
            if (block_id == 1ULL) {
              return replica1;
            } else if (block_id == 2ULL) {
              return replica2;
            } else if (block_id == 3ULL) {
              return replica3;
            } else if (block_id == 4ULL) {
              return replica4;
            }
            return nullptr;
          }));
  replica4->SetStorageClass(StorageClass::WARM);
  disk_usage_->IncReplica(replica1);
  block_entry_mgr_->AddBlock(store_, replica1);
  disk_usage_->IncReplica(replica2);
  block_entry_mgr_->AddBlock(store_, replica2);
  disk_usage_->IncReplica(replica3);
  block_entry_mgr_->AddBlock(store_, replica3);
  disk_usage_->IncReplica(replica4);
  block_entry_mgr_->AddBlock(store_, replica4);
  EXPECT_CALL(*store_, SubmitInvalidateReplicaBG(::testing::_, ::testing::_,
                                                 ::testing::_, ::testing::_,
                                                 ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Invoke([&](const std::string& bpid, uint64_t block_id,
                                bool is_evict, bytestore::IOPriority priority,
                                Closure<void, exceptions::Exception>* cb) {
            EXPECT_EQ(block_id, 3);
            block_entry_mgr_->RemoveBlock(replica3);
            disk_usage_->DecReplica(replica3);
          }))
      .WillOnce(
          ::testing::Invoke([&](const std::string& bpid, uint64_t block_id,
                                bool is_evict, bytestore::IOPriority priority,
                                Closure<void, exceptions::Exception>* cb) {
            EXPECT_EQ(block_id, 2);
            block_entry_mgr_->RemoveBlock(replica2);
            disk_usage_->DecReplica(replica2);
          }));
  block_entry_mgr_->TestScanForNumber(1);
  FLAGS_bytestore_cfs_evict_threthold_cnt = origin_threshold;
  FLAGS_bytestore_cfs_min_retained_warm_cnt = origin_warm_threshold;
}

}  // namespace bds::dancedn::cloudfs
