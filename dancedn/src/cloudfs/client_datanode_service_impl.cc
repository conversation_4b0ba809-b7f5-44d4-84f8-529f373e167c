// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/client_datanode_service_impl.h"

#include <cstdint>
#include <memory>
#include <string>

#include "byte/include/assert.h"
#include "byte/string/algorithm.h"
#include "byte/system/timestamp.h"
#include "cloudfs/block_local_path_info.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_report_options.h"
#include "cloudfs/blocks_with_locations.h"
#include "cloudfs/constants.h"
#include "cloudfs/datanode.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/hdfs_blocks_metadata.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/message/calculate_block_crc.h"
#include "cloudfs/message/get_block_local_path_info_message.h"
#include "cloudfs/message/get_blocks_message.h"
#include "cloudfs/message/get_hdfs_block_locations_message.h"
#include "cloudfs/message/get_replica_visible_length_message.h"
#include "cloudfs/message/get_replica_visible_length_v2_message.h"
#include "cloudfs/message/init_replica_recovery_message.h"
#include "cloudfs/message/read_block_message.h"
#include "cloudfs/message/refresh_namenodes_message.h"
#include "cloudfs/message/rpc_request_message.h"
#include "cloudfs/message/rpc_response_message.h"
#include "cloudfs/message/seal_block_message.h"
#include "cloudfs/message/shutdown_datanode_message.h"
#include "cloudfs/message/trigger_block_report_message.h"
#include "cloudfs/message/update_replica_under_recovery_message.h"
#include "cloudfs/opstats/op_stats.h"
#include "cloudfs/opstats/trace_baggage.h"
#include "cloudfs/proto/InterDatanodeProtocol.pb.h"
#include "cloudfs/proto/RpcHeader.pb.h"
#include "cloudfs/recovering_block.h"
#include "cloudfs/replica_recovery_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/security/token.h"
#include "cloudfs/services/hdfs_io_service.h"
#include "cloudfs/services/simple_byterpc_controller.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs/util.h"
#include "common/memory_pool.h"
#include "util/scope_guard.h"

namespace bds::dancedn::cloudfs {

const char ClientDatanodeServiceImpl::PROTOCOL_NAME[] =
    "org.apache.hadoop.hdfs.protocol.ClientDatanodeProtocol";
const uint32_t ClientDatanodeServiceImpl::PROTOCOL_VERSION = 1;

ClientDatanodeServiceImpl::ClientDatanodeServiceImpl() : mem_pool_repo_() {
  Initialize();
}

ClientDatanodeServiceImpl::ClientDatanodeServiceImpl(DataNode* datanode)
    : datanode_(datanode), mem_pool_repo_() {
  Initialize();
}

message::RpcRequestMessage* ClientDatanodeServiceImpl::NewRequest(
    const std::string& name) {
  auto itr = method_ids_.find(name);
  if (itr == method_ids_.end()) return nullptr;
  auto r = builders_[itr->second]();
  r->MethodId(itr->second);
  return r;
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::CallMethod(
    message::RpcRequestMessage* r) {
  BYTE_ASSERT(r->MethodId() >= 0);
  return (this->*methods_[r->MethodId()])(r);
}

::cloudfs::RpcResponseHeaderProto*
ClientDatanodeServiceImpl::ProcessExceptionAfterCallMethod(
    const exceptions::Exception& e,
    const ::cloudfs::RpcRequestHeaderProto* request_header) const {
  if (e.OK()) {
    return BuildResponseHeader(request_header);
  } else {
    return BuildResponseHeaderWithException(
        request_header, e,
        ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR,
        ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  }
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::getReplicaVisibleLength(
    message::RpcRequestMessage* r) {
  auto start_time = byte::GetCurrentTimeInUs();
  auto m = static_cast<message::GetReplicaVisibleLengthMessage*>(r);
  int64_t len = -1;
  int64_t physical_len = -1;
  uint64_t gs = -1;
  auto block = ExtendedBlock::ParseProto(m->Body()->block());

  auto e = datanode_->GetReplicaVisibleLength(*block, &len);
  if (e.OK()) {
    e = datanode_->GetReplicaPhysicalLength(*block, &physical_len);
  }
  if (e.OK()) {
    e = datanode_->GetReplicaGs(*block, &gs);
  }
  auto resp_header = ProcessExceptionAfterCallMethod(e, r->RpcHeader());
  ::cloudfs::GetReplicaVisibleLengthResponseProto* resp_body = nullptr;

  LOG(INFO) << "Call getReplicaVisibleLength, block: " << block->ToString()
            << ", result: " << e.ToString() << ", len: " << len
            << ", physical_len: " << physical_len;

  if (e.OK()) {
    resp_body = new ::cloudfs::GetReplicaVisibleLengthResponseProto();
    resp_body->set_length(len);
    resp_body->set_physicallength(physical_len);
    resp_body->set_latestgenerationstamp(gs);
  }

  OpKey key = BuildKey(r, block->GetBlockPoolID(), block->GetBlockID(),
                       Operation::GetReplicaVisibleLength);
  OpStats::GetInstance().Record(key, 0, 0, start_time / 1000,
                                byte::GetCurrentTimeInUs() - start_time);
  delete block;
  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage*
ClientDatanodeServiceImpl::getReplicaVisibleLengthV2(
    message::RpcRequestMessage* r) {
  auto start_time = byte::GetCurrentTimeInUs();
  auto m = static_cast<message::GetReplicaVisibleLengthV2Message*>(r);
  int64_t len = -1;
  int32_t replica_state = -1;
  int64_t physical_len = -1;
  uint64_t gs = -1;
  auto block = ExtendedBlock::ParseProto(m->Body()->block());

  auto e = datanode_->GetReplicaVisibleLengthV2(*block, &len, &replica_state);
  if (e.OK()) {
    e = datanode_->GetReplicaPhysicalLength(*block, &physical_len);
  }
  if (e.OK()) {
    e = datanode_->GetReplicaGs(*block, &gs);
  }
  LOG(INFO) << "Call getReplicaVisibleLength, block: " << block->ToString()
            << ", result: " << e.ToString() << ", len: " << len
            << ", replica_state: " << replica_state
            << ", physical_len: " << physical_len;
  auto resp_header = ProcessExceptionAfterCallMethod(e, r->RpcHeader());
  ::cloudfs::GetReplicaVisibleLengthV2ResponseProto* resp_body = nullptr;

  if (e.OK()) {
    resp_body = new ::cloudfs::GetReplicaVisibleLengthV2ResponseProto();
    resp_body->set_length(len);
    resp_body->set_state(replica_state);
    resp_body->set_physicallength(physical_len);
    resp_body->set_latestgenerationstamp(gs);
  }

  OpKey key = BuildKey(r, block->GetBlockPoolID(), block->GetBlockID(),
                       Operation::GetReplicaVisibleLength);
  OpStats::GetInstance().Record(key, 0, 0, start_time / 1000,
                                byte::GetCurrentTimeInUs() - start_time);
  delete block;
  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::refreshNamenodes(
    message::RpcRequestMessage* r) {
  exceptions::Exception e;
  auto resp_header = ProcessExceptionAfterCallMethod(e, r->RpcHeader());
  ::cloudfs::RefreshNamenodesResponseProto* resp_body = nullptr;
  resp_body = new ::cloudfs::RefreshNamenodesResponseProto();
  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::deleteBlockPool(
    message::RpcRequestMessage* r) {
  // const ::cloudfs::DeleteBlockPoolRequestProto* request,
  // ::cloudfs::DeleteBlockPoolResponseProto* response,
  // TODO(livexmm) deleteBlockPool unimplemented in this version
  return nullptr;
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::getBlockLocalPathInfo(
    message::RpcRequestMessage* r) {
  auto m = static_cast<message::GetBlockLocalPathInfoMessage*>(r);
  auto block = ExtendedBlock::ParseProto(m->Body()->block());
  BlockLocalPathInfo path_info;
  auto token = Token::ParseProto(m->Body()->token());
  auto e = datanode_->GetBlockLocalPathInfo(*block, &path_info, token);

  auto resp_header = ProcessExceptionAfterCallMethod(e, m->RpcHeader());
  ::cloudfs::GetBlockLocalPathInfoResponseProto* resp_body = nullptr;

  if (e.OK()) {
    resp_body = path_info.ToProto();
  }

  delete block;
  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::getHdfsBlockLocations(
    message::RpcRequestMessage* r) {
  auto m = static_cast<message::GetHdfsBlockLocationsMessage*>(r);
  auto request_body = m->Body();
  auto block_pool_id = request_body->blockpoolid();

  std::vector<uint64_t> block_ids;
  auto ids = request_body->blockids();
  for (auto it = ids.begin(); it != ids.end(); it++) {
    block_ids.push_back(*it);
  }

  HdfsBlocksMetadata hdfsBlocksMetadata;
  auto tokens = Token::ParseProtos(m->Body()->tokens());
  tokens.resize(block_ids.size());
  auto e = datanode_->GetHdfsBlocksMetadata(block_pool_id, block_ids,
                                            &hdfsBlocksMetadata, tokens);

  auto resp_header = ProcessExceptionAfterCallMethod(e, m->RpcHeader());
  ::cloudfs::GetHdfsBlockLocationsResponseProto* resp_body = nullptr;

  if (e.OK()) {
    resp_body = hdfsBlocksMetadata.ToProto();
  }

  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::shutdownDatanode(
    message::RpcRequestMessage* r) {
  auto m = static_cast<message::ShutdownDatanodeMessage*>(r);
  auto for_upgrade = m->Body()->forupgrade();
  auto e = datanode_->ShutdownDatanode(for_upgrade);
  auto resp_header = ProcessExceptionAfterCallMethod(e, m->RpcHeader());
  ::cloudfs::ShutdownDatanodeResponseProto* resp_body = nullptr;

  if (e.OK()) {
    resp_body = new ::cloudfs::ShutdownDatanodeResponseProto();
  }

  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::getDatanodeInfo(
    message::RpcRequestMessage* r) {
  // const ::cloudfs::GetDatanodeInfoRequestProto* request,
  // ::cloudfs::GetDatanodeInfoResponseProto* response,
  // TODO(livexmm) getDatanodeInfo unimplemented in this version
  return nullptr;
}

message::RpcResponseMessage*
ClientDatanodeServiceImpl::getReconfigurationStatus(
    message::RpcRequestMessage* r) {
  // const ::cloudfs::GetReconfigurationStatusRequestProto* request,
  // ::cloudfs::GetReconfigurationStatusResponseProto* response
  // TODO(livexmm) getReconfigurationStatus unimplemented in this version
  return nullptr;
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::startReconfiguration(
    message::RpcRequestMessage* r) {
  // const ::cloudfs::StartReconfigurationRequestProto* request,
  // ::cloudfs::StartReconfigurationResponseProto* response,
  // TODO(livexmm) startReconfiguration unimplemented in this version
  return nullptr;
}

message::RpcResponseMessage*
ClientDatanodeServiceImpl::listReconfigurableProperties(
    message::RpcRequestMessage* r) {
  // const ::cloudfs::ListReconfigurablePropertiesRequestProto* request,
  // ::cloudfs::ListReconfigurablePropertiesResponseProto* response,
  // TODO(livexmm) listReconfigurableProperties unimplemented in this version
  return nullptr;
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::triggerBlockReport(
    message::RpcRequestMessage* r) {
  auto m = static_cast<message::TriggerBlockReportMessage*>(r);
  auto incremental = m->Body()->incremental();
  BlockReportOptions options(incremental);
  auto e = datanode_->TriggerBlockReport(options);
  auto resp_header = ProcessExceptionAfterCallMethod(e, m->RpcHeader());
  ::cloudfs::TriggerBlockReportResponseProto* resp_body = nullptr;

  if (e.OK()) {
    resp_body = new ::cloudfs::TriggerBlockReportResponseProto();
  }

  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::getBlocks(
    message::RpcRequestMessage* r) {
  auto start_time = byte::GetCurrentTimeInUs();
  auto m = static_cast<message::GetBlocksMessage*>(r);
  auto request_body = m->Body();
  BlocksWithLocations blocks_with_locations;
  auto e = datanode_->GetBlocks(request_body->bpid(), request_body->size(),
                                &blocks_with_locations);
  ::cloudfs::GetBlocksResponseProto* resp_body = nullptr;

  if (e.OK()) {
    auto blocks_proto = new ::cloudfs::BlocksWithLocationsProto();
    e = blocks_with_locations.ToProto(blocks_proto);
    if (!e.OK()) {
      delete blocks_proto;
      auto resp_header = ProcessExceptionAfterCallMethod(e, m->RpcHeader());
      return new message::RpcResponseMessage(resp_header, resp_body);
    }
    resp_body = new ::cloudfs::GetBlocksResponseProto();
    resp_body->set_allocated_blocks(blocks_proto);
  }

  auto resp_header = ProcessExceptionAfterCallMethod(e, m->RpcHeader());
  OpKey key = BuildKey(r, request_body->bpid(), 0, Operation::GetBlocks);
  OpStats::GetInstance().Record(key, 0, 0, start_time / 1000,
                                byte::GetCurrentTimeInUs() - start_time);

  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::initReplicaRecovery(
    message::RpcRequestMessage* r) {
  auto msg = static_cast<message::InitReplicaRecoveryMessage*>(r);
  auto request_body = msg->Body();
  auto rblk = RecoveringBlock::ParseProto(request_body->block());

  ReplicaRecoveryInfo* rinfo = nullptr;
  auto e = datanode_->InitReplicaRecovery(rblk, &rinfo);
  std::unique_ptr<ReplicaRecoveryInfo> control(rinfo);

  ::cloudfs::InitReplicaRecoveryResponseProto* resp_body = nullptr;
  if (e.OK()) {
    resp_body = new ::cloudfs::InitReplicaRecoveryResponseProto();
    if (rinfo == nullptr) {
      resp_body->set_replicafound(false);
      goto FINISH;
    }
    resp_body->set_replicafound(true);
    auto blk = new ::cloudfs::BlockProto();
    e = rinfo->ToProto(blk);
    if (!e.OK()) {
      delete blk;
      goto FINISH;
    }
    resp_body->set_allocated_block(blk);
    resp_body->set_state(ToReplicaStateProto(rinfo->GetOriginalReplicaState()));
  }

FINISH:
  auto resp_header = ProcessExceptionAfterCallMethod(e, msg->RpcHeader());

  delete rblk;
  return new message::RpcResponseMessage(resp_header, resp_body);
}

message::RpcResponseMessage*
ClientDatanodeServiceImpl::updateReplicaUnderRecovery(
    message::RpcRequestMessage* r) {
  auto msg = static_cast<message::UpdateReplicaUnderRecoveryMessage*>(r);
  auto request_body = msg->Body();
  auto blk = ExtendedBlock::ParseProto(request_body->block());

  std::string storage_id;
  auto e = datanode_->UpdateReplicaUnderRecovery(
      blk, request_body->recoveryid(), request_body->newlength(), &storage_id);

  ::cloudfs::UpdateReplicaUnderRecoveryResponseProto* resp_body = nullptr;
  if (e.OK()) {
    resp_body = new ::cloudfs::UpdateReplicaUnderRecoveryResponseProto();
    resp_body->set_storageuuid(storage_id);
  }

  auto resp_header = ProcessExceptionAfterCallMethod(e, msg->RpcHeader());

  delete blk;
  return new message::RpcResponseMessage(resp_header, resp_body);
}

static void SealBlockDone(byte::CountDownLatch* latch) {
  latch->CountDown();
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::sealBlock(
    message::RpcRequestMessage* r) {
  auto msg = static_cast<message::SealBlockMessage*>(r);
  auto request_body = msg->Body();
  std::unique_ptr<ExtendedBlock> blk(
      ExtendedBlock::ParseProto(request_body->header().block()));
  ::cloudfs::SealBlockResponseProto* resp =
      new ::cloudfs::SealBlockResponseProto();
  auto e = exceptions::Exception();
  do {
    auto replica = datanode_->GetStorage()->GetReplica(blk->GetBlockPoolID(),
                                                       blk->GetBlockID());
    if (replica == nullptr) {
      resp->mutable_header()->set_status(::cloudfs::ERROR_DN_BLOCK_NOT_EXIST);
      LOG(INFO) << "Block is not exist, " << blk->ToString();
      break;
    }
    auto io_service = datanode_->GetHdfsIOService(replica->GetDiskId());
    if (io_service == nullptr) {
      std::string msg = byte::StringPrint("No io service for disk_id %d",
                                          replica->GetDiskId());
      resp->mutable_header()->set_status(::cloudfs::ERROR_DN_INTERNAL_ERROR);
      resp->mutable_header()->set_errorcontext(msg);
      LOG(INFO) << msg << " Block: " << blk->ToString();
      break;
    }
    SimpleController controller;
    byte::CountDownLatch latch(1);
    google::protobuf::Closure* done =
        google::protobuf::NewCallback(&SealBlockDone, &latch);
    bytestore::MemoryPool* mem_pool = mem_pool_repo_.Get();
    datanode_->GetStorage()->SealBlockAsync(io_service->GetCSIOService(),
                                            &controller, request_body, resp,
                                            mem_pool, done);
    latch.Wait();
    mem_pool->DecRef();
    if (resp->header().status() != ::cloudfs::SUCCESS) {
      LOG(INFO) << " Seal block fail: " << blk->ToString()
                << resp->header().errorcontext();
      break;
    }
  } while (false);
  auto resp_header = ProcessExceptionAfterCallMethod(e, msg->RpcHeader());
  return new message::RpcResponseMessage(resp_header, resp);
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::readBlock(
    message::RpcRequestMessage* r) {
  auto msg = static_cast<message::ReadBlockMessage*>(r);
  auto request_body = msg->Body();
  std::unique_ptr<ExtendedBlock> blk(
      ExtendedBlock::ParseProto(request_body->block()));
  uint32_t offset = request_body->offset();
  uint32_t length = request_body->length();
  LOG(INFO) << "Read block " << blk->ToString() << ", offset: " << offset
            << ", length: " << length;
  io::IOChunk* chunk = new io::IOChunk(length);
  byte::ScopeGuard chunks_deleter([&chunk]() {
    chunk->AlwaysDestroy();
  });
  exceptions::Exception e(exceptions::kIllegalArgumentException,
                          "read_priority must be specified");
  bytestore::IOPriority pri = bytestore::PRIORITY_ELASTIC;
  for (auto&& mode = request_body->readpriority().begin();
       mode != request_body->readpriority().end(); ++mode) {
    switch (*mode) {
      case ::cloudfs::ReadBlockRequestProto::HDFS:
      case ::cloudfs::ReadBlockRequestProto::TOS:
        e = datanode_->GetStorage()->ReadBlock(blk.get(), chunk, offset, length,
                                               true, pri);
        break;
      case ::cloudfs::ReadBlockRequestProto::LOCAL:
        e = datanode_->GetStorage()->ReadBlock(blk.get(), chunk, offset, length,
                                               false, pri);
        break;
      default:
        e = datanode_->GetStorage()->ReadBlock(blk.get(), chunk, offset, length,
                                               false, pri);
        break;
    }
    if (!e.OK()) {
      LOG(ERROR) << "Read block " << blk->ToString()
                 << " failed: " << e.ToString();
    } else {
      break;
    }
  }
  if (!e.OK()) {
    LOG(ERROR) << "Read block " << blk->ToString()
               << " failed: " << e.ToString();
    return new message::RpcResponseMessage(
        ProcessExceptionAfterCallMethod(e, msg->RpcHeader()), nullptr);
  } else {
    LOG(INFO) << "Send " << chunk->UsedLength() << " bytes to remote";
    ::cloudfs::ReadBlockResponseProto* proto =
        new ::cloudfs::ReadBlockResponseProto();
    proto->set_length(chunk->UsedLength());
    proto->set_data(reinterpret_cast<const void*>(chunk->UsedData()),
                    chunk->UsedLength());
    return new message::RpcResponseMessage(
        ProcessExceptionAfterCallMethod(e, msg->RpcHeader()), proto);
  }
}

message::RpcResponseMessage* ClientDatanodeServiceImpl::calculateCrc(
    message::RpcRequestMessage* r) {
  auto msg = static_cast<message::CalculateBlockCrcMessage*>(r);
  auto request_body = msg->Body();
  std::shared_ptr<ExtendedBlock> blk(
      ExtendedBlock::ParseProto(request_body->block()));
  LOG(INFO) << "Calculate crc for block " << blk->ToString();
  uint32_t crc;
  auto&& e = datanode_->GetStorage()->CalculateCrc(blk, &crc);
  if (!e.OK()) {
    LOG(ERROR) << "Failed to calculate crc for block " << blk->ToString()
               << ": " << e.ToString();
    return new message::RpcResponseMessage(
        ProcessExceptionAfterCallMethod(e, msg->RpcHeader()), nullptr);
  } else {
    LOG(INFO) << "Calculated crc for block " << blk->ToString() << ": " << crc;
    ::cloudfs::CalculateBlockCrcResponseProto* proto =
        new ::cloudfs::CalculateBlockCrcResponseProto();
    proto->set_crc(crc);
    return new message::RpcResponseMessage(
        ProcessExceptionAfterCallMethod(e, msg->RpcHeader()), proto);
  }
}

OpKey ClientDatanodeServiceImpl::BuildKey(message::RpcRequestMessage* r,
                                          const std::string& bpid,
                                          uint64_t block_id,
                                          const Operation& op) {
  LOG(DEBUG) << "bpid:" << bpid << " operation:" << OperationToString(op);
  auto trace_baggage = TraceBaggage::ParseHeader(r->RpcHeader()).GetBaggages();
  for (auto iter = trace_baggage.cbegin(); iter != trace_baggage.cend();
       ++iter) {
    LOG(DEBUG) << "key:" << iter->first << " value:" << iter->second;
  }
  uint64_t ns_id = BlockPoolManager::ParseNsIdFromBpid(bpid);
  std::string user = trace_baggage["user"];
  std::string task_id = trace_baggage[TRACE_TASK_ID_KEY];
  std::string client_ip = trace_baggage["client"];
  std::string path = trace_baggage["path"];
  std::string local_ip = datanode_->GetHostAddress();
  return OpKey::New(0, ns_id, block_id, task_id, user, client_ip, local_ip,
                    path, op);
}

void ClientDatanodeServiceImpl::Initialize() {
  Register("getReplicaVisibleLength",
           &ClientDatanodeServiceImpl::getReplicaVisibleLength,
           message::GetReplicaVisibleLengthMessage::New);
  Register("getReplicaVisibleLengthV2",
           &ClientDatanodeServiceImpl::getReplicaVisibleLengthV2,
           message::GetReplicaVisibleLengthV2Message::New);
  Register("refreshNamenodes", &ClientDatanodeServiceImpl::refreshNamenodes,
           message::RefreshNamenodesMessage::New);
  Register("deleteBlockPool", &ClientDatanodeServiceImpl::deleteBlockPool,
           nullptr);
  Register("getBlockLocalPathInfo",
           &ClientDatanodeServiceImpl::getBlockLocalPathInfo,
           message::GetBlockLocalPathInfoMessage::New);
  Register("getHdfsBlockLocations",
           &ClientDatanodeServiceImpl::getHdfsBlockLocations,
           message::GetHdfsBlockLocationsMessage::New);
  Register("shutdownDatanode", &ClientDatanodeServiceImpl::shutdownDatanode,
           message::ShutdownDatanodeMessage::New);
  Register("getDatanodeInfo", &ClientDatanodeServiceImpl::getDatanodeInfo,
           nullptr);
  Register("getReconfigurationStatus",
           &ClientDatanodeServiceImpl::getReconfigurationStatus, nullptr);
  Register("startReconfiguration",
           &ClientDatanodeServiceImpl::startReconfiguration, nullptr);
  Register("listReconfigurableProperties",
           &ClientDatanodeServiceImpl::listReconfigurableProperties, nullptr);
  Register("triggerBlockReport", &ClientDatanodeServiceImpl::triggerBlockReport,
           message::TriggerBlockReportMessage::New);
  Register("getBlocks", &ClientDatanodeServiceImpl::getBlocks,
           message::GetBlocksMessage::New);
  Register("initReplicaRecovery",
           &ClientDatanodeServiceImpl::initReplicaRecovery,
           message::InitReplicaRecoveryMessage::New);
  Register("updateReplicaUnderRecovery",
           &ClientDatanodeServiceImpl::updateReplicaUnderRecovery,
           message::UpdateReplicaUnderRecoveryMessage::New);
  Register("readBlock", &ClientDatanodeServiceImpl::readBlock,
           message::ReadBlockMessage::New);
  Register("calculateBlockCrc", &ClientDatanodeServiceImpl::calculateCrc,
           message::CalculateBlockCrcMessage::New);
  Register("sealBlock", &ClientDatanodeServiceImpl::sealBlock,
           message::SealBlockMessage::New);
}

void ClientDatanodeServiceImpl::Register(const std::string& name,
                                         const Method& method,
                                         const Builder& builder) {
  // auto fn = std::bind(method, this, std::placeholders::_1,
  // std::placeholders::_2); methods_.emplace_back(fn);
  methods_.emplace_back(method);
  method_ids_.emplace(name, methods_.size() - 1);
  builders_.emplace_back(builder);
}

}  // namespace bds::dancedn::cloudfs
