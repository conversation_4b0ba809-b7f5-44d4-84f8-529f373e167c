// Copyright (c) 2018, ByteDance Inc. All rights reserved.

#include "cloudfs/blocks_with_locations.h"

#include "cloudfs/block.h"
#include "cloudfs/storage_type.h"

namespace bds::dancedn::cloudfs {

BlocksWithLocations::BlocksWithLocations(
    const std::vector<BlockWithLocations*>& blocks)
    : blocks_(blocks) {}

BlocksWithLocations::~BlocksWithLocations() {
  for (auto block : blocks_) {
    delete block;
  }
  blocks_.clear();
}

exceptions::Exception BlocksWithLocations::ToProto(
    ::cloudfs::BlocksWithLocationsProto* proto) const {
  for (auto block : blocks_) {
    auto blocks_with_locations_proto = proto->add_blocks();
    auto e = block->ToProto(blocks_with_locations_proto);
    if (!e.OK()) {
      return e;
    }
  }
  return exceptions::Exception();
}

}  // namespace bds::dancedn::cloudfs
