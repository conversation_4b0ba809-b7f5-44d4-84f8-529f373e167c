// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/datanode_sender.h"

#include <arpa/inet.h>

#include "butil/crc32c.h"
#include "cloudfs/block_construction_stage.h"
#include "cloudfs/block_sender.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/constants.h"
#include "cloudfs/data_checksum.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_config.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_stream_server.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/io/address.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/io/net.h"
#include "cloudfs/proto/datatransfer.pb.h"
#include "cloudfs/proto/hdfs.pb.h"
#include "cloudfs/security/token.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store.h"

DECLARE_string(bytestore_cfs_location_az);
DECLARE_string(bytestore_cfs_location_switch);
DECLARE_string(bytestore_cfs_location_host);

namespace bds::dancedn::cloudfs {

static const uint8_t PACKET_TYPE_LEN = 1;
static const uint8_t PACKET_CRC_LEN = 4;
static const uint8_t PACKET_MAGIC_LEN = 4;
static const uint8_t MAGIC_NUMBER[4] = {67, 70, 83, 0};

DatanodeSender::DatanodeSender(const IOPriorityOptions& io_priority_options,
                               io::Connection* out_conn)
    : io_priority_options_(io_priority_options),
      out_conn_(out_conn),
      trace_baggage_() {}

int DatanodeSender::WriteBlock(
    ExtendedBlock* block, const StorageType& storage_type,
    const int32_t resident_time, const Token& token,
    const std::string& client_name, const std::vector<DatanodeInfo*>& targets,
    const std::vector<StorageType>& target_storage_types, DatanodeInfo* source,
    const BlockConstructionStage& stage, uint32_t pipeline_size,
    uint64_t min_bytes_rcvd, uint64_t max_bytes_rcvd, uint64_t latest_gs,
    DataChecksum* requested_checksum, CachingStrategy* caching_strategy,
    bool allow_lazy_persist, const std::string& del_hint,
    uint32_t socket_write_timeout_hint, uint32_t socket_read_timeout_hint,
    ::cloudfs::IOPriority io_priority, bool enable_packet_check) {
  auto header = new ::cloudfs::ClientOperationHeaderProto();
  if (BuildClientHeader(block, client_name, token, io_priority, header) !=
      IO_OK) {
    LOG(INFO) << "BuildClientHeader failed";
    delete header;
    return IO_ERR;
  }
  auto checksum_proto = new ::cloudfs::ChecksumProto();
  auto e = requested_checksum->ToProto(checksum_proto);
  if (!e.OK()) {
    LOG(INFO) << "covert requested_checksum to ChecksumProto failed "
                 "exception : "
              << e.ToString();
    delete header;
    delete checksum_proto;
    return IO_ERR;
  }
  ::cloudfs::OpWriteBlockProto proto;
  proto.set_allocated_header(header);
  proto.set_allocated_requestedchecksum(checksum_proto);
  proto.set_enablepacketcheck(enable_packet_check);
  ::cloudfs::StorageTypeProto storage_type_proto;
  e = storage_type.ToProto(&storage_type_proto);
  if (!e.OK()) {
    LOG(INFO) << "covert storage_type to StorageTypeProto failed "
                 "exception : "
              << e.ToString();
    return IO_ERR;
  }
  proto.set_storagetype(storage_type_proto);
  if (resident_time >= 0) {
    proto.set_residenttime(resident_time);
  }
  for (size_t i = 1; i < targets.size(); ++i) {
    auto target_proto = proto.add_targets();
    auto e = targets[i]->ToProto(target_proto);
    if (!e.OK()) {
      LOG(INFO) << "covert targets to DatanodeInfoProto failed "
                   "exception : "
                << e.ToString();
      return IO_ERR;
    }
  }
  for (size_t i = 1; i < target_storage_types.size(); ++i) {
    ::cloudfs::StorageTypeProto target_storage_type_proto;
    e = target_storage_types[i].ToProto(&target_storage_type_proto);
    if (!e.OK()) {
      LOG(INFO) << "covert target_storage_types "
                   "to StorageTypeProto failed "
                   "exception : "
                << e.ToString();
      return IO_ERR;
    }
    proto.add_targetstoragetypes(target_storage_type_proto);
  }
  ::cloudfs::OpWriteBlockProto_BlockConstructionStage stage_proto;
  e = stage.ToProto(&stage_proto);
  if (!e.OK()) {
    return IO_ERR;
  }
  proto.set_stage(stage_proto);
  proto.set_pipelinesize(pipeline_size);
  proto.set_minbytesrcvd(min_bytes_rcvd);
  proto.set_maxbytesrcvd(max_bytes_rcvd);
  proto.set_latestgenerationstamp(latest_gs);
  auto caching_strategy_proto = new ::cloudfs::CachingStrategyProto();
  e = caching_strategy->ToProto(caching_strategy_proto);
  if (!e.OK()) {
    LOG(INFO) << "covert caching_strategy "
                 "to CachingStrategyProto failed "
                 "exception : "
              << e.ToString();
    delete caching_strategy_proto;
    return IO_ERR;
  }
  proto.set_allocated_cachingstrategy(caching_strategy_proto);
  proto.set_allowlazypersist(allow_lazy_persist);
  proto.set_socketwritetimeouthint(socket_write_timeout_hint);
  proto.set_socketreadtimeouthint(socket_read_timeout_hint);

  if (source != nullptr) {
    auto dn_info_proto = new ::cloudfs::DatanodeInfoProto();
    auto e = source->ToProto(dn_info_proto);
    if (!e.OK()) {
      LOG(INFO) << "covert source to DatanodeInfoProto failed "
                   "exception : "
                << e.ToString();
      delete dn_info_proto;
      return IO_ERR;
    }
    proto.set_allocated_source(dn_info_proto);
  }
  proto.set_delhint(del_hint);

  return Send(StreamType::WRITE_BLOCK, proto.SerializeAsString(), out_conn_);
}

int DatanodeSender::CopyBlock(const ExtendedBlock* block, const Token& token,
                              bool ignore_quota) {
  auto block_proto = new ::cloudfs::ExtendedBlockProto();
  auto token_proto = new ::cloudfs::TokenProto();
  token.ToProto(token_proto);
  auto e = block->ToProto(block_proto);
  if (!e.OK()) {
    LOG(INFO) << "CopyBlock failed while convert block to ExtendedBlockProto "
                 "received exceptions: "
              << e.ToString();
    delete token_proto;
    delete block_proto;
    return IO_ERR;
  }
  auto base_header = new ::cloudfs::BaseHeaderProto();
  base_header->set_allocated_block(block_proto);
  auto baggages = trace_baggage_.GetBaggages();
  for (const auto& baggage : baggages) {
    auto added = base_header->add_baggages();
    added->set_name(baggage.first);
    added->set_value(baggage.second);
  }
  base_header->set_allocated_token(token_proto);
  ::cloudfs::OpCopyBlockProto proto;
  proto.set_allocated_header(base_header);
  proto.set_ignorequota(ignore_quota);

  return Send(StreamType::COPY_BLOCK, proto.SerializeAsString(), out_conn_);
}

int DatanodeSender::ReplaceBlock(const ExtendedBlock* block, const Token& token,
                                 const StorageType& storage_type,
                                 const std::string& del_hint,
                                 const DatanodeInfo* source) {
  auto block_proto = new ::cloudfs::ExtendedBlockProto();
  auto token_proto = new ::cloudfs::TokenProto();
  token.ToProto(token_proto);
  auto e = block->ToProto(block_proto);
  if (!e.OK()) {
    LOG(INFO)
        << "ReplaceBlock failed while convert block to ExtendedBlockProto "
           "received exceptions: "
        << e.ToString();
    delete token_proto;
    delete block_proto;
    return IO_ERR;
  }
  auto base_header = new ::cloudfs::BaseHeaderProto();
  base_header->set_allocated_block(block_proto);
  base_header->set_allocated_token(token_proto);

  ::cloudfs::StorageTypeProto storage_type_proto;
  e = storage_type.ToProto(&storage_type_proto);
  if (!e.OK()) {
    LOG(INFO) << "ReplaceBlock failed while convert storageType to "
                 "StorageTypeProto received exceptions: "
              << e.ToString();
    // delete base_header just ok, its destructor will release elements' memory
    delete base_header;
    return IO_ERR;
  }

  auto datanode_info_proto = new ::cloudfs::DatanodeInfoProto();
  e = source->ToProto(datanode_info_proto);
  if (!e.OK()) {
    LOG(INFO) << "ReplaceBlock failed while convert datanodeInfo to "
                 "DatanodeInfoProto received exceptions: "
              << e.ToString();
    delete base_header;
    return IO_ERR;
  }

  ::cloudfs::OpReplaceBlockProto proto;
  proto.set_allocated_header(base_header);
  proto.set_delhint(del_hint);
  proto.set_storagetype(storage_type_proto);
  proto.set_allocated_source(datanode_info_proto);

  return Send(StreamType::REPLACE_BLOCK, proto.SerializeAsString(), out_conn_);
}

int DatanodeSender::BuildClientHeader(
    ExtendedBlock* block, const std::string& client_name, const Token& token,
    ::cloudfs::IOPriority io_priority,
    ::cloudfs::ClientOperationHeaderProto* header) {
  auto base_header = new ::cloudfs::BaseHeaderProto();
  auto block_proto = new ::cloudfs::ExtendedBlockProto();
  auto token_proto = new ::cloudfs::TokenProto();
  token.ToProto(token_proto);
  auto e = block->ToProto(block_proto);
  if (!e.OK()) {
    LOG(INFO) << "covert block to ExtendedBlockProto failed "
                 "exception : "
              << e.ToString();
    delete block_proto;
    delete base_header;
    delete token_proto;
    return IO_ERR;
  }
  base_header->set_allocated_token(token_proto);
  base_header->set_allocated_block(block_proto);
  base_header->set_ioprioclass(io_priority_options_.GetIoPriorityClass());
  base_header->set_iopriolevel(io_priority_options_.GetIoPriorityLevel());
  base_header->mutable_srclocation()->set_az(FLAGS_bytestore_cfs_location_az);
  base_header->mutable_srclocation()->set_switch_(
      FLAGS_bytestore_cfs_location_switch);
  base_header->mutable_srclocation()->set_host(
      FLAGS_bytestore_cfs_location_host);
  auto baggages = trace_baggage_.GetBaggages();
  for (const auto& baggage : baggages) {
    auto added = base_header->add_baggages();
    added->set_name(baggage.first);
    added->set_value(baggage.second);
  }
  // TODO(caibingfeng) Set Trace info while requestShortCircuitShm or
  // releaseShortCircuitFds
  header->set_allocated_baseheader(base_header);
  header->set_clientname(client_name);
  header->set_iopriority(io_priority);
  return IO_OK;
}

void DatanodeSender::Op(StreamType opcode, io::IOChunk* chunk) {
  chunk->WriteFixed16BE(DATANODE_STREAM_VERSION);
  chunk->WriteFixed8BE(opcode);
}

int DatanodeSender::Send(StreamType opcode, const std::string& proto_msg,
                         io::Connection* conn) {
  io::IOBuf* buf = conn->WriteBuf();
  uint32_t chunk_size =
      2 + 1 + io::IOChunk::CalcVarintSize(proto_msg.size()) + proto_msg.size();
  io::IOChunk* chunk = new io::IOChunk(chunk_size);
  // Write Version And OP
  Op(opcode, chunk);

  chunk->WriteVarint(proto_msg.size());
  chunk->WriteBytes((const uint8_t*)proto_msg.data(), proto_msg.size());
  buf->Append(chunk);
  return conn->Write(true);
}

// TODO(yejieqing): StorageType should not be TIERED_NVME
int DatanodeSender::TransferBlock(
    ExtendedBlock* block, const Token& token, const std::string& client_name,
    const std::vector<DatanodeInfo*>& targets,
    const std::vector<StorageType>& target_storage_types) {
  ::cloudfs::OpTransferBlockProto proto;
  ::cloudfs::ClientOperationHeaderProto header;
  if (BuildClientHeader(block, client_name, token,
                        ::cloudfs::IOPriority::PRIORITY_ELASTIC,
                        &header) != IO_OK) {
    LOG(INFO) << "BuildClientHeader failed";
    return IO_ERR;
  }
  proto.set_allocated_header(&header);

  for (auto it = targets.begin(); it != targets.end(); it++) {
    auto target_proto = proto.add_targets();
    auto e = (*it)->ToProto(target_proto);
    if (!e.OK()) {
      LOG(INFO) << "Convert targets to DatanodeInfoProto failed "
                   "exception: "
                << e.ToString();
      return IO_ERR;
    }
  }

  for (auto it = target_storage_types.begin(); it != target_storage_types.end();
       it++) {
    ::cloudfs::StorageTypeProto type_proto;
    auto e = it->ToProto(&type_proto);
    if (!e.OK()) {
      LOG(INFO) << "Convert target_storage_types to StorageTypeProto failed "
                   "exception: "
                << e.ToString();
      return IO_ERR;
    }
    proto.add_targetstoragetypes(type_proto);
  }

  return Send(StreamType::TRANSFER_BLOCK, proto.SerializeAsString(), out_conn_);
}

}  // namespace bds::dancedn::cloudfs
