// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>

#include "aws/s3/S3Client.h"
#include "cloudfs/cfs/rate_limiter.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/lifecycle/block_entry_mgr.h"
#include "cloudfs/store/fetch_manager.h"
#include "cloudfs/store/ufs/hdfs_store.h"
#include "cloudfs/store/ufs/tos_store.h"
#include "cloudfs/store/upload_tos_mgr.h"
#include "common/async_thread.h"
#include "gflags/gflags.h"
#include "gmock/gmock-generated-function-mockers.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace bds::dancedn::cloudfs {
class MockDisk : public bytestore::chunkserver::Disk {
 public:
  MockDisk() {}
  ~MockDisk() {}
  MOCK_METHOD0(Start, CsErrorcode());
  MOCK_METHOD0(Stop, void());
  MOCK_METHOD0(GetChunkIOSystem, bytestore::chunkserver::ChunkIOSystem*());
  MOCK_METHOD0(GetChunkStore, bytestore::chunkserver::ChunkStore*());
  MOCK_METHOD0(GetIOEngine, bytestore::chunkserver::IOEngine*());
  MOCK_METHOD0(GetBaseIOThread, bytestore::AsyncThread*());
  MOCK_METHOD0(GetTaskThreadPool, bytestore::AsyncThreadPool*());
  MOCK_METHOD0(GetReplicateManager,
               bytestore::chunkserver::ReplicateManager*());
  MOCK_METHOD0(GetReplicationFlowController,
               bytestore::chunkserver::FlowController*());
  MOCK_METHOD0(GetTieringMigrationFlowController,
               bytestore::chunkserver::FlowController*());
  MOCK_METHOD0(GetDeleteManager, bytestore::chunkserver::DeleteManager*());
  MOCK_METHOD0(GetDecomRecover, bytestore::chunkserver::DecomRecover*());
  MOCK_METHOD0(GetRecycleBin, bytestore::chunkserver::RecycleBin*());
  MOCK_METHOD0(GetRecycleBinRestorer,
               bytestore::chunkserver::RecycleBinRestorer*());
  MOCK_METHOD0(GetMetaScrubber, bytestore::chunkserver::GenericScrubber*());
  MOCK_METHOD0(GetDataScrubber, bytestore::chunkserver::GenericScrubber*());
  MOCK_METHOD0(GetChunkIOSystemOptions,
               bytestore::chunkserver::ChunkIOSystemOptions());
  MOCK_METHOD0(GetDiskLiveInfoRepo,
               bytestore::chunkserver::DiskLiveInfoRepo*());
  MOCK_METHOD0(GetOpCallbackThreadPool, bytestore::AsyncThreadPool*());
  MOCK_METHOD1(SetPowerStatus, CsErrorcode(bytestore::DiskStatus));
  MOCK_METHOD1(SetDiskStatus, bool(bytestore::DiskStatus));
  MOCK_METHOD1(ForceSetDiskStatus, bool(bytestore::DiskStatus));
  MOCK_CONST_METHOD0(GetDiskStatus, bytestore::DiskStatus());
  MOCK_CONST_METHOD0(GetHardwareStatus,
                     bytestore::chunkserver::DiskHardwareStatus());
  MOCK_CONST_METHOD0(GetDiskId, CsDiskId());
  MOCK_CONST_METHOD0(GetMediaType, bytestore::MediaType());
  MOCK_METHOD0(GetDiskConfig, bytestore::chunkserver::DiskConfig*());
  MOCK_CONST_METHOD0(GetBaseDir, std::string());
  MOCK_METHOD0(DoCheckpoint, void());
  MOCK_METHOD0(WriteCheckpoint, void());
  MOCK_METHOD0(CheckDiskIO, void());
  MOCK_METHOD1(HealthCheck, CsErrorcode(bool));
  MOCK_METHOD1(DEBUG_SetDivicePowerHandler,
               void(bytestore::chunkserver::DevicePowerHandler*));
  MOCK_METHOD1(GetTotalSizeInBytes, CsErrorcode(uint64_t*));
  MOCK_METHOD1(GetUsedSizeInBytes, CsErrorcode(uint64_t*));
  MOCK_METHOD0(DEBUG_GetCommonBackgroundThread, bytestore::AsyncThread*());
  MOCK_METHOD0(DEBUG_GetReplicateThread, bytestore::AsyncThread*());
  MOCK_METHOD0(DEBUG_SetDiskStart, void());
  MOCK_METHOD0(HangUp, void());
  MOCK_METHOD0(HangDown, void());
  MOCK_CONST_METHOD0(IsHangUp, bool());
  MOCK_METHOD1(AtomicSetReferenceCount, int32_t(int32_t));
  MOCK_METHOD0(AtomicIncreReferenceCount, int32_t());
  MOCK_METHOD0(AtomicDecreReferenceCount, int32_t());
  MOCK_CONST_METHOD0(AtomicGetReferenceCount, int32_t());
  MOCK_METHOD0(GetAdaptiveController,
               bytestore::chunkserver::AdaptiveController*());
  MOCK_METHOD0(GetWriteRateLimiter, bytestore::RateLimiter*());
  MOCK_CONST_METHOD0(GetLoadState, bytestore::chunkserver::DiskLoadState());
  MOCK_CONST_METHOD0(GetStartBeginUs, uint64_t());
  MOCK_CONST_METHOD0(GetStartCostUs, uint64_t());
  MOCK_CONST_METHOD0(IsHangCandidate, bool());
  MOCK_METHOD0(GetDiskMetrics, bytestore::chunkserver::DiskMetrics*());
};

}  // namespace bds::dancedn::cloudfs
