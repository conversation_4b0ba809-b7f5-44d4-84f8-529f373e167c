// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include <cstdint>
#include <cstring>
#include <iostream>
#include <memory>
#include <string>

#include "butil/crc32c.h"
#include "byte/concurrent/count_down_latch.h"
#include "byterpc/controller.h"
#include "bytestore/bytestore.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/constants.h"
#include "cloudfs/datanode.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/mocks/mock_cs_io_service.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_partial_block_store.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/proto/hdfs.pb.h"
#include "cloudfs/services/simple_byterpc_controller.h"
#include "cloudfs/store/ufs/tos_info.h"
#include "cloudfs/store/upload_tos_mgr.h"
#include "common/memory_pool.h"
#include "gmock/gmock-actions.h"
#include "gmock/gmock-cardinalities.h"
#include "gmock/gmock-generated-actions.h"
#include "gmock/gmock-more-actions.h"
#include "gmock/gmock-spec-builders.h"
#include "include/macros.h"
#include "system/timestamp.h"
#include "thread/this_thread.h"

DECLARE_int64(bytestore_cfs_remote_reader_cache_ttl);
DECLARE_bool(bytestore_cfs_enable_partial_block_read);

namespace bds::dancedn::cloudfs {

class UnifiedBlockStoreTests : public ::testing::Test {
 public:
  void SetUp() override {
    byterpc::ExecCtx::Init(byterpc::InitOptions());
    bytestore::metrics_internal::InitFastMetrics();
    datanode_ = new MockDataNode();
    chunkserver_store_ = new MockChunkserverStore();
    tos_store_ = new MockTosStore();
    fetch_mgr_ = new MockFetchManager();
    store_ = new UnifiedBlockStore();
    partial_block_store_ = new MockPartialBloockStore();
    store_->SetDn(datanode_);
    store_->SetLocalStore(chunkserver_store_);
    store_->TEST_SetFetchManager(fetch_mgr_);
    store_->TEST_SetRemote(NamespaceType::TOS_MANAGED, tos_store_);
    store_->TEST_SetPartialBlockStore(partial_block_store_);
    ns_info_ = new NameSpaceInfo(1, "", "", 2, "", "", 3, 4,
                                 NamespaceType::TOS_MANAGED);
    EXPECT_CALL(*datanode_, GetStorage())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(store_));
    EXPECT_CALL(*datanode_, GetNamespaceInfo(::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(ns_info_));
  }

  void TearDown() override {
    store_->StopMgr();
    delete datanode_;
    delete store_;
    delete ns_info_;
  }

  static void SetUpTestCase() {}

  // static void TearDownTestCase() { byte::GetLoggingSystem()->Shutdown(); }

  void generate_random_data(char* data, size_t size) {
    const char charset[] =
        "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    const size_t max_index = sizeof(charset);
    for (size_t i = 0; i < size; ++i) {
      data[i] = charset[random() % max_index];
    }
  }

 public:
  UnifiedBlockStore* store_;
  MockDataNode* datanode_;
  MockChunkserverStore* chunkserver_store_;
  MockTosStore* tos_store_;
  MockFetchManager* fetch_mgr_;
  MockPartialBloockStore* partial_block_store_;
  CsMemPool mem_pool_;
  NameSpaceInfo* ns_info_;
};

void DummyBlockOPDone(BlockOPType* type) {}
struct BlockOPContext {
  BlockOPType type;
  byte::CountDownLatch* latch;
};
void BlockOPDone(BlockOPContext* ctx) {
  // LOG(INFO) << "Block OP finish, type: " << ctx->type;
  ctx->latch->CountDown();
}

TEST_F(UnifiedBlockStoreTests, DoMerge) {
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  std::shared_ptr<std::vector<std::shared_ptr<LocatedBlock>>> located_blocks =
      std::make_shared<std::vector<std::shared_ptr<LocatedBlock>>>();
  std::shared_ptr<NameSpaceInfo> tos_managed = std::make_shared<NameSpaceInfo>(
      0, "cluster_id", "bpid", 0, "build_version", "software_version", 0, 0,
      NamespaceType::TOS_MANAGED);
  std::shared_ptr<NameSpaceInfo> tos_local = std::make_shared<NameSpaceInfo>(
      0, "cluster_id", "bpid", 0, "build_version", "software_version", 0, 0,
      NamespaceType::TOS_LOCAL);
  EXPECT_CALL(*datanode_, GetNamespaceInfo(::testing::_))
      .Times(3)
      .WillOnce(::testing::Return(nullptr))
      .WillOnce(::testing::Return(tos_local.get()))
      .WillOnce(::testing::Return(tos_managed.get()));
  store_->TestDoMerge(block, located_blocks);
  store_->TestDoMerge(block, located_blocks);
  store_->TestDoMerge(block, located_blocks);
}

TEST_F(UnifiedBlockStoreTests, CalculateCrc) {
  char* buffer = new char[1024 * 1024 * 128];
  memset(buffer, 'a', 128 * 1024 * 1024);
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  uint32_t crc;
  EXPECT_CALL(*chunkserver_store_,
              ReadBlock(::testing::_, ::testing::_, ::testing::_, ::testing::_,
                        ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(
          ::testing::Invoke([&buffer](ExtendedBlock* block, io::IOChunk* chunk,
                                      uint32_t data_offset, uint32_t data_len,
                                      CsIoPriority priority) {
            chunk->WriteBytes(reinterpret_cast<const uint8_t*>(buffer),
                              128 * 1024 * 1024);
            RETURN_NO_EXCEPTION();
          }));
  exceptions::Exception e = store_->CalculateCrc(block, &crc);
  EXPECT_EQ(e.GetE(), exceptions::kIOException);
  e = store_->CalculateCrc(block, &crc);
  EXPECT_TRUE(e.OK());
  EXPECT_EQ(crc, butil::crc32c::Extend(0, buffer, 128 * 1024 * 1024));
  delete[] buffer;
}

TEST_F(UnifiedBlockStoreTests, AsyncIO) {
  EXPECT_CALL(
      *chunkserver_store_,
      CreateBlockAsync(::testing::_, ::testing::_, ::testing::_, ::testing::_,
                       ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](bytestore::DiskId disk_id, CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::CreateBlockRequestProto* request,
                         ::cloudfs::CreateBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_,
              WriteBlockAsync(::testing::_, ::testing::_, ::testing::_,
                              ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::WriteBlockRequestProto* request,
                         ::cloudfs::WriteBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_,
              ReadBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::ClientReadBlockRequestProto* request,
                         ::cloudfs::ClientReadBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_, GetNamespaceType(::testing::_, ::testing::_))
      .WillRepeatedly([](const std::string& bpid, NamespaceType* ns_type) {
        return true;
      });
  EXPECT_CALL(*chunkserver_store_,
              SyncBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::SyncBlockRequestProto* request,
                         ::cloudfs::SyncBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_,
              SealBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::SealBlockRequestProto* request,
                         ::cloudfs::SealBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_,
              FinalizeBlockAsync(::testing::_, ::testing::_, ::testing::_,
                                 ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::FinalizeBlockRequestProto* request,
                         ::cloudfs::FinalizeBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_,
              PingBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::PingBlockRequestProto* request,
                         ::cloudfs::PingBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(::cloudfs::SUCCESS);
        done->Run();
      });
  ::cloudfs::CreateBlockRequestProto c_request;
  ::cloudfs::CreateBlockResponseProto c_response;
  BlockOPType type = BlockOPType::CREATE;
  google::protobuf::Closure* done =
      google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  store_->CreateBlockAsync(1, nullptr, nullptr, &c_request, &c_response,
                           &mem_pool_, done);
  EXPECT_TRUE(c_response.has_header());
  EXPECT_TRUE(c_response.header().has_status());
  EXPECT_EQ(c_response.header().status(), ::cloudfs::SUCCESS);

  ::cloudfs::WriteBlockRequestProto w_request;
  ::cloudfs::WriteBlockResponseProto w_response;
  type = BlockOPType::WRITE;
  done = google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  store_->WriteBlockAsync(nullptr, nullptr, &w_request, &w_response, &mem_pool_,
                          done);
  EXPECT_TRUE(w_response.has_header());
  EXPECT_TRUE(w_response.header().has_status());
  EXPECT_EQ(w_response.header().status(), ::cloudfs::SUCCESS);

  ::cloudfs::ClientReadBlockRequestProto r_request;
  ::cloudfs::ClientReadBlockResponseProto r_response;
  type = BlockOPType::READ_DISK;
  done = google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  std::shared_ptr<SimpleController> controller =
      std::make_shared<SimpleController>();
  store_->ReadBlockAsync(nullptr, controller.get(), &r_request, &r_response,
                         &mem_pool_, done);
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);

  ::cloudfs::SyncBlockRequestProto s_request;
  ::cloudfs::SyncBlockResponseProto s_response;
  type = BlockOPType::SYNC;
  done = google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  store_->SyncBlockAsync(nullptr, nullptr, &s_request, &s_response, &mem_pool_,
                         done);
  EXPECT_TRUE(s_response.has_header());
  EXPECT_TRUE(s_response.header().has_status());
  EXPECT_EQ(s_response.header().status(), ::cloudfs::SUCCESS);

  ::cloudfs::SealBlockRequestProto s2_request;
  ::cloudfs::SealBlockResponseProto s2_response;
  type = BlockOPType::SEAL;
  done = google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  store_->SealBlockAsync(nullptr, nullptr, &s2_request, &s2_response,
                         &mem_pool_, done);
  EXPECT_TRUE(s2_response.has_header());
  EXPECT_TRUE(s2_response.header().has_status());
  EXPECT_EQ(s2_response.header().status(), ::cloudfs::SUCCESS);

  ::cloudfs::FinalizeBlockRequestProto f_request;
  ::cloudfs::FinalizeBlockResponseProto f_response;
  type = BlockOPType::SEAL;
  done = google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  store_->FinalizeBlockAsync(nullptr, nullptr, &f_request, &f_response,
                             &mem_pool_, done);
  EXPECT_TRUE(f_response.has_header());
  EXPECT_TRUE(f_response.header().has_status());
  EXPECT_EQ(f_response.header().status(), ::cloudfs::SUCCESS);

  ::cloudfs::PingBlockRequestProto p_request;
  ::cloudfs::PingBlockResponseProto p_response;
  type = BlockOPType::PING;
  done = google::protobuf::NewCallback(&DummyBlockOPDone, &type);
  store_->PingBlockAsync(nullptr, nullptr, &p_request, &p_response, &mem_pool_,
                         done);
  EXPECT_TRUE(p_response.has_header());
  EXPECT_TRUE(p_response.header().has_status());
  EXPECT_EQ(p_response.header().status(), ::cloudfs::SUCCESS);
}

// 1. Get Namespace type failed
// 2. Get Namesapce type LOCAL
//   1. Read from block_store successfully
//   2. Read from blcok_store failed
TEST_F(UnifiedBlockStoreTests, UnifiedReadAsync) {
  FLAGS_bytestore_cfs_enable_partial_block_read = true;
  bytestore::chunkserver::CSIOServiceOptions options;
  std::shared_ptr<byterpc::Controller> controller;
  ::cloudfs::ClientReadBlockRequestProto r_request;
  ::cloudfs::ClientReadBlockResponseProto r_response;
  r_request.mutable_header()->mutable_block()->set_poolid("BP-1-1");
  r_request.mutable_header()->mutable_block()->set_blockid(1);
  r_request.mutable_header()->mutable_block()->set_numbytes(1024 * 1024);
  r_request.set_offset(0);
  r_request.set_length(1024);
  r_request.set_stream_id(1);
  std::shared_ptr<byte::CountDownLatch> latch;
  BlockOPContext context;
  google::protobuf::Closure* done;

  // 1
  controller = std::make_shared<SimpleController>();
  latch = std::make_shared<byte::CountDownLatch>(1);
  context.latch = latch.get();
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  EXPECT_CALL(*chunkserver_store_, GetNamespaceType(::testing::_, ::testing::_))
      .WillOnce(::testing::Invoke(
          [](const std::string& bpid, NamespaceType* ns_type) {
            return false;
          }));
  store_->ReadBlockAsync(nullptr, controller.get(), &r_request, &r_response,
                         &mem_pool_, done);
  latch->Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::ERROR_INVALID);

  // 2.1
  controller = std::make_shared<SimpleController>();
  latch = std::make_shared<byte::CountDownLatch>(1);
  context.latch = latch.get();
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  EXPECT_CALL(*chunkserver_store_, GetNamespaceType(::testing::_, ::testing::_))
      .WillOnce(::testing::Invoke(
          [](const std::string& bpid, NamespaceType* ns_type) {
            *ns_type = NamespaceType::LOCAL;
            return true;
          }));

  EXPECT_CALL(*chunkserver_store_,
              ReadBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .WillOnce(::testing::Invoke(
          [](CsIoService* io_service,
             google::protobuf::RpcController* controller,
             const ::cloudfs::ClientReadBlockRequestProto* request,
             ::cloudfs::ClientReadBlockResponseProto* response,
             CsMemPool* mem_pool, google::protobuf::Closure* done) {
            char* buffer = new char[request->length()];
            BYTE_DEFER(delete[] buffer);
            byterpc::Controller* ctrl =
                reinterpret_cast<byterpc::Controller*>(controller);
            ctrl->response_attachment().append(buffer, request->length());
            response->mutable_header()->set_status(::cloudfs::SUCCESS);
            done->Run();
          }));
  store_->ReadBlockAsync(nullptr, controller.get(), &r_request, &r_response,
                         &mem_pool_, done);
  latch->Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(controller->response_attachment().length(), r_request.length());

  // 2.2
  controller = std::make_shared<SimpleController>();
  latch = std::make_shared<byte::CountDownLatch>(1);
  context.latch = latch.get();
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  EXPECT_CALL(*chunkserver_store_, GetNamespaceType(::testing::_, ::testing::_))
      .WillOnce(::testing::Invoke(
          [](const std::string& bpid, NamespaceType* ns_type) {
            *ns_type = NamespaceType::LOCAL;
            return true;
          }));

  EXPECT_CALL(*chunkserver_store_,
              ReadBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .WillOnce(::testing::Invoke(
          [](CsIoService* io_service,
             google::protobuf::RpcController* controller,
             const ::cloudfs::ClientReadBlockRequestProto* request,
             ::cloudfs::ClientReadBlockResponseProto* response,
             CsMemPool* mem_pool, google::protobuf::Closure* done) {
            response->mutable_header()->set_status(
                ::cloudfs::ERROR_DN_INTERNAL_ERROR);
            done->Run();
          }));
  store_->ReadBlockAsync(nullptr, controller.get(), &r_request, &r_response,
                         &mem_pool_, done);
  latch->Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::ERROR_DN_INTERNAL_ERROR);
}

TEST_F(UnifiedBlockStoreTests, ReadAsync) {
  FLAGS_bytestore_cfs_enable_partial_block_read = false;
  BYTE_DEFER(FLAGS_bytestore_cfs_enable_partial_block_read = true);
  EXPECT_CALL(*chunkserver_store_,
              ReadBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](CsIoService* io_service,
                         google::protobuf::RpcController* controller,
                         const ::cloudfs::ClientReadBlockRequestProto* request,
                         ::cloudfs::ClientReadBlockResponseProto* response,
                         CsMemPool* mem_pool, google::protobuf::Closure* done) {
        response->mutable_header()->set_status(
            ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST);
        done->Run();
      });
  EXPECT_CALL(*chunkserver_store_, GetNamespaceType(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](const std::string& bpid, NamespaceType* ns_type) {
        *ns_type = NamespaceType::TOS_MANAGED;
        return true;
      });
  const uint64_t TEST_BLOCK_SIZE = 32 * 1024 * 1024;
  std::shared_ptr<char> read_buf(new char[TEST_BLOCK_SIZE]);
  generate_random_data(read_buf.get(), TEST_BLOCK_SIZE);
  EXPECT_CALL(*tos_store_,
              GetBlockInfo(testing::_, testing::_, testing::_, testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(testing::Invoke(
          [&](const std::string& bpid, const std::string& object_key,
              CsIoPriority priority, std::shared_ptr<ExtendedBlock>& block) {
            block = std::make_shared<ExtendedBlock>("bpid", 1, TEST_BLOCK_SIZE,
                                                    1, false);
            return exceptions::Exception();
          }));
  EXPECT_CALL(*tos_store_, ReadBlock(testing::_, testing::_, testing::_,
                                     testing::_, testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(testing::Invoke(
          [&read_buf](const ExtendedBlock& block, uint64_t data_offset,
                      uint32_t data_len, CsIoPriority priority,
                      io::IOChunk* chunk) {
            if (data_offset + data_len > TEST_BLOCK_SIZE) {
              return exceptions::Exception(exceptions::E::kEOFException);
            }
            chunk->WriteBytes(
                reinterpret_cast<uint8_t*>(read_buf.get()) + data_offset,
                data_len);
            return exceptions::Exception();
          }));
  EXPECT_CALL(*fetch_mgr_, StartSyncCacheBlock(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(testing::Return(nullptr));
  EXPECT_CALL(*fetch_mgr_, AsyncCacheBlock(::testing::_, ::testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(testing::Return());
  SimpleController controller;

  // Read first 1024 bytes
  ::cloudfs::ClientReadBlockRequestProto r_request;
  ::cloudfs::ClientReadBlockResponseProto r_response;
  r_request.mutable_header()->mutable_block()->set_blockid(1);
  r_request.mutable_header()->mutable_block()->set_numbytes(TEST_BLOCK_SIZE);
  r_request.set_offset(0);
  r_request.set_length(1024);
  r_request.set_stream_id(1);
  byte::CountDownLatch latch(1);
  BlockOPContext context;
  context.latch = &latch;
  context.type = BlockOPType::READ_REMOTE;
  google::protobuf::Closure* done =
      google::protobuf::NewCallback(&BlockOPDone, &context);
  store_->ReadBlockAsync(nullptr, &controller, &r_request, &r_response,
                         &mem_pool_, done);
  latch.Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(r_response.header().errorcontext(), "");
  EXPECT_TRUE(controller.HasOutgoingAttachment());
  EXPECT_EQ(controller.response_attachment().length(), 1024);
  {
    std::unique_ptr<char> tmp_buf(new char[1024]);
    controller.response_attachment().copy_to(tmp_buf.get(), 1024);
    EXPECT_EQ(memcmp(tmp_buf.get(), read_buf.get(), 1024), 0);
  }

  // Read following 2M bytes
  r_response.clear_header();
  controller.Reset();
  r_request.set_offset(1024);
  r_request.set_length(2 * 1024 * 1024);
  r_request.set_stream_id(1);
  byte::CountDownLatch latch2(1);
  context.latch = &latch2;
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  store_->ReadBlockAsync(nullptr, &controller, &r_request, &r_response,
                         &mem_pool_, done);
  latch2.Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(r_response.header().errorcontext(), "");
  EXPECT_TRUE(controller.HasOutgoingAttachment());
  EXPECT_EQ(controller.response_attachment().length(), 2 * 1024 * 1024);
  {
    std::unique_ptr<char> tmp_buf(new char[2 * 1024 * 1024]);
    controller.response_attachment().copy_to(tmp_buf.get(), 2 * 1024 * 1024);
    EXPECT_EQ(memcmp(tmp_buf.get(), read_buf.get() + 1024, 2 * 1024 * 1024), 0);
    EXPECT_NE(memcmp(tmp_buf.get(), read_buf.get() + 1023, 2 * 1024 * 1024), 0);
  }

  EXPECT_EQ(store_->TEST_GetRemoteReaderCacheSize(), 1);
  EXPECT_CALL(*chunkserver_store_, GetNamespaceType(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly([](const std::string& bpid, NamespaceType* ns_type) {
        *ns_type = NamespaceType::ACC_TOS;
        return true;
      });
  // Use another stream id.
  r_response.clear_header();
  controller.Reset();
  r_request.set_offset(1024 + 2 * 1024 * 1024);
  r_request.set_length(2 * 1024 * 1024);
  r_request.set_stream_id(2);
  byte::CountDownLatch latch3(1);
  context.latch = &latch3;
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  store_->ReadBlockAsync(nullptr, &controller, &r_request, &r_response,
                         &mem_pool_, done);
  latch3.Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(r_response.header().errorcontext(), "");
  EXPECT_TRUE(controller.HasOutgoingAttachment());
  EXPECT_EQ(controller.response_attachment().length(), 2 * 1024 * 1024);
  {
    std::unique_ptr<char> tmp_buf(new char[2 * 1024 * 1024]);
    controller.response_attachment().copy_to(tmp_buf.get(), 2 * 1024 * 1024);
    EXPECT_EQ(memcmp(tmp_buf.get(), read_buf.get() + 1024 + 2 * 1024 * 1024,
                     2 * 1024 * 1024),
              0);
    EXPECT_NE(memcmp(tmp_buf.get(), read_buf.get() + 1023 + 2 * 1024 * 1024,
                     2 * 1024 * 1024),
              0);
  }
  EXPECT_EQ(store_->TEST_GetRemoteReaderCacheSize(), 2);
  // Use same stream id but seek to another offset, [15MB, 16MB), block.offset
  // == 10MB
  r_response.clear_header();
  controller.Reset();
  r_request.mutable_header()->mutable_block()->set_offset(10 * 1024 * 1024);
  r_request.set_offset(5 * 1024 * 1024);
  r_request.set_length(2 * 1024 * 1024);
  r_request.set_stream_id(2);
  byte::CountDownLatch latch4(1);
  context.latch = &latch4;
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  store_->ReadBlockAsync(nullptr, &controller, &r_request, &r_response,
                         &mem_pool_, done);
  latch4.Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(r_response.header().errorcontext(), "");
  EXPECT_TRUE(controller.HasOutgoingAttachment());
  EXPECT_EQ(controller.response_attachment().length(), 2 * 1024 * 1024);
  {
    std::unique_ptr<char> tmp_buf(new char[2 * 1024 * 1024]);
    controller.response_attachment().copy_to(tmp_buf.get(), 2 * 1024 * 1024);
    EXPECT_EQ(memcmp(tmp_buf.get(), read_buf.get() + 15 * 1024 * 1024,
                     2 * 1024 * 1024),
              0);
    EXPECT_NE(memcmp(tmp_buf.get(), read_buf.get() + 5 * 1024 * 1024,
                     2 * 1024 * 1024),
              0);
  }
  // Still have 2 reader
  EXPECT_EQ(store_->TEST_GetRemoteReaderCacheSize(), 2);

  // PRead
  r_response.clear_header();
  controller.Reset();
  r_request.mutable_header()->mutable_block()->set_offset(0);
  r_request.set_offset(10 * 1024 * 1024);
  r_request.set_length(2 * 1024 * 1024);
  r_request.set_stream_id(2);
  r_request.set_type(::cloudfs::ClientReadBlockRequestProto_ReadType_PRead);
  byte::CountDownLatch latch5(1);
  context.latch = &latch5;
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  store_->ReadBlockAsync(nullptr, &controller, &r_request, &r_response,
                         &mem_pool_, done);
  latch5.Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(r_response.header().errorcontext(), "");
  EXPECT_TRUE(controller.HasOutgoingAttachment());
  EXPECT_EQ(controller.response_attachment().length(), 2 * 1024 * 1024);
  {
    std::unique_ptr<char> tmp_buf(new char[2 * 1024 * 1024]);
    controller.response_attachment().copy_to(tmp_buf.get(), 2 * 1024 * 1024);
    EXPECT_EQ(memcmp(tmp_buf.get(), read_buf.get() + 10 * 1024 * 1024,
                     2 * 1024 * 1024),
              0);
    EXPECT_NE(memcmp(tmp_buf.get(), read_buf.get() + 10 * 1024 * 1024 - 1,
                     2 * 1024 * 1024),
              0);
  }

  // PRead [999, 999 + 2M)
  r_response.clear_header();
  controller.Reset();
  r_request.mutable_header()->mutable_block()->set_offset(999);
  r_request.set_offset(0);
  r_request.set_length(2 * 1024 * 1024);
  r_request.set_stream_id(2);
  r_request.set_type(::cloudfs::ClientReadBlockRequestProto_ReadType_PRead);
  byte::CountDownLatch latch6(1);
  context.latch = &latch6;
  context.type = BlockOPType::READ_REMOTE;
  done = google::protobuf::NewCallback(&BlockOPDone, &context);
  store_->ReadBlockAsync(nullptr, &controller, &r_request, &r_response,
                         &mem_pool_, done);
  latch6.Wait();
  EXPECT_TRUE(r_response.has_header());
  EXPECT_TRUE(r_response.header().has_status());
  EXPECT_EQ(r_response.header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(r_response.header().errorcontext(), "");
  EXPECT_TRUE(controller.HasOutgoingAttachment());
  EXPECT_EQ(controller.response_attachment().length(), 2 * 1024 * 1024);
  {
    std::unique_ptr<char> tmp_buf(new char[2 * 1024 * 1024]);
    controller.response_attachment().copy_to(tmp_buf.get(), 2 * 1024 * 1024);
    EXPECT_EQ(memcmp(tmp_buf.get(), read_buf.get() + 999, 2 * 1024 * 1024), 0);
    EXPECT_NE(memcmp(tmp_buf.get(), read_buf.get(), 2 * 1024 * 1024), 0);
  }

  byte::ThisThread::SleepInMs(FLAGS_bytestore_cfs_remote_reader_cache_ttl * 2 +
                              500);
  EXPECT_EQ(store_->TEST_GetRemoteReaderCacheSize(), 0);
}

}  // namespace bds::dancedn::cloudfs
