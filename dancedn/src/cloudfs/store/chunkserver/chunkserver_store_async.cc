// copyright (c) 2019-present, bytedance inc. all rights reserved.
#include <cstdint>
#include <memory>
#include <string>

#include "base/closure.h"
#include "byterpc/util/closure_guard.h"
#include "cloudfs/cfs/io_priority.h"
#include "cloudfs/datanode.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/lifecycle/block_entry.h"
#include "cloudfs/lifecycle/block_entry_mgr.h"
#include "cloudfs/meta.h"
#include "cloudfs/metrics.h"
#include "cloudfs/proto/hdfs.pb.h"
#include "cloudfs/replica_sealed.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "common/memory_pool.h"
#include "common/metrics.h"
#include "include/macros.h"
#include "util/defer.h"

DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_uint32(bytestore_hdfs_write_timeout_us);
DECLARE_uint32(bytestore_hdfs_read_timeout_us);
DECLARE_uint32(bytestore_hdfs_set_xattr_timeout_us);
DECLARE_uint32(bytestore_hdfs_sync_timeout_us);
DECLARE_uint32(bytestore_hdfs_create_timeout_us);
DECLARE_uint32(bytestore_hdfs_truncate_timeout_us);

namespace bds::dancedn::cloudfs {

const char* BlockOPToString(BlockOPType type) {
  switch (type) {
    case BlockOPType::QUERY: return "query";
    case BlockOPType::CREATE: return "create";
    case BlockOPType::WRITE: return "write";
    case BlockOPType::READ_DISK: return "read_disk";
    case BlockOPType::READ_REMOTE: return "read_remote";
    case BlockOPType::SYNC: return "sync";
    case BlockOPType::SEAL: return "seal";
    case BlockOPType::PING: return "ping";
    case BlockOPType::FINALIZE: return "finalize";
    case BlockOPType::CHECKSUM: return "checksum";
    default: BYTE_ASSERT(false); return "unkown";
  }
}

struct ScopedAsyncCallbackMetric {
  explicit ScopedAsyncCallbackMetric(BlockOPType type)
      : start_us_(byte::GetCurrentTimeInUs()), type_(type) {}
  ~ScopedAsyncCallbackMetric() {
    METRICS_rpc_async_resp_callback_latency_us
        ->GetMetric({{"type", BlockOPToString(type_)}})
        ->Set(byte::GetCurrentTimeInUs() - start_us_);
  }

 private:
  uint64_t start_us_;
  BlockOPType type_;
};

template <class ResponseT>
static inline void FillResponseCommonHeader(ResponseT* res,
                                            ::cloudfs::Status status,
                                            const std::string& err_msg) {
  res->mutable_header()->set_status(status);
  if (status != ::cloudfs::SUCCESS) {
    res->mutable_header()->set_errorcontext(err_msg);
  }
}

BlockAsyncCbContext::BlockAsyncCbContext(bytestore::MemoryPool* pool,
                                         ExtendedBlock* blk, BlockOPType type,
                                         int64_t req_t_us)
    : mem_pool_(pool),
      req_start_time_us_(req_t_us),
      init_time_us_(byte::GetCurrentTimeInUs()),
      block_(blk),
      op_type_(type) {
  const std::string& bpid = block_->GetBlockPoolID();
  bytestore::Tags block_op_tags = {{"bpid", bpid},
                                   {"optype", BlockOPToString(op_type_)}};
  METRICS_rpc_async_block_op_num->GetMetric(block_op_tags)->Increment();
  METRICS_rpc_async_block_op_ongoing_num->GetMetric(block_op_tags)->Increment();
}

BlockAsyncCbContext::~BlockAsyncCbContext() {}

void BlockAsyncCbContext::Complete(::cloudfs::Status status,
                                   const std::string& err_msg) {
  const std::string& bpid = block_->GetBlockPoolID();
  bytestore::Tags block_op_tags = {{"bpid", bpid},
                                   {"optype", BlockOPToString(op_type_)}};
  METRICS_rpc_async_block_op_ongoing_num->GetMetric(block_op_tags)->Decrement();
  if (this->status_ == ::cloudfs::SUCCESS) {
    int64_t end_time_us = byte::GetCurrentTimeInUs();
    METRICS_rpc_async_block_op_latency_us->GetMetric(block_op_tags)
        ->Set(end_time_us - this->req_start_time_us_);
    int64_t init_time = this->init_time_us_ - this->req_start_time_us_;
    int64_t io_time = this->io_finish_time_us_ - this->init_time_us_;
    int64_t resp_time = end_time_us - this->io_finish_time_us_;
    if (init_time > 0) {
      METRICS_rpc_async_block_op_init_latency_us->GetMetric(block_op_tags)
          ->Set(init_time);
    }
    if (io_time > 0) {
      METRICS_rpc_async_block_op_io_latency_us->GetMetric(block_op_tags)
          ->Set(io_time);
    }
    if (resp_time > 0) {
      METRICS_rpc_async_block_op_resp_latency_us->GetMetric(block_op_tags)
          ->Set(resp_time);
    }
  }
}

CreateBlockCbContext::CreateBlockCbContext(bytestore::MemoryPool* pool,
                                           ExtendedBlock* blk, int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::CREATE, req_t_us) {}
CreateBlockCbContext::~CreateBlockCbContext() {}

void CreateBlockCbContext::SendResponse(::cloudfs::Status status,
                                        const std::string& err_msg) {
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Create block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  } else {
    LOG(INFO) << "Create block finish, block id:" << block_->ToString();
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}

void CreateBlockCbContext::Complete(::cloudfs::Status status,
                                    const std::string& err_msg) {
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  if (status != ::cloudfs::SUCCESS) {
    return SendResponse(status, err_msg);
  }
  StorageDirectory directory;
  if (!cs_store_->GetStorageDirectory(disk_id_, &directory).OK()) {
    return SendResponse(::cloudfs::ERROR_INVALID,
                        "Get storage directory failed.");
  }
  std::string bpid = block_->GetBlockPoolID();
  bool checksum_enabled = FLAGS_bytestore_hdfs_checksum_mode >= 2;
  std::shared_ptr<ReplicaBeingWritten> new_replica_info =
      std::make_shared<ReplicaBeingWritten>(
          bpid, block_->GetBlockID(), block_->GetGS(), block_->IsPin(),
          directory.storage_uuid_, directory.storage_type_.IsTransient(), false,
          static_cast<int>(this->ns_type_), checksum_enabled, disk_id_,
          this->placement_type_);
  cs_store_->AddVolumeMap(bpid, new_replica_info);
  SendResponse(::cloudfs::SUCCESS, "");
}

WriteBlockCbContext::WriteBlockCbContext(bytestore::MemoryPool* pool,
                                         ExtendedBlock* blk, int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::WRITE, req_t_us) {}
WriteBlockCbContext::~WriteBlockCbContext() {
  if (this->status_ == ::cloudfs::SUCCESS) {
    METRICS_rpc_async_block_write_bytes
        ->GetMetric({{"bpid", block_->GetBlockPoolID()}})
        ->Add(length_);
  }
}
void WriteBlockCbContext::Complete(::cloudfs::Status status,
                                   const std::string& err_msg) {
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  ReplicaBeingWritten* replica_rbw =
      dynamic_cast<ReplicaBeingWritten*>(replica_.get());
  if (offset_ + length_ > replica_rbw->GetBytesOnDisk()) {
    replica_rbw->SetDiskDataLen(offset_ + length_);
  }
  if (offset_ + length_ > replica_->GetNumBytes()) {
    replica_rbw->SetNumBytes(offset_ + length_);
  }
  if (visible_length_ >= 0 &&
      visible_length_ > replica_rbw->GetVisibleLength()) {
    replica_rbw->SetBytesAcked(visible_length_);
  }
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Write block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}

ReadBlockCbContext::ReadBlockCbContext(bytestore::MemoryPool* pool,
                                       ExtendedBlock* blk, int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::READ_DISK, req_t_us) {}
ReadBlockCbContext::~ReadBlockCbContext() {
  if (this->status_ == ::cloudfs::SUCCESS) {
    METRICS_rpc_async_block_read_bytes
        ->GetMetric({{"bpid", block_->GetBlockPoolID()}, {"source", "disk"}})
        ->Add(read_bytes_);
  }
}
void ReadBlockCbContext::Complete(::cloudfs::Status status,
                                  const std::string& err_msg) {
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Read block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}

SyncBlockCbContext::SyncBlockCbContext(bytestore::MemoryPool* pool,
                                       ExtendedBlock* blk, int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::SYNC, req_t_us) {}
SyncBlockCbContext::~SyncBlockCbContext() {}
void SyncBlockCbContext::Complete(::cloudfs::Status status,
                                  const std::string& err_msg) {
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Sync block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}

SealBlockCbContext::SealBlockCbContext(bytestore::MemoryPool* pool,
                                       ExtendedBlock* blk, int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::SEAL, req_t_us) {}
SealBlockCbContext::~SealBlockCbContext() {}

void SealBlockCbContext::SendResponse(::cloudfs::Status status,
                                      const std::string& err_msg) {
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Seal block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  } else {
    LOG(INFO) << "Seal block finish, block id:" << block_->ToString()
              << ", seal len:" << length_
              << ", phy length:" << replica_->GetBytesOnDisk()
              << ", visible len:" << replica_->GetVisibleLength();
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}
void SealBlockCbContext::Complete(::cloudfs::Status status,
                                  const std::string& err_msg) {
  BYTE_DEFER(replica_->StopSeal());
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  if (status != ::cloudfs::SUCCESS) {
    return SendResponse(status, err_msg);
  }
  uint64_t physical_length =
      (this->length_ < 0) ? replica_->GetBytesOnDisk() : this->length_;
  int64_t visible_length =
      (this->length_ < 0) ? replica_->GetVisibleLength() : this->length_;
  std::shared_ptr<ReplicaSealed> new_replica_info =
      std::make_shared<ReplicaSealed>(
          block_->GetBlockPoolID(), block_->GetBlockID(), physical_length,
          visible_length,
          0,  //  GS
          block_->IsPin(), replica_->GetStorageUuid(),
          replica_->IsOnTransientStorage(), false, replica_->GetNamespaceType(),
          replica_->ChecksumEnabled(), replica_->GetDiskId(),
          replica_->GetPlacementType());
  cs_store_->AddVolumeMap(block_->GetBlockPoolID(), new_replica_info);
  hdfs_response_->set_blocklength(physical_length);
  hdfs_response_->set_storageuuid(replica_->GetStorageUuid());
  this->replica_ = new_replica_info;
  block_->SetNumBytes(physical_length);
  SendResponse(status, err_msg);
}

FinalizeBlockCbContext::FinalizeBlockCbContext(bytestore::MemoryPool* pool,
                                               ExtendedBlock* blk,
                                               int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::FINALIZE, req_t_us) {}
FinalizeBlockCbContext::~FinalizeBlockCbContext() {}

void FinalizeBlockCbContext::SendResponse(::cloudfs::Status status,
                                          const std::string& err_msg) {
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Finalize block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  } else {
    LOG(INFO) << "Finalize block finish, block id:" << block_->ToString()
              << ", len:" << length_
              << ", phy length:" << replica_->GetBytesOnDisk()
              << ", visible len:" << replica_->GetVisibleLength();
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}
void FinalizeBlockCbContext::Complete(::cloudfs::Status status,
                                      const std::string& err_msg) {
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  if (status != ::cloudfs::SUCCESS) {
    return SendResponse(status, err_msg);
  }
  uint64_t physical_length = this->length_;
  std::shared_ptr<FinalizedReplica> new_replica_info =
      std::make_shared<FinalizedReplica>(
          block_->GetBlockPoolID(), block_->GetBlockID(), physical_length,
          0,  //  GS
          block_->IsPin(), replica_->GetStorageUuid(),
          replica_->IsOnTransientStorage(), false, replica_->GetNamespaceType(),
          replica_->ChecksumEnabled(), replica_->GetDiskId(),
          replica_->GetPlacementType());
  cs_store_->AddVolumeMap(block_->GetBlockPoolID(), new_replica_info);
  this->replica_ = new_replica_info;
  block_->SetNumBytes(physical_length);
  cs_store_->GetDataNode()->NotifyNamenodeReceivedBlock(
      block_, "", replica_->GetStorageUuid());
  SendResponse(status, err_msg);
}

PingBlockCbContext::PingBlockCbContext(bytestore::MemoryPool* pool,
                                       ExtendedBlock* blk, int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::PING, req_t_us) {}
PingBlockCbContext::~PingBlockCbContext() {}
void PingBlockCbContext::Complete(::cloudfs::Status status,
                                  const std::string& err_msg) {
  this->status_ = status;
  this->io_finish_time_us_ = byte::GetCurrentTimeInUs();
  BlockAsyncCbContext::Complete(status, err_msg);
  byterpc::util::ClosureGuard done_guard(hdfs_done_);
  if (status != ::cloudfs::SUCCESS) {
    LOG(ERROR) << "Ping block failed, block id:" << block_->ToString()
               << ", error: " << status << ", msg:" << err_msg;
  }
  FillResponseCommonHeader(hdfs_response_, status, err_msg);
}

SetXAttrCbContext::SetXAttrCbContext(BlockAsyncCbContext* parent)
    : parent_ctx_(parent) {}
SetXAttrCbContext::~SetXAttrCbContext() {}

void ChunkServerStore::CreateBlockAsync(
    bytestore::DiskId disk_id, bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::CreateBlockRequestProto* request,
    ::cloudfs::CreateBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  NamespaceType ns_type;
  if (!GetNamespaceType(block->GetBlockPoolID(), &ns_type)) {
    std::string err_msg = byte::StringPrint(
        "Cannot find namespace type, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  uint32_t chunk_size_bits = 0;
  if (block->GetNumBytes() < 2) {
    chunk_size_bits = 1;
  } else {
    chunk_size_bits = 64 - __builtin_clzll(block->GetNumBytes() - 1);
  }
  bytestore::ChunkId chunk_id;
  bytestore::CreateChunkRequest create_request;
  block->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(create_request.mutable_chunk_id());
  create_request.set_chunk_type(
      ChunkTypeToPB(bytestore::ChunkType::TYPE_REPLICATED_CHUNK));
  create_request.set_io_priority(
      IOPriorityConverter::ConvertCloudfsIOPriorityToBytestoreProto(
          request->header().iopriority()));
  bytestore::ClientDesc client_desc;
  client_desc.Serialize(create_request.mutable_client_desc());
  int64_t timeout = request->has_timeoutms()
                        ? request->timeoutms()
                        : FLAGS_bytestore_hdfs_create_timeout_us / 1000;
  create_request.set_timeout_ms(timeout);
  create_request.set_max_chunk_size_bits(chunk_size_bits);
  if (request->has_sync()) {
    create_request.set_sync(request->sync());
  }
  create_request.set_disk_id(disk_id);
  auto placement_type = StorageType::ParseProto(request->storagetype())
                                .Equals(StorageType::TIERED_NVME)
                            ? bytestore::PLM_STORAGE_TIERED_NVME_SSD_HDD
                            : bytestore::PLM_STORAGE_ANY;
  create_request.set_storage_type(
      bytestore::PlacementStorageTypeToPB(placement_type));
  create_request.set_resident_seconds(-1);
  CreateBlockCbContext* ctx =
      mem_pool->New<CreateBlockCbContext>(mem_pool, block, start_us);
  ctx->controller_ = controller;
  ctx->io_service_ = io_service;
  ctx->priority_ = create_request.io_priority();
  ctx->placement_type_ = placement_type;
  ctx->ns_type_ = ns_type;
  ctx->disk_id_ = disk_id;
  ctx->hdfs_response_ = response;
  ctx->hdfs_done_ = done_guard.release();
  ctx->cs_store_ = this;
  google::protobuf::Closure* create_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::CreateBlockCallback, ctx);
  io_service->CreateChunk(controller, &create_request, &ctx->cs_response_,
                          create_callback);
}

void ChunkServerStore::CreateBlockCallback(CreateBlockCbContext* ctx) {
  if (ctx->cs_response_.error_code() != bytestore::BYTESTORE_OK) {
    ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                  ctx->cs_response_.error_context());
    return;
  }
  char* write_xattr = ctx->mem_pool_->NewArray<char>(XATTR_BYTES);
  memset(write_xattr, 0, XATTR_BYTES);
  BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
  meta->gs_ = 0;
  meta->checksum_enabled_ = false;
  meta->fin_ = 0;
  meta->tmp_ = 0;
  meta->is_evictable_ = 0;
  meta->deprecated_pin_ = 0;
  meta->namespace_type = static_cast<int>(ctx->ns_type_);

  bytestore::ChunkId chunk_id;
  bytestore::CSSetXattrRequest xattr_request;
  ctx->block_->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(xattr_request.mutable_chunk_id());
  xattr_request.set_io_priority(ctx->priority_);
  xattr_request.set_timeout_ms(FLAGS_bytestore_hdfs_set_xattr_timeout_us /
                               1000);
  xattr_request.set_xattr(std::string(write_xattr, XATTR_BYTES));
  xattr_request.set_disk_id(ctx->disk_id_);
  xattr_request.set_storage_type(
      bytestore::PlacementStorageTypeToPB(ctx->placement_type_));
  SetXAttrCbContext* xattr_ctx = ctx->mem_pool_->New<SetXAttrCbContext>(ctx);
  google::protobuf::Closure* xattr_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::SetXAttrBlockCallback, xattr_ctx);
  ctx->io_service_->SetXATTRChunk(ctx->controller_, &xattr_request,
                                  &xattr_ctx->cs_response_, xattr_callback);
}

void ChunkServerStore::WriteBlockAsync(
    bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::WriteBlockRequestProto* request,
    ::cloudfs::WriteBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  LOG(DEBUG) << "WriteBlockAsync start, block id: "
             << request->header().block().blockid();
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  std::shared_ptr<ReplicaInfo> replica =
      GetReplica(block->GetBlockPoolID(), block->GetBlockID());
  if (replica == nullptr) {
    std::string err_msg = byte::StringPrint(
        "Write block replica not found, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST,
                             err_msg);
    return;
  }
  if (replica->GetState() != ReplicaState::RBW) {
    std::string err_msg = byte::StringPrint(
        "Write block replica is not RBW stat, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_SEALED,
                             err_msg);
    return;
  } else {
    auto rbw = std::dynamic_pointer_cast<ReplicaBeingWritten>(replica);
    if (!rbw->CanWrite()) {
      std::string err_msg = byte::StringPrint(
          "Write block replica is not RBW stat, block: %s", block->ToString());
      LOG(ERROR) << err_msg;
      FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_SEALED,
                               err_msg);
      return;
    }
  }
  bytestore::ChunkId chunk_id;
  bytestore::WriteChunkRequest write_request;
  block->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(write_request.mutable_chunk_id());
  write_request.set_io_priority(
      IOPriorityConverter::ConvertCloudfsIOPriorityToBytestoreProto(
          request->header().iopriority()));
  bytestore::ClientDesc client_desc;
  client_desc.Serialize(write_request.mutable_client_desc());
  int64_t timeout = request->has_timeoutms()
                        ? request->timeoutms()
                        : FLAGS_bytestore_hdfs_write_timeout_us / 1000;
  write_request.set_timeout_ms(timeout);
  write_request.set_length(request->length());
  write_request.set_offset(request->offset());
  if (request->has_sync()) {
    write_request.set_sync(request->sync());
  }
  write_request.set_disk_id(replica->GetDiskId());
  WriteBlockCbContext* ctx =
      mem_pool->New<WriteBlockCbContext>(mem_pool, block, start_us);
  ctx->hdfs_response_ = response;
  ctx->hdfs_done_ = done_guard.release();
  ctx->offset_ = request->offset();
  ctx->length_ = request->length();
  ctx->visible_length_ =
      request->has_visiblelength() ? request->visiblelength() : -1;
  ctx->replica_ = replica;
  google::protobuf::Closure* write_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::WriteBlockCallback, ctx);
  io_service->WriteChunk(controller, &write_request, &ctx->cs_response_,
                         write_callback);
}

void ChunkServerStore::WriteBlockCallback(WriteBlockCbContext* ctx) {
  ScopedAsyncCallbackMetric metric_guard(BlockOPType::WRITE);
  if (ctx->cs_response_.error_code() ==
      bytestore::BYTESTORE_ERR_CHUNKSERVER_ALREADY_WRITTEN) {
    METRICS_rpc_async_write_block_duplicate->Increment();
    ctx->cs_response_.set_error_code(bytestore::BYTESTORE_OK);
  }
  if (ctx->cs_response_.error_code() ==
      bytestore::BYTESTORE_ERR_CHUNKSERVER_INVALID_DISK_ID) {
    ctx->hdfs_response_->set_redirectport(
        data_node_->GetDiskPort(ctx->replica_->GetDiskId()));
  }
  ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                ctx->cs_response_.error_context());
}

void ChunkServerStore::ReadBlockAsync(
    bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::ClientReadBlockRequestProto* request,
    ::cloudfs::ClientReadBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  std::shared_ptr<ReplicaInfo> replica = GetReplica(
      request->header().block().poolid(), request->header().block().blockid());
  if (replica == nullptr || replica->GetState() == ReplicaState::TEMPORARY) {
    // std::string err_msg =
    // byte::StringPrint("Read block replica not found, block: %s",
    // block->ToString()); LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST,
                             "replica not found");
    return;
  }
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  if (request->length() == 0) {
    std::string err_msg =
        byte::StringPrint("Read block len zero, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  uint64_t read_len = request->length();
  bytestore::ChunkId chunk_id;
  bytestore::ReadChunkRequest read_request;
  block->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(read_request.mutable_chunk_id());
  read_request.set_io_priority(
      IOPriorityConverter::ConvertCloudfsIOPriorityToBytestoreProto(
          request->header().iopriority()));
  bytestore::ClientDesc client_desc;
  client_desc.Serialize(read_request.mutable_client_desc());
  int64_t timeout = request->has_timeoutms()
                        ? request->timeoutms()
                        : FLAGS_bytestore_hdfs_read_timeout_us / 1000;
  read_request.set_timeout_ms(timeout);
  read_request.set_offset(request->offset());
  read_request.set_length(read_len);
  read_request.set_is_replication(false);
  read_request.set_storage_type(
      bytestore::PlacementStorageTypeToPB(replica->GetPlacementType()));
  read_request.set_disk_id(replica->GetDiskId());
  ReadBlockCbContext* ctx =
      mem_pool->New<ReadBlockCbContext>(mem_pool, block, start_us);
  ctx->hdfs_response_ = response;
  ctx->hdfs_done_ = done_guard.release();
  ctx->read_bytes_ = request->length();
  ctx->replica_ = replica;
  google::protobuf::Closure* read_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::ReadBlockCallback, ctx);
  io_service->ReadChunk(controller, &read_request, &ctx->cs_response_,
                        read_callback);
}

void ChunkServerStore::ReadBlockCallback(ReadBlockCbContext* ctx) {
  ScopedAsyncCallbackMetric metric_guard(BlockOPType::READ_DISK);
  if (ctx->cs_response_.error_code() ==
      bytestore::BYTESTORE_ERR_CHUNKSERVER_INVALID_DISK_ID) {
    ctx->hdfs_response_->set_redirectport(
        data_node_->GetDiskPort(ctx->replica_->GetDiskId()));
  }
  ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                ctx->cs_response_.error_context());
}

void ChunkServerStore::SealBlockAsync(
    bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::SealBlockRequestProto* request,
    ::cloudfs::SealBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  std::shared_ptr<ReplicaInfo> replica =
      GetReplica(block->GetBlockPoolID(), block->GetBlockID());
  if (replica == nullptr) {
    std::string err_msg = byte::StringPrint(
        "Seal block replica not found, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST,
                             err_msg);
    return;
  }
  if (replica->GetState() == ReplicaState::SEALED ||
      replica->GetState() == ReplicaState::FINALIZED) {
    if (!request->has_length()) {
      std::string msg = byte::StringPrint(
          "The replica is %s, and length is not set, block: %s.",
          ReplicaStateToString(replica->GetState()), block->ToString());
      LOG(INFO) << msg;
      response->set_blocklength(replica->GetBytesOnDisk());
      response->set_storageuuid(replica->GetStorageUuid());
      FillResponseCommonHeader(response, ::cloudfs::SUCCESS, "");
      return;
    } else {
      if (request->length() !=
          static_cast<int64_t>(replica->GetBytesOnDisk())) {
        std::string msg = byte::StringPrint(
            "The replica is %s, and the length in request is %lld while the "
            "physical "
            "length is %llu, block: %s.",
            ReplicaStateToString(replica->GetState()), request->length(),
            replica->GetBytesOnDisk(), block->ToString());
        LOG(ERROR) << msg;
        FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, msg);
        return;
      } else {
        std::string msg = byte::StringPrint(
            "The replica is %s, and the length in request is equal to physical "
            "length "
            "%llu, block: %s.",
            ReplicaStateToString(replica->GetState()),
            replica->GetBytesOnDisk(), block->ToString());
        LOG(INFO) << msg;
        response->set_blocklength(replica->GetBytesOnDisk());
        response->set_storageuuid(replica->GetStorageUuid());
        FillResponseCommonHeader(response, ::cloudfs::SUCCESS, "");
        return;
      }
    }
  }
  if (request->has_length() &&
      request->length() > static_cast<int64_t>(replica->GetBytesOnDisk())) {
    std::string err_msg = byte::StringPrint(
        "Seal block's length %ld is over than physical length %lu, block: %s.",
        request->length(), replica->GetBytesOnDisk(), block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  // Will call replica->StopSeal in SealBlockCbContext::Complete
  if (!replica->StartSeal()) {
    std::string err_msg = byte::StringPrint(
        "Seal block replica is locked, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_BUSY, err_msg);
    return;
  }
  SealBlockCbContext* seal_ctx =
      mem_pool->New<SealBlockCbContext>(mem_pool, block, start_us);
  seal_ctx->controller_ = controller;
  seal_ctx->io_service_ = io_service;
  seal_ctx->cs_store_ = this;
  seal_ctx->length_ = request->has_length() ? request->length() : -1;
  seal_ctx->replica_ = replica;
  seal_ctx->priority_ =
      IOPriorityConverter::ConvertCloudfsIOPriorityToBytestoreProto(
          request->header().iopriority());
  seal_ctx->hdfs_response_ = response;
  seal_ctx->hdfs_done_ = done_guard.release();
  if (request->has_length() &&
      request->length() <= static_cast<int64_t>(replica->GetBytesOnDisk())) {
    bytestore::ChunkId chunk_id;
    bytestore::TruncateChunkRequest truncate_request;
    seal_ctx->block_->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
    chunk_id.Serialize(truncate_request.mutable_chunk_id());
    truncate_request.set_io_priority(seal_ctx->priority_);
    truncate_request.set_timeout_ms(FLAGS_bytestore_hdfs_truncate_timeout_us /
                                    1000);
    truncate_request.set_disk_id(replica->GetDiskId());
    truncate_request.set_length(request->length());
    google::protobuf::Closure* truncate_callback =
        google::protobuf::NewCallback(
            this, &ChunkServerStore::SealBlockCallback, seal_ctx);
    io_service->TruncateChunk(controller, &truncate_request,
                              &seal_ctx->cs_response_, truncate_callback);
  } else {
    SealBlockCallback(seal_ctx);
  }
}

void ChunkServerStore::SealBlockCallback(SealBlockCbContext* ctx) {
  // length >=0 means the block is truncated.
  if (ctx->length_ >= 0 &&
      ctx->cs_response_.error_code() != bytestore::BYTESTORE_OK) {
    ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                  ctx->cs_response_.error_context());
    return;
  }

  char* write_xattr = ctx->mem_pool_->NewArray<char>(XATTR_BYTES);
  memset(write_xattr, 0, XATTR_BYTES);
  BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
  meta->gs_ = 0;
  meta->checksum_enabled_ = false;
  meta->fin_ = 0;
  meta->tmp_ = 0;
  meta->is_evictable_ = 0;
  meta->deprecated_pin_ = 0;
  meta->sealed_ = 1;
  meta->namespace_type = ctx->replica_->GetNamespaceType();

  bytestore::ChunkId chunk_id;
  bytestore::CSSetXattrRequest xattr_request;
  ctx->block_->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(xattr_request.mutable_chunk_id());
  xattr_request.set_io_priority(ctx->priority_);
  xattr_request.set_timeout_ms(FLAGS_bytestore_hdfs_set_xattr_timeout_us /
                               1000);
  xattr_request.set_xattr(std::string(write_xattr, XATTR_BYTES));
  xattr_request.set_disk_id(ctx->replica_->GetDiskId());
  xattr_request.set_storage_type(
      bytestore::PlacementStorageTypeToPB(ctx->replica_->GetPlacementType()));
  SetXAttrCbContext* xattr_ctx = ctx->mem_pool_->New<SetXAttrCbContext>(ctx);
  google::protobuf::Closure* xattr_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::SetXAttrBlockCallback, xattr_ctx);
  ctx->io_service_->SetXATTRChunk(ctx->controller_, &xattr_request,
                                  &xattr_ctx->cs_response_, xattr_callback);
}

void ChunkServerStore::SyncBlockAsync(
    bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::SyncBlockRequestProto* request,
    ::cloudfs::SyncBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  std::shared_ptr<ReplicaInfo> replica =
      GetReplica(block->GetBlockPoolID(), block->GetBlockID());
  if (replica == nullptr) {
    std::string err_msg = byte::StringPrint(
        "Sync block replica not found, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST,
                             err_msg);
    return;
  }
  bytestore::ChunkId chunk_id;
  bytestore::SyncChunkRequest sync_request;
  block->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(sync_request.mutable_chunk_id());
  sync_request.set_io_priority(
      IOPriorityConverter::ConvertCloudfsIOPriorityToBytestoreProto(
          request->header().iopriority()));
  bytestore::ClientDesc client_desc_;
  client_desc_.Serialize(sync_request.mutable_client_desc());
  sync_request.set_version(replica->GetBytesOnDisk());
  sync_request.set_disk_id(replica->GetDiskId());
  int64_t timeout = request->has_timeoutms()
                        ? request->timeoutms()
                        : FLAGS_bytestore_hdfs_sync_timeout_us / 1000;
  sync_request.set_timeout_ms(timeout);
  SyncBlockCbContext* ctx =
      mem_pool->New<SyncBlockCbContext>(mem_pool, block, start_us);
  ctx->hdfs_response_ = response;
  ctx->hdfs_done_ = done_guard.release();
  google::protobuf::Closure* sync_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::SyncBlockCallback, ctx);
  io_service->SyncChunk(controller, &sync_request, &ctx->cs_response_,
                        sync_callback);
}

void ChunkServerStore::SyncBlockCallback(SyncBlockCbContext* ctx) {
  ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                ctx->cs_response_.error_context());
}

void ChunkServerStore::SetXAttrBlockCallback(SetXAttrCbContext* ctx) {
  BlockAsyncCbContext* parent_ctx = ctx->parent_ctx_;
  if (ctx->cs_response_.error_code() != bytestore::BYTESTORE_OK) {
    parent_ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                         "Set xattr failed.");
  } else {
    parent_ctx->Complete(::cloudfs::SUCCESS, "");
  }
}

void ChunkServerStore::PingBlockAsync(
    bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::PingBlockRequestProto* request,
    ::cloudfs::PingBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  std::shared_ptr<ReplicaInfo> replica =
      GetReplica(block->GetBlockPoolID(), block->GetBlockID());
  if (replica == nullptr) {
    std::string err_msg = byte::StringPrint(
        "Ping block replica not found, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST,
                             err_msg);
    return;
  }
  if (replica->GetState() != ReplicaState::RBW &&
      replica->GetState() != ReplicaState::SEALED) {
    std::string err_msg = byte::StringPrint(
        "Ping block replica is not RBW/SEALED stat, replica: %s",
        replica->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  if (!request->has_visiblelength()) {
    std::string err_msg = byte::StringPrint(
        "Ping block has no visible length, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  if (request->visiblelength() < replica->GetVisibleLength()) {
    std::string err_msg = byte::StringPrint(
        "Ping block vlen %ld is smaller than current %ld, block: %s",
        request->visiblelength(), replica->GetVisibleLength(),
        block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  PingBlockCbContext context(mem_pool, block, start_us);
  context.hdfs_response_ = response;
  context.hdfs_done_ = done_guard.release();
  if (replica->GetState() == ReplicaState::RBW) {
    ReplicaBeingWritten* replica_rbw =
        dynamic_cast<ReplicaBeingWritten*>(replica.get());
    replica_rbw->SetBytesAcked(request->visiblelength());
  } else if (replica->GetState() == ReplicaState::SEALED) {
    ReplicaSealed* replica_sealed = dynamic_cast<ReplicaSealed*>(replica.get());
    replica_sealed->SetVisibleLength(request->visiblelength());
  } else {
    // Should not occurs.
    LOG(ERROR) << "Ping block replica state is invalid, replica: "
               << replica->ToString();
  }
  context.Complete(::cloudfs::SUCCESS, "");
}

void ChunkServerStore::FinalizeBlockAsync(
    bytestore::CSIOService* io_service,
    google::protobuf::RpcController* controller,
    const ::cloudfs::FinalizeBlockRequestProto* request,
    ::cloudfs::FinalizeBlockResponseProto* response,
    bytestore::MemoryPool* mem_pool, google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  int64_t start_us = byte::GetCurrentTimeInUs();
  ExtendedBlock* block =
      ExtendedBlock::ParseProto(request->header().block(), mem_pool);
  std::shared_ptr<ReplicaInfo> replica =
      GetReplica(block->GetBlockPoolID(), block->GetBlockID());
  if (replica == nullptr) {
    std::string err_msg = byte::StringPrint(
        "Finalize block replica not found, block: %s", block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST,
                             err_msg);
    return;
  }
  if (replica->GetState() == ReplicaState::FINALIZED) {
    LOG(INFO) << block->ToString()
              << " is already finalized, replica: " << replica->ToString();
    FillResponseCommonHeader(response, ::cloudfs::SUCCESS, "");
    return;
  }
  if (!request->has_length() ||
      request->length() > static_cast<int64_t>(replica->GetBytesOnDisk())) {
    std::string err_msg = byte::StringPrint(
        "Finalize has no len or length %ld is over than physical length %lu, "
        "block: %s.",
        request->length(), replica->GetBytesOnDisk(), block->ToString());
    LOG(ERROR) << err_msg;
    FillResponseCommonHeader(response, ::cloudfs::ERROR_INVALID, err_msg);
    return;
  }
  FinalizeBlockCbContext* finalize_ctx =
      mem_pool->New<FinalizeBlockCbContext>(mem_pool, block, start_us);
  finalize_ctx->controller_ = controller;
  finalize_ctx->io_service_ = io_service;
  finalize_ctx->cs_store_ = this;
  finalize_ctx->length_ = request->length();
  finalize_ctx->replica_ = replica;
  finalize_ctx->priority_ =
      IOPriorityConverter::ConvertCloudfsIOPriorityToBytestoreProto(
          request->header().iopriority());
  finalize_ctx->hdfs_response_ = response;
  finalize_ctx->hdfs_done_ = done_guard.release();
  if (!replica->IsTruncated() &&
      request->length() <= static_cast<int64_t>(replica->GetBytesOnDisk())) {
    bytestore::ChunkId chunk_id;
    bytestore::TruncateChunkRequest truncate_request;
    finalize_ctx->block_->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
    chunk_id.Serialize(truncate_request.mutable_chunk_id());
    truncate_request.set_io_priority(finalize_ctx->priority_);
    truncate_request.set_timeout_ms(FLAGS_bytestore_hdfs_truncate_timeout_us /
                                    1000);
    truncate_request.set_disk_id(replica->GetDiskId());
    truncate_request.set_length(request->length());
    google::protobuf::Closure* truncate_callback =
        google::protobuf::NewCallback(
            this, &ChunkServerStore::FinalizeBlockCallback, finalize_ctx);
    io_service->TruncateChunk(controller, &truncate_request,
                              &finalize_ctx->cs_response_, truncate_callback);
  } else {
    FinalizeBlockCallback(finalize_ctx);
  }
}

void ChunkServerStore::FinalizeBlockCallback(FinalizeBlockCbContext* ctx) {
  // length >=0 means the block is truncated.
  if (ctx->cs_response_.error_code() != bytestore::BYTESTORE_OK) {
    ctx->Complete(ConvertErrorToStatus(ctx->cs_response_.error_code()),
                  ctx->cs_response_.error_context());
    return;
  }

  char* write_xattr = ctx->mem_pool_->NewArray<char>(XATTR_BYTES);
  memset(write_xattr, 0, XATTR_BYTES);
  BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
  meta->gs_ = 0;
  meta->checksum_enabled_ = false;
  meta->fin_ = 1;
  meta->tmp_ = 0;
  meta->is_evictable_ = 0;
  meta->deprecated_pin_ = 0;
  meta->sealed_ = 1;
  meta->namespace_type = ctx->replica_->GetNamespaceType();

  bytestore::ChunkId chunk_id;
  bytestore::CSSetXattrRequest xattr_request;
  ctx->block_->GetChunkIdMeta(ChunkIdToChunkIdMeta(&chunk_id));
  chunk_id.Serialize(xattr_request.mutable_chunk_id());
  xattr_request.set_io_priority(ctx->priority_);
  xattr_request.set_timeout_ms(FLAGS_bytestore_hdfs_set_xattr_timeout_us /
                               1000);
  xattr_request.set_xattr(std::string(write_xattr, XATTR_BYTES));
  xattr_request.set_disk_id(ctx->replica_->GetDiskId());
  xattr_request.set_storage_type(
      bytestore::PlacementStorageTypeToPB(ctx->replica_->GetPlacementType()));
  SetXAttrCbContext* xattr_ctx = ctx->mem_pool_->New<SetXAttrCbContext>(ctx);
  google::protobuf::Closure* xattr_callback = google::protobuf::NewCallback(
      this, &ChunkServerStore::SetXAttrBlockCallback, xattr_ctx);
  ctx->io_service_->SetXATTRChunk(ctx->controller_, &xattr_request,
                                  &xattr_ctx->cs_response_, xattr_callback);
}

::cloudfs::Status ChunkServerStore::ConvertErrorToStatus(int32_t err_code) {
  switch (err_code) {
    case bytestore::BYTESTORE_OK: return ::cloudfs::SUCCESS;
    case bytestore::BYTESTORE_ERR_CHUNKSERVER_INVALID_PARAMETER:
      return ::cloudfs::ERROR_INVALID;
    case bytestore::BYTESTORE_ERR_CHUNKSERVER_NOT_ENOUGH_SPACE:
      return ::cloudfs::ERROR_NOT_ENOUGH_SPACE;
    case bytestore::BYTESTORE_ERR_CHUNKSERVER_DISK_BUSY:
      return ::cloudfs::ERROR_BUSY;
    case bytestore::BYTESTORE_ERR_CHUNKSERVER_INVALID_DISK_ID:
      return ::cloudfs::ERROR_DN_INVALID_PORT;
    default: return ::cloudfs::ERROR_DN_INTERNAL_ERROR;
  }
}

}  // namespace bds::dancedn::cloudfs
