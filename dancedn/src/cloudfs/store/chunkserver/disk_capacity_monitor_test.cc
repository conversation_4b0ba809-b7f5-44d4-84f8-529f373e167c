// copyright (c) 2024-present, bytedance inc. all rights reserved.

#include "cloudfs/store/chunkserver/disk_capacity_monitor.h"

#include <algorithm>
#include <memory>
#include <set>
#include <string>

#include "byte/io/local_filesystem.h"
#include "chunkserver/disk.h"
#include "chunkserver/env.h"
#include "cloudfs/metrics.h"
#include "cloudfs/store/chunkserver/chunkserver_alias.h"
#include "cloudfs/store/chunkserver/chunkserver_runtime.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"

DECLARE_string(bytestore_chunkserver_work_dir);
DECLARE_bool(bytestore_chunkserver_admit_duplicate_uuid_disk);
DECLARE_uint32(bytestore_cfs_disk_monitory_interval_ms);
DECLARE_double(bytestore_cfs_evict_threthold_percentage);

namespace bds::dancedn::cloudfs {

class DiskCapacityMonitorTest : public testing::Test {
 public:
  DiskCapacityMonitorTest() {}
  ~DiskCapacityMonitorTest() {}
  void SetUp() override {
    FLAGS_bytestore_cfs_disk_monitory_interval_ms = 1200;
    FLAGS_bytestore_chunkserver_admit_duplicate_uuid_disk = true;
    bytestore::metrics_internal::InitFastMetrics();
    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    work_dir_ = "./DiskCapacityMonitorTest/";
    FLAGS_bytestore_chunkserver_work_dir = work_dir_;
    local_fs.DeleteDir(work_dir_, delete_options);
    EXPECT_TRUE(local_fs.CreateDir(work_dir_, byte::CreateOptions()).ok());
    EXPECT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk1", byte::CreateOptions()).ok());
    EXPECT_TRUE(
        local_fs.CreateDir(work_dir_ + "/disk2", byte::CreateOptions()).ok());
    disk_id_conf_map_[1] = bytestore::chunkserver::DiskConfig(
        1, work_dir_ + "/disk1", bytestore::chunkserver::CDT_NVME_SSD);
    disk_id_conf_map_[2] = bytestore::chunkserver::DiskConfig(
        2, work_dir_ + "/disk2", bytestore::chunkserver::CDT_NVME_SSD);
    /*
    env_ = new Env(work_dir_);
    cs_config_.DEBUG_ImportConfig(disk_id_conf_map_);
    EnvOptions env_options;
    env_options.cs_config_ = &cs_config_;
    EXPECT_EQ(env_->Init(env_options), bytestore::BYTESTORE_OK);
    ASSERT_EQ(env_->Start(), bytestore::BYTESTORE_OK);
    EXPECT_EQ(env_->UpdateCSStatus(CHUNKSERVER_NORMAL),
    bytestore::BYTESTORE_OK);
    */
    chunkserver_runtime_.reset(new ChunkServerRuntime(work_dir_));
    cs_config_.DEBUG_ImportConfig(disk_id_conf_map_);
    chunkserver_runtime_->DEBUG_ImportCsConfig(cs_config_);
    ASSERT_EQ(chunkserver_runtime_->Init().GetE(), exceptions::kNoException);
    ASSERT_EQ(chunkserver_runtime_->Start().GetE(), exceptions::kNoException);
    env_ = chunkserver_runtime_->GetEnv();
    std::set<uint32_t> disk_ids = {1, 2};
    limiter_ = std::make_shared<RateLimiterLocal>(disk_ids);
    monitor_ = std::make_shared<DiskCapacityMonitor>(env_, limiter_);
    monitor_->Start();
  }
  void TearDown() override {
    monitor_->Stop();
    monitor_->Join();
    chunkserver_runtime_->Stop();
    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    local_fs.DeleteDir(work_dir_, delete_options);
  }

  std::shared_ptr<DiskCapacityMonitor> GetMonitor() {
    return monitor_;
  }

 private:
  std::shared_ptr<ChunkServerRuntime> chunkserver_runtime_;
  CsEnv* env_;
  std::shared_ptr<DiskCapacityMonitor> monitor_;
  std::shared_ptr<RateLimiterLocal> limiter_;

  std::string work_dir_;
  CsConfig cs_config_;
  bytestore::chunkserver::DiskIdConfMap disk_id_conf_map_;
};

TEST_F(DiskCapacityMonitorTest, TestDiskCapacityMonitor) {
  GetMonitor()->ClientWriteDisk(1, 100);
  GetMonitor()->ClientWriteDisk(3, 100);  // no coredump
  byte::ThisThread::SleepInMs(2000);
  double quota = METRICS_local_capacity_qos_quota
                     ->GetMetric({{"disk_id", std::to_string(1)}})
                     ->GetValue();
  ASSERT_GT(quota, 0.0);
}

}  // namespace bds::dancedn::cloudfs
