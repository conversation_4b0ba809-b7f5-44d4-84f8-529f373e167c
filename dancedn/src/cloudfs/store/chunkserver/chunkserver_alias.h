// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#include "bytestore/chunkserver/datanode_interface.h"
#include "bytestore/chunkserver/env.h"
#include "chunkserver/disk.h"
#include "common/memory_pool.h"
#include "common/store_errorcode.h"
#include "common/store_types.h"

namespace bds::dancedn::cloudfs {
using CsEnv = bytestore::chunkserver::Env;
using CsDisk = bytestore::chunkserver::Disk;
using CsDiskId = bytestore::DiskId;
using CsChunkId = bytestore::ChunkId;
using CsIoService = bytestore::CSIOService;
using CsMetaService = bytestore::CSMetaService;
using CsMgrService = bytestore::CSManagementService;
using CsConfig = bytestore::chunkserver::ChunkServerConfig;
using CsRpcType = bytestore::chunkserver::RpcType;
using CsBRPCServer = bytestore::chunkserver::BRPCServer;
using CsDiskIdConfMap = bytestore::chunkserver::DiskIdConfMap;
using CsErrorcode = bytestore::Errorcode;
// TODO(lijiye.2021): remove CsDnInterface
using CsDnInterface = bytestore::chunkserver::DatanodeInterface;
using CsMemPool = bytestore::MemoryPool;
using CsIoPriority = bytestore::IOPriority;
}  // namespace bds::dancedn::cloudfs
