// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/upload_tos_mgr.h"

#include <endian.h>

#include <algorithm>
#include <memory>
#include <utility>

#include "aws/s3/model/CompleteMultipartUploadRequest.h"
#include "butil/md5.h"
#include "byte/util/scope_guard.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/datanode.h"
#include "cloudfs/metrics.h"
#include "cloudfs/opstats/op_stats.h"
#include "cloudfs/receive_deleted_block_info.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/ufs/tos_info.h"
#include "cloudfs/util.h"
#include "system/timestamp.h"

DECLARE_uint32(bytestore_cfs_upload_thread_pool_size);
DECLARE_uint32(bytestore_cfs_put_object_threshold);
DECLARE_uint32(bytestore_cfs_upload_tos_priority);
DECLARE_uint64(bytestore_cfs_upload_max_wait_bytes);
DECLARE_uint64(bytestore_cfs_upload_max_wait_number);
DECLARE_uint32(bytestore_cfs_upload_retry_cache_number);
DECLARE_uint32(bytestore_cfs_upload_retry_cache_time_ms);

#define REMOVE_EVENT(event)                                                    \
  if (!processing_.Remove(event->GetBlockPoolId(), event->GetBlockId())) {     \
    LOG(WARNING) << "remove " << event->block_->ToString() << " event failed"; \
  }

static inline bytestore::IOPriority GetUploadPriority() {
  return static_cast<bytestore::IOPriority>(
      FLAGS_bytestore_cfs_upload_tos_priority);
}

namespace bds::dancedn::cloudfs {

static std::atomic<uint64_t> clock_generator_(0);

UploadTosMgr::UploadTosMgr()
    : Thread(),
      datanode_(nullptr),
      chunkserver_store_(nullptr),
      remote_store_(nullptr),
      processing_(),
      qmutex_(),
      queue_(),
      cache_(FLAGS_bytestore_cfs_upload_retry_cache_number,
             FLAGS_bytestore_cfs_upload_retry_cache_time_ms) {}

UploadTosMgr::UploadTosMgr(DataNode* datanode,
                           ChunkServerStore* chunkserver_store,
                           RemoteStore* remote_store)
    : Thread(),
      datanode_(datanode),
      chunkserver_store_(chunkserver_store),
      remote_store_(remote_store),
      processing_(),
      qmutex_(),
      queue_(),
      cache_(FLAGS_bytestore_cfs_upload_retry_cache_number,
             FLAGS_bytestore_cfs_upload_retry_cache_time_ms) {}

UploadTosMgr::~UploadTosMgr() {
  Stop();
  Join();
}

void UploadTosMgr::TriggerUpload(ExtendedBlock* block,
                                 const std::string& upload_id) {
  uint64_t current_time = byte::GetCurrentTimeInMs();
  std::shared_ptr<ExtendedBlock> ptr(block->Clone());
  Enqueue(
      std::make_shared<Event>(UPLOAD, std::move(ptr), current_time, upload_id));
}

void UploadTosMgr::TriggerMultipartUpload(
    ExtendedBlock* block, const std::string& object_key,
    const std::string& upload_id, const std::vector<uint32_t>& part_nums,
    const std::vector<LocatedBlock*>& prev_blocks) {
  uint64_t current_time = byte::GetCurrentTimeInMs();
  std::shared_ptr<ExtendedBlock> ptr(block->Clone());
  Enqueue(std::make_shared<Event>(MULTIPART_UPLOAD, std::move(ptr),
                                  current_time, object_key, upload_id,
                                  part_nums, prev_blocks));
}

void UploadTosMgr::TriggerUploadAppend(ExtendedBlock* block) {
  uint64_t current_time = byte::GetCurrentTimeInMs();
  std::shared_ptr<ExtendedBlock> ptr(block->Clone());
  Enqueue(std::make_shared<Event>(APPEND, std::move(ptr), current_time));
}

void UploadTosMgr::Run() {
  while (!IsStopped()) {
    WaitUtil(
        [this]() {
          return !IsEmpty() || IsStopped();
        },
        0);
    if (IsStopped()) {
      break;
    }

    std::shared_ptr<Event> event = Pop();

    // wait for next trigger time
    uint64_t current = byte::GetCurrentTimeInMs();
    if (current < event->next_trigger_time_) {
      Enqueue(event);
      WaitFor(event->next_trigger_time_ - current);
      continue;
    }

    auto&& bpid = event->block_->GetBlockPoolID();
    if (processing_.Size(bpid) > FLAGS_bytestore_cfs_upload_max_wait_number ||
        processing_.NumBytes(bpid) >
            FLAGS_bytestore_cfs_upload_max_wait_bytes) {
      LOG(INFO) << "too many pending events, skip this " << event->ToString();
      datanode_->NotifyNamenodeFailed(
          event->block_.get(), BlockStatus::UPLOAD_FAILED, "too many events");
      continue;
    }

    if (cache_.Contains(event->block_->ToString() + event->upload_id_)) {
      LOG(INFO) << event->ToString() << " has been uploaded recently, skip...";
      continue;
    }

    if (!processing_.Add(event->GetBlockPoolId(), event->GetBlockId(), event)) {
      LOG(INFO) << event->ToString() << " is being processed, skip...";
      continue;
    }

    LOG_EVERY(INFO, 100) << "processing event num: " << processing_.Size()
                         << ", bytes: " << processing_.NumBytes();

    if (!CheckEventValid(event)) {
      REMOVE_EVENT(event);
      continue;
    }

    uint64_t ns_id = BlockPoolManager::ParseNsIdFromBpid(bpid);
    if (thread_pools_.find(ns_id) == thread_pools_.end()) {
      thread_pools_[ns_id] = std::make_unique<byte::DynamicThreadPool>(
          FLAGS_bytestore_cfs_upload_thread_pool_size,
          "upload_" + std::to_string(ns_id & 0xff));
      LOG(INFO) << "create thread pool for ns_id: " << ns_id;
    }
    auto&& thread_pool = thread_pools_[ns_id];

    switch (event->type_) {
      case UPLOAD:
        LOG(INFO) << "Add upload Task, block=" << event->block_->ToString();
        METRICS_upload_tos_mgr_thread_pool_queue_num->Increment();
        thread_pool->AddTask(
            NewClosure(this, &UploadTosMgr::MultipartUpload, event));
        break;
      case MULTIPART_UPLOAD:
        LOG(INFO) << "Add multipart upload Task, block="
                  << event->block_->ToString();
        METRICS_upload_tos_mgr_thread_pool_queue_num->Increment();
        thread_pool->AddTask(
            NewClosure(this, &UploadTosMgr::AccMultipartUpload, event));
        break;
      case APPEND:
        LOG(INFO) << "Add append Task, block=" << event->block_->ToString();
        METRICS_upload_tos_mgr_thread_pool_queue_num->Increment();
        thread_pool->AddTask(
            NewClosure(this, &UploadTosMgr::AppendObject, event));
        break;
      default: LOG(ERROR) << "Unsupported event type:" << event->type_; break;
    }
  }
}

bool UploadTosMgr::IsEmpty() {
  byte::MutexLocker guard(&qmutex_);
  return queue_.empty();
}

void UploadTosMgr::Enqueue(const std::shared_ptr<Event>& event) {
  event->clock_ = clock_generator_.fetch_add(1);
  LOG(DEBUG) << "Enqueue" << event->ToString();
  {
    byte::MutexLocker guard(&qmutex_);
    queue_.emplace(event);
  }
  Signal();
}

bool UploadTosMgr::CheckEventValid(const std::shared_ptr<Event>& event) {
  auto& blk = event->block_;
  auto replica =
      chunkserver_store_->GetReplica(blk->GetBlockPoolID(), blk->GetBlockID());
  if (UNLIKELY(replica == nullptr)) {
    datanode_->NotifyNamenodeFailed(blk.get(), BlockStatus::UPLOAD_FAILED,
                                    "replica not exist");
    return false;
  } else if (UNLIKELY(replica->GetState() != ReplicaState::FINALIZED)) {
    datanode_->NotifyNamenodeFailed(blk.get(), BlockStatus::UPLOAD_FAILED,
                                    "replica is not finalized");
    return false;
  } else if (UNLIKELY(replica->GetGS() != blk->GetGS())) {
    std::string msg =
        byte::StringPrint("gs mismatch, replica: %d, event block: %d",
                          replica->GetGS(), blk->GetGS());
    datanode_->NotifyNamenodeFailed(blk.get(), BlockStatus::UPLOAD_FAILED, msg);
    return false;
  } else if (UNLIKELY(replica->GetNumBytes() != blk->GetNumBytes())) {
    std::string msg =
        byte::StringPrint("num bytes mismatch, replica: %d, event block: %d",
                          replica->GetNumBytes(), blk->GetNumBytes());
    datanode_->NotifyNamenodeFailed(blk.get(), BlockStatus::UPLOAD_FAILED, msg);
    return false;
  }
  return true;
}

void UploadTosMgr::AccMultipartUpload(std::shared_ptr<Event> event) {
  LOG(INFO) << "Start to acc multipart upload block "
            << event->block_->ToString() << " upload id " << event->upload_id_
            << " prev block: ["
            << ObjectJoiner<LocatedBlock>(",").Join(event->prev_blocks_) << "]";
  byte::ScopeGuard defer([&]() {
    REMOVE_EVENT(event);
  });
  METRICS_upload_tos_mgr_thread_pool_queue_num->Decrement();
  METRICS_upload_tos_mgr_acc_multipart_upload_num->Increment();
  DURATION_START(acc_multipart_upload);
  std::vector<ExtendedBlock> blocks;
  auto e = datanode_->FetchBlocksForMultipartUpload(
      *(event->block_), event->prev_blocks_, &blocks);
  if (!e.OK()) {
    LOG(WARNING) << "Multipart upload block " << event->block_->ToString()
                 << " failed when fetch previous blocks, error: "
                 << e.ToString() << ", upload id: " << event->upload_id_;
    return;
  }

  Aws::S3::Model::CompletedPart completed;
  e = remote_store_->AccMultipartUploadBlock(
      blocks, event->object_key_, event->upload_id_, event->part_nums_,
      GetUploadPriority(), &completed);
  if (!e.OK()) {
    LOG(WARNING) << "Multipart upload block " << event->block_->ToString()
                 << " failed: " << e.ToString()
                 << ", upload id: " << event->upload_id_;
  } else {
    int64_t cost_us = DURATION_END(acc_multipart_upload);
    METRICS_upload_tos_mgr_acc_multipart_upload_latency->Set(cost_us);
    datanode_->NotifyNamenodeUploaded(event->block_.get(), "",
                                      event->upload_id_, completed.GetETag());
    cache_.Insert(event->block_->ToString() + event->upload_id_);

    uint64_t ns_id =
        BlockPoolManager::ParseNsIdFromBpid(event->block_->GetBlockPoolID());
    uint64_t block_id = event->block_->GetBlockID();
    const std::string& object_key = event->block_->GetObjectKey();
    OpKey key = OpKey::New(0, ns_id, block_id, "", "", "", "", object_key,
                           Operation::MpUploadRemoteBlock);
    OpStats::GetInstance().Record(key, event->block_->GetOffset(),
                                  event->block_->GetNumBytes(),
                                  acc_multipart_upload_start / 1000, cost_us);
  }
}

void UploadTosMgr::MultipartUpload(std::shared_ptr<Event> event) {
  LOG(INFO) << "Start to upload block " << event->block_->ToString();
  byte::ScopeGuard defer([&]() {
    REMOVE_EVENT(event);
  });
  METRICS_upload_tos_mgr_thread_pool_queue_num->Decrement();
  METRICS_upload_tos_mgr_multipart_upload_num->Increment();
  DURATION_START(multipart_upload);
  Aws::S3::Model::CompletedPart completed;
  auto e = remote_store_->UploadBlock(*(event->block_), event->upload_id_,
                                      GetUploadPriority(), &completed);
  if (!e.OK()) {
    LOG(WARNING) << "Upload block " << event->block_->ToString()
                 << " failed: " << e.ToString()
                 << ", upload id: " << event->upload_id_;
  } else {
    int64_t cost_us = DURATION_END(multipart_upload);
    METRICS_upload_tos_mgr_multipart_upload_latency->Set(cost_us);
    datanode_->NotifyNamenodeUploaded(event->block_.get(), "",
                                      event->upload_id_, completed.GetETag());
    cache_.Insert(event->block_->ToString() + event->upload_id_);

    uint64_t ns_id =
        BlockPoolManager::ParseNsIdFromBpid(event->block_->GetBlockPoolID());
    uint64_t block_id = event->block_->GetBlockID();
    const std::string& object_key = event->block_->GetObjectKey();
    OpKey key = OpKey::New(0, ns_id, block_id, "", "", "", "", object_key,
                           Operation::UploadRemoteBlock);
    OpStats::GetInstance().Record(key, event->block_->GetOffset(),
                                  event->block_->GetNumBytes(),
                                  multipart_upload_start / 1000, cost_us);
  }
}

// void UploadTosMgr::PutObject(std::shared_ptr<Event> event) {
//     METRICS_upload_tos_mgr_thread_pool_queue_num->Decrement();
//     METRICS_upload_tos_mgr_put_num->Increment();
//     DURATION_START(put_object);
//     LOG(INFO) << "Start to upload block " << event->block_->ToString() << "
//     in put object"; auto e = tos_store_->PutBlock(*(event->block_),
//     GetUploadPriority()); if (!e.OK()) {
//         event->retries_ = info->retries_ + 1;
//         LOG(WARNING) << "Upload block " << event->block_->ToString() << "
//         failed: " << e.ToString();  // NOLINT Retry(event->block_,
//         event->retries_);
//     } else {
//         METRICS_upload_tos_mgr_put_latency->Set(DURATION_END(put_object));
//         event->trigger_interval_ = 5 * 1000;
//         event->type_ = NOTIFY_UPLOADED;
//         Enqueue(event);
//     }
// }

void UploadTosMgr::AppendObject(std::shared_ptr<Event> event) {
  LOG(INFO) << "Start to append block " << event->block_->ToString();
  byte::ScopeGuard defer([&]() {
    REMOVE_EVENT(event);
  });
  METRICS_upload_tos_mgr_thread_pool_queue_num->Decrement();
  METRICS_upload_tos_mgr_append_num->Increment();
  DURATION_START(append_object);
  std::string etag;
  auto e =
      remote_store_->AppendBlock(*(event->block_), GetUploadPriority(), etag);
  if (!e.OK()) {
    LOG(WARNING) << "append block " << event->block_->ToString()
                 << " failed: " << e.ToString();
  } else {
    int64_t cost_us = DURATION_END(append_object);
    METRICS_upload_tos_mgr_append_latency->Set(cost_us);
    datanode_->NotifyNamenodeUploaded(event->block_.get(), "", "", etag);
    cache_.Insert(event->block_->ToString());

    uint64_t ns_id =
        BlockPoolManager::ParseNsIdFromBpid(event->block_->GetBlockPoolID());
    uint64_t block_id = event->block_->GetBlockID();
    const std::string& object_key = event->block_->GetObjectKey();
    OpKey key = OpKey::New(0, ns_id, block_id, "", "", "", "", object_key,
                           Operation::AppendRemoteBlock);
    OpStats::GetInstance().Record(key, event->block_->GetOffset(),
                                  event->block_->GetNumBytes(),
                                  append_object_start / 1000, cost_us);
  }
}

void RecentUploadedCache::Insert(const std::string& key) {
  byte::MutexLocker guard(&mutex_);
  CleanUp();
  if (uploaded_set_.find(key) == uploaded_set_.end()) {
    uploaded_queue_.emplace_back(key, byte::GetCurrentTimeInMs());
    uploaded_set_.insert(key);
  }
}

bool RecentUploadedCache::Contains(const std::string& key) {
  byte::MutexLocker guard(&mutex_);
  CleanUp();
  return uploaded_set_.find(key) != uploaded_set_.end();
}

void RecentUploadedCache::CleanUp() {
  auto now = byte::GetCurrentTimeInMs();
  // Remove expired elements
  while (!uploaded_queue_.empty() &&
         (now - uploaded_queue_.front().second) > max_duration_ms_) {
    uploaded_set_.erase(uploaded_queue_.front().first);
    uploaded_queue_.pop_front();
  }
  // Remove excess elements
  while (uploaded_queue_.size() > max_size_) {
    uploaded_set_.erase(uploaded_queue_.front().first);
    uploaded_queue_.pop_front();
  }
}

}  // namespace bds::dancedn::cloudfs
