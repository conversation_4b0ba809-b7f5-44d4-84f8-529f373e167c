// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/store/ufs/tos_store.h"

#include <atomic>
#include <cstdint>
#include <cstring>
#include <iostream>
#include <memory>
#include <set>
#include <sstream>
#include <string>
#include <vector>

#include "aws/core/Aws.h"
#include "aws/core/auth/AWSCredentials.h"
#include "aws/core/external/cjson/cJSON.h"
#include "aws/core/http/HttpRequest.h"
#include "aws/core/http/HttpResponse.h"
#include "aws/core/http/URI.h"
#include "aws/core/http/standard/StandardHttpRequest.h"
#include "aws/core/http/standard/StandardHttpResponse.h"
#include "aws/core/utils/HashingUtils.h"
#include "aws/core/utils/Outcome.h"
#include "aws/core/utils/memory/stl/AWSString.h"
#include "aws/core/utils/memory/stl/AWSVector.h"
#include "aws/core/utils/stream/PreallocatedStreamBuf.h"
#include "aws/core/utils/stream/ResponseStream.h"
#include "aws/s3/S3Client.h"
#include "aws/s3/S3Errors.h"
#include "aws/s3/model/AbortMultipartUploadRequest.h"
#include "aws/s3/model/AppendObjectRequest.h"
#include "aws/s3/model/CompleteMultipartUploadRequest.h"
#include "aws/s3/model/CompletedPart.h"
#include "aws/s3/model/CreateMultipartUploadRequest.h"
#include "aws/s3/model/CreateMultipartUploadResult.h"
#include "aws/s3/model/DeleteObjectRequest.h"
#include "aws/s3/model/DeleteObjectsRequest.h"
#include "aws/s3/model/DeletedObject.h"
#include "aws/s3/model/Error.h"
#include "aws/s3/model/GetObjectRequest.h"
#include "aws/s3/model/HeadObjectRequest.h"
#include "aws/s3/model/HeadObjectResult.h"
#include "aws/s3/model/ObjectIdentifier.h"
#include "aws/s3/model/PutObjectRequest.h"
#include "aws/s3/model/UploadPartRequest.h"
#include "base/closure.h"
#include "byterpc/io_block.h"
#include "cloudfs/constants.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/mocks/mock_aws.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/store/ufs/aws_credentials_provider.h"
#include "cloudfs/store/ufs/remote_store.h"
#include "cloudfs/store/ufs/tos_info.h"
#include "common/store_types.h"
#include "concurrent/count_down_latch.h"
#include "gmock/gmock-actions.h"
#include "gmock/gmock-cardinalities.h"
#include "gmock/gmock-generated-actions.h"
#include "gmock/gmock-more-actions.h"
#include "gmock/gmock-spec-builders.h"
#include "gtest/gtest.h"
#include "string/format/print.h"
#include "util/scope_guard.h"

DECLARE_uint32(bytestore_cfs_upload_read_unit_size);
DECLARE_bool(bytestore_cfs_tos_throughput_qos_by_namespace_enable);

namespace bds::dancedn::cloudfs {

Aws::Utils::Outcome<Aws::S3::Model::CreateMultipartUploadResult,
                    Aws::S3::S3Error>
MakeCreateMultipartUploadResponse(
    const Aws::S3::Model::CreateMultipartUploadRequest& request, bool success,
    Aws::Http::HttpResponseCode code, const ExtendedBlock& block,
    const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  auto&& metadata = request.GetMetadata();
  int total_find = 0;
  for (auto&& iter = metadata.begin(); iter != metadata.end(); ++iter) {
    if (iter->first == "numbytes") {
      total_find++;
      EXPECT_EQ(iter->second, std::to_string(block.GetNumBytes()));
    } else if (iter->first == "blockpoolid") {
      total_find++;
      EXPECT_EQ(iter->second, block.GetBlockPoolID());
    } else if (iter->first == "blockid") {
      total_find++;
      EXPECT_EQ(iter->second, std::to_string(block.GetBlockID()));
    } else if (iter->first == "gs") {
      total_find++;
      EXPECT_EQ(iter->second, std::to_string(block.GetGS()));
    }
  }
  EXPECT_EQ(total_find, 4);
  EXPECT_EQ(request.ServerSideEncryptionHasBeenSet(),
            tos_info->EnableServerSideEncryption());
  if (success) {
    Aws::S3::Model::CreateMultipartUploadResult result;
    result.SetUploadId("upload_id");
    return Aws::Utils::Outcome<Aws::S3::Model::CreateMultipartUploadResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::CreateMultipartUploadResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::AbortMultipartUploadResult,
                    Aws::S3::S3Error>
MakeAbortMultipartUploadResponse(
    const Aws::S3::Model::AbortMultipartUploadRequest& request, bool success,
    Aws::Http::HttpResponseCode code, const ExtendedBlock& block,
    const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  EXPECT_EQ(request.GetUploadId(), "upload_id");
  if (success) {
    Aws::S3::Model::AbortMultipartUploadResult result;
    return Aws::Utils::Outcome<Aws::S3::Model::AbortMultipartUploadResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::AbortMultipartUploadResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::UploadPartResult, Aws::S3::S3Error>
MakeUploadPartResponse(const Aws::S3::Model::UploadPartRequest& request,
                       bool success, Aws::Http::HttpResponseCode code,
                       const std::string& object_key,
                       const std::shared_ptr<TOSInfo>& tos_info,
                       std::vector<int>* upload_part_num,
                       std::atomic<uint32_t>* length) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), object_key);
  EXPECT_EQ(request.GetUploadId(), "upload_id");
  length->fetch_add(request.GetContentLength());
  char* buffer = new char[request.GetContentLength()];
  byte::ScopeGuard buffer_deleter([&buffer]() {
    delete[] buffer;
  });
  uint32_t readed =
      request.GetBody()->readsome(buffer, request.GetContentLength());
  EXPECT_EQ(readed, request.GetContentLength());
  for (uint32_t i = 0; i < request.GetContentLength(); i++) {
    EXPECT_EQ(buffer[i], 'a');
  }
  (*upload_part_num)[request.GetPartNumber()] = 1;
  if (success) {
    Aws::S3::Model::UploadPartResult result;
    result.SetETag("etag");
    return Aws::Utils::Outcome<Aws::S3::Model::UploadPartResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::UploadPartResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::CompleteMultipartUploadResult,
                    Aws::S3::S3Error>
MakeCompleteMultipartUploadResponse(
    const Aws::S3::Model::CompleteMultipartUploadRequest& request, bool success,
    Aws::Http::HttpResponseCode code, const ExtendedBlock& block,
    const std::shared_ptr<TOSInfo>& tos_info, int part_num,
    std::map<uint32_t, std::string>* etags = nullptr) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  EXPECT_EQ(request.GetUploadId(), "upload_id");
  auto&& mpu = request.GetMultipartUpload();
  EXPECT_EQ(mpu.GetParts().size(), part_num);
  if (success) {
    for (auto&& part : request.GetMultipartUpload().GetParts()) {
      if (etags != nullptr) {
        (*etags)[part.GetPartNumber()] = part.GetETag();
      }
    }
    Aws::S3::Model::CompleteMultipartUploadResult result;
    return Aws::Utils::Outcome<Aws::S3::Model::CompleteMultipartUploadResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::CompleteMultipartUploadResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::PutObjectResult, Aws::S3::S3Error>
MakePutObjectResponse(const Aws::S3::Model::PutObjectRequest& request,
                      bool success, Aws::Http::HttpResponseCode code,
                      const ExtendedBlock& block,
                      const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  auto&& metadata = request.GetMetadata();
  int total_find = 0;
  for (auto&& iter = metadata.begin(); iter != metadata.end(); ++iter) {
    if (iter->first == "numbytes") {
      total_find++;
      EXPECT_EQ(iter->second, std::to_string(block.GetNumBytes()));
    } else if (iter->first == "blockpoolid") {
      total_find++;
      EXPECT_EQ(iter->second, block.GetBlockPoolID());
    } else if (iter->first == "blockid") {
      total_find++;
      EXPECT_EQ(iter->second, std::to_string(block.GetBlockID()));
    } else if (iter->first == "gs") {
      total_find++;
      EXPECT_EQ(iter->second, std::to_string(block.GetGS()));
    }
  }
  EXPECT_EQ(total_find, 4);
  EXPECT_EQ(request.ServerSideEncryptionHasBeenSet(),
            tos_info->EnableServerSideEncryption());
  char* buffer = new char[request.GetContentLength()];
  byte::ScopeGuard buffer_deleter([&buffer]() {
    delete[] buffer;
  });
  uint32_t readed =
      request.GetBody()->readsome(buffer, request.GetContentLength());
  EXPECT_EQ(readed, request.GetContentLength());
  EXPECT_EQ(readed, block.GetNumBytes());
  if (success) {
    Aws::S3::Model::PutObjectResult result;
    return Aws::Utils::Outcome<Aws::S3::Model::PutObjectResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::PutObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::PutObjectResult, Aws::S3::S3Error>
MakeAccPutObjectResponse(const Aws::S3::Model::PutObjectRequest& request,
                         bool success, Aws::Http::HttpResponseCode code,
                         const ExtendedBlock& block,
                         const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  auto&& metadata = request.GetMetadata();
  int total_find = 0;
  for (auto&& iter = metadata.begin(); iter != metadata.end(); ++iter) {
    if (iter->first == "Status") {
      total_find++;
      EXPECT_EQ(iter->second, "Created");
    } else if (iter->first == "CreateType") {
      total_find++;
      EXPECT_EQ(iter->second, "Put");
    } else if (iter->first == "Creator") {
      total_find++;
      EXPECT_EQ(iter->second, "CloudFS");
    }
  }
  EXPECT_EQ(total_find, 3);
  EXPECT_EQ(request.ServerSideEncryptionHasBeenSet(),
            tos_info->EnableServerSideEncryption());
  char* buffer = new char[request.GetContentLength()];
  byte::ScopeGuard buffer_deleter([&buffer]() {
    delete[] buffer;
  });
  uint32_t readed =
      request.GetBody()->readsome(buffer, request.GetContentLength());
  EXPECT_EQ(readed, request.GetContentLength());
  EXPECT_EQ(readed, block.GetNumBytes());
  if (success) {
    Aws::S3::Model::PutObjectResult result;
    return Aws::Utils::Outcome<Aws::S3::Model::PutObjectResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::PutObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::GetObjectResult, Aws::S3::S3Error>
MakeGetObjectResponse(const Aws::S3::Model::GetObjectRequest& request,
                      bool success, Aws::Http::HttpResponseCode code,
                      const ExtendedBlock& block,
                      const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  uint32_t start = 0;
  uint32_t end = block.GetNumBytes() - 1;
  std::string expected_range = byte::StringPrint("bytes=%u-%u", start, end);
  // EXPECT_EQ(request.GetRange(), expected_range);
  if (success) {
    auto&& iostream = request.GetResponseStreamFactory()();
    char* buffer = new char[block.GetNumBytes()];
    memset(buffer, 'a', block.GetNumBytes());
    (*iostream) << buffer;
    delete[] buffer;
    delete iostream;
    Aws::Utils::Outcome<Aws::S3::Model::GetObjectResult, Aws::S3::S3Error>
        outcome;
    outcome.GetResultWithOwnership().SetContentLength(block.GetNumBytes());
    char* tmp = reinterpret_cast<char*>(&outcome);
    *(reinterpret_cast<bool*>(tmp + sizeof(Aws::S3::Model::GetObjectResult) +
                              sizeof(Aws::S3::S3Error))) = true;
    return outcome;
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::GetObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::GetObjectResult, Aws::S3::S3Error>
MakeGetObjectNetworkConnectionErrorResponse(
    const Aws::S3::Model::GetObjectRequest& request, bool success,
    Aws::Http::HttpResponseCode code, const ExtendedBlock& block,
    const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  uint32_t start = 0;
  uint32_t end = block.GetNumBytes() - 1;
  std::string expected_range = byte::StringPrint("bytes=%u-%u", start, end);
  // EXPECT_EQ(request.GetRange(), expected_range);
  if (success) {
    auto&& iostream = request.GetResponseStreamFactory()();
    char* buffer = new char[block.GetNumBytes()];
    memset(buffer, 'a', block.GetNumBytes());
    (*iostream) << buffer;
    delete[] buffer;
    delete iostream;
    Aws::Utils::Outcome<Aws::S3::Model::GetObjectResult, Aws::S3::S3Error>
        outcome;
    outcome.GetResultWithOwnership().SetContentLength(block.GetNumBytes());
    char* tmp = reinterpret_cast<char*>(&outcome);
    *(reinterpret_cast<bool*>(tmp + sizeof(Aws::S3::Model::GetObjectResult) +
                              sizeof(Aws::S3::S3Error))) = true;
    return outcome;
  } else {
    Aws::Client::AWSError<Aws::S3::S3Errors> aws_error(
        Aws::S3::S3Errors::NETWORK_CONNECTION, "", "", false);
    Aws::S3::S3Error error(aws_error);
    return Aws::Utils::Outcome<Aws::S3::Model::GetObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::DeleteObjectResult, Aws::S3::S3Error>
MakeDeleteObjectResponse(const Aws::S3::Model::DeleteObjectRequest& request,
                         bool success, Aws::Http::HttpResponseCode code,
                         const std::string& object_key,
                         const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), object_key);
  if (success) {
    Aws::S3::Model::DeleteObjectResult result;
    return Aws::Utils::Outcome<Aws::S3::Model::DeleteObjectResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::DeleteObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::DeleteObjectsResult, Aws::S3::S3Error>
MakeDeleteObjectsResponse(const Aws::S3::Model::DeleteObjectsRequest& request,
                          bool success, Aws::Http::HttpResponseCode code,
                          const std::vector<std::string>& object_keys,
                          const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  auto&& to_delete = request.GetDelete();
  std::set<std::string> keyset;
  for (auto&& ele : object_keys) {
    keyset.emplace(ele);
  }
  for (auto&& iter = to_delete.GetObjects().begin();
       iter != to_delete.GetObjects().end(); iter++) {
    EXPECT_TRUE(keyset.find(iter->GetKey()) != keyset.end());
    keyset.erase(iter->GetKey());
  }
  EXPECT_TRUE(keyset.empty());
  if (success) {
    Aws::S3::Model::DeleteObjectsResult result;
    Aws::S3::Model::DeletedObject delete_object;
    delete_object.SetKey(object_keys[0]);
    result.AddDeleted(delete_object);
    Aws::S3::Model::Error error;
    error.SetKey(object_keys[1]);
    error.SetMessage("error");
    result.AddErrors(error);
    return Aws::Utils::Outcome<Aws::S3::Model::DeleteObjectsResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::DeleteObjectsResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::HeadObjectResult, Aws::S3::S3Error>
MakeHeadObjectResponse(const Aws::S3::Model::HeadObjectRequest& request,
                       bool success, Aws::Http::HttpResponseCode code,
                       const ExtendedBlock& block,
                       const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  if (success) {
    Aws::S3::Model::HeadObjectResult result;
    result.AddMetadata("numbytes", "0");
    result.AddMetadata("blockpoolid", block.GetBlockPoolID());
    result.AddMetadata("blockid", std::to_string(block.GetBlockID()));
    result.AddMetadata("gs", std::to_string(block.GetGS()));
    result.SetContentLength(block.GetNumBytes());
    result.SetETag("etag");
    return Aws::Utils::Outcome<Aws::S3::Model::HeadObjectResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::HeadObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

Aws::Utils::Outcome<Aws::S3::Model::HeadObjectResult, Aws::S3::S3Error>
MakeAppendObjectResponse(const Aws::S3::Model::AppendObjectRequest& request,
                         bool success, Aws::Http::HttpResponseCode code,
                         const ExtendedBlock& block,
                         const std::shared_ptr<TOSInfo>& tos_info) {
  EXPECT_EQ(request.GetBucket(), tos_info->GetBucket());
  EXPECT_EQ(request.GetKey(), block.GetObjectKey());
  auto&& metadata = request.GetMetadata();
  int total_find = 0;
  for (auto&& iter = metadata.begin(); iter != metadata.end(); ++iter) {
    if (iter->first == "Status") {
      total_find++;
      EXPECT_EQ(iter->second, "Created");
    } else if (iter->first == "CreateType") {
      total_find++;
      EXPECT_EQ(iter->second, "Append");
    } else if (iter->first == "Creator") {
      total_find++;
      EXPECT_EQ(iter->second, "CloudFS");
    }
  }
  EXPECT_EQ(total_find, 3);
  if (success) {
    Aws::S3::Model::AppendObjectResult result;
    result.SetETag("etag");
    return Aws::Utils::Outcome<Aws::S3::Model::AppendObjectResult,
                               Aws::S3::S3Error>(result);
  } else {
    Aws::S3::S3Error error;
    error.SetResponseCode(code);
    return Aws::Utils::Outcome<Aws::S3::Model::AppendObjectResult,
                               Aws::S3::S3Error>(error);
  }
}

void AsyncCallback(byte::CountDownLatch* latch, exceptions::Exception* ret,
                   exceptions::Exception e) {
  latch->CountDown();
  *ret = e;
}

class TosStoreTests : public ::testing::Test {
 public:
  void SetUp() override {
    // Init AWS
    {
      Aws::SDKOptions opt;
      Aws::InitAPI(opt);
    }
    FLAGS_bytestore_cfs_tos_throughput_qos_by_namespace_enable = false;
    bytestore::metrics_internal::InitFastMetrics();
    byterpc::ExecCtx::Init(byterpc::InitOptions());
    tos_info_ = std::make_shared<TOSInfo>("endpoint", "region", "access_key",
                                          "secret_key", "sts_token", "bucket",
                                          "prefix", true, 0);
    store_ = new MockChunkserverStore();
    dn_ = new MockDataNode();
    tos_store_ = new TosStore(store_, dn_);
  }

  void TearDown() override {
    delete store_;
    delete dn_;
    delete tos_store_;
    {
      Aws::SDKOptions opt;
      Aws::ShutdownAPI(opt);
    }
  }

 public:
  MockChunkserverStore* store_;
  MockDataNode* dn_;
  TosStore* tos_store_;
  BlockPoolManager* manager_;
  std::shared_ptr<TOSInfo> tos_info_;
};

TEST_F(TosStoreTests, GetS3Client) {
  std::string bpid = "bpid";
  EXPECT_CALL(*dn_, GetAwsCredentialsProvider)
      .Times(1)
      .WillOnce(
          ::testing::Return(std::make_shared<AwsCredentialsProviderImpl>()));
  std::shared_ptr<TOSInfo> tos_info = std::make_shared<TOSInfo>(
      "endpoint", "region", "access_key", "secret_key", "sts_token", "bucket",
      "prefix", true, 0);
  auto&& client = tos_store_->GetS3Client(bpid, tos_info);
  client = tos_store_->GetS3Client(bpid, tos_info);
  EXPECT_EQ(tos_store_->GetS3Client(bpid, tos_info).get(),
            tos_store_->GetS3Client(bpid, tos_info).get());
}

TEST_F(TosStoreTests, UploadBlock) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  ExtendedBlock block("bpid", 1, 8 * 1024 * 1024, 1, false);
  std::string upload_id = "";
  Aws::S3::Model::CompletedPart completed;

  // test if upload_id is missing
  exceptions::Exception e = tos_store_->UploadBlock(
      block, upload_id, bytestore::PRIORITY_ELASTIC, &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  upload_id = "upload_id";

  // test if object_key is empty
  e = tos_store_->UploadBlock(block, upload_id, bytestore::PRIORITY_ELASTIC,
                              &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  block.SetObjectKey("object_key");

  // test if tos_info is nullptr
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));
  e = tos_store_->UploadBlock(block, upload_id, bytestore::PRIORITY_ELASTIC,
                              &completed);
  ASSERT_EQ(e.GetE(), exceptions::kFalseException);

  io::IOChunk* buf = new io::IOChunk(16 * 1024 * 1024);
  byte::ScopeGuard buf_deleter([&buf]() {
    buf->AlwaysDestroy();
  });
  memset(buf->Data(), 'a', buf->Capacity());
  buf->IncrLength(buf->Capacity());
  // test read from local failed
  EXPECT_CALL(*store_, ReadBlock(::testing::_, ::testing::_, ::testing::_,
                                 ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillRepeatedly(::testing::Invoke(
          [&](ExtendedBlock* block, io::IOChunk* chunk, uint32_t data_offset,
              uint32_t data_len, CsIoPriority priority) {
            chunk->WriteBytes(buf->Data(), data_len);
            RETURN_NO_EXCEPTION();
          }));
  e = tos_store_->UploadBlock(block, upload_id, bytestore::PRIORITY_ELASTIC,
                              &completed);
  EXPECT_EQ(e.GetE(), exceptions::kIOException);

  std::vector<int> upload_part_num = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
  std::atomic<uint32_t> total_length(0);
  EXPECT_CALL(*client, UploadPart(::testing::_))
      .Times(3)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::UploadPartRequest& request) {
            return MakeUploadPartResponse(request, false,
                                          Aws::Http::HttpResponseCode::CONFLICT,
                                          block.GetObjectKey(), tos_info_,
                                          &upload_part_num, &total_length);
          }))
      .WillRepeatedly(::testing::Invoke(
          [&](const Aws::S3::Model::UploadPartRequest& request) {
            return MakeUploadPartResponse(request, true,
                                          Aws::Http::HttpResponseCode::OK,
                                          block.GetObjectKey(), tos_info_,
                                          &upload_part_num, &total_length);
          }));

  // test upload failed
  e = tos_store_->UploadBlock(block, upload_id, bytestore::PRIORITY_ELASTIC,
                              &completed);
  EXPECT_FALSE(e.OK());
  ASSERT_EQ(total_length, block.GetNumBytes());
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 1);
  for (int i = 2; i < 9; i++) {
    ASSERT_EQ(upload_part_num[i], 0);
  }
  upload_part_num[1] = 0;
  total_length = 0;

  // test upload succeed
  e = tos_store_->UploadBlock(block, upload_id, bytestore::PRIORITY_ELASTIC,
                              &completed);
  EXPECT_TRUE(e.OK());
  ASSERT_EQ(total_length, block.GetNumBytes());
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 1);
  for (int i = 2; i < 9; i++) {
    ASSERT_EQ(upload_part_num[i], 0);
  }
  upload_part_num[1] = 0;
  total_length = 0;

  // test big block
  block.SetNumBytes(127 * 1024 * 1024);
  e = tos_store_->UploadBlock(block, upload_id, bytestore::PRIORITY_ELASTIC,
                              &completed);
  EXPECT_TRUE(e.OK());
  ASSERT_EQ(total_length, block.GetNumBytes());
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 1);
  for (int i = 2; i < 9; i++) {
    ASSERT_EQ(upload_part_num[i], 0);
  }
}

// TEST_F(TosStoreTests, PutBlock) {
//     std::string bpid = "bpid";
//     std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
//     tos_store_->SetS3Client(client, bpid);
//     ExtendedBlock block("bpid", 1, 2 * 1024 * 1024, 1);
//     block.SetObjectKey("object_key");

//     EXPECT_CALL(*dn_, GetTosInfo(bpid))
//         .Times(::testing::AnyNumber())
//         .WillOnce(::testing::Return(nullptr))
//         .WillRepeatedly(::testing::Return(tos_info_));
//     // test if tos_info is nullptr
//     exceptions::Exception e = tos_store_->PutBlock(block,
//     bytestore::PRIORITY_ELASTIC); ASSERT_EQ(e.GetE(),
//     exceptions::kFalseException);

//     io::IOChunk* buf = new io::IOChunk(16 * 1024 * 1024);
//     byte::ScopeGuard buf_deleter([&buf]() { buf->AlwaysDestroy(); });
//     EXPECT_CALL(*store_,
//         ReadBlock(::testing::_, ::testing::_, ::testing::_, ::testing::_,
//         ::testing::_)) .Times(::testing::AnyNumber())
//         .WillOnce(::testing::Return(exceptions::Exception(exceptions::kIOException)))
//         .WillRepeatedly(::testing::Invoke(
//             [&](ExtendedBlock* block, io::IOChunk* chunk, uint32_t
//             data_offset,
//                 uint32_t data_len, CsIoPriority priority) {
//                 chunk->WriteBytes(buf->Data(), data_len);
//                 RETURN_NO_EXCEPTION();
//             }));

//     // test if read from local failed
//     e = tos_store_->PutBlock(block, bytestore::PRIORITY_ELASTIC);
//     ASSERT_FALSE(e.OK());

//     // test if put failed
//     EXPECT_CALL(*client, PutObject(::testing::_))
//         .Times(3)
//         .WillOnce(::testing::Invoke([&](const
//         Aws::S3::Model::PutObjectRequest& request) {
//             return MakePutObjectResponse(
//                 request, false, Aws::Http::HttpResponseCode::CONFLICT, block,
//                 tos_info_);
//         }))
//         .WillOnce(::testing::Invoke([&](const
//         Aws::S3::Model::PutObjectRequest& request) {
//             return MakePutObjectResponse(
//                 request, true, Aws::Http::HttpResponseCode::OK, block,
//                 tos_info_);
//         }))
//         .WillOnce(::testing::Invoke([&](const
//         Aws::S3::Model::PutObjectRequest& request) {
//             return MakeAccPutObjectResponse(
//                 request, true, Aws::Http::HttpResponseCode::OK, block,
//                 tos_info_);
//         }));
//     NameSpaceInfo ns_info(
//         1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
//         NamespaceType::TOS_MANAGED);
//     NameSpaceInfo acc_ns_info(
//         1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
//         NamespaceType::ACC_TOS);
//     EXPECT_CALL(*dn_, GetNamespaceInfo(::testing::_))
//         .Times(3)
//         .WillOnce(::testing::Return(&ns_info))
//         .WillOnce(::testing::Return(&ns_info))
//         .WillOnce(::testing::Return(&acc_ns_info));

//     e = tos_store_->PutBlock(block, bytestore::PRIORITY_ELASTIC);
//     ASSERT_FALSE(e.OK());

//     // test if put succeed
//     e = tos_store_->PutBlock(block, bytestore::PRIORITY_ELASTIC);
//     ASSERT_TRUE(e.OK());
//     e = tos_store_->PutBlock(block, bytestore::PRIORITY_ELASTIC);
//     ASSERT_TRUE(e.OK());
// }

/*
 * This test accesses the actual tos instead of using gmock for simulated
 * access. Therefore, it is in a DISABLED state by default. You can test it by
 * '/build/bytestore/chunkserver/hdfs_tos_store_test
 * --gtest_filter=*PutEmptyBlock*
 * --gtest_also_run_disabled_tests', and before run this unit test, you need to
 * input the correct endpoint/region of tos.
 */
// TEST_F(TosStoreTests, DISABLED_PutEmptyBlock) {
//     std::string bpid = "bpid";
//     std::shared_ptr<TOSInfo> tos_info =
//         std::make_shared<TOSInfo>("endpoint",
//                                   "region",
//                                   "access_key",
//                                   "secret_key",
//                                   "sts_token",
//                                   "cloudfs-ut",
//                                   "dancedn",
//                                   true,
//                                   0);
//     Aws::Client::ClientConfiguration config;
//     config.endpointOverride.assign(tos_info->GetEndpoint());
//     config.region.assign(tos_info->GetRegion());
//     std::shared_ptr<Aws::S3::S3Client> client =
//     std::make_shared<Aws::S3::S3Client>(config);
//     tos_store_->SetS3Client(client, bpid);
//     ExtendedBlock block("bpid", 1, 0, 1);
//     block.SetObjectKey("dancedn/empty.block");

//     EXPECT_CALL(*dn_, GetTosInfo(bpid))
//         .Times(1)
//         .WillOnce(::testing::Return(tos_info));

//     io::IOChunk* buf = new io::IOChunk(16 * 1024 * 1024);
//     byte::ScopeGuard buf_deleter([&buf]() { buf->AlwaysDestroy(); });

//     NameSpaceInfo ns_info(
//         1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
//         NamespaceType::TOS_MANAGED);

//     auto&& e = tos_store_->PutBlock(block, bytestore::PRIORITY_ELASTIC);
//     ASSERT_TRUE(e.OK());
// }

TEST_F(TosStoreTests, ReadBlock) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  ExtendedBlock block("bpid", 1, 2 * 1024 * 1024, 1, false);
  block.SetObjectKey("object_key");

  io::IOChunk* chunk = new io::IOChunk(2 * 1024 * 1024);
  io::ChunkPtr chunk_deleter(chunk, io::IOChunk::Destroy);
  memset(chunk->Data(), 0, 2 * 1024 * 1024);
  // test if argument is mission
  exceptions::Exception e = tos_store_->ReadBlock(
      ExtendedBlock("", 1, 1, 1, false, "object_key"), 0, block.GetNumBytes(),
      bytestore::PRIORITY_ELASTIC, chunk);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  e = tos_store_->ReadBlock(ExtendedBlock("bpid", 1, 1, 1, false, ""), 0,
                            block.GetNumBytes(), bytestore::PRIORITY_ELASTIC,
                            chunk);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  e = tos_store_->ReadBlock(ExtendedBlock("bpid", 1, 1, 1, false, "object_key"),
                            0, 0, bytestore::PRIORITY_ELASTIC, chunk);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  e = tos_store_->ReadBlock(block, 0, 16 * 1024 * 1024,
                            bytestore::PRIORITY_ELASTIC, chunk);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));

  // test if tos_info is nullptr
  e = tos_store_->ReadBlock(block, 0, block.GetNumBytes(),
                            bytestore::PRIORITY_ELASTIC, chunk);
  ASSERT_EQ(e.GetE(), exceptions::kFalseException);

  // test if read failed
  EXPECT_CALL(*client, GetObject(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectResponse(request, false,
                                         Aws::Http::HttpResponseCode::CONFLICT,
                                         block, tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectResponse(request, true,
                                         Aws::Http::HttpResponseCode::OK, block,
                                         tos_info_);
          }));
  e = tos_store_->ReadBlock(block, 0, block.GetNumBytes(),
                            bytestore::PRIORITY_ELASTIC, chunk);
  ASSERT_FALSE(e.OK());

  // test if put succeed
  e = tos_store_->ReadBlock(block, 0, block.GetNumBytes(),
                            bytestore::PRIORITY_ELASTIC, chunk);
  ASSERT_TRUE(e.OK());
  bool pass = true;
  for (uint32_t i = 0; i < block.GetNumBytes(); i++) {
    if ((chunk->Data())[i] != 'a') {
      pass = false;
    }
  }
  EXPECT_TRUE(pass);
  EXPECT_CALL(*client, GetObject(::testing::_))
      .Times(1)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectResponse(request, true,
                                         Aws::Http::HttpResponseCode::OK, block,
                                         tos_info_);
          }));
  io::IOChunk* chunk2 = new io::IOChunk(block.GetNumBytes() + 100);
  io::ChunkPtr chunk_deleter2(chunk2, io::IOChunk::Destroy);
  e = tos_store_->ReadBlock(block, 0, block.GetNumBytes() + 100,
                            bytestore::PRIORITY_ELASTIC, chunk2);
  ASSERT_FALSE(e.OK());
  ASSERT_TRUE(e.ToString().find("!= expect") != std::string::npos);
}

TEST_F(TosStoreTests, ReadBlockAsync) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  ExtendedBlock block("bpid", 1, 2 * 1024 * 1024, 1, false);
  block.SetObjectKey("object_key");

  EXPECT_CALL(*dn_, GetRemoteBlockInfo(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));

  std::shared_ptr<byte::CountDownLatch> latch;
  byterpc::IOBuf buffer;
  exceptions::Exception e;
  Closure<void, exceptions::Exception>* done;

  EXPECT_CALL(*client, GetObject(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectResponse(request, false,
                                         Aws::Http::HttpResponseCode::CONFLICT,
                                         block, tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectResponse(request, true,
                                         Aws::Http::HttpResponseCode::OK, block,
                                         tos_info_);
          }));
  // 1. read failed
  latch = std::make_shared<byte::CountDownLatch>(1);
  buffer.clear();
  e = exceptions::Exception(exceptions::kNoException);
  done = NewClosure(&AsyncCallback, latch.get(), &e);
  tos_store_->ReadBlockAsync(block, 0, block.GetNumBytes(),
                             bytestore::PRIORITY_ELASTIC, &buffer, done);
  latch->Wait();
  ASSERT_FALSE(e.OK());
  ASSERT_EQ(buffer.size(), 0);

  // 1. read successfully
  latch = std::make_shared<byte::CountDownLatch>(1);
  buffer.clear();
  e = exceptions::Exception(exceptions::kNoException);
  done = NewClosure(&AsyncCallback, latch.get(), &e);
  tos_store_->ReadBlockAsync(block, 0, block.GetNumBytes(),
                             bytestore::PRIORITY_ELASTIC, &buffer, done);
  latch->Wait();
  ASSERT_TRUE(e.OK());
  ASSERT_EQ(buffer.length(), block.GetNumBytes());
  buffer.clear();
}

// mutipart upload for ACC_TOS
TEST_F(TosStoreTests, AccMultipartUpload) {
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, "bpid");
  std::vector<ExtendedBlock> blocks = {
      ExtendedBlock("bpid", 1, 2 * 1024 * 1024, 1, false),
      ExtendedBlock("bpid", 2, 4 * 1024 * 1024, 1, false)};
  std::vector<uint32_t> part_nums = {2};
  Aws::S3::Model::CompletedPart completed;
  // upload id empty
  exceptions::Exception e = tos_store_->AccMultipartUploadBlock(
      blocks, "object_key", "", part_nums, bytestore::PRIORITY_ELASTIC,
      &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);
  e = tos_store_->AccMultipartUploadBlock(blocks, "", "upload_id", part_nums,
                                          bytestore::PRIORITY_ELASTIC,
                                          &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);
  e = tos_store_->AccMultipartUploadBlock(
      blocks, "object_key", "upload_id", std::vector<uint32_t>(),
      bytestore::PRIORITY_ELASTIC, &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));

  // expect tos_info == nullptr
  e = tos_store_->AccMultipartUploadBlock(
      blocks, "object_key", "upload_id", part_nums, bytestore::PRIORITY_ELASTIC,
      &completed);
  ASSERT_EQ(e.GetE(), exceptions::kFalseException);
  EXPECT_CALL(*store_, ReadBlock(::testing::_, ::testing::_, ::testing::_,
                                 ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillRepeatedly(::testing::Invoke(
          [&](ExtendedBlock* block, io::IOChunk* chunk, uint32_t data_offset,
              uint32_t data_len, CsIoPriority priority) {
            char* buffer = new char[data_len];
            memset(buffer, 'a', data_len);
            chunk->WriteBytes(reinterpret_cast<const uint8_t*>(buffer),
                              data_len);
            delete[] buffer;
            RETURN_NO_EXCEPTION();
          }));

  // expect read block failed
  e = tos_store_->AccMultipartUploadBlock(
      blocks, "object_key", "upload_id", part_nums, bytestore::PRIORITY_ELASTIC,
      &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIOException);

  std::vector<int> upload_part_num = {0, 0, 0};
  std::atomic<uint32_t> length(0);
  EXPECT_CALL(*client, UploadPart(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::UploadPartRequest& request) {
            return MakeUploadPartResponse(
                request, false, Aws::Http::HttpResponseCode::CONFLICT,
                "object_key", tos_info_, &upload_part_num, &length);
          }))
      .WillRepeatedly(::testing::Invoke(
          [&](const Aws::S3::Model::UploadPartRequest& request) {
            return MakeUploadPartResponse(
                request, true, Aws::Http::HttpResponseCode::OK, "object_key",
                tos_info_, &upload_part_num, &length);
          }));
  // expect upload failed
  e = tos_store_->AccMultipartUploadBlock(
      blocks, "object_key", "upload_id", part_nums, bytestore::PRIORITY_ELASTIC,
      &completed);
  ASSERT_EQ(e.GetE(), exceptions::kIOException);
  ASSERT_EQ(upload_part_num[2], 1);
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 0);
  upload_part_num[2] = 0;
  ASSERT_EQ(length, 6 * 1024 * 1024);
  length = 0;

  e = tos_store_->AccMultipartUploadBlock(
      blocks, "object_key", "upload_id", part_nums, bytestore::PRIORITY_ELASTIC,
      &completed);
  ASSERT_EQ(e.GetE(), exceptions::kNoException);
  ASSERT_EQ(upload_part_num[2], 1);
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 0);
  ASSERT_EQ(length, 6 * 1024 * 1024);
  ASSERT_EQ(completed.GetPartNumber(), 2);
  ASSERT_EQ(completed.GetETag(), "etag");
}

TEST_F(TosStoreTests, AppendObject) {
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, "bpid");
  ExtendedBlock block("bpid", 1, 2 * 1024 * 1024, 1, false);
  block.SetOffset(1024 * 1024 * 8);
  block.SetObjectKey("object_key");
  NameSpaceInfo ns_info(1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
                        NamespaceType::TOS_MANAGED);
  NameSpaceInfo acc_ns_info(1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
                            NamespaceType::ACC_TOS);
  EXPECT_CALL(*dn_, GetNamespaceInfo(::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(&ns_info))
      .WillRepeatedly(::testing::Return(&acc_ns_info));
  std::string etag;
  // expect namespace info is not acc_tos
  exceptions::Exception e =
      tos_store_->AppendBlock(block, bytestore::PRIORITY_ELASTIC, etag);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  EXPECT_CALL(*store_, ReadBlock(::testing::_, ::testing::_, ::testing::_,
                                 ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillRepeatedly(::testing::Invoke(
          [&](ExtendedBlock* block, io::IOChunk* chunk, uint32_t data_offset,
              uint32_t data_len, CsIoPriority priority) {
            char* buffer = new char[data_len];
            memset(buffer, 'a', data_len);
            chunk->WriteBytes(reinterpret_cast<const uint8_t*>(buffer),
                              data_len);
            delete[] buffer;
            RETURN_NO_EXCEPTION();
          }));

  EXPECT_CALL(*client, AppendObject(::testing::_))
      .Times(3)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AppendObjectRequest& request) {
            return std::move(MakeAppendObjectResponse(
                request, true, Aws::Http::HttpResponseCode::OK, block,
                tos_info_));
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AppendObjectRequest& req) {
            return MakeAppendObjectResponse(
                req, false, Aws::Http::HttpResponseCode::CONFLICT, block,
                tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AppendObjectRequest& req) {
            return MakeAppendObjectResponse(
                req, false, Aws::Http::HttpResponseCode::CONFLICT, block,
                tos_info_);
          }));

  // expect read block failed
  e = tos_store_->AppendBlock(block, bytestore::PRIORITY_ELASTIC, etag);
  ASSERT_EQ(e.GetE(), exceptions::kIOException);

  // expect call append object successfully
  e = tos_store_->AppendBlock(block, bytestore::PRIORITY_ELASTIC, etag);
  ASSERT_EQ(e.GetE(), exceptions::kNoException);

  EXPECT_CALL(*client, HeadObject(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(
          ::testing::Invoke([&](const Aws::S3::Model::HeadObjectRequest& req) {
            return MakeHeadObjectResponse(req, true,
                                          Aws::Http::HttpResponseCode::CONFLICT,
                                          block, tos_info_);
          }));
  block.SetOffset(0);

  // expect length mismatch
  e = tos_store_->AppendBlock(block, bytestore::PRIORITY_ELASTIC, etag);
  ASSERT_EQ(e.GetE(), exceptions::kNoException);
  ASSERT_EQ(etag, "etag");
  etag = "";
  block.SetOffset(1024 * 1024 * 2);
  e = tos_store_->AppendBlock(block, bytestore::PRIORITY_ELASTIC, etag);
  ASSERT_EQ(e.GetE(), exceptions::kIOException);
  ASSERT_EQ(etag, "");
}

TEST_F(TosStoreTests, DeleteBlock) {
  std::string bpid = "bpid";
  std::string object_key = "object_key";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);

  // test if bpid is empty
  exceptions::Exception e =
      tos_store_->DeleteBlock("", object_key, bytestore::PRIORITY_ELASTIC);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  // test if object_key is empty
  e = tos_store_->DeleteBlock(bpid, "", bytestore::PRIORITY_ELASTIC);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  // test if tos_info is nullptr
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));
  e = tos_store_->DeleteBlock(bpid, object_key, bytestore::PRIORITY_ELASTIC);
  EXPECT_EQ(e.GetE(), exceptions::kIOException);

  // test if send failed
  EXPECT_CALL(*client, DeleteObject(::testing::_))
      .Times(3)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::DeleteObjectRequest& req) {
            return MakeDeleteObjectResponse(
                req, false, Aws::Http::HttpResponseCode::CONFLICT, object_key,
                tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::DeleteObjectRequest& req) {
            return MakeDeleteObjectResponse(
                req, false, Aws::Http::HttpResponseCode::NOT_FOUND, object_key,
                tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::DeleteObjectRequest& req) {
            return MakeDeleteObjectResponse(req, true,
                                            Aws::Http::HttpResponseCode::OK,
                                            object_key, tos_info_);
          }));
  e = tos_store_->DeleteBlock(bpid, object_key, bytestore::PRIORITY_ELASTIC);
  ASSERT_FALSE(e.OK());

  // test if not found
  e = tos_store_->DeleteBlock(bpid, object_key, bytestore::PRIORITY_ELASTIC);
  ASSERT_TRUE(e.OK());

  // test if delete succeed
  e = tos_store_->DeleteBlock(bpid, object_key, bytestore::PRIORITY_ELASTIC);
  ASSERT_TRUE(e.OK());
}

TEST_F(TosStoreTests, DeleteBlocks) {
  std::string bpid = "bpid";
  std::vector<std::string> object_keys = {"object_key1", "object_key2"};
  std::vector<std::string> deleted;
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);

  // test if bpid is empty
  exceptions::Exception e = tos_store_->DeleteBlocks(
      "", object_keys, bytestore::PRIORITY_ELASTIC, &deleted);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  // test if object_keys is empty
  e = tos_store_->DeleteBlocks(bpid, std::vector<std::string>(),
                               bytestore::PRIORITY_ELASTIC, &deleted);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  // test if tos_info is nullptr
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));
  e = tos_store_->DeleteBlocks(bpid, object_keys, bytestore::PRIORITY_ELASTIC,
                               &deleted);
  EXPECT_EQ(e.GetE(), exceptions::kIOException);

  // test if send failed
  EXPECT_CALL(*client, DeleteObjects(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::DeleteObjectsRequest& req) {
            return MakeDeleteObjectsResponse(
                req, false, Aws::Http::HttpResponseCode::CONFLICT, object_keys,
                tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::DeleteObjectsRequest& req) {
            return MakeDeleteObjectsResponse(req, true,
                                             Aws::Http::HttpResponseCode::OK,
                                             object_keys, tos_info_);
          }));
  e = tos_store_->DeleteBlocks(bpid, object_keys, bytestore::PRIORITY_ELASTIC,
                               &deleted);
  ASSERT_FALSE(e.OK());

  // test if delete object 1 succeed and delete object 2 failed
  e = tos_store_->DeleteBlocks(bpid, object_keys, bytestore::PRIORITY_ELASTIC,
                               &deleted);
  ASSERT_TRUE(e.OK());
  EXPECT_EQ(deleted.size(), 1);
  EXPECT_EQ(deleted[0], object_keys[0]);
}

TEST_F(TosStoreTests, GetBlockInfo) {
  std::string bpid = "bpid";
  std::string object_key = "object_key";
  ExtendedBlock block("bpid", 1, 128 * 1024 * 1024, 1, false);
  block.SetObjectKey(object_key);
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);

  // test if bpid is empty
  std::shared_ptr<ExtendedBlock> block_ptr;
  exceptions::Exception e = tos_store_->GetBlockInfo(
      "", object_key, bytestore::PRIORITY_ELASTIC, block_ptr);
  EXPECT_FALSE(e.OK());
  EXPECT_EQ(block_ptr, nullptr);

  // test if object_keys is empty
  e = tos_store_->GetBlockInfo(bpid, "", bytestore::PRIORITY_ELASTIC,
                               block_ptr);
  EXPECT_FALSE(e.OK());
  EXPECT_EQ(block_ptr, nullptr);

  // test if tos_info is nullptr
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));
  e = tos_store_->GetBlockInfo(bpid, object_key, bytestore::PRIORITY_ELASTIC,
                               block_ptr);
  EXPECT_FALSE(e.OK());
  EXPECT_EQ(block_ptr, nullptr);

  // test if send failed
  EXPECT_CALL(*client, HeadObject(::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(
          ::testing::Invoke([&](const Aws::S3::Model::HeadObjectRequest& req) {
            return MakeHeadObjectResponse(req, false,
                                          Aws::Http::HttpResponseCode::CONFLICT,
                                          block, tos_info_);
          }))
      .WillRepeatedly(
          ::testing::Invoke([&](const Aws::S3::Model::HeadObjectRequest& req) {
            return MakeHeadObjectResponse(
                req, true, Aws::Http::HttpResponseCode::OK, block, tos_info_);
          }));
  e = tos_store_->GetBlockInfo(bpid, object_key, bytestore::PRIORITY_ELASTIC,
                               block_ptr);
  EXPECT_FALSE(e.OK());
  EXPECT_EQ(block_ptr, nullptr);

  // test succeed
  e = tos_store_->GetBlockInfo(bpid, object_key, bytestore::PRIORITY_ELASTIC,
                               block_ptr);
  EXPECT_NE(block_ptr, nullptr);
  EXPECT_EQ(block_ptr->GetBlockPoolID(), block.GetBlockPoolID());
  EXPECT_EQ(block_ptr->GetBlockID(), block.GetBlockID());
  EXPECT_EQ(block_ptr->GetNumBytes(), block.GetNumBytes());
  EXPECT_EQ(block_ptr->GetGS(), block.GetGS());

  NameSpaceInfo ns_info(1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
                        NamespaceType::BOUNDARY);
  NameSpaceInfo acc_ns_info(1, "cluster", "bpid", 0, "2.6.0", "2.6.3", 1, 1,
                            NamespaceType::ACC_TOS);
  EXPECT_CALL(*dn_, GetNamespaceInfo(::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(&ns_info))
      .WillRepeatedly(::testing::Return(&acc_ns_info));

  RemoteStoreImpl* impl = new RemoteStoreImpl(nullptr, dn_);
  impl->TEST_SetRemote(NamespaceType::ACC_TOS, tos_store_);
  e = impl->GetBlockInfo(bpid, object_key, bytestore::PRIORITY_ELASTIC,
                         block_ptr);
  EXPECT_EQ(e.GetE(), exceptions::kIllegalArgumentException);
  e = impl->GetBlockInfo(bpid, object_key, bytestore::PRIORITY_ELASTIC,
                         block_ptr);
  EXPECT_TRUE(e.OK());
  impl->TEST_SetRemote(NamespaceType::ACC_TOS, nullptr);
}

TEST_F(TosStoreTests, CreateMultipartUploadV2) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  void* ctx;

  // test if object_key is missing
  exceptions::Exception e = tos_store_->CreateMultipartUploadV2(
      block, &ctx, bytestore::PRIORITY_ELASTIC);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  block->SetObjectKey("object_key");

  // test if tos_info is nullptr
  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr))
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));
  e = tos_store_->CreateMultipartUploadV2(block, &ctx,
                                          bytestore::PRIORITY_ELASTIC);
  ASSERT_EQ(e.GetE(), exceptions::kIllegalArgumentException);

  // test send request
  EXPECT_CALL(*client, CreateMultipartUpload(::testing::_))
      .Times(3)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::CreateMultipartUploadRequest& req) {
            return MakeCreateMultipartUploadResponse(
                req, false, Aws::Http::HttpResponseCode::CONFLICT, *block,
                tos_info_);
          }))  // without retry
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::CreateMultipartUploadRequest& req) {
            return MakeCreateMultipartUploadResponse(
                req, false, Aws::Http::HttpResponseCode::TOO_MANY_REQUESTS,
                *block, tos_info_);
          }))  // should retry
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::CreateMultipartUploadRequest& req) {
            return MakeCreateMultipartUploadResponse(
                req, true, Aws::Http::HttpResponseCode::OK, *block, tos_info_);
          }));
  e = tos_store_->CreateMultipartUploadV2(block, &ctx,
                                          bytestore::PRIORITY_ELASTIC);
  EXPECT_FALSE(e.OK());
  e = tos_store_->CreateMultipartUploadV2(block, &ctx,
                                          bytestore::PRIORITY_ELASTIC);
  EXPECT_TRUE(e.OK());
  TosStore::UploadContextV2* context =
      reinterpret_cast<TosStore::UploadContextV2*>(ctx);
  EXPECT_EQ(context->upload_id_, "upload_id");
  delete context;
}

TEST_F(TosStoreTests, MultipartUploadPartV2) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  block->SetObjectKey("object_key");
  std::unique_ptr<TosStore::UploadContextV2> ctx(new TosStore::UploadContextV2{
      block, "upload_id", client, tos_info_,
      byte::concurrent::HashTable<uint32_t, Aws::S3::Model::CompletedPart>(),
      bytestore::PRIORITY_ELASTIC});
  // test if chunk's used length is 0
  io::IOChunk* zero_chunk = new io::IOChunk(0);
  io::ChunkPtr zero_chunk_ptr(zero_chunk, io::IOChunk::Destroy);

  exceptions::Exception e =
      tos_store_->MultipartUploadPartV2(ctx.get(), zero_chunk, 1);
  EXPECT_EQ(e.GetE(), exceptions::kIOException);
  std::vector<int> upload_part_num = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
  std::atomic<uint32_t> length(0);
  EXPECT_CALL(*client, UploadPart(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::UploadPartRequest& request) {
            return MakeUploadPartResponse(
                request, false, Aws::Http::HttpResponseCode::CONFLICT,
                block->GetObjectKey(), tos_info_, &upload_part_num, &length);
          }))
      .WillRepeatedly(::testing::Invoke(
          [&](const Aws::S3::Model::UploadPartRequest& request) {
            return MakeUploadPartResponse(
                request, true, Aws::Http::HttpResponseCode::OK,
                block->GetObjectKey(), tos_info_, &upload_part_num, &length);
          }));
  io::IOChunk* chunk = new io::IOChunk(16 * 1024 * 1024);
  io::ChunkPtr chunk_deleter(chunk, io::IOChunk::Destroy);
  chunk->IncrLength(16 * 1024 * 1024);
  memset(chunk->Data(), 'a', 16 * 1024 * 1024);
  // test upload failed
  e = tos_store_->MultipartUploadPartV2(ctx.get(), chunk, 2);
  EXPECT_FALSE(e.OK());
  ASSERT_EQ(length, 16 * 1024 * 1024);
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 0);
  ASSERT_EQ(upload_part_num[2], 1);
  for (int i = 3; i < 9; i++) {
    ASSERT_EQ(upload_part_num[i], 0);
  }
  ASSERT_EQ(ctx->parts_.Size(), 0);
  upload_part_num[2] = 0;
  length = 0;

  // test upload succeed
  e = tos_store_->MultipartUploadPartV2(ctx.get(), chunk, 3);
  EXPECT_TRUE(e.OK());
  ASSERT_EQ(length, 16 * 1024 * 1024);
  ASSERT_EQ(upload_part_num[0], 0);
  ASSERT_EQ(upload_part_num[1], 0);
  ASSERT_EQ(upload_part_num[2], 0);
  ASSERT_EQ(upload_part_num[3], 1);
  for (int i = 4; i < 9; i++) {
    ASSERT_EQ(upload_part_num[i], 0);
  }
  ASSERT_EQ(ctx->parts_.Size(), 1);
  Aws::S3::Model::CompletedPart part;
  bool ret = ctx->parts_.Get(3, part);
  ASSERT_TRUE(ret);
  ASSERT_EQ(part.GetETag(), "etag");
  upload_part_num[3] = 0;
  length = 0;
}

TEST_F(TosStoreTests, CompleteMultipartUploadV2) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  TosStore::UploadContextV2* ctx = new TosStore::UploadContextV2{
      block,
      "upload_id",
      client,
      tos_info_,
      byte::concurrent::HashTable<uint32_t, Aws::S3::Model::CompletedPart>(),
      bytestore::PRIORITY_ELASTIC};
  Aws::S3::Model::CompletedPart part1;
  part1.SetPartNumber(1);
  part1.SetETag("etag1");
  Aws::S3::Model::CompletedPart part3;
  part3.SetPartNumber(3);
  part3.SetETag("etag3");

  ctx->parts_.Put(1, part1);
  std::map<uint32_t, std::string> etags;
  EXPECT_CALL(*client, CompleteMultipartUpload(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::CompleteMultipartUploadRequest& request) {
            return MakeCompleteMultipartUploadResponse(
                request, false, Aws::Http::HttpResponseCode::CONFLICT, *block,
                tos_info_, 1);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::CompleteMultipartUploadRequest& request) {
            return MakeCompleteMultipartUploadResponse(
                request, true, Aws::Http::HttpResponseCode::OK, *block,
                tos_info_, 2, &etags);
          }));
  exceptions::Exception e = tos_store_->CompleteMultipartUploadV2(ctx);
  ASSERT_FALSE(e.OK());

  ctx = new TosStore::UploadContextV2{
      block,
      "upload_id",
      client,
      tos_info_,
      byte::concurrent::HashTable<uint32_t, Aws::S3::Model::CompletedPart>(),
      bytestore::PRIORITY_ELASTIC};
  ctx->parts_.Put(1, part1);
  ctx->parts_.Put(3, part3);
  e = tos_store_->CompleteMultipartUploadV2(ctx);
  ASSERT_TRUE(e.OK());
  ASSERT_EQ(etags.size(), 2);
  ASSERT_EQ(etags[1], "etag1");
  ASSERT_EQ(etags[3], "etag3");
}

TEST_F(TosStoreTests, AbortMultipartUploadV2) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);

  // test send request
  EXPECT_CALL(*client, AbortMultipartUpload(::testing::_))
      .Times(4)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AbortMultipartUploadRequest& req) {
            return MakeAbortMultipartUploadResponse(
                req, false, Aws::Http::HttpResponseCode::CONFLICT, *block,
                tos_info_);
          }))  // without retry
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AbortMultipartUploadRequest& req) {
            return MakeAbortMultipartUploadResponse(
                req, false, Aws::Http::HttpResponseCode::TOO_MANY_REQUESTS,
                *block, tos_info_);
          }))  // should retry
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AbortMultipartUploadRequest& req) {
            return MakeAbortMultipartUploadResponse(
                req, true, Aws::Http::HttpResponseCode::OK, *block, tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::AbortMultipartUploadRequest& req) {
            return MakeAbortMultipartUploadResponse(
                req, false, Aws::Http::HttpResponseCode::NOT_FOUND, *block,
                tos_info_);
          }));  // should assume successfuly
  TosStore::UploadContextV2* ctx = new TosStore::UploadContextV2{
      block,
      "upload_id",
      client,
      tos_info_,
      byte::concurrent::HashTable<uint32_t, Aws::S3::Model::CompletedPart>(),
      bytestore::PRIORITY_ELASTIC};
  exceptions::Exception e = tos_store_->AbortMultipartUploadV2(ctx);
  EXPECT_FALSE(e.OK());

  ctx = new TosStore::UploadContextV2{
      block,
      "upload_id",
      client,
      tos_info_,
      byte::concurrent::HashTable<uint32_t, Aws::S3::Model::CompletedPart>(),
      bytestore::PRIORITY_ELASTIC};
  e = tos_store_->AbortMultipartUploadV2(ctx);
  EXPECT_TRUE(e.OK());

  ctx = new TosStore::UploadContextV2{
      block,
      "upload_id",
      client,
      tos_info_,
      byte::concurrent::HashTable<uint32_t, Aws::S3::Model::CompletedPart>(),
      bytestore::PRIORITY_ELASTIC};
  e = tos_store_->AbortMultipartUploadV2(ctx);
  EXPECT_TRUE(e.OK());
}

TEST_F(TosStoreTests, AwsPreallocStreamBuf) {
  char bufferStr[] = "xxxxxxxxxxxx";
  char replaceBuf[] = "yyyyyyyyyyyy";
  int prefix_len = 5;  // Should less than sizeof(bufferStr)
  Aws::Utils::Stream::PreallocatedStreamBuf stream_buf(
      reinterpret_cast<unsigned char*>(bufferStr), prefix_len);

  Aws::IOStream ioStream(&stream_buf);
  ioStream.write(replaceBuf, sizeof(replaceBuf));
  ASSERT_EQ(0, memcmp(bufferStr, replaceBuf, prefix_len));
  ASSERT_NE(0, memcmp(bufferStr, replaceBuf, prefix_len + 1));
}

TEST_F(TosStoreTests, ReadBlockFromLocal) {
  FLAGS_bytestore_cfs_upload_read_unit_size = 256 * 1024;
  ExtendedBlock block("bpid", 1, 512 * 1024 - 1, 1, false);
  EXPECT_CALL(*store_, ReadBlock(::testing::_, ::testing::_, ::testing::_,
                                 ::testing::_, ::testing::_))
      .Times(3)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(::testing::Invoke([&](ExtendedBlock* block, io::IOChunk* chunk,
                                      uint32_t data_offset, uint32_t data_len,
                                      CsIoPriority priority) {
        EXPECT_EQ(data_len, 256 * 1024);
        char* buffer = new char[data_len];
        chunk->WriteBytes(reinterpret_cast<const uint8_t*>(buffer), data_len);
        delete[] buffer;
        RETURN_NO_EXCEPTION();
      }))
      .WillOnce(::testing::Invoke([&](ExtendedBlock* block, io::IOChunk* chunk,
                                      uint32_t data_offset, uint32_t data_len,
                                      CsIoPriority priority) {
        EXPECT_EQ(data_len, 256 * 1024 - 1);
        char* buffer = new char[data_len];
        chunk->WriteBytes(reinterpret_cast<const uint8_t*>(buffer), data_len);
        delete[] buffer;
        RETURN_NO_EXCEPTION();
      }));
  io::IOChunk* chunk = new io::IOChunk(block.GetNumBytes());
  exceptions::Exception e =
      tos_store_->TestReadBlockFromLocal(&block, 0, block.GetNumBytes(), chunk,
                                         CsIoPriority::PRIORITY_BEST_EFFORT);
  ASSERT_FALSE(e.OK());
  e = tos_store_->TestReadBlockFromLocal(&block, 0, block.GetNumBytes(), chunk,
                                         CsIoPriority::PRIORITY_BEST_EFFORT);
  ASSERT_TRUE(e.OK());
  ASSERT_EQ(chunk->UnusedLength(), 0);
  chunk->AlwaysDestroy();
}

TEST_F(TosStoreTests, RetryNetworkConnectionError) {
  std::string bpid = "bpid";
  std::shared_ptr<MockS3Client> client = std::make_shared<MockS3Client>();
  tos_store_->SetS3Client(client, bpid);
  ExtendedBlock block("bpid", 1, 2 * 1024 * 1024, 1, false);
  block.SetObjectKey("object_key");

  io::IOChunk* chunk = new io::IOChunk(2 * 1024 * 1024);
  io::ChunkPtr chunk_deleter(chunk, io::IOChunk::Destroy);
  memset(chunk->Data(), 0, 2 * 1024 * 1024);
  exceptions::Exception e;

  EXPECT_CALL(*dn_, GetRemoteBlockInfo(bpid))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(
          std::dynamic_pointer_cast<RemoteBlockInfo>(tos_info_)));

  // test if read failed
  EXPECT_CALL(*client, GetObject(::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectNetworkConnectionErrorResponse(
                request, false, Aws::Http::HttpResponseCode::CONFLICT, block,
                tos_info_);
          }))
      .WillOnce(::testing::Invoke(
          [&](const Aws::S3::Model::GetObjectRequest& request) {
            return MakeGetObjectNetworkConnectionErrorResponse(
                request, true, Aws::Http::HttpResponseCode::OK, block,
                tos_info_);
          }));
  e = tos_store_->ReadBlock(block, 0, block.GetNumBytes(),
                            bytestore::PRIORITY_ELASTIC, chunk);
  ASSERT_TRUE(e.OK());
  bool pass = true;
  for (uint32_t i = 0; i < block.GetNumBytes(); i++) {
    if ((chunk->Data())[i] != 'a') {
      pass = false;
    }
  }
  EXPECT_TRUE(pass);
}

}  // namespace bds::dancedn::cloudfs
