// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/ufs/tos_retry_strategy.h"

#include "aws/core/client/AWSError.h"
#include "aws/core/client/CoreErrors.h"
#include "common/metrics.h"
#include "gtest/gtest.h"

namespace bds::dancedn::cloudfs {

class TosRetryStrategyTests : public ::testing::Test {
 public:
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
    tos_retry_strategy_ = new TosRetryStrategy();
  }
  void TearDown() override {
    delete tos_retry_strategy_;
  }

 public:
  TosRetryStrategy* tos_retry_strategy_;
};

TEST_F(TosRetryStrategyTests, ShouldRetry) {
  Aws::Client::AWSError<Aws::Client::CoreErrors> error;
  ASSERT_FALSE(tos_retry_strategy_->ShouldRetry(error, 0));
}

}  // namespace bds::dancedn::cloudfs
