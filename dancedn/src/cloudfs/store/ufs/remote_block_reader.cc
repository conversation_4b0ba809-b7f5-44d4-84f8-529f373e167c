// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.
#include "cloudfs/store/ufs/remote_block_reader.h"

#include <algorithm>

#include "cloudfs/constants.h"
#include "cloudfs/metrics.h"
#include "cloudfs/store/unified_block_store.h"

DECLARE_int64(bytestore_cfs_remote_reader_cache_ttl);

namespace bds::dancedn::cloudfs {

RemoteReadBlockContext::RemoteReadBlockContext(bytestore::MemoryPool* pool,
                                               ExtendedBlock* blk,
                                               int64_t req_t_us)
    : BlockAsyncCbContext(pool, blk, BlockOPType::READ_REMOTE, req_t_us) {}
RemoteReadBlockContext::~RemoteReadBlockContext() {
  if (chunk_ != nullptr) {
    chunk_->AlwaysDestroy();
  }
}
void RemoteReadBlockContext::Complete(::cloudfs::Status status,
                                      const std::string& err_msg) {
  BlockAsyncCbContext::Complete(status, err_msg);
}

RemoteBlockReader::RemoteBlockReader(RemoteBlockReaderFactory* factory,
                                     std::string& key,
                                     UnifiedBlockStore* unified_store)
    : factory_(factory),
      reader_key_(key),
      unified_store_(unified_store),
      last_read_timestamp_ms_(byte::GetCurrentTimeInMs()) {}

RemoteBlockReader::~RemoteBlockReader() {
  METRICS_rpc_remote_reader_reset_num->SetValue(reset_cnt_);
  METRICS_rpc_remote_reader_read_num->SetValue(read_cnt_);
  byte::MutexLocker lock(&lock_);
  for (const auto& task : task_list_) {
    task.ctx->read_status_ =
        exceptions::Exception(exceptions::E::kInterruptedException, "Canceled");
    LOG(WARNING) << "Remote reader is stopped, reader: " << reader_key_
                 << ", offset:" << task.ctx->offset_;
    task.done->Run();
  }
  task_list_.clear();
}

void RemoteBlockReader::ReadAsync(RemoteReadBlockContext* context,
                                  google::protobuf::Closure* done) {
  if (IsStopped()) {
    context->read_status_ =
        exceptions::Exception(exceptions::E::kInterruptedException, "Canceled");
    LOG(WARNING) << "Remote reader is stopped, reader: " << reader_key_
                 << ", offset:" << context->offset_;
    done->Run();
    return;
  }
  last_read_timestamp_ms_ = byte::GetCurrentTimeInMs();
  {
    byte::MutexLocker lock(&lock_);
    ReadTask task;
    task.ctx = context;
    task.done = done;
    task_list_.emplace_back(task);
  }
  METRICS_rpc_remote_read_pool_pending_num->Increment();
  Signal();
}

size_t RemoteBlockReader::TaskCount() {
  byte::MutexLocker lock(&lock_);
  return task_list_.size();
}

bool RemoteBlockReader::IsIdleFor(int64_t duration_ms) {
  return !reading_ && (TaskCount() == 0) &&
         (byte::GetCurrentTimeInMs() - last_read_timestamp_ms_ > duration_ms);
}

bool RemoteBlockReader::IsEof() {
  return !reading_ && (TaskCount() == 0) && read_eof_;
}

void RemoteBlockReader::Run() {
  while (!IsStopped()) {
    WaitUtil(
        [this]() {
          return (TaskCount() > 0) || IsStopped();
        },
        0);
    std::vector<ReadTask> tasks;
    {
      byte::MutexLocker lock(&lock_);
      if (task_list_.empty()) {
        break;
      }
      reading_ = true;
      tasks.swap(task_list_);
    }
    for (auto& task : tasks) {
      read_eof_ = false;
      RunReadTask(task);
      read_cnt_++;
    }
    reading_ = false;
  }
}

void RemoteBlockReader::RunReadTask(ReadTask& task) {
  METRICS_rpc_remote_read_pool_pending_num->Decrement();
  RemoteReadBlockContext* context = task.ctx;
  uint64_t current_offset =
      (remote_stream_ == nullptr) ? 0 : remote_stream_->GetCurrentOffset();
  if (remote_stream_ == nullptr ||
      context->offset_ != remote_stream_->GetCurrentOffset()) {
    auto e = ResetRemoteStream(context);
    if (!e.OK()) {
      LOG(ERROR) << "Fail to create tos reader for " << reader_key_;
      context->read_status_ = e;
      return task.done->Run();
    }
    LOG(INFO) << "Reset tos stream for " << reader_key_
              << ", original offset: " << current_offset
              << ", request offset: " << context->offset_;
  }
  if (context->chunk_ == nullptr) {
    context->chunk_ = new io::IOChunk(context->length_);
  }
  bool block_read_eof = (context->offset_ + context->length_ ==
                         remote_stream_->GetBlock()->GetNumBytes());
  uint64_t remain_length = context->length_;
  while (remain_length > 0) {
    uint32_t returned_size = 0;
    exceptions::Exception e =
        remote_stream_->Read(remain_length, context->chunk_, &returned_size);
    if (!e.OK()) {
      context->read_status_ = e;
      break;
    }
    remain_length -= returned_size;
  }
  task.done->Run();
  if (block_read_eof) {
    read_eof_ = true;
    remote_stream_.reset(nullptr);
    factory_->SignalCleaner();
  }
}

exceptions::Exception RemoteBlockReader::ResetRemoteStream(
    RemoteReadBlockContext* ctx) {
  remote_stream_.reset(nullptr);
  BlockStream* tmp_remote_stream = nullptr;
  // For ACC mode, the offset should be offset in TOS file. In HDFS mode,
  // block->offset == 0.
  uint64_t offset = ctx->block_->GetOffset() + ctx->offset_;
  uint64_t length = ctx->block_->GetNumBytes() - ctx->offset_;
  auto e = unified_store_->CreateBlockStream(
      ctx->block_, offset, length, *ctx->strategy_.get(), &tmp_remote_stream);
  if (!e.OK()) {
    return e;
  }
  reset_cnt_++;
  remote_stream_.reset(dynamic_cast<RemoteBlockStream*>(tmp_remote_stream));
  return exceptions::Exception();
}

class RemoteBlockReaderCleaner : public Thread {
 public:
  explicit RemoteBlockReaderCleaner(RemoteBlockReaderFactory* reader_factory)
      : reader_factory_(reader_factory) {}
  void Run() override {
    while (!IsStopped()) {
      WaitUtil(
          [this]() {
            return IsStopped() || immediate_clean_;
          },
          FLAGS_bytestore_cfs_remote_reader_cache_ttl);
      if (IsStopped()) {
        return;
      }
      immediate_clean_ = false;
      reader_factory_->CleanRemoteBlockReaderCache();
    }
  }

  void Signal() {
    immediate_clean_ = true;
    Thread::Signal();
  }

 private:
  RemoteBlockReaderFactory* reader_factory_;
  std::atomic<bool> immediate_clean_{false};
};

RemoteBlockReaderFactory::RemoteBlockReaderFactory(UnifiedBlockStore* store)
    : unified_store_(store), stopped_(false) {
  remote_reader_cleaner_ = new RemoteBlockReaderCleaner(this);
  remote_reader_cleaner_->Start();
}

exceptions::Exception RemoteBlockReaderFactory::CheckRemoteReadRequest(
    ExtendedBlock* block, uint64_t offset, uint32_t length) {
  NamespaceType ns_type;
  const std::string& bpid = block->GetBlockPoolID();
  if (!unified_store_->GetNamespaceType(bpid, &ns_type)) {
    std::string msg = byte::StringPrint(
        "Error: Failed to get namespace type of bpid %s", bpid);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  if (!IsAccType(ns_type)) {
    std::shared_ptr<ExtendedBlock> replica;
    exceptions::Exception e =
        unified_store_->GetRemoteStore(ns_type)->GetBlockInfo(
            bpid, block->GetObjectKey(), bytestore::PRIORITY_ELASTIC, replica);
    if (!e.OK()) {
      std::string msg =
          "Replica is not found from remote, block=" + block->ToString();
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }
    if (replica->GetNumBytes() < 0) {
      std::string msg = "Replica is not readable, block=" + block->ToString() +
                        ", replica=" + replica->ToString();
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }
    if (block->GetNumBytes() != replica->GetNumBytes()) {
      std::string msg = byte::StringPrint(
          "Number of bytes in replica %s does not match block %s",
          replica->ToString(), block->ToString());
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }
  }
  return exceptions::Exception();
}

exceptions::Exception RemoteBlockReaderFactory::GetOrCreateRemoteBlockReader(
    uint64_t stream_id, ExtendedBlock* block, uint64_t offset, uint32_t length,
    const CachingStrategy& strategy,
    std::shared_ptr<RemoteBlockReader>* reader) {
  std::string remote_reader_key = RemoteReaderKey(block, stream_id);
  {
    byte::MutexLocker lock_guard(&remote_reader_mutex_);
    auto iter = remote_reader_cache_.find(remote_reader_key);
    if (iter != remote_reader_cache_.end()) {
      *reader = iter->second;
      return exceptions::Exception();
    }
  }
  std::shared_ptr<RemoteBlockReader> remote_reader(nullptr);
  if (remote_reader == nullptr) {
    auto e = CheckRemoteReadRequest(block, offset, length);
    if (!e.OK()) {
      return e;
    }
  }
  remote_reader = std::make_shared<RemoteBlockReader>(this, remote_reader_key,
                                                      unified_store_);
  byte::MutexLocker lock_guard(&remote_reader_mutex_);
  auto iter = remote_reader_cache_.find(remote_reader_key);
  if (iter != remote_reader_cache_.end()) {
    remote_reader = iter->second;
  } else {
    remote_reader_cache_[remote_reader_key] = remote_reader;
    remote_reader->Start();
  }
  *reader = remote_reader;
  return exceptions::Exception();
}

void RemoteBlockReaderFactory::SignalCleaner() {
  if (!stopped_ && remote_reader_cleaner_ != nullptr) {
    remote_reader_cleaner_->Signal();
  }
}

void RemoteBlockReaderFactory::StopRemoteReaderCache() {
  // Stop all readers, then stop cleaner.
  if (stopped_) {
    return;
  }
  stopped_ = true;
  CleanRemoteBlockReaderCache();

  remote_reader_cleaner_->Stop();
  remote_reader_cleaner_->Join();
  delete remote_reader_cleaner_;
  remote_reader_cleaner_ = nullptr;
}

void RemoteBlockReaderFactory::CleanRemoteBlockReaderCache() {
  std::vector<std::shared_ptr<RemoteBlockReader>> readers_to_clean;
  size_t total_cnt = 0;
  {
    byte::MutexLocker lock_guard(&remote_reader_mutex_);
    total_cnt = remote_reader_cache_.size();
    for (auto iter = remote_reader_cache_.begin();
         iter != remote_reader_cache_.end();) {
      if (iter->second->IsIdleFor(
              FLAGS_bytestore_cfs_remote_reader_cache_ttl) ||
          iter->second->IsEof() || stopped_) {
        readers_to_clean.push_back(iter->second);
        iter = remote_reader_cache_.erase(iter);
      } else {
        iter++;
      }
    }
  }
  METRICS_rpc_remote_reader_num->SetValue(total_cnt);
  METRICS_rpc_remote_reader_clean_num->Add(readers_to_clean.size());
  for (auto& reader : readers_to_clean) {
    reader->Stop();
    reader->Join();
    LOG(INFO) << "Remote Block reader is cleaned. Block: " << reader->Key()
              << ", Idle: "
              << reader->IsIdleFor(FLAGS_bytestore_cfs_remote_reader_cache_ttl)
              << ", Eof: " << reader->IsEof() << ", stopped: " << stopped_;
  }
  readers_to_clean.clear();
}

void RemoteBlockReaderFactory::SubmitDirectRead(
    RemoteReadBlockContext* ctx, google::protobuf::Closure* done) {
  auto e = CheckRemoteReadRequest(ctx->block_, ctx->offset_, ctx->length_);
  if (!e.OK()) {
    ctx->read_status_ = e;
    return done->Run();
  }
  if (ctx->chunk_ == nullptr) {
    ctx->chunk_ = new io::IOChunk(ctx->length_);
  }
  // For ACC mode, the offset should be offset in TOS file. In HDFS mode,
  // block->offset == 0.
  uint64_t cur_offset = ctx->block_->GetOffset() + ctx->offset_;
  uint64_t end_offset = cur_offset + ctx->length_;
  ExtendedBlock* block = ctx->block_;
  while (end_offset > cur_offset) {
    uint32_t read_length = std::min(
        static_cast<uint32_t>(end_offset - cur_offset), TOS_DOWNLOAD_PART_SIZE);
    exceptions::Exception e =
        unified_store_->GetRemoteStore(ctx->ns_type_)
            ->ReadBlock(*block, cur_offset, read_length,
                        bytestore::PRIORITY_ELASTIC, ctx->chunk_);
    if (!e.OK()) {
      ctx->read_status_ = e;
      break;
    }
    cur_offset += read_length;
  }
  done->Run();
}

size_t RemoteBlockReaderFactory::GetRemoteReaderCount() {
  byte::MutexLocker lock_guard(&remote_reader_mutex_);
  return remote_reader_cache_.size();
}

}  // namespace bds::dancedn::cloudfs
