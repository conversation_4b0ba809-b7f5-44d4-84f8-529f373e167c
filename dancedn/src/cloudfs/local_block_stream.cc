
// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/local_block_stream.h"

#include "cloudfs/store/chunkserver/chunkserver_store.h"

namespace bds::dancedn::cloudfs {

exceptions::Exception LocalBlockStream::Read(
    uint32_t size, io::IOChunk* chunk,
    /* OUT */ uint32_t* returned_size) {
  if (nullptr == returned_size) {
    return exceptions::Exception(exceptions::kIllegalArgumentException);
  }
  uint64_t offset = GetCurrentOffset();
  uint64_t endoffset = GetStartOffset() + GetLength();
  if (offset + size > endoffset) {
    return exceptions::Exception(
        exceptions::kEOFException,
        byte::StringPrint("Requested size out of range. stream end: [%lu], "
                          "offset: [%lu], size: [%u]",
                          endoffset, offset, size));
  }
  auto e = local_store_->ReadBlock(GetBlock().get(), chunk, offset, size,
                                   bytestore::PRIORITY_ELASTIC);
  if (e.OK()) {
    IncreaseCurrentOffset(size);
    *returned_size = size;
  }
  return e;
}

}  // namespace bds::dancedn::cloudfs
