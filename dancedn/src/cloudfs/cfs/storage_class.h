// copyright (c) 2021-present, bytedance inc. all rights reserved.

#pragma once

#include <cstdint>
#include <memory>
#include <string>

#include "cloudfs/proto/lifecycle.pb.h"
namespace bds::dancedn::cloudfs {

// the max value must be less than 16(can only use 4 bits to represent the
// storage class)
enum class StorageClass : uint8_t { WARM = 0, HOT = 1, COLD = 2, BOUNDARY = 3 };

static_assert(static_cast<int>(StorageClass::BOUNDARY) < 16, "exceed limit");

const StorageClass DEFAULT_STORAGE_CLASS = StorageClass::WARM;

const std::string& StorageClassToString(StorageClass storage_class);

::cloudfs::StorageClassProto ConvertStorageClassToProto(
    StorageClass storage_class);

}  // namespace bds::dancedn::cloudfs
