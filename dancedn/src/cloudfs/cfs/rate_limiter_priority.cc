// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/rate_limiter_priority.h"

#include "byte/thread/this_thread.h"

DECLARE_double(bytestore_cfs_low_pri_limiter_ratio);

namespace bds::dancedn::cloudfs {

double PriorityRateLimiter::Acquire(uint32_t permits,
                                    bytestore::IOPriority priority,
                                    uint64_t timeout_us) {
  BYTE_DEFER(total_acquired_rate_.Add(permits));
  byte::MutexLocker locker(&mutex_);
  if (stopping_) {
    return 0.0;
  }

  UpdateAvailablePermits();
  if (IsHighPriority(priority)) {
    if (available_permits_ >= permits) {
      available_permits_ -= permits;
      return 0.0;
    } else {
      int64_t start_us = byte::GetCurrentTimeInUs();
      while (available_permits_ < permits) {
        if (stopping_) {
          return 0.0;
        }
        mutex_.Unlock();
        byte::ThisThread::SleepInUs(10);
        mutex_.Lock();
        UpdateAvailablePermits();
      }
      available_permits_ -= permits;
      return static_cast<double>(byte::GetCurrentTimeInUs() - start_us) / 1e6;
    }
  } else {  // low priority
    BYTE_DEFER(low_acquired_rate_.Add(permits));
    if (available_permits_ >= permits &&
        total_acquired_rate_.GetRate() < total_rate_ * 0.95) {
      available_permits_ -= permits;
      return 0;
    }
    double low_pri_limit = total_rate_ * low_priority_ratio_;
    int64_t start_us = byte::GetCurrentTimeInUs();
    while (available_permits_ < permits ||
           (low_acquired_rate_.GetRate() > low_pri_limit &&
            total_acquired_rate_.GetRate() >= total_rate_ * 0.95)) {
      if (stopping_) {
        return 0.0;
      }
      mutex_.Unlock();
      byte::ThisThread::SleepInUs(10);
      mutex_.Lock();
      UpdateAvailablePermits();
    }
    available_permits_ -= permits;
    return static_cast<double>(byte::GetCurrentTimeInUs() - start_us) / 1e6;
  }
}

// Only support two level priority
bool PriorityRateLimiter::IsHighPriority(bytestore::IOPriority priority) {
  return priority <= bytestore::PRIORITY_ELASTIC;
}

void PriorityRateLimiter::UpdateAvailablePermits() {
  auto now = byte::GetCurrentTimeInUs();
  auto elapsed = now - last_update_us_;
  available_permits_ += (elapsed / 1e6) * total_rate_;
  if (available_permits_ > total_rate_) {
    available_permits_ = total_rate_;
  }
  last_update_us_ = now;
}

void PriorityRateLimiter::UpdateRate(double permits_per_second) {
  byte::MutexLocker locker(&mutex_);
  total_rate_ = permits_per_second;
  low_priority_ratio_ = FLAGS_bytestore_cfs_low_pri_limiter_ratio;
}

void PriorityRateLimiter::QuickStop() {
  stopping_.store(true);
}

}  // namespace bds::dancedn::cloudfs
