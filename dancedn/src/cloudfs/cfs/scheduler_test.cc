// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/scheduler.h"

#include <cassert>
#include <cstdint>
#include <functional>

#include "base/closure.h"
#include "byte_log/byte_log_impl.h"
#include "concurrent/count_down_latch.h"
#include "gtest/gtest.h"
#include "system/timestamp.h"
#include "thread/this_thread.h"

namespace bds::dancedn::cloudfs {

void CountDownLatch(byte::CountDownLatch* latch) {
  latch->CountDown();
}

void ClosureHelper(std::function<void()> func) {
  func();
}

class SchedulerTest : public ::testing::Test {
 public:
  static void SetUpTestCase() {}

  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(SchedulerTest, SchedulerClosureOnce) {
  Scheduler scheduler("scheduler-test");
  byte::CountDownLatch latch1(1);
  uint64_t start = byte::GetCurrentTimeInMs();
  scheduler.ScheduleOnce(NewClosure(&CountDownLatch, &latch1), 1000ULL * 100);
  latch1.Wait();
  uint64_t end1 = byte::GetCurrentTimeInMs();
  EXPECT_NEAR(100, end1 - start, 10);

  byte::CountDownLatch latch2(1);
  byte::CountDownLatch latch3(1);
  start = byte::GetCurrentTimeInMs();
  scheduler.ScheduleOnce(NewClosure(&CountDownLatch, &latch2), 1000ULL * 100);
  scheduler.ScheduleOnce(NewClosure(&CountDownLatch, &latch3), 1000ULL * 200);
  latch2.Wait();
  uint64_t end2 = byte::GetCurrentTimeInMs();
  latch3.Wait();
  uint64_t end3 = byte::GetCurrentTimeInMs();
  EXPECT_NEAR(100, end2 - start, 10);
  EXPECT_NEAR(200, end3 - start, 10);
}

TEST_F(SchedulerTest, SchedulerFunctionOnce) {
  Scheduler scheduler("scheduler-test");
  byte::CountDownLatch latch1(1);
  auto task1 = [&]() {
    latch1.CountDown();
  };
  uint64_t start = byte::GetCurrentTimeInMs();
  scheduler.ScheduleOnce(task1, 1000ULL * 100);
  latch1.Wait();
  uint64_t end1 = byte::GetCurrentTimeInMs();
  EXPECT_NEAR(100, end1 - start, 10);

  byte::CountDownLatch latch2(1);
  auto task2 = [&]() {
    latch2.CountDown();
  };
  byte::CountDownLatch latch3(1);
  auto task3 = [&]() {
    latch3.CountDown();
  };
  start = byte::GetCurrentTimeInMs();
  scheduler.ScheduleOnce(task2, 1000ULL * 100);
  scheduler.ScheduleOnce(task3, 1000ULL * 200);
  latch2.Wait();
  uint64_t end2 = byte::GetCurrentTimeInMs();
  latch3.Wait();
  uint64_t end3 = byte::GetCurrentTimeInMs();
  EXPECT_NEAR(100, end2 - start, 10);
  EXPECT_NEAR(200, end3 - start, 10);
}

TEST_F(SchedulerTest, SchedulerClosureRepeatedly) {
  Scheduler* scheduler = new Scheduler("scheduler-test");
  std::vector<uint64_t> timestamps;
  std::function<void()> func = [&timestamps]() {
    timestamps.emplace_back(byte::GetCurrentTimeInMs());
  };
  Closure<void>* closure = NewPermanentClosure(ClosureHelper, func);
  scheduler->ScheduleRepeatedly(closure, 100ULL * 1000);
  uint64_t start_time = byte::GetCurrentTimeInMs();
  byte::ThisThread::SleepInMs(5000);
  delete scheduler;
  for (size_t i = 0; i < timestamps.size(); ++i) {
    EXPECT_NEAR(100 * (i + 1), timestamps[i] - start_time, 50);
  }
  delete closure;

  scheduler = new Scheduler("scheduler-test");
  timestamps.clear();
  func = [&timestamps]() {
    timestamps.emplace_back(byte::GetCurrentTimeInMs());
    byte::ThisThread::SleepInMs(1000);
  };
  closure = NewPermanentClosure(ClosureHelper, func);
  scheduler->ScheduleRepeatedly(closure, 100ULL * 1000);
  start_time = byte::GetCurrentTimeInMs();
  byte::ThisThread::SleepInMs(5000);
  delete scheduler;
  for (size_t i = 0; i < timestamps.size(); ++i) {
    EXPECT_NEAR(100 + 1000 * i, timestamps[i] - start_time, 50);
  }
  delete closure;
}

TEST_F(SchedulerTest, SchedulerFunctionRepeatedly) {
  Scheduler* scheduler = new Scheduler("scheduler-test");
  std::vector<uint64_t> timestamps;
  std::function<void()> func = [&timestamps]() {
    timestamps.emplace_back(byte::GetCurrentTimeInMs());
  };
  scheduler->ScheduleRepeatedly(func, 100ULL * 1000);
  uint64_t start_time = byte::GetCurrentTimeInMs();
  byte::ThisThread::SleepInMs(5000);
  delete scheduler;
  for (size_t i = 0; i < timestamps.size(); ++i) {
    EXPECT_NEAR(100 * (i + 1), timestamps[i] - start_time, 50);
  }

  scheduler = new Scheduler("scheduler-test");
  timestamps.clear();
  func = [&timestamps]() {
    timestamps.emplace_back(byte::GetCurrentTimeInMs());
    byte::ThisThread::SleepInMs(1000);
  };
  scheduler->ScheduleRepeatedly(func, 100ULL * 1000);
  start_time = byte::GetCurrentTimeInMs();
  byte::ThisThread::SleepInMs(5000);
  delete scheduler;
  for (size_t i = 0; i < timestamps.size(); ++i) {
    EXPECT_NEAR(100 + 1000 * i, timestamps[i] - start_time, 50);
  }
}

}  // namespace bds::dancedn::cloudfs
