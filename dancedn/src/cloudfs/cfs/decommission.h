// copyright (c) 2021-present, bytedance inc. all rights reserved.

#pragma once

#include <atomic>
#include <cstdint>
#include <memory>
#include <string>

#include "byte/concurrent/count_down_latch.h"
#include "cloudfs/block.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/thread.h"

namespace bds::dancedn::cloudfs {

class DataNode;
class ChunkServerStore;

class DecommissionWorker;

enum class DecommissionStatus {
  NORMAL,
  DECOMMISSION_IN_PROGRESS,
  DECOMMISSION_FINISHED
};

class DecommissionMgr {
 public:
  explicit DecommissionMgr(DataNode* datanode_);
  ~DecommissionMgr();

  // this function should be execute after disk loaded
  void Init();

  exceptions::Exception StartDecommission();
  void FinishDecommission();
  exceptions::Exception StopDecommission();

  bool IsDecommission();
  bool IsDecommissioned();
  bool IsDecommissionInProgress();

  void SetBlockNumTotal(uint32_t num) {
    total_num_ = num;
  }
  void SetBlockNumInProgress(uint32_t num) {
    in_progress_num_ = num;
  }
  void SetBlockNumWriteCacheTotal(uint32_t num) {
    write_cache_total_num_ = num;
  }
  void SetInProgressDetail(const std::string detail) {
    byte::MutexLocker guard(&detail_mutex_);
    in_progress_detail_ = detail;
  }

  uint32_t GetBlockNumTotal() {
    return total_num_;
  }
  uint32_t GetBlockNumInProgress() {
    return in_progress_num_;
  }
  uint32_t GetBlockNumWriteCacheTotal() {
    return write_cache_total_num_;
  }
  std::string GetInProgressDetail() {
    byte::MutexLocker guard(&detail_mutex_);
    return in_progress_detail_;
  }

 private:
  DataNode* datanode_;
  ChunkServerStore* store_;
  byte::Mutex mutex_;
  byte::Mutex detail_mutex_;
  DecommissionStatus status_;
  std::unique_ptr<DecommissionWorker> worker_;
  uint32_t total_num_;
  uint32_t in_progress_num_;
  uint32_t write_cache_total_num_;
  std::string in_progress_detail_;
  bool enable_transfer_limit_;
};

class DecommissionWorker : public Thread {
 public:
  DecommissionWorker(DecommissionMgr* decommission_mgr, DataNode* datanode);
  ~DecommissionWorker();

 private:
  void Run();
  void CountDownWaitLatch(ClosureThread* async_thread);

 private:
  DecommissionMgr* decommission_mgr_;
  DataNode* datanode_;
  ChunkServerStore* store_;
  byte::CountDownLatch wait_latch_;
};

}  // namespace bds::dancedn::cloudfs
