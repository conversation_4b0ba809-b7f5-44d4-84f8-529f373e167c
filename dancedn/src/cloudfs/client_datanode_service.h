// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "cloudfs/exceptions.h"
#include "cloudfs/opstats/op_key.h"
#include "cloudfs/opstats/operation.h"
#include "cloudfs/proto/ClientDatanodeProtocol.pb.h"

namespace cloudfs {
class RpcRequestHeaderProto;
class RequestHeaderProto;
class RpcResponseHeaderProto;
}  // namespace cloudfs

namespace cloudfs {
class RefreshNamenodesResponseProto;
class ShutdownDatanodeResponseProto;
class TriggerBlockReportResponseProto;
}  // namespace cloudfs

namespace bds::dancedn::cloudfs {

namespace message {
class RpcRequestMessage;
class RpcResponseMessage;
}  // namespace message

class ClientDatanodeService {
 public:
  ClientDatanodeService() {}
  virtual ~ClientDatanodeService() {}
  virtual message::RpcRequestMessage* NewRequest(const std::string& name) = 0;
  virtual message::RpcResponseMessage* CallMethod(
      message::RpcRequestMessage* r) = 0;
  virtual std::string GetProtocolName() = 0;
};

}  // namespace bds::dancedn::cloudfs
