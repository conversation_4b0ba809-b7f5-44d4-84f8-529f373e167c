// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/block_recovery.h"

#include <memory>
#include <string>
#include <vector>

#include "byte/string/format/print.h"
#include "cloudfs/client_io.h"
#include "cloudfs/constants.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_registration.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/inter_datanode_client.h"
#include "cloudfs/mocks/mock_block_recovery_thread.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namenode_client.h"
#include "cloudfs/namenode_rpc.h"
#include "cloudfs/recovering_block.h"
#include "cloudfs/replica_recovery_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/services/hdfs_io_service.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace bds::dancedn::cloudfs {

static const char storage_id[] = "suuid";

static const char bp_id[] = "tbpi";
static const uint64_t blk_gs0 = 1;
static const uint64_t blk_gs1 = 2;
static const uint64_t blk_id = 20190828;
static const uint64_t blk_len = 4096;
ExtendedBlock test_block(bp_id, blk_id, blk_len, blk_gs0, false);

// DatanodeIDs
static const char ip_addr[] = "127.0.0.1";
static const char hostname[] = "localhost";
static const char uuid[] = "duuid001";
static const int xfer_port = 5060;
static const int info_port = 5061;
static const int ipc_port = 5062;
static const char node_name[] = "";
std::shared_ptr<DatanodeID> local_dn_id(new DatanodeID(
    ip_addr, hostname, uuid, xfer_port, info_port, ipc_port, node_name,
    std::vector<std::string>(), std::vector<std::string>()));

// DatanodeRegistration
auto local_dn_reg = std::make_shared<DatanodeRegistration>(
    local_dn_id, new StorageInfo(NodeType::DATA_NODE),
    DATANODE_SOFTWARE_VERSION);

class BlockRecoveryTests : public ::testing::Test {
 public:
  BlockRecoveryTests() {}

  ~BlockRecoveryTests() {}

  void SetUp() {
    bytestore::metrics_internal::InitFastMetrics();
    std::vector<DatanodeInfo*> dn_infos = {new DatanodeInfo(local_dn_id),
                                           new DatanodeInfo(local_dn_id),
                                           new DatanodeInfo(local_dn_id)};
    rb_.reset(new RecoveringBlock(test_block.Clone(), dn_infos, blk_gs1, true));
  }

  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  void PrepareForTestRecoveryInfos(MockStore* storage, MockDataNode* dn,
                                   MockBlockRecoveryThread* t) {
    EXPECT_CALL(*dn, GetDNRegistrationForBP(bp_id, ::testing::_))
        .WillRepeatedly(
            ::testing::DoAll(::testing::SetArgPointee<1>(local_dn_reg),
                             ::testing::Return(exceptions::Exception())));

    EXPECT_CALL(*dn, CommitBlockSynchronization(bp_id, ::testing::_,
                                                ::testing::_, ::testing::_,
                                                ::testing::_, ::testing::_,
                                                ::testing::_, ::testing::_))
        .WillRepeatedly(::testing::Return(exceptions::Exception()));

    EXPECT_CALL(*dn, NotifyNamenodeReceivedBlock(::testing::_, "", storage_id))
        .WillRepeatedly(::testing::Return());

    dn->SetStorage(storage);
  }

 public:
  std::unique_ptr<RecoveringBlock> rb_;
};

TEST_F(BlockRecoveryTests, BPServiceNotFound) {
  MockDataNode mock_dn;
  std::string msg =
      byte::StringPrint("Cannot find BPOfferService for bpid=%s", bp_id);
  EXPECT_CALL(mock_dn, GetDNRegistrationForBP(bp_id, ::testing::_))
      .WillRepeatedly(::testing::Return(
          exceptions::Exception(exceptions::E::kIOException, msg)));

  MockBlockRecoveryThread t(&mock_dn);
  auto e = t.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("All datanodes failed"), std::string::npos);
}

TEST_F(BlockRecoveryTests, RecoveryInProgress) {
  MockDataNode mock_dn;
  EXPECT_CALL(mock_dn, GetDNRegistrationForBP(bp_id, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(local_dn_reg),
                           ::testing::Return(exceptions::Exception())));

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(2)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(
          ::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                           ::testing::Return(exceptions::Exception(
                               exceptions::E::kRecoveryInProgressException))));

  mock_dn.SetStorage(mock_storage);

  MockBlockRecoveryThread t(&mock_dn);

  auto e = t.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kRecoveryInProgressException);
}

TEST_F(BlockRecoveryTests, InvalidRecovertyInfo) {
  ReplicaRecoveryInfo* rinfo_with_less_gs = new ReplicaRecoveryInfo(
      blk_id, blk_len, (uint64_t)0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo_with_zero_bytes =
      new ReplicaRecoveryInfo(blk_id, (uint64_t)0, blk_gs1, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(
          ::testing::DoAll(::testing::SetArgPointee<1>(rinfo_with_less_gs),
                           ::testing::Return(exceptions::Exception())))
      .WillOnce(
          ::testing::DoAll(::testing::SetArgPointee<1>(rinfo_with_zero_bytes),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, NoReplicaToBeRecovered) {
  ReplicaRecoveryInfo* rinfo1 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RUR);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RUR);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RUR);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, TruncateUnfinalizedReplica) {
  ReplicaRecoveryInfo* rinfo1 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 2, blk_gs0, ReplicaState::RBW);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 4, blk_gs0, ReplicaState::RWR);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // truncate rbw replica to min_length
  EXPECT_CALL(*mock_storage,
              UpdateReplicaUnderRecovery(::testing::_, blk_gs1, blk_len / 3,
                                         ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, RecoveryFinalizedReplica) {
  ReplicaRecoveryInfo* rinfo1 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len, blk_gs0, ReplicaState::RBW);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 2, blk_gs0, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // recovery the finalized replica and the rbw replica
  // which has equal length with the former
  EXPECT_CALL(*mock_storage, UpdateReplicaUnderRecovery(::testing::_, blk_gs1,
                                                        blk_len, ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, RunBlockRecoveryThread) {
  ReplicaRecoveryInfo* rinfo1 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len, blk_gs0, ReplicaState::RBW);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 2, blk_gs0, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // recovery the finalized replica and the rbw replica
  // which has equal length with the former
  EXPECT_CALL(*mock_storage, UpdateReplicaUnderRecovery(::testing::_, blk_gs1,
                                                        blk_len, ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  const char who[] = "mock namenode";
  auto rb = rb_.release();
  std::vector<RecoveringBlock*> rblks;
  rblks.push_back(rb);

  auto dn = new MockDataNode();

  EXPECT_CALL(*dn, GetDNRegistrationForBP(bp_id, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(local_dn_reg),
                           ::testing::Return(exceptions::Exception())));

  EXPECT_CALL(*dn, CommitBlockSynchronization(
                       bp_id, ::testing::_, ::testing::_, ::testing::_,
                       ::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));

  EXPECT_CALL(*dn, NotifyNamenodeReceivedBlock(::testing::_, "", storage_id))
      .WillRepeatedly(::testing::Return());

  dn->SetStorage(mock_storage);
  auto t = new MockBlockRecoveryThread(dn, who, rblks);
  t->Start();
  t->Join();
  delete t;
  delete dn;
}

TEST_F(BlockRecoveryTests, RecoveryBlockV2) {
  MockStore* mock_storage = new MockStore();
  std::shared_ptr<ReplicaBeingWritten> replica_info(
      new ReplicaBeingWritten("bpid", 1, 123, 234, "storage_uuid", false, true,
                              1, true, 1, bytestore::PLM_STORAGE_NVME_SSD));
  replica_info->SetBytesAcked(1024);
  replica_info->SetDiskDataLen(2048);
  EXPECT_CALL(*mock_storage, GetReplica(testing::_, testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(testing::Return(replica_info));

  EXPECT_CALL(*mock_storage,
              SealBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(testing::Invoke(
          [&](CsIoService* io_service,
              google::protobuf::RpcController* controller,
              const ::cloudfs::SealBlockRequestProto* request,
              ::cloudfs::SealBlockResponseProto* response, CsMemPool* mem_pool,
              google::protobuf::Closure* done) {
            response->mutable_header()->set_status(::cloudfs::SUCCESS);
            response->set_blocklength(1000);
            response->set_storageuuid("storageuuid");
            done->Run();
          }));
  MockDataNode mock_dn;
  bytestore::chunkserver::CSIOServiceOptions options;
  CsEnv env("./RecoveryBlockV2test");
  HdfsIOService hdfs_io_service(&mock_dn, &env, options, nullptr);
  EXPECT_CALL(mock_dn, GetStorage())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(mock_storage));
  EXPECT_CALL(mock_dn, GetHdfsIOService(testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(&hdfs_io_service));
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlockV2(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);

  mock_storage->StopMgr();
  delete mock_storage;
  mock_dn.SetStorage(nullptr);
}

}  // namespace bds::dancedn::cloudfs
