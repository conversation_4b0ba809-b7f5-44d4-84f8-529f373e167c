// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/storage_info.h"

#include "byte/string/format/print.h"
#include "cloudfs/proto/hdfs.pb.h"

namespace bds::dancedn::cloudfs {

StorageInfo::StorageInfo(NodeType type)
    : layout_version_(0),
      namespace_id_(0),
      ctime_(0),
      storage_type_(type),
      cluster_id_("") {}

StorageInfo::StorageInfo(int layout_version, uint64_t namespace_id,
                         const std::string& cluster_id, uint64_t ctime,
                         NodeType type)
    : layout_version_(layout_version),
      namespace_id_(namespace_id),
      ctime_(ctime),
      storage_type_(type),
      cluster_id_(cluster_id) {}

StorageInfo::StorageInfo(const StorageInfo& info) {
  layout_version_ = info.layout_version_;
  namespace_id_ = info.namespace_id_;
  ctime_ = info.ctime_;
  storage_type_ = info.storage_type_;
  cluster_id_ = info.cluster_id_;
}

StorageInfo* StorageInfo::Clone() const {
  return new StorageInfo(layout_version_, namespace_id_, cluster_id_, ctime_,
                         storage_type_);
}

std::string StorageInfo::GetRegistrationID() {
  return byte::StringPrint("NS-%d-%s-%llu", namespace_id_, cluster_id_, ctime_);
}

std::string StorageInfo::ToString() const {
  return byte::StringPrint("lv=%d;cid=%s;nsid=%d;c=%llu", layout_version_,
                           cluster_id_, namespace_id_, ctime_);
}

exceptions::Exception StorageInfo::ToProto(
    ::cloudfs::StorageInfoProto* proto) const {
  proto->set_layoutversion(layout_version_);
  proto->set_namespaceid(namespace_id_);
  proto->set_clusterid(cluster_id_);
  proto->set_ctime(ctime_);
  return exceptions::Exception();
}

StorageInfo* StorageInfo::ParseProto(const ::cloudfs::StorageInfoProto* proto) {
  auto res = new StorageInfo(NodeType::DATA_NODE);
  res->layout_version_ = proto->layoutversion();
  res->namespace_id_ = proto->namespaceid();
  res->ctime_ = proto->ctime();
  res->cluster_id_ = proto->clusterid();
  return res;
}

bool StorageInfo::Equals(const StorageInfo* rhs) const {
  if (layout_version_ != rhs->GetLayoutVersion()) return false;
  if (namespace_id_ != rhs->GetNamespaceID()) return false;
  if (ctime_ != rhs->GetCTime()) return false;
  if (cluster_id_ != rhs->GetClusterID()) return false;
  return true;
}

}  // namespace bds::dancedn::cloudfs
