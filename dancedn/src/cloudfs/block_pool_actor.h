// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <algorithm>
#include <atomic>
#include <cstdint>
#include <deque>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "byte/concurrent/cond.h"
#include "byte/concurrent/mutex.h"
#include "cloudfs/block_pool_report.h"
#include "cloudfs/block_report_options.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/ha_service_state.h"
#include "cloudfs/security/exported_block_keys.h"
#include "cloudfs/thread.h"
#include "cloudfs/util.h"
#include "container/lockfree_queue.h"

namespace bds::dancedn::cloudfs {

class TOSInfo;
class RemoteBlockInfo;

}  // namespace bds::dancedn::cloudfs

namespace bds::dancedn::cloudfs {

class NameSpaceInfo;
class DatanodeInfo;
class ExtendedBlock;
class DatanodeRegistration;
class DatanodeCommand;
class DataNodeConfig;
class BlockPoolService;
class NameSpaceInfo;
class NamenodeClient;
class DataNode;
class HeartbeatResponse;
class BPServiceActorAction;
class ReceivedDeletedBlockInfo;
class BlockReportOptions;
class StorageBlockReport;
class BlockReportContext;
class StorageReceivedDeletedBlocks;

struct DatanodeStorageHash {
  std::size_t operator()(const DatanodeStorage& rhs) const noexcept {
    return std::hash<std::string>()(rhs.GetStorageID());
  }
};

enum RunningState : uint8_t {
  CONNECTING,
  INIT_FAILED,
  RUNNING,
  EXITED,
  FAILED,
};

struct IBR;

class BlockPoolActor : public Thread {
 private:
  class FullBlockReporter;
  class PerStoragePendingIncrementalBR;

 public:
  BlockPoolActor();
  explicit BlockPoolActor(const std::string& ns_name,
                          const std::string& nn_name,
                          const std::string& nn_addr,
                          std::shared_ptr<BlockPoolService> bpos);
  virtual ~BlockPoolActor();

  void Run() override;

  bool IsActive() const {
    return is_active_;
  }
  std::string GetNameNodeAddr() const {
    return nn_addr_;
  }
  std::string GetNsName() const {
    return ns_name_;
  }
  std::string GetName() const {
    return name_;
  }
  const std::string& name() const {
    return name_;
  }
  HAServiceState GetState() const {
    return state_;
  }
  DataNode* GetDataNode() const {
    return dn_;
  }
  NamenodeClient* GetNamenodeClient() const {
    return namenode_client_;
  }
  // UnitTest OK
  bool IsAlive();
  void ScheduleBlockReport(uint64_t delay);
  // UnitTest OK
  void BpThreadEnqueue(BPServiceActorAction* action);
  // UnitTest OK
  void NotifyNamenodeBlock(std::shared_ptr<ReceivedDeletedBlockInfo> b_info,
                           const std::string& storage_uuid, bool now);
  void NotifyNamenodeMigratedBlock(
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info_deleted,
      const std::string& src_storage_uuid,
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info_received,
      const std::string& dst_storage_uuid, bool now);
  // UnitTest OK
  void NotifyNamenodeDeletedBlock(
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info,
      const std::string& storage_uuid);
  void NotifyNamenodeEvictBlock(
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info,
      const std::string& storage_uuid);

  void NotifyNamenodeDeletedTosBlock(
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info);

  void NotifyNamenodeMergedBlock(
      const std::shared_ptr<ReceivedDeletedBlockInfo>& b_info);

  exceptions::Exception ConnectToNNAndHandshake();

  // UnitTest OK
  exceptions::Exception Register();
  // UnitTest OK
  exceptions::Exception RedoRegister();
  // UnitTest OK
  exceptions::Exception RetrieveNamespaceInfo(NameSpaceInfo** info,
                                              RemoteBlockInfo** tos_info);
  // UnitTest OK
  exceptions::Exception SendHeartbeat(HeartbeatResponse** res);
  // UnitTest OK
  exceptions::Exception ReportReceiveAndDeletedBlocks();

  // report error will implement in ErrorReportAction
  // exceptions::Exception ReportError(const DatanodeRegistration* r,
  //         int error_code, const std::string& message);

  // UnitTest OK
  exceptions::Exception ReportRemoteBadBlock(DatanodeInfo* info,
                                             ExtendedBlock* b);

  // this method will implement in Datanode
  // exceptions::Exception CommitBlockSynchronizaton();  // ?

  // // UnitTest OK
  // exceptions::Exception BlockReport(std::vector<DatanodeCommand*>* cmds);
  // exceptions::Exception BlockReportV2(std::vector<DatanodeCommand*>* cmds);
  // not impletement
  exceptions::Exception CacheReport(DatanodeCommand** cmd);

  exceptions::Exception TriggerBlockReport(const BlockReportOptions& options);
  exceptions::Exception AbortBlockReport(int64_t id);

  exceptions::Exception GetBlockReportContext(
      std::vector<StorageBlockReport*>* storage_reports,
      std::shared_ptr<BlockReportContext>* block_report_context,
      const BlockReportInternalOptions& options);
  exceptions::Exception SplittedBlockReport(
      const std::vector<StorageBlockReport*>& report,
      std::shared_ptr<BlockReportContext> ctx, DatanodeCommand** cmd);

  std::string ToString() const;
  std::string LockedToString() const;
  void SetActive(bool is_active);

  bool GetSendImmediateIBR() {
    return *(static_cast<volatile bool*>(&send_immediate_ibr_));
  }

  void SetSendImmediateIBR(bool value) {
    *(static_cast<volatile bool*>(&send_immediate_ibr_)) = value;
  }

  int64_t GetLastBlockReport() {
    return *(static_cast<volatile int64_t*>(&last_block_report_));
  }

  void SetLastBlockReport(int64_t value) {
    *(static_cast<volatile int64_t*>(&last_block_report_)) = value;
  }

  int64_t GetLastIBR() {
    return *(static_cast<volatile int64_t*>(&last_incremental_block_report_));
  }

  void SetLastIBR(int64_t value) {
    *(static_cast<volatile int64_t*>(&last_incremental_block_report_)) = value;
  }

  int64_t GetLastHeartbeat() {
    return *(static_cast<volatile int64_t*>(&last_heart_beat_));
  }

  void SetLastHeartbeat(int64_t value) {
    *(static_cast<volatile int64_t*>(&last_heart_beat_)) = value;
  }

  void ProcessKeyUpdateCmd(const ExportedBlockKeys& exported_block_keys) const;
  virtual const std::string& GetId() const {
    return id_;
  }

  const std::shared_ptr<BlockPoolService>& GetBpos() const {
    return bpos_;
  }

  void TryToClearIBR(bool force);
  void CountPendingIBR();

  virtual void ScheduleHeartbeat();
  virtual void DoHeartbeat();

 public:
  // follow method only for unit test
  void SetNamenodeClient(NamenodeClient* namenode_client);
  std::deque<BPServiceActorAction*> GetThreadQueue();
  std::unordered_map<DatanodeStorage, PerStoragePendingIncrementalBR*,
                     DatanodeStorageHash>
  GetPendingIncreBrPerStorage();
  uint32_t GetPerStoragePendingIncrementBRSize(const DatanodeStorage& storage);
  PerStoragePendingIncrementalBR* NewPerStorageBR();
  void DeletePerStorageBR(PerStoragePendingIncrementalBR* per_br);
  BlockPoolActorReport GetBpActorReport();
  std::vector<std::shared_ptr<ReceivedDeletedBlockInfo>> TestDequeueIBR(
      const std::string& storage_uuid);

  void TestEnqueueIBR(
      const std::string& storage_uuid,
      const std::vector<std::shared_ptr<ReceivedDeletedBlockInfo>>& ibrs);
  void TestSetLastSuccessfullyHeartbeat(int64_t value) {
    last_successfully_heartbeat_ = value;
  }
  void TestSetIsAlive(bool alive) {
    is_alive_ = alive;
  }
  void TestTakeIBRFromQueue() {
    return TakeIBRFromQueue();
  }
  void TestDiscardCommand(const std::vector<DatanodeCommand*>& cmds) {
    return DiscardCommand(cmds);
  }

 private:
  void AppendNormalIBR(IBR* ibr);
  void AppendMigratedIBR(IBR* ibr);
  void TakeIBRFromQueue();
  bool ShouldRetryInit();
  void CleanUp();
  exceptions::Exception OfferService();
  bool ProcessCommand(const std::vector<DatanodeCommand*>& cmds);
  void DiscardCommand(const std::vector<DatanodeCommand*>& cmds);
  void ProcessQueueMessages();
  uint64_t GenerateUniqueBlockReportID();
  void AddPendingReplicationBlockInfo(
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info,
      const DatanodeStorage& storage);
  void AddPendingMigratedBlockInfo(
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info_deleted,
      const DatanodeStorage& src_storage,
      std::shared_ptr<ReceivedDeletedBlockInfo> b_info_received,
      const DatanodeStorage& dst_storage);

  PerStoragePendingIncrementalBR* GetIncrementalBRMapForStorage(
      const DatanodeStorage& storage);
  bool NeedClearIBR();

  void IBRTrace(const StorageReceivedDeletedBlocks* report, int64_t start_ms,
                int64_t cost_us);

 private:
  bool is_active_;
  HAServiceState state_;
  std::string nn_addr_;
  DataNode* dn_;
  std::shared_ptr<BlockPoolService> bpos_;
  NamenodeClient* namenode_client_;
  std::unique_ptr<DatanodeRegistration> r_;
  DataNodeConfig* dn_conf_;
  bool reset_block_report_time_;
  std::deque<BPServiceActorAction*> bp_thread_queue_;
  mutable byte::Mutex quemutex_;

  std::shared_ptr<FullBlockReporter> reporter_;
  std::unordered_map<DatanodeStorage, PerStoragePendingIncrementalBR*,
                     DatanodeStorageHash>
      pending_incremental_br_per_storage_;
  mutable byte::Mutex block_report_mutex_;

  RunningState running_state_;
  int64_t last_block_report_;
  int64_t last_incremental_block_report_;
  int64_t last_heart_beat_;
  std::atomic<bool> is_alive_;
  std::atomic<int64_t> last_successfully_heartbeat_;
  std::atomic<int64_t> last_update_pending_ibr_num_;
  bool send_immediate_ibr_;
  uint64_t pre_block_report_id_;
  //  namenode name this actor services to
  std::string name_;
  std::string ns_name_;
  std::string id_;  // uniq id for each block pool actor
  uint64_t opt_ip_version_;
  byte::LockFreeQueue<IBR*> ibr_queue_;
  std::atomic<int> heartbeat_ref_;
  byte::LockFreeQueue<std::pair<exceptions::Exception, HeartbeatResponse*>>
      heartbeat_responses_;
};

}  // namespace bds::dancedn::cloudfs
