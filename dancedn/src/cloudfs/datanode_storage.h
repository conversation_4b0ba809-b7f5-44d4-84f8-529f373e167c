// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "cloudfs/proto/hdfs.pb.h"
#include "cloudfs/storage_type.h"

namespace bds::dancedn::cloudfs {

class DatanodeStorage {
 public:
  enum State {
    NORMAL = 0,
    READ_ONLY_SHARED = 1,
    FAILED = 2,
  };

  static std::string StateToString(State state) {
    switch (state) {
      case State::NORMAL: return "NORMAL";
      case State::READ_ONLY_SHARED: return "READ_ONLY_SHARED";
      case State::FAILED: return "FAILED";
      default: return "";
    }
  }

 public:
  explicit DatanodeStorage(const std::string& storage_id);
  DatanodeStorage(const std::string& storage_id, State state, StorageType type);
  ~DatanodeStorage() {}

  DatanodeStorage* Clone() const;

  std::string GetStorageID() const {
    return storage_id_;
  }
  State GetState() const {
    return state_;
  }
  StorageType GetStorageType() const {
    return storage_type_;
  }

  void SetState(State state) {
    state_ = state;
  }
  std::string GetStateString() const {
    return StateToString(state_);
  }

  std::string ToString() const;
  // overwrite operator== in order to use hash_map
  bool operator==(const DatanodeStorage& rhs) const;

 public:
  static std::string GenerateUUID();

  exceptions::Exception ToProto(::cloudfs::DatanodeStorageProto* proto) const;

  ::cloudfs::DatanodeStorageProto_StorageState ConvertState() const;

 private:
  std::string storage_id_;
  State state_;
  StorageType storage_type_;
};

}  // namespace bds::dancedn::cloudfs
