// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/message/shutdown_datanode_message.h"

#include "cloudfs/io/io_buf.h"
#include "cloudfs/proto/ClientDatanodeProtocol.pb.h"

namespace bds::dancedn::cloudfs {
namespace message {

ShutdownDatanodeMessage::ShutdownDatanodeMessage() : r_(nullptr) {}

ShutdownDatanodeMessage::~ShutdownDatanodeMessage() {
  delete r_;
}

RpcRequestMessage* ShutdownDatanodeMessage::New() {
  return new ShutdownDatanodeMessage();
}

bool ShutdownDatanodeMessage::DecodeBody(cloudfs::io::IOChunk* chunk, int len) {
  // decode varint body length
  int body_len = 0;
  if (!chunk->ReadVarint32(reinterpret_cast<uint32_t*>(&body_len))) {
    return false;
  }
  len = len - cloudfs::io::IOChunk::CalcVarintSize(body_len) - body_len;
  if (len < 0) return false;

  // decode body
  auto proto = new ::cloudfs::ShutdownDatanodeRequestProto();
  if (!proto->ParseFromArray(chunk->ReadBytes(body_len), body_len)) {
    delete proto;
    return false;
  }
  r_ = proto;
  return true;
}

int ShutdownDatanodeMessage::Encode(cloudfs::io::IOChunk* chunk) {
  int used = RpcRequestMessage::Encode(chunk);
  std::string r = r_->SerializeAsString();
  int len = cloudfs::io::IOChunk::CalcVarintSize(r.size()) + r.size();
  chunk->EnsureHasUnused(len);
  chunk->WriteVarint(r.size());
  chunk->WriteBytes((const uint8_t*)r.c_str(), r.size());
  return used + len;
}

}  // namespace message
}  // namespace bds::dancedn::cloudfs
