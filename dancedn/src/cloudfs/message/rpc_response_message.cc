// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/message/rpc_response_message.h"

#include "byte/include/byte_log.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/io/message_request.h"
#include "cloudfs/proto/ClientDatanodeProtocol.pb.h"
#include "cloudfs/proto/DatanodeProtocol.pb.h"
#include "cloudfs/proto/InterDatanodeProtocol.pb.h"
#include "cloudfs/proto/NamenodeProtocol.pb.h"
#include "cloudfs/proto/RpcHeader.pb.h"

namespace bds::dancedn::cloudfs {
namespace message {

RpcResponseMessage::RpcResponseMessage() {
  header_ = nullptr;
  body_ = nullptr;
}

RpcResponseMessage::RpcResponseMessage(
    ::cloudfs::RpcResponseHeaderProto* header,
    google::protobuf::Message* body) {
  header_ = header;
  body_ = body;
}

RpcResponseMessage::~RpcResponseMessage() {
  delete header_;
  delete body_;
}

void RpcResponseMessage::SetHeaderAndBody(
    ::cloudfs::RpcResponseHeaderProto* header,
    google::protobuf::Message* body) {
  header_ = header;
  body_ = body;
}

::cloudfs::RpcResponseHeaderProto* RpcResponseMessage::ReleaseRpcHeader() {
  auto t = header_;
  header_ = nullptr;
  return t;
}

google::protobuf::Message* RpcResponseMessage::ReleaseBody() {
  auto t = body_;
  body_ = nullptr;
  return t;
}

const ::cloudfs::RpcResponseHeaderProto* RpcResponseMessage::RpcHeader() const {
  return header_;
}

const google::protobuf::Message* RpcResponseMessage::Body() const {
  return body_;
}

int RpcResponseMessage::DecodeResponseHeader(
    cloudfs::io::IOChunk* chunk, int len,
    ::cloudfs::RpcResponseHeaderProto** proto) {
  // decode response header length
  int header_len = 0;
  if (!chunk->ReadVarint32(reinterpret_cast<uint32_t*>(&header_len))) {
    LOG(ERROR) << "read varint32 failed";
    return -1;
  }

  len = len - cloudfs::io::IOChunk::CalcVarintSize(header_len) - header_len;
  if (len < 0) {
    LOG(ERROR) << "remain length < 0";
    return -1;
  }

  // decode response header
  auto header = new ::cloudfs::RpcResponseHeaderProto();
  if (!header->ParseFromArray(chunk->ReadBytes(header_len), header_len)) {
    LOG(ERROR) << "parse reponse header failed";
    return -1;
  }

  *proto = header;
  return len;
}

bool RpcResponseMessage::DecodeSync(cloudfs::io::Context* ctx,
                                    cloudfs::io::IOChunk* chunk, int len) {
  ::cloudfs::RpcResponseHeaderProto* header = nullptr;
  int remain_length =
      RpcResponseMessage::DecodeResponseHeader(chunk, len, &header);
  if (remain_length < 0) {
    LOG(ERROR) << "decode reponse header failed";
    return false;
  }

  if (header->status() !=
      ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS) {
    LOG(ERROR) << "header status not SUCCESS:" << header->status();
    if (remain_length != 0) {
      delete header;
      return false;
    }

    if (header->has_errormsg()) {
      LOG(ERROR) << "nn response error and errormsg is : "
                 << header->errormsg();
    }

    // header will process in decode function
    SetHeaderAndBody(header, nullptr);
    return true;
  }

  // decode body length
  int body_len = 0;
  if (!chunk->ReadVarint32(reinterpret_cast<uint32_t*>(&body_len))) {
    LOG(ERROR) << "read varint32 failed";
    delete header;
    return false;
  }
  remain_length =
      remain_length - cloudfs::io::IOChunk::CalcVarintSize(body_len) - body_len;
  if (remain_length != 0) {
    LOG(ERROR)
        << "RPC response length mismatch on rpc success decode user proto";
    delete header;
    return false;
  }

  const uint8_t* user_data = chunk->ReadBytes(body_len);
  auto body = DecodeProto(*reinterpret_cast<uint32_t*>(ctx->Data()), user_data,
                          body_len);
  if (body == nullptr) {
    delete header;
    return false;
  }
  SetHeaderAndBody(header, body);
  return true;
}

google::protobuf::Message* RpcResponseMessage::DecodeProto(uint32_t method,
                                                           const uint8_t* data,
                                                           int len) {
  google::protobuf::Message* message = nullptr;
  switch (method) {
    case cloudfs::io::Method::VersionRequest:
      message = new ::cloudfs::VersionResponseProto();
      break;
    case cloudfs::io::Method::ReportBadBlocks:
      message = new ::cloudfs::datanode::ReportBadBlocksResponseProto();
      break;
    case cloudfs::io::Method::ErrorReport:
      message = new ::cloudfs::datanode::ErrorReportResponseProto();
      break;
    case cloudfs::io::Method::CommitBlockSynchronization:
      message =
          new ::cloudfs::datanode::CommitBlockSynchronizationResponseProto();
      break;
    case cloudfs::io::Method::Register:
      message = new ::cloudfs::datanode::RegisterDatanodeResponseProto();
      break;
    case cloudfs::io::Method::SendHeartbeat:
      message = new ::cloudfs::datanode::HeartbeatResponseProto();
      break;
    case cloudfs::io::Method::BlockReport:
      message = new ::cloudfs::datanode::BlockReportResponseProto();
      break;
    case cloudfs::io::Method::BlockReceivedAndDeleted:
      message = new ::cloudfs::datanode::BlockReceivedAndDeletedResponseProto();
      break;
    case cloudfs::io::Method::InitReplicaRecovery:
      message = new ::cloudfs::InitReplicaRecoveryResponseProto();
      break;
    case cloudfs::io::Method::UpdateReplicaUnderRecovery:
      message = new ::cloudfs::UpdateReplicaUnderRecoveryResponseProto();
      break;
    case cloudfs::io::Method::GetBlocks:
      message = new ::cloudfs::namenode::GetBlocksResponseProto();
      break;
    case cloudfs::io::Method::GetReplicaVisibleLength:
      message = new ::cloudfs::GetReplicaVisibleLengthResponseProto();
      break;
    case cloudfs::io::Method::RefreshNamenodes:
      message = new ::cloudfs::RefreshNamenodesResponseProto();
      break;
    case cloudfs::io::Method::GetBlockLocalPathInfo:
      message = new ::cloudfs::GetBlockLocalPathInfoResponseProto();
      break;
    case cloudfs::io::Method::GetHdfsBlockLocations:
      message = new ::cloudfs::GetHdfsBlockLocationsResponseProto();
      break;
    case cloudfs::io::Method::ShutdownDatanode:
      message = new ::cloudfs::ShutdownDatanodeResponseProto();
      break;
    case cloudfs::io::Method::TriggerBlockReport:
      message = new ::cloudfs::TriggerBlockReportResponseProto();
      break;
    case cloudfs::io::Method::Acquire:
      message = new ::cloudfs::datanode::AcquireResponse();
      break;
    case cloudfs::io::Method::GetNamespaces:
      message = new ::cloudfs::datanode::GetNamespacesResponseProto();
      break;
    case cloudfs::io::Method::ReadBlock:
      message = new ::cloudfs::ReadBlockResponseProto();
      break;
    case cloudfs::io::Method::CalculateBlockCrc:
      message = new ::cloudfs::CalculateBlockCrcResponseProto();
      break;
    case cloudfs::io::Method::SealBlock:
      message = new ::cloudfs::SealBlockResponseProto();
      break;
    default:
      LOG(ERROR) << "can not recognize method : " << method;
      return nullptr;
  }

  if (!message->ParseFromArray(data, len)) {
    LOG(ERROR) << "parse response body failed, len:" << len;
    delete message;
    return nullptr;
  }

  return message;
}

int RpcResponseMessage::Encode(cloudfs::io::IOChunk* chunk) {
  // | total_len | header_len | header | body_len | body
  // |  4 bits   |  varint32  |        | varint32 |
  std::string hstr = header_->SerializeAsString();
  std::string bstr = "";
  if (body_ != nullptr) {
    bstr = body_->SerializeAsString();
  }
  int fullen = cloudfs::io::IOChunk::CalcVarintSize(hstr.size()) + hstr.size();
  if (bstr.size() > 0) {
    fullen += cloudfs::io::IOChunk::CalcVarintSize(bstr.size()) + bstr.size();
  }

  chunk->EnsureHasUnused(fullen + 4);
  chunk->WriteFixed32BE(fullen);
  chunk->WriteVarint(hstr.size());
  chunk->WriteBytes((const uint8_t*)hstr.data(), hstr.size());
  if (bstr.size() > 0) {
    chunk->WriteVarint(bstr.size());
    chunk->WriteBytes((const uint8_t*)bstr.data(), bstr.size());
  }
  return fullen;
}

}  // namespace message
}  // namespace bds::dancedn::cloudfs
