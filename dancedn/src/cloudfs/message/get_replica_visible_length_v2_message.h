// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "cloudfs/message/rpc_request_message.h"
#include "cloudfs/proto/ClientDatanodeProtocol.pb.h"

namespace bds::dancedn::cloudfs {
namespace message {

class GetReplicaVisibleLengthV2Message : public RpcRequestMessage {
 public:
  ~GetReplicaVisibleLengthV2Message();

  bool DecodeBody(cloudfs::io::IOChunk* chunk, int len) override;
  int Encode(cloudfs::io::IOChunk* chunk) override;

  ::cloudfs::GetReplicaVisibleLengthV2RequestProto* Body() const {
    return r_;
  }

  google::protobuf::Message* Message() const override {
    return r_;
  }

 public:
  static RpcRequestMessage* New();

 public:
  // following method only for test
  void SetRequestBody(::cloudfs::GetReplicaVisibleLengthV2RequestProto* r) {
    r_ = r;
  }

 private:
  GetReplicaVisibleLengthV2Message();

 private:
  ::cloudfs::GetReplicaVisibleLengthV2RequestProto* r_;
};

}  // namespace message
}  // namespace bds::dancedn::cloudfs
