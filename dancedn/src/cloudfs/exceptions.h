// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include "cloudfs/proto/hdfs.pb.h"
#include "common/utils/exceptions.h"

#define RETURN_NO_EXCEPTION() \
  return bds::dancedn::cloudfs::exceptions::Exception()
#define RETURN_IF_ERROR(e) \
  {                        \
    if (!e.OK()) {         \
      return e;            \
    }                      \
  }

namespace bds::dancedn::exceptions {

inline ::cloudfs::Status ExceptionToStatus(
    bds::dancedn::exceptions::Exception e) {
  switch (e.GetE()) {
    case kNoException: return ::cloudfs::SUCCESS;
    case kInvalidPort: return ::cloudfs::ERROR_DN_INVALID_PORT;
    case kNoVisibleData: return ::cloudfs::ERROR_DN_NO_VISIBLE_DATA;
    case kBlockNotFound:
    case kReplicaNotFoundException: return ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST;
    case kUnexpectedReplicaStateException:
      return ::cloudfs::ERROR_DN_BLOCK_INVALID_STATE;
    case kTooManyConnections: return ::cloudfs::ERROR_BUSY;
    case kRemoteReplicaNotFound:
      return ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST_REMOTE;
    case kDiskOutOfSpaceException: return ::cloudfs::ERROR_NOT_ENOUGH_SPACE;
    case kReplicaFrozenException: return ::cloudfs::FREEZE;
    case kPreventWrite: return ::cloudfs::ERROR_DN_PREVENT_WRITE;
    default: return ::cloudfs::ERROR_DN_INTERNAL_ERROR;
  }
}

inline exceptions::E ExceptionFromStatus(::cloudfs::Status status) {
  switch (status) {
    case ::cloudfs::SUCCESS: return exceptions::E::kNoException;
    case ::cloudfs::ERROR_DN_INVALID_PORT: return exceptions::E::kInvalidPort;
    case ::cloudfs::ERROR_DN_NO_VISIBLE_DATA:
      return exceptions::E::kNoVisibleData;
    case ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST:
      return exceptions::E::kBlockNotFound;
    case ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST_REMOTE:
      return exceptions::E::kRemoteReplicaNotFound;
    default: return exceptions::E::kUndefinedException;
  }
}

}  // namespace bds::dancedn::exceptions

namespace bds::dancedn::cloudfs {
namespace exceptions = bds::dancedn::exceptions;
}  // namespace bds::dancedn::cloudfs
