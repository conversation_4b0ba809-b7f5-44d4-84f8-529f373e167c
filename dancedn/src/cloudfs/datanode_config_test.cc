// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/datanode_config.h"

#include <fstream>
#include <iostream>

#include "byte/byte_log/byte_log_impl.h"
#include "byte/io/local_filesystem.h"
#include "byte_log/byte_log_impl.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"

namespace {
// Prepare configuration contents; should be saved as an individual file in
// future.
static const std::string& k_test_content = R"(
{
    "BlockReportInterval": 60,
    "InitialBlockReportDelay": 300,
    "MinimunNameNodeVersion": "2.6.0",
    "DeleteReportInterval": 50,
    "HeartBeatInterval": 70,
    "FailoverBlockReportDelay": 80,
    "BlockReportSplitThreshold": 99,
    "SocketKeepaliveTimeout": 120,
    "SocketTimeout": 120,
    "IOFileBufferSize": 4096,
    "EstimateBlockSize": 51200,
    "RestartReplicaExpiry": 333,
    "DatanodeSlowIoWarningThresholdMs": 334,
    "SyncBehindWrites": false,
    "GetSyncBehindWriteInBackground": true
})";

static const std::string& k_test_invalid_content = R"(
{
    "BlockReportInterval": 60,
    "MinimunNameNodeVersion": "2.6.0",
    "DeleteReportInterval": 50,
    "HeartBeatInterval": 70,
    "FailoverBlockReportDelay": 80
})";

static const std::string& k_test_nn_content = R"(
{
    "Namenodes": {
         "ns0": ["nn0", "nn1"],
         "ns1": ["nn3"]
    },
    "DNProxy": "dnproxy:5082",
    "Version": "*******"
})";

static const std::string& k_test_nic_content = R"(
{
    "NicConfig": [
        {
            "IP": "********",
            "ClusterId": "vpc-default"
        },
        {
            "IP": "********",
            "ClusterId": "rdma-abcd-123-xyz"
        }
    ]
})";

}  // namespace

namespace bds::dancedn::cloudfs {

class DataNodeConfigTests : public ::testing::Test {
 public:
  void SetUp() {}
  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(DataNodeConfigTests, ParseConfFile) {
  gflags::FlagSaver saver;
  const std::string work_dir = "./DatanodeConfigTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  const std::string test_nic_conf_file = work_dir + "/nicconfig.json";
  const std::string test_empty_conf_file = work_dir + "/emptyconfig.json";
  const std::string test_invaild_conf_file = work_dir + "/invaildconfig.json";

  auto DumpJsonFile = [](const std::string& file_name,
                         const std::string& content) {
    std::ofstream json_file(file_name);  // close in dtor
    json_file << content << std::endl;
  };
  DumpJsonFile(test_conf_file, k_test_content);
  DumpJsonFile(test_invaild_conf_file, k_test_invalid_content);
  DumpJsonFile(test_nn_conf_file, k_test_nn_content);
  DumpJsonFile(test_nic_conf_file, k_test_nic_content);

  DataNodeConfig dn_conf;

  EXPECT_FALSE(dn_conf.Parse(test_empty_conf_file, test_nn_conf_file,
                             test_empty_conf_file));
  EXPECT_FALSE(dn_conf.Parse(test_invaild_conf_file, test_nn_conf_file,
                             test_empty_conf_file));
  EXPECT_TRUE(dn_conf.Parse(test_conf_file, test_nn_conf_file, ""));
  EXPECT_TRUE(
      dn_conf.Parse(test_conf_file, test_nn_conf_file, test_empty_conf_file));

  EXPECT_EQ(60, dn_conf.GetBlockReportInterval());
  EXPECT_EQ(300, dn_conf.GetInitialBlockReportDelay());
  EXPECT_EQ(std::string("2.6.0"), dn_conf.GetMinimumNameNodeVersion());
  EXPECT_EQ(70, dn_conf.GetHeartBeatInterval());
  EXPECT_EQ(80, dn_conf.GetFailoverBlockReportDelay());
  EXPECT_EQ(80, dn_conf.GetFailoverBlockReportDelay());
  DataNodeConfig::NamespaceMap nss =
      dn_conf.GetNNServiceRpcAddressesForCluster();
  EXPECT_EQ(2, nss.size());
  EXPECT_EQ(2, nss.at("ns0").size());
  EXPECT_EQ(1, nss.at("ns1").size());
  EXPECT_EQ(99, dn_conf.GetBlockReportSplitThreshold());
  EXPECT_EQ(120, dn_conf.GetSocketKeepaliveTimeout());
  EXPECT_EQ(120, dn_conf.GetSocketTimeout());
  EXPECT_EQ(4096, dn_conf.GetIOFileBufferSize());
  EXPECT_EQ(51200, dn_conf.GetEstimateBlockSize());
  EXPECT_EQ(333, dn_conf.GetRestartReplicaExpiry());
  EXPECT_EQ(334, dn_conf.GetDatanodeSlowIoWarningThresholdMs());
  EXPECT_FALSE(dn_conf.GetSyncBehindWrites());
  EXPECT_TRUE(dn_conf.GetSyncBehindWriteInBackground());

  EXPECT_FALSE(dn_conf.Parse(test_conf_file, test_nn_conf_file,
                             test_conf_file));  // invalid nic
  EXPECT_TRUE(
      dn_conf.Parse(test_conf_file, test_nn_conf_file, test_nic_conf_file));
  auto ip2tag = dn_conf.GetIP2TagMap();
  EXPECT_EQ(2, ip2tag.size());
  EXPECT_EQ("vpc-default", ip2tag.at("********"));
  EXPECT_EQ("rdma-abcd-123-xyz", ip2tag.at("********"));

  local_fs.DeleteDir(work_dir, delete_options);
}

}  // namespace bds::dancedn::cloudfs
