// copyright (c) 2019-present, bytedance inc. all rights reserved.

#pragma once

#include <stddef.h>

#include <cstdint>
#include <memory>
#include <string>

#include "chunkserver/services/rpc_util.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/proto/ClientDatanodeV2Protocol.pb.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/storage_type.h"
#include "common/memory_pool_repo.h"
#include "proto/chunkserver.pb.h"

namespace bds::dancedn::cloudfs {
namespace io {
class Chunk;
}
class UnifiedBlockReader;
class ChunkServerStore;

enum class BlockOPType {
  QUERY,
  CREATE,
  WRITE,
  READ_DISK,
  READ_REMOTE,
  SYNC,
  SEAL,
  PING,
  FINALIZE,
  CHECKSUM,
  MAX,
};
const char* BlockOPToString(BlockOPType type);

struct BlockAsyncCbContext {
  BlockAsyncCbContext(bytestore::MemoryPool* mem_pool, ExtendedBlock* blk,
                      BlockOPType type, int64_t req_t_us);
  virtual ~BlockAsyncCbContext();
  virtual void Complete(::cloudfs::Status status, const std::string& err_msg);
  bytestore::MemoryPool* mem_pool_;
  int64_t req_start_time_us_ = 0;
  int64_t init_time_us_ = 0;
  int64_t io_finish_time_us_ = 0;
  ExtendedBlock* block_;
  BlockOPType op_type_;
  ::cloudfs::Status status_;
};

struct CreateBlockCbContext : public BlockAsyncCbContext {
  google::protobuf::RpcController* controller_;
  bytestore::CSIOService* io_service_;
  ChunkServerStore* cs_store_;
  bytestore::IOPriorityProto priority_;
  bytestore::PlacementStorageType placement_type_;
  NamespaceType ns_type_;
  bytestore::DiskId disk_id_;
  bytestore::CreateChunkResponse cs_response_;
  ::cloudfs::CreateBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit CreateBlockCbContext(bytestore::MemoryPool* pool, ExtendedBlock* blk,
                                int64_t req_t_us);
  virtual ~CreateBlockCbContext();
  void SendResponse(::cloudfs::Status status, const std::string& err_msg);
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct WriteBlockCbContext : public BlockAsyncCbContext {
  uint64_t offset_;
  uint64_t length_;
  int64_t visible_length_;  // update visible_length if > 0;
  std::shared_ptr<ReplicaInfo> replica_;
  bytestore::WriteChunkResponse cs_response_;
  ::cloudfs::WriteBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit WriteBlockCbContext(bytestore::MemoryPool* pool, ExtendedBlock* blk,
                               int64_t req_t_us);
  virtual ~WriteBlockCbContext();
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct ReadBlockCbContext : public BlockAsyncCbContext {
  int64_t read_bytes_;
  std::shared_ptr<ReplicaInfo> replica_;
  bytestore::ReadChunkResponse cs_response_;
  ::cloudfs::ClientReadBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit ReadBlockCbContext(bytestore::MemoryPool* pool, ExtendedBlock* blk,
                              int64_t req_t_us);
  virtual ~ReadBlockCbContext();
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct SyncBlockCbContext : public BlockAsyncCbContext {
  bytestore::SyncChunkResponse cs_response_;
  ::cloudfs::SyncBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit SyncBlockCbContext(bytestore::MemoryPool* pool, ExtendedBlock* blk,
                              int64_t req_t_us);
  virtual ~SyncBlockCbContext();
  void SendResponse(::cloudfs::Status status, const std::string& err_msg);
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct SealBlockCbContext : public BlockAsyncCbContext {
  google::protobuf::RpcController* controller_;
  bytestore::CSIOService* io_service_;
  ChunkServerStore* cs_store_;
  int64_t length_;  // Truncate block if length >=0, do nothing if -1
  bytestore::IOPriorityProto priority_;
  std::shared_ptr<ReplicaInfo> replica_;
  bytestore::TruncateChunkResponse cs_response_;
  ::cloudfs::SealBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit SealBlockCbContext(bytestore::MemoryPool* pool, ExtendedBlock* blk,
                              int64_t req_t_us);
  virtual ~SealBlockCbContext();
  void SendResponse(::cloudfs::Status status, const std::string& err_msg);
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct PingBlockCbContext : public BlockAsyncCbContext {
  ::cloudfs::PingBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit PingBlockCbContext(bytestore::MemoryPool* pool, ExtendedBlock* blk,
                              int64_t req_t_us);
  virtual ~PingBlockCbContext();
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct FinalizeBlockCbContext : public BlockAsyncCbContext {
  google::protobuf::RpcController* controller_;
  bytestore::CSIOService* io_service_;
  ChunkServerStore* cs_store_;
  int64_t length_;  // Truncate block if length >=0, do nothing if -1
  bytestore::IOPriorityProto priority_;
  std::shared_ptr<ReplicaInfo> replica_;
  bytestore::TruncateChunkResponse cs_response_;
  ::cloudfs::FinalizeBlockResponseProto* hdfs_response_;
  google::protobuf::Closure* hdfs_done_;
  explicit FinalizeBlockCbContext(bytestore::MemoryPool* pool,
                                  ExtendedBlock* blk, int64_t req_t_us);
  virtual ~FinalizeBlockCbContext();
  void SendResponse(::cloudfs::Status status, const std::string& err_msg);
  void Complete(::cloudfs::Status status, const std::string& err_msg) override;
};

struct SetXAttrCbContext {
  // SetXAttr is the operation that executed after another, like
  // Create/Seal/Finalize
  BlockAsyncCbContext* parent_ctx_;
  bytestore::CSSetXattrResponse cs_response_;
  explicit SetXAttrCbContext(BlockAsyncCbContext* parent);
  virtual ~SetXAttrCbContext();
};

struct UnifiedReadBlockContext {
  bytestore::MemoryPool* mem_pool_;
  google::protobuf::RpcController* controller_;
  const ::cloudfs::ClientReadBlockRequestProto* request_;
  ::cloudfs::ClientReadBlockResponseProto* response_;
  google::protobuf::Closure* hdfs_done_;
  UnifiedBlockReader* reader_;
};

struct RemoteReadBlockContext : public BlockAsyncCbContext {
  uint64_t offset_;
  uint32_t length_;
  ::cloudfs::ClientReadBlockRequestProto_ReadType read_type_;
  NamespaceType ns_type_;
  std::unique_ptr<CachingStrategy> strategy_;
  google::protobuf::RpcController* controller_;
  ::cloudfs::ClientReadBlockResponseProto* response_;
  google::protobuf::Closure* hdfs_done_;
  io::IOChunk* chunk_;
  exceptions::Exception read_status_;

  explicit RemoteReadBlockContext(bytestore::MemoryPool* pool,
                                  ExtendedBlock* blk, int64_t req_t_us);
  virtual ~RemoteReadBlockContext();
  virtual void Complete(::cloudfs::Status status, const std::string& err_msg);
};

class ConcurrencyControl {
 public:
  static ConcurrencyControl& Instance();
  bool Acquire(BlockOPType type);
  void Release(BlockOPType type);
  int32_t GetCurConcurrency(BlockOPType type);

 private:
  ConcurrencyControl();
  ConcurrencyControl(const ConcurrencyControl&) = delete;
  ConcurrencyControl& operator=(const ConcurrencyControl&) = delete;

  int32_t GetMaxConcurrency(BlockOPType type);

 private:
  std::atomic<int32_t> cur_concurrency_[static_cast<int32_t>(BlockOPType::MAX)];
};

}  // namespace bds::dancedn::cloudfs
