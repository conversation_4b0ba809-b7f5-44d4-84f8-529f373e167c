// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

// do not delete these two lines for compatibility
#cmakedefine DANCEDN_GIT_HASH "@GIT_HASH@"
#cmakedefine DANCEDN_GIT_BRANCH "@<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@"
#cmakedefine DANCEDN_SCM_VERSION "@SCM_VERSION@"
#cmakedefine DANCEDN_MAJOR_VERSION @DANCEDN_MAJOR_VERSION @
#cmakedefine DANCEDN_MINOR_VERSION @DANCEDN_MINOR_VERSION @
#cmakedefine DANCEDN_PATCH_VERSION @DANCEDN_PATCH_VERSION @
#ifndef DANCEDN_MAJOR_VERSION
#define DANCEDN_MAJOR_VERSION 0
#endif
#ifndef DANCEDN_MINOR_VERSION
#define DANCEDN_MINOR_VERSION 0
#endif
#ifndef DANCEDN_PATCH_VERSION
#define DANCEDN_PATCH_VERSION 0
#endif
