// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/block_sender.h"

#include <cstdint>
#include <memory>
#include <set>
#include <vector>

#include "byte/io/local_filesystem.h"
#include "cloudfs/block.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/constants.h"
#include "cloudfs/data_checksum.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_registration.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/finalized_replica.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/lifecycle/block_entry_mgr.h"
#include "cloudfs/mocks/mock_connection.h"
#include "cloudfs/mocks/mock_data_transfer.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_rbw.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/opstats/trace_baggage.h"
#include "cloudfs/replica.h"
#include "cloudfs/replica_being_written.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/replica_under_recovery.h"
#include "cloudfs/replica_waiting_to_be_recovered.h"
#include "cloudfs/storage_report.h"
#include "cloudfs/store.h"
#include "cloudfs/store/chunkserver/chunkserver_runtime.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/chunkserver/disk_usage.h"
#include "cloudfs/store/chunkserver/partial_block_store.h"
#include "cloudfs/store/upload_tos_mgr.h"
#include "common/media_flags.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "gmock/gmock-cardinalities.h"
#include "gmock/gmock-spec-builders.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "services/csioservice_impl.h"

#define SYNC_HANDLE_OPER(FUNC)                                               \
  {                                                                          \
    byte::CountDownLatch latch(1);                                           \
    google::protobuf::Closure* done = google::protobuf::NewCallback(         \
        this, &BlockSenderTests::IOOperDone, &latch);                        \
    oper->SetCallback(done);                                                 \
    oper->request_.set_disk_id(disk_id);                                     \
    chunkserver_runtime_->GetDnInterface()->FUNC(                            \
        &oper->controller_, &oper->request_, &oper->response_, oper->done_); \
    latch.Wait();                                                            \
    EXPECT_EQ(bytestore::BYTESTORE_OK, oper->response_.error_code());        \
  }

DECLARE_string(bytestore_chunkserver_work_dir);
DECLARE_uint32(bytestore_chunkserver_max_num_discs);
DECLARE_bool(bytestore_chunkserver_truncate_chunk_after_freeze);
DECLARE_MEDIA_FLAG_int64(bytestore_chunkserver_reserved_disk_size);
DECLARE_int32(bytestore_chunkserver_log_level);
DECLARE_bool(bytestore_chunkserver_admit_duplicate_uuid_disk);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_string(bytestore_hdfs_default_user);
DECLARE_bool(bytestore_cfs_enable_partial_block_read);

// Test Data
namespace bds::dancedn::cloudfs {

static const char test_bp_id[] = "tbpi";
static const char test_acc_bp_id[] = "acc";
static const uint64_t test_blk_gs0 = 1;
static const uint64_t test_blk_gs1 = 2;
static const uint64_t test_blk_id = 20190828;
static const uint64_t test_blk_len = 4096;
static bytestore::chunkserver::DiskOptions diskOptions;
std::shared_ptr<ExtendedBlock> test_block(new ExtendedBlock(
    test_bp_id, test_blk_id, test_blk_len, test_blk_gs0, false));

std::shared_ptr<ExtendedBlock> test_acc_block(new ExtendedBlock(
    test_acc_bp_id, test_blk_id, test_blk_len, test_blk_gs0, false, "", 1024));

std::shared_ptr<ExtendedBlock> test_later_block(new ExtendedBlock(
    test_bp_id, test_blk_id, test_blk_len, test_blk_gs1, false));

static const char storage_uuid[] = "suuid";
std::shared_ptr<FinalizedReplica> test_finalized_replica(new FinalizedReplica(
    test_block.get(), storage_uuid, true, false, 0, false));

std::shared_ptr<FinalizedReplica> test_acc_finalized_replica(
    new FinalizedReplica(test_acc_block.get(), storage_uuid, true, false, 0,
                         false));

std::shared_ptr<FinalizedReplica> test_later_finalized_replica(
    new FinalizedReplica(test_later_block.get(), storage_uuid, true, false, 0,
                         false));

std::shared_ptr<ReplicaWaitingToBeRecovered> test_rwr_replica(
    new ReplicaWaitingToBeRecovered(test_block.get(), storage_uuid, false,
                                    false, 0, false));

static const uint64_t recovery_id = 20180902;

static const char ip_addr[] = "127.0.0.1";
static const char hostname[] = "localhost";
static const char uuid[] = "duuid";
static const int xfer_port = 5060;
static const int info_port = 5061;
static const int ipc_port = 5062;
static const char node_name[] = "";
}  // namespace bds::dancedn::cloudfs

namespace bds::dancedn::cloudfs {

class DataNode;
class MockStore;
class BlockSender;
class ExtendedBlock;

class BlockSenderTests : public ::testing::Test {
 public:
  BlockSenderTests() {}

  ~BlockSenderTests() {}

  void SetUp() {
    byterpc::ExecCtx::Init(byterpc::InitOptions());
    bytestore::metrics_internal::InitFastMetrics();
    dn_ = new MockDataNode();
    tos_store_ = new MockTosStore();
    MockDataTransferManager* dtm = new MockDataTransferManager();
    dn_->SetDataTransferManager(dtm);
    mock_storage_ = new MockStore();
    EXPECT_CALL(*reinterpret_cast<MockStore*>(mock_storage_),
                GetRemoteStore(::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(tos_store_));
    EXPECT_CALL(*reinterpret_cast<MockStore*>(mock_storage_),
                ReadedBlock(testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return());

    dn_->SetStorage(mock_storage_);
    MFLAGS_set(bytestore_chunkserver_reserved_disk_size,
               bytestore::TYPE_SATA_HDD, 3ULL * 1000 * 1000 * 1000);

    ns_info_ =
        new NameSpaceInfo(1, "", "", 2, "", "", 3, 4, NamespaceType::TOS_LOCAL);
    acc_ns_info_ =
        new NameSpaceInfo(1, "", "", 2, "", "", 3, 4, NamespaceType::ACC_TOS);
    EXPECT_CALL(*dn_, GetNamespaceInfo(test_bp_id))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(ns_info_));
    EXPECT_CALL(*dn_, GetNamespaceInfo(test_acc_bp_id))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(acc_ns_info_));
    EXPECT_CALL(*dn_, GetStorage())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(mock_storage_));
    EXPECT_CALL(*dn_, NotifyNamenodeReceivedBlockV2(testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return());
    EXPECT_CALL(*reinterpret_cast<MockStore*>(mock_storage_),
                GetRemoteStore(testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(tos_store_));

    bytestore::metrics_internal::InitFastMetrics();
    std::string work_dir = "./BlockSenderTestDir/";
    namespace_id_ = 1004580134;
    cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
    create_time_ = 1546064706393;
    build_version_ = "2.6.0";
    software_version_ = "2.6.3";
    bpid_ = "BP-13882352884257380248-1546064706393";
    fs_id_ = 4013797767642215140;
    ns_id_ = 13882352884257380248ULL;
    ns_type_ = NamespaceType::TOS_LOCAL;

    EXPECT_CALL(*dn_, GetNamespaceInfo(bpid_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(ns_info_));

    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    local_fs.DeleteDir(std::string(work_dir), delete_options);
    EXPECT_TRUE(
        local_fs.CreateDir(std::string(work_dir), byte::CreateOptions()).ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk1", byte::CreateOptions())
            .ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk2", byte::CreateOptions())
            .ok());
    disk_id_conf_map_[1] = bytestore::chunkserver::DiskConfig(
        1, std::string(work_dir) + "/disk1",
        bytestore::chunkserver::CDT_SATA_HDD);
    disk_id_conf_map_[2] = bytestore::chunkserver::DiskConfig(
        2, std::string(work_dir) + "/disk2",
        bytestore::chunkserver::CDT_SATA_HDD);
    gflags::FlagSaver saver;
    FLAGS_bytestore_chunkserver_work_dir = std::string(work_dir);
    FLAGS_bytestore_chunkserver_truncate_chunk_after_freeze = true;
    FLAGS_bytestore_chunkserver_admit_duplicate_uuid_disk = true;

    chunkserver_runtime_.reset(new ChunkServerRuntime(work_dir));
    cs_config_.DEBUG_ImportConfig(disk_id_conf_map_);
    chunkserver_runtime_->DEBUG_ImportCsConfig(cs_config_);
    ASSERT_EQ(chunkserver_runtime_->Init().GetE(), exceptions::kNoException);
    ASSERT_EQ(chunkserver_runtime_->Start().GetE(), exceptions::kNoException);
    env_ = chunkserver_runtime_->GetEnv();

    EXPECT_CALL(*dn_, GetChunkServerRuntime())
        .WillRepeatedly(::testing::Invoke(
            [this]() -> const std::unique_ptr<ChunkServerRuntime>& {
              return chunkserver_runtime_;
            }));

    local_store_ = new ChunkServerStore(env_);
    std::set<uint32_t> disks;
    env_->GetDiskSet(&disks);
    std::shared_ptr<DiskUsage> disk_usage(new DiskUsage(disks));
    local_store_->SetDiskUsage(disk_usage);
    std::shared_ptr<BlockEntryMgr> block_entry_mgr(
        new BlockEntryMgr(dn_, local_store_, disks, disk_usage));
    local_store_->SetBlockEntryMgr(block_entry_mgr);
    EXPECT_CALL(*reinterpret_cast<MockStore*>(mock_storage_), GetLocalStore())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(local_store_));
    partial_store_ = new PartialBlockStore(dn_);
    partial_store_->SetDiskUsage(disk_usage);
    EXPECT_CALL(*mock_storage_, GetPartialBlockStore())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(partial_store_));
    // block id = 1, bpid = BP-13882352884257380248-1546064706393
    ChunkIdMeta chunk_id1(1, 13882352884257380248ULL, 1546064706393);
    std::unique_ptr<ExtendedBlock> block(
        new ExtendedBlock(bpid_, chunk_id1.block_id_, false));
    CreateChunk(1, block.get());

    static const uint32_t k_buf_size =
        4096;  // Must be equals to multiple of 32
    char* data = new char[k_buf_size];
    std::unique_ptr<char[]> scoped_data(data);
    for (uint32_t i = 0; i < k_buf_size; ++i) {
      data[i] = i % 32 + 'A';
    }

    WriteChunk(1, block.get(), data, k_buf_size, 0);
    char* write_xattr = new char[XATTR_BYTES];
    memset(write_xattr, 0, 32);
    std::unique_ptr<char[]> write_xattr_deleter(write_xattr);
    BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
    // gs_ = 1546064706393, finalized
    meta->gs_ = 1546064706393;
    meta->fin_ = 1;
    meta->checksum_enabled_ = 1;
    SetChunkAttr(1, block.get(), write_xattr);
    FreezeChunk(1, block.get(), k_buf_size);
    char* result = new char[XATTR_BYTES];
    GetChunkAttr(1, block.get(), result);
    EXPECT_EQ(std::string(write_xattr, XATTR_BYTES),
              std::string(result, XATTR_BYTES));
    delete[] result;

    conn_ = new MockConnection();
    conn_->SetIsClosed(false);
    EXPECT_CALL(*conn_, WriteBuf())
        .WillRepeatedly(::testing::Return(&write_buf_));
    EXPECT_CALL(*conn_, Write(::testing::_))
        .WillRepeatedly([this](bool is_sync) {
          io::IOChunk* chunk = conn_->WriteBuf()->Front();
          sended_.append(reinterpret_cast<char*>(chunk->UsedData()),
                         chunk->UsedLength());
          io::IOChunk::Destroy(conn_->WriteBuf()->Pop());
          return IO_OK;
        });
  }

  void TearDown() {
    delete conn_;
    chunkserver_runtime_->Stop();
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    byte::LocalFileSystem local_fs;
    std::string work_dir = "./BlockSenderTestDir/";
    local_fs.DeleteDir(std::string(work_dir), delete_options);
    delete dn_;
    // mock_storage_ is deleted inside of DataNode;
    mock_storage_ = nullptr;
    delete tos_store_;
    delete local_store_;
    delete partial_store_;
    delete ns_info_;
    delete acc_ns_info_;
  }

  static void SetUpTestCase() {}

  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  void PrepareForSendBlockTest(DataNode* dn) {
    EXPECT_CALL(*mock_storage_,
                CreateBlockStream(::testing::_, ::testing::_, ::testing::_,
                                  ::testing::_, ::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(exceptions::Exception()));
    EXPECT_CALL(*mock_storage_, GetReplica(::testing::_, ::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(
            ::testing::Invoke(local_store_, &ChunkServerStore::GetReplica));
    EXPECT_CALL(*mock_storage_, GetDisk(::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(
            ::testing::Invoke(local_store_, &ChunkServerStore::GetDisk));
    EXPECT_CALL(*mock_storage_,
                ReadChecksum(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(
            ::testing::Invoke(local_store_, &ChunkServerStore::ReadChecksum));
    EXPECT_CALL(*mock_storage_, ReadDiskChecksum(::testing::_, ::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Invoke(local_store_,
                                          &ChunkServerStore::ReadDiskChecksum));
    EXPECT_CALL(*mock_storage_, GetBlockEntryMgr())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(nullptr));

    auto e = local_store_->InitStorage(dn_, &disk_id_conf_map_);
    EXPECT_TRUE(e.OK());
    e = local_store_->AddDirAndBlock(ns_info_);
    EXPECT_TRUE(e.OK());
  }

  void CreateChunk(uint32_t disk_id, ExtendedBlock* block) {
    std::unique_ptr<CreateOper> oper(
        new CreateOper(bytestore::PRIORITY_REAL_TIME, block, 5000000,
                       bytestore::ChunkType::TYPE_REPLICATED_CHUNK, 0, false,
                       bytestore::PLM_STORAGE_ANY, 0));
    SYNC_HANDLE_OPER(CreateChunk)
  }

  void WriteChunk(uint32_t disk_id, ExtendedBlock* block, char* data,
                  uint32_t length, uint32_t offset) {
    std::unique_ptr<WriteOper> oper(new WriteOper(bytestore::PRIORITY_ELASTIC,
                                                  block, 5000000, length,
                                                  offset, data, false));
    SYNC_HANDLE_OPER(WriteChunk)
  }

  void FreezeChunk(uint32_t disk_id, ExtendedBlock* block, uint32_t length) {
    std::unique_ptr<FreezeOper> oper(
        new FreezeOper(bytestore::PRIORITY_ELASTIC, block, 5000000, length,
                       false, bytestore::PLM_STORAGE_ANY));
    SYNC_HANDLE_OPER(FreezeChunk)
  }

  void SetChunkAttr(const uint32_t disk_id, ExtendedBlock* block, char* meta) {
    std::unique_ptr<XAttrSetOper> oper(
        new XAttrSetOper(bytestore::PRIORITY_ELASTIC, block, 5000000, meta,
                         bytestore::PLM_STORAGE_ANY));
    SYNC_HANDLE_OPER(SetXATTRChunk)
  }

  void GetChunkAttr(const uint32_t disk_id, ExtendedBlock* block,
                    char* result) {
    std::unique_ptr<XAttrGetOper> oper(
        new XAttrGetOper(bytestore::PRIORITY_ELASTIC, block, 5000000,
                         bytestore::PLM_STORAGE_ANY));
    SYNC_HANDLE_OPER(GetXATTRChunk)
    oper->response_.xattr().copy(result, XATTR_BYTES);
  }

  void IOOperDone(byte::CountDownLatch* latch) {
    latch->CountDown();
  }

 public:
  MockDataNode* dn_;
  MockStore* mock_storage_;
  std::unique_ptr<ChunkServerRuntime> chunkserver_runtime_;
  CsEnv* env_;
  CsConfig cs_config_;
  NameSpaceInfo* ns_info_;
  NameSpaceInfo* acc_ns_info_;
  MockTosStore* tos_store_;
  ChunkServerStore* local_store_;
  PartialBlockStore* partial_store_;
  bytestore::chunkserver::DiskIdConfMap disk_id_conf_map_;
  int namespace_id_ = 1004580134;
  std::string cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
  uint64_t create_time_ = 1546064706393;
  std::string build_version_ = "2.6.0";
  std::string software_version_ = "2.6.3";
  std::string bpid_ = "BP-13882352884257380248-1546064706393";
  uint64_t fs_id_ = 4013797767642215140;
  uint64_t ns_id_ = 13882352884257380248ULL;
  NamespaceType ns_type_ = NamespaceType::TOS_LOCAL;
  MockConnection* conn_;
  io::IOBuf write_buf_;
  std::string sended_ = "";
};

TEST_F(BlockSenderTests, IllegalArguments) {
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, true, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIllegalArgumentException);
  EXPECT_EQ(block_sender, nullptr);

  // create tos sender
  caching_strategy->SetCachingMode(CachingStrategy::THROUGH);
  e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, true, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIllegalArgumentException);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, ReplicaNotFound) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::ReturnNull());

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);

  EXPECT_EQ(e.GetE(), exceptions::E::kReplicaNotFoundException);
  EXPECT_EQ(block_sender, nullptr);

  EXPECT_CALL(
      *reinterpret_cast<MockTosStore*>(tos_store_),
      GetBlockInfo(::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kRemoteReplicaNotFound)));

  e = BlockSender::CreateRemoteBlockSender(tmp_block, 0, test_blk_len, false,
                                           false, dn_, caching_strategy, b,
                                           &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kRemoteReplicaNotFound);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, DiskNull) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::ReturnNull());

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  std::unique_ptr<CachingStrategy> caching_strategy(
      CachingStrategy::NewDefaultStrategy());
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0UL, test_blk_len, false, false, dn_,
      caching_strategy.get(), false, FLAGS_bytestore_hdfs_default_user, -1,
      true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, ConnNullAndClose) {
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);

  BlockSender* block_sender = nullptr;
  TraceBaggage b;
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(nullptr, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);

  conn_->SetIsClosed(true);
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, WaitForRwbLengthFailed) {
  std::shared_ptr<MockRbwReplica> tmp_rbw_replica =
      std::make_shared<MockRbwReplica>(test_bp_id, test_blk_id, test_blk_gs0,
                                       false, storage_uuid, true, false);
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(tmp_rbw_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  EXPECT_CALL(*tmp_rbw_replica, GetBytesOnDisk())
      .Times(31)
      .WillRepeatedly(::testing::Return(0));

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("bytes, but only"), std::string::npos);
  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
  // delete disk;
}

TEST_F(BlockSenderTests, WaitForRwbLengthSucceed) {
  std::shared_ptr<MockRbwReplica> tmp_rbw_replica =
      std::make_shared<MockRbwReplica>(test_bp_id, test_blk_id, test_blk_gs0,
                                       false, storage_uuid, true, false);
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(tmp_rbw_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetBlockEntryMgr())
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr));
  EXPECT_CALL(*tmp_rbw_replica, GetBytesOnDisk())
      .Times(4)
      .WillOnce(::testing::Return(test_blk_len / 3))
      .WillOnce(::testing::Return(test_blk_len / 2))
      .WillOnce(::testing::Return(test_blk_len))
      .WillOnce(::testing::Return(test_blk_len));
  EXPECT_CALL(*tmp_rbw_replica, GetDiskDataLen())
      .Times(1)
      .WillOnce(::testing::Return(test_blk_len));

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);

  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, ReplicaGS_LT_BlockGS) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetBlockEntryMgr())
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(nullptr));
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_later_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica genstamp < block genstamp"),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);

  std::shared_ptr<ExtendedBlock> f_block_ptr(
      test_finalized_replica->GetBlock()->Clone());
  EXPECT_CALL(
      *reinterpret_cast<MockTosStore*>(tos_store_),
      GetBlockInfo(::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Invoke(
          [&](const std::string& bpid, const std::string& object_key,
              CsIoPriority priority, std::shared_ptr<ExtendedBlock>& block) {
            block = f_block_ptr;
            return exceptions::Exception();
          }));
  e = BlockSender::CreateRemoteBlockSender(tmp_block, 0, test_blk_len, false,
                                           false, dn_, caching_strategy, b,
                                           &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica genstamp < block genstamp"),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);

  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, ReplicaGS_GT_BlockGS) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_later_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetBlockEntryMgr())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(nullptr));
  BlockSender* block_sender = nullptr;
  ExtendedBlock* test_fblock = test_block->Clone();
  TraceBaggage b;
  ASSERT_EQ(test_fblock->GetGS(), test_blk_gs0);
  EXPECT_CALL(*mock_storage,
              CreateBlockStream(::testing::_, ::testing::_, ::testing::_,
                                ::testing::_, ::testing::_))
      .WillOnce(::testing::Return(exceptions::Exception()));
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, test_fblock, 0, test_blk_len, false, false, dn_,
      caching_strategy, false, FLAGS_bytestore_hdfs_default_user, -1, true, b,
      &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_GT(test_fblock->GetGS(), test_blk_gs0);
  EXPECT_EQ(test_fblock->GetGS(), test_blk_gs1);
  EXPECT_NE(block_sender, nullptr);
  delete block_sender;

  std::shared_ptr<ExtendedBlock> f_block_ptr(
      test_later_finalized_replica->GetBlock()->Clone());
  EXPECT_CALL(
      *reinterpret_cast<MockTosStore*>(tos_store_),
      GetBlockInfo(::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Invoke(
          [&](const std::string& bpid, const std::string& object_key,
              CsIoPriority priority, std::shared_ptr<ExtendedBlock>& block) {
            block = f_block_ptr;
            return exceptions::Exception();
          }));
  e = BlockSender::CreateRemoteBlockSender(test_fblock, 0, test_blk_len, false,
                                           false, dn_, caching_strategy, b,
                                           &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_GT(test_fblock->GetGS(), test_blk_gs0);
  EXPECT_EQ(test_fblock->GetGS(), test_blk_gs1);
  EXPECT_NE(block_sender, nullptr);
  delete block_sender;

  delete test_fblock;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, InvisibleReplica) {
  std::shared_ptr<ReplicaUnderRecovery> test_rur_replica;
  ReplicaUnderRecovery::New(test_rwr_replica, recovery_id, &test_rur_replica);
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(2)
      .WillOnce(::testing::Return(test_rwr_replica))
      .WillOnce(::testing::Return(test_rur_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(2)
      .WillOnce(::testing::Return(env_->GetDisk(1)))
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  ASSERT_EQ(test_rwr_replica->GetVisibleLength(), -1);
  ASSERT_EQ(test_rur_replica->GetVisibleLength(), -1);
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica is not readable"), std::string::npos);
  EXPECT_NE(e.GetMessage().find(test_rwr_replica->ToString()),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);
  delete tmp_block;

  tmp_block = test_block->Clone();
  e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica is not readable"), std::string::npos);
  EXPECT_NE(e.GetMessage().find(test_rur_replica->ToString()),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);

  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}

// TEST_F(BlockSenderTests, DataChecksumNotFound) {
//     ExtendedBlock* tmp_block = test_block->Clone();
//     MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
//     EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
//         .Times(1)
//         .WillOnce(::testing::Return(test_finalized_replica));
//     DataChecksum* tmp_data_checksum = nullptr;
//     EXPECT_CALL(*mock_storage, ReadDiskChecksum(tmp_block, ::testing::_))
//         .Times(1)
//         .WillOnce(::testing::DoAll(
//             ::testing::SetArgPointee<1>(tmp_data_checksum),
//             ::testing::Return(exceptions::Exception(exceptions::E::kIOException))));
//     EXPECT_CALL(*mock_storage,
//         GetDisk(0)).Times(1).WillOnce(::testing::Return(env_->GetDisk(1)));
//     BlockSender* block_sender
//     = nullptr; auto&& caching_strategy =
//     CachingStrategy::NewDefaultStrategy(); auto e =
//     BlockSender::CreateLocalBlockSender(tmp_block,
//                                                  0,
//                                                  test_blk_len,
//                                                  true,
//                                                  true,
//                                                  dn_,
//                                                  caching_strategy,
//                                                  false,
//                                                  &block_sender);
//     EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
//     delete block_sender;
//     delete tmp_block;
//     delete caching_strategy;
// }

TEST_F(BlockSenderTests, IllegalOffsetOrLength) {
  auto dn_id = std::make_shared<DatanodeID>(
      ip_addr, hostname, uuid, xfer_port, info_port, ipc_port, node_name,
      std::vector<std::string>(), std::vector<std::string>());
  auto dn_reg = std::make_shared<DatanodeRegistration>(
      dn_id, new StorageInfo(NodeType::DATA_NODE), DATANODE_SOFTWARE_VERSION);
  EXPECT_CALL(*dn_, GetDNRegistrationForBP(test_bp_id, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(dn_reg),
                           ::testing::Return(exceptions::Exception())));
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, test_blk_len / 2, test_blk_len, false, false, dn_,
      caching_strategy, false, FLAGS_bytestore_hdfs_default_user, -1, true, b,
      &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("don't match block"), std::string::npos);
  delete block_sender;

  std::shared_ptr<ExtendedBlock> f_block_ptr(
      test_finalized_replica->GetBlock()->Clone());
  EXPECT_CALL(
      *reinterpret_cast<MockTosStore*>(tos_store_),
      GetBlockInfo(::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Invoke(
          [&](const std::string& bpid, const std::string& object_key,
              CsIoPriority priority, std::shared_ptr<ExtendedBlock>& block) {
            block = f_block_ptr;
            return exceptions::Exception();
          }));
  e = BlockSender::CreateRemoteBlockSender(tmp_block, test_blk_len / 2,
                                           test_blk_len, false, false, dn_,
                                           caching_strategy, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("don't match block"), std::string::npos);

  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}
TEST_F(BlockSenderTests, OffsetAlignment) {
  ExtendedBlock* tmp_block = test_block->Clone();
  std::shared_ptr<ReplicaInfo> replica(new FinalizedReplica(
      test_block.get(), storage_uuid, true, false, 0, true));
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(replica));
  DataChecksum* tmp_data_checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_NULL, 512);
  EXPECT_CALL(*mock_storage, ReadDiskChecksum(tmp_block, ::testing::_))
      .Times(1)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(tmp_data_checksum),
                                 ::testing::Return(exceptions::Exception())));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetBlockEntryMgr())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(nullptr));
  BlockSender* block_sender = nullptr;
  EXPECT_CALL(*mock_storage,
              CreateBlockStream(::testing::_, ::testing::_, ::testing::_,
                                ::testing::_, ::testing::_))
      .WillOnce(::testing::Return(exceptions::Exception()));
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, tmp_block, 256, test_blk_len / 2, true, true, dn_,
      caching_strategy, false, FLAGS_bytestore_hdfs_default_user, -1, true, b,
      &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(block_sender->GetOffset(), 0);
  delete block_sender;

  std::shared_ptr<ExtendedBlock> f_block_ptr(
      test_finalized_replica->GetBlock()->Clone());
  EXPECT_CALL(
      *reinterpret_cast<MockTosStore*>(tos_store_),
      GetBlockInfo(::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Invoke(
          [&](const std::string& bpid, const std::string& object_key,
              CsIoPriority priority, std::shared_ptr<ExtendedBlock>& block) {
            block = f_block_ptr;
            return exceptions::Exception();
          }));
  e = BlockSender::CreateRemoteBlockSender(tmp_block, 256, test_blk_len / 2,
                                           true, true, dn_, caching_strategy, b,
                                           &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(block_sender->GetOffset(), 0);
  delete block_sender;
  delete tmp_block;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, SendBlock) {
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);
  BlockSender* block_sender = nullptr;
  TraceBaggage b;
  auto&& caching_strategy = CachingStrategy::NewDefaultStrategy();

  auto e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, false, true, dn_, caching_strategy,
      false, FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 4096);
  ASSERT_EQ(sended_.size(), 4158);  // 4158 = 31 * 2 + 4096
  EXPECT_EQ(sended_.substr(31, 4), "ABCD");
  delete block_sender;
  delete block;
  delete caching_strategy;
}

TEST_F(BlockSenderTests, InvalidChecksum) {
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);

  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 51200000);
  auto e = local_store_->WriteBlockMetaHeader(
      block, checksum, StorageType::DISK, bytestore::PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  TraceBaggage b;
  e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_EQ(block_sender, nullptr);
  delete checksum;
  delete block;
}

TEST_F(BlockSenderTests, ReadChecksumFailed) {
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);

  // only create meta file, not write to it
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  auto e = local_store_->WriteBlockMetaHeader(
      block, checksum, StorageType::DISK, bytestore::PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateLocalBlockSender(nullptr, block, 0, test_blk_len, true,
                                          true, dn_, nullptr, false,
                                          FLAGS_bytestore_hdfs_default_user, -1,
                                          true, TraceBaggage(), &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, ReadBlockFailed) {
  EXPECT_CALL(*dn_, ReportBadBlock(::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(exceptions::Exception()));
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);
  byte::LocalFileSystem local_fs;
  std::string file_name1 =
      "./BlockSenderTestDir/disk1/data/825/"
      "1.2026362776.3232237157_4171447129.103@1";
  uint64_t file_size = 0;
  EXPECT_TRUE(local_fs.GetFileSize(file_name1, &file_size).ok());
  byte::DeleteOptions delete_options;
  local_fs.DeleteFile(file_name1, delete_options);
  EXPECT_TRUE(local_fs.GetFileSize(file_name1, &file_size).IsNotFound());

  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, TraceBaggage(),
      &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);
  block_sender->SetClientVersion(10506);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, VerifyChecksumFailed) {
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);

  // create meta file, use incorrect data
  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 31 + 'A';
  }
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  auto e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                         bytestore::PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteChecksum(block, checksum_chunk, 0,
                                  checksum_chunk->Length(), false);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateLocalBlockSender(nullptr, block, 0, test_blk_len, true,
                                          true, dn_, nullptr, false,
                                          FLAGS_bytestore_hdfs_default_user, -1,
                                          true, TraceBaggage(), &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kChecksumException);
  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, ChecksumFinalized) {
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);

  // create meta file
  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  auto e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                         bytestore::PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteChecksum(block, checksum_chunk, 0,
                                  checksum_chunk->Length(), false);
  EXPECT_TRUE(e.OK());
  TraceBaggage b;

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 4128);
  ASSERT_EQ(sended_.size(), 4190);  // 4190 = 31 * 2 + 32 + 4096
  EXPECT_EQ(0xda85273U, be32toh(*reinterpret_cast<const uint32_t*>(
                            sended_.substr(31, 4).data())));
  EXPECT_EQ(sended_.substr(63, 4), "ABCD");
  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, ChecksumRBW) {
  EXPECT_CALL(*dn_, GetNamespaceInfo(::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(ns_info_));
  FLAGS_bytestore_hdfs_checksum_mode = 2;
  PrepareForSendBlockTest(dn_);
  ExtendedBlock* block = new ExtendedBlock(bpid_, 2, 800, 1546064706393, false);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e = local_store_->CreateRbw(StorageType::DISK, block, false, &replica,
                                   bytestore::PRIORITY_ELASTIC);
  EXPECT_TRUE(e.OK());

  // create and write meta file
  static const uint32_t k_buf_size = 800;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  e = local_store_->WriteBlock(block, data_chunk, 0, k_buf_size, false,
                               bytestore::PRIORITY_ELASTIC);
  EXPECT_TRUE(e.OK());
  replica->SetDiskDataLen(k_buf_size);
  e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                         bytestore::PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteChecksum(block, checksum_chunk, 0,
                                  checksum_chunk->Length(), false);
  EXPECT_TRUE(e.OK());
  TraceBaggage b;
  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 200, 500, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, b,
      &block_sender);  // read 200-700
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 808);
  ASSERT_EQ(sended_.size(), 870);  // 31 * 2 + 808
  EXPECT_EQ(0xda85273U,            // crc32c of data[:512]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(31, 4).data())));
  EXPECT_EQ(0x316e4021U,  // crc32c of data[512:]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(35, 4).data())));
  EXPECT_EQ(sended_.substr(39, 4), "ABCD");

  // Simulate received 50B after create block sender
  replica->SetDiskDataLen(k_buf_size - 50);
  delete block_sender;
  e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 200, 500, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, b,
      &block_sender);  // read 200-700
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  block_sender->SetOpKey(op_key);

  sended_ = "";
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 758);
  ASSERT_EQ(sended_.size(), 820);  // 31 * 2 + 758
  EXPECT_EQ(0xda85273U,            // crc32c of data[:512]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(31, 4).data())));
  EXPECT_EQ(0xdd325b3eU,  // crc32c of data[512:750]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(35, 4).data())));
  EXPECT_EQ(sended_.substr(39, 4), "ABCD");
  EXPECT_EQ(sended_.substr(39 + 512, 4), "ABCD");

  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, GenerateChecksumFinalized) {
  byte::SetMinLogLevel(byte::LOG_LEVEL_DEBUG);
  FLAGS_bytestore_hdfs_checksum_mode = 1;
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 1, 4096, 1546064706393, false);
  PrepareForSendBlockTest(dn_);

  // Do not create checksum file, generate crc32c from data
  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, TraceBaggage(),
      &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 4100);
  ASSERT_EQ(sended_.size(), 4162);  // 4162 = 31 * 2 + 4 + 4096
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, GenerateChecksumPerf) {
  byte::SetMinLogLevel(byte::LOG_LEVEL_DEBUG);
  FLAGS_bytestore_hdfs_checksum_mode = 1;
  int64_t start_ms = byte::GetCurrentTimeInMs();
  // block id = 10, bpid = BP-2026362776-192.168.6.101-1546064706393
  ChunkIdMeta chunk_id(10, 13882352884257380248ULL, 1546064706393);
  static const uint32_t k_large_buf_size = 128 * 1024 * 1024;
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 10, k_large_buf_size, 1546064706393, false);
  CreateChunk(1, block);

  char* data = new char[k_large_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_large_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }

  WriteChunk(1, block, data, k_large_buf_size, 0);
  char* write_xattr = new char[XATTR_BYTES];
  memset(write_xattr, 0, 32);
  std::unique_ptr<char[]> write_xattr_deleter(write_xattr);
  BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
  meta->gs_ = 1546064706393;
  meta->fin_ = 1;
  meta->checksum_enabled_ = 1;
  SetChunkAttr(1, block, write_xattr);
  FreezeChunk(1, block, k_large_buf_size);

  PrepareForSendBlockTest(dn_);

  int64_t cpt1_ms = byte::GetCurrentTimeInMs();
  std::cerr << "prepare 128M data cost " << cpt1_ms - start_ms << " ms"
            << std::endl;

  // Do not create checksum file, generate crc32c from data
  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, k_large_buf_size, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, TraceBaggage(),
      &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(ns_id_, block->GetBlockID(), Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);

  int64_t cpt2_ms = byte::GetCurrentTimeInMs();
  std::cerr << "generate checksum and send data cost " << cpt2_ms - cpt1_ms
            << " ms" << std::endl;

  // Create meta file and read from it
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 4096);
  io::IOChunk* data_chunk = new io::IOChunk(k_large_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_large_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_large_buf_size);
  e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                         bytestore::PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = local_store_->WriteChecksum(block, checksum_chunk, 0,
                                  checksum_chunk->Length(), false);
  EXPECT_TRUE(e.OK());

  int64_t cpt3_ms = byte::GetCurrentTimeInMs();
  std::cerr << "write meta file cost " << cpt3_ms - cpt2_ms << " ms"
            << std::endl;

  delete block_sender;
  TraceBaggage b;
  e = BlockSender::CreateLocalBlockSender(
      nullptr, block, 0, k_large_buf_size, true, true, dn_, nullptr, false,
      FLAGS_bytestore_hdfs_default_user, -1, true, b, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  block_sender->SetOpKey(op_key);

  sended_ = "";
  uint64_t readcount2;
  e = block_sender->SendBlock(conn_, &readcount2, false);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, readcount2);

  int64_t end_ms = byte::GetCurrentTimeInMs();
  std::cerr << "read checksum and send data cost " << end_ms - cpt3_ms << " ms"
            << std::endl;

  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}
TEST_F(BlockSenderTests, CreateAccTosBlockSender) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetBlockEntryMgr())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(nullptr));
  BlockSender* block_sender = nullptr;
  ExtendedBlock* test_fblock = test_acc_block->Clone();
  EXPECT_CALL(*mock_storage,
              CreateBlockStream(::testing::_, ::testing::_, ::testing::_,
                                ::testing::_, ::testing::_))
      .WillOnce(::testing::Return(exceptions::Exception()));

  std::shared_ptr<ExtendedBlock> f_block_ptr(
      test_acc_finalized_replica->GetBlock()->Clone());
  TraceBaggage b;
  auto e = BlockSender::CreateRemoteBlockSender(test_fblock, 0, test_blk_len,
                                                false, false, dn_, nullptr, b,
                                                &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(test_fblock->GetGS(), test_blk_gs0);
  EXPECT_NE(block_sender, nullptr);
  delete block_sender;
  delete test_fblock;
}

TEST_F(BlockSenderTests, CreatePartialBlockSenderLocal) {
  FLAGS_bytestore_cfs_enable_partial_block_read = true;
  PrepareForSendBlockTest(dn_);

  static const uint32_t TEST_BLOCK_SIZE = 8 * 1024 * 1024;
  uint64_t TEST_BLOCK_ID = 99898;
  ExtendedBlock* test_block = new ExtendedBlock(
      bpid_, TEST_BLOCK_ID, TEST_BLOCK_SIZE, 1546064706393, false);
  BYTE_DEFER(delete test_block);
  char* test_data = new char[TEST_BLOCK_SIZE];
  std::unique_ptr<char[]> scoped_data(test_data);
  for (uint32_t i = 0; i < TEST_BLOCK_SIZE; ++i) {
    test_data[i] = i % 32 + 'A';
  }
  CsIoPriority priority = bytestore::PRIORITY_ELASTIC;
  std::shared_ptr<ReplicaInPipeline> replica;
  exceptions::Exception e = local_store_->CreateRbw(
      StorageType::DISK, test_block, false, &replica, priority);
  EXPECT_TRUE(e.OK());

  io::IOChunk* chunk = new io::IOChunk(TEST_BLOCK_SIZE);
  chunk->WriteBytes(reinterpret_cast<const uint8_t*>(test_data),
                    TEST_BLOCK_SIZE);
  e = local_store_->WriteBlock(test_block, chunk, 0, TEST_BLOCK_SIZE, false);
  EXPECT_TRUE(e.OK());
  io::IOChunk::Destroy(chunk);
  replica->SetNumBytes(TEST_BLOCK_SIZE);
  e = local_store_->FinalizeBlock(test_block, priority);
  EXPECT_TRUE(e.OK());

  auto cs_io_service = chunkserver_runtime_->GetBrpcCSIOService();

  EXPECT_CALL(*dn_,
              GetCSIOService(bytestore::chunkserver::RpcType::BRPC, testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(cs_io_service));

  BlockSender* block_sender = nullptr;
  TraceBaggage b;
  e = BlockSender::CreatePartialBlockSender(test_block, 0, TEST_BLOCK_SIZE,
                                            false, false, dn_, nullptr, b,
                                            &block_sender);
  EXPECT_TRUE(e.OK());
  EXPECT_NE(block_sender, nullptr);
  BYTE_DEFER(delete block_sender);
  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_TRUE(e.OK());
  EXPECT_EQ(readcount, TEST_BLOCK_SIZE);
  ASSERT_GT(sended_.size(), TEST_BLOCK_SIZE);
}

TEST_F(BlockSenderTests, CreatePartialBlockSenderTos) {
  FLAGS_bytestore_cfs_enable_partial_block_read = true;
  PrepareForSendBlockTest(dn_);

  static const uint32_t TEST_BLOCK_SIZE = 8 * 1024 * 1024;
  uint64_t TEST_BLOCK_ID = 99899;
  ExtendedBlock* test_block = new ExtendedBlock(
      bpid_, TEST_BLOCK_ID, TEST_BLOCK_SIZE, 1546064706393, false);
  BYTE_DEFER(delete test_block);
  char* test_data = new char[TEST_BLOCK_SIZE];
  std::unique_ptr<char[]> scoped_data(test_data);
  for (uint32_t i = 0; i < TEST_BLOCK_SIZE; ++i) {
    test_data[i] = i % 32 + 'A';
  }
  EXPECT_CALL(*dn_, GetNamespaceType(::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::Invoke([&](uint64_t ns_id, NamespaceType* type) {
            *type = NamespaceType::TOS_LOCAL;
            return exceptions::Exception();
          }));
  EXPECT_CALL(*mock_storage_,
              GetNextVolume(::testing::_, ::testing::_, ::testing::_,
                            ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Invoke(
          [&](const StorageType& storage_type, NamespaceType namespace_type,
              const std::string& bpid, uint64_t block_size, uint16_t* disk_id) {
            return local_store_->GetNextVolume(storage_type, namespace_type,
                                               bpid, block_size, disk_id);
          }));
  EXPECT_CALL(*tos_store_,
              ReadBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(::testing::Invoke(
          [&](const ExtendedBlock& block, uint64_t data_offset,
              uint32_t data_len, CsIoPriority priority, byterpc::IOBuf* buffer,
              Closure<void, exceptions::Exception>* done) {
            buffer->append(test_data + data_offset, data_len);
            done->Run(exceptions::Exception());
          }));

  bytestore::chunkserver::CSIOServiceOptions opts;
  bytestore::chunkserver::CSIOServiceImpl<
      bytestore::chunkserver::RpcType::BRPC>* csio_service =
      new bytestore::chunkserver::CSIOServiceImpl<
          bytestore::chunkserver::RpcType::BRPC>(env_, opts);
  EXPECT_CALL(*dn_,
              GetCSIOService(bytestore::chunkserver::RpcType::BRPC, testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(csio_service));
  BYTE_DEFER(delete csio_service);

  BlockSender* block_sender = nullptr;
  TraceBaggage b;
  exceptions::Exception e = BlockSender::CreatePartialBlockSender(
      test_block, 0, TEST_BLOCK_SIZE, false, false, dn_, nullptr, b,
      &block_sender);
  EXPECT_TRUE(e.OK());
  EXPECT_NE(block_sender, nullptr);
  BYTE_DEFER(delete block_sender);
  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount, false);
  EXPECT_TRUE(e.OK());
  EXPECT_EQ(readcount, TEST_BLOCK_SIZE);
  ASSERT_GT(sended_.size(), TEST_BLOCK_SIZE);
}

}  // namespace bds::dancedn::cloudfs
