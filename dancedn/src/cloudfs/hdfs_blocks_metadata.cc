// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/hdfs_blocks_metadata.h"

#include <sstream>

#include "cloudfs/proto/ClientDatanodeProtocol.pb.h"

namespace bds::dancedn::cloudfs {

HdfsBlocksMetadata::HdfsBlocksMetadata(
    const std::string& block_pool_id, const std::vector<uint64_t>& block_ids,
    const std::vector<std::vector<uint8_t>>& volume_ids,
    const std::vector<uint32_t>& volume_indexes)
    : block_pool_id_(block_pool_id),
      block_ids_(block_ids),
      volume_ids_(volume_ids),
      volume_indexes_(volume_indexes) {}

::cloudfs::GetHdfsBlockLocationsResponseProto* HdfsBlocksMetadata::ToProto()
    const {
  auto proto = new ::cloudfs::GetHdfsBlockLocationsResponseProto();

  for (auto& id : volume_ids_) {
    std::string s(id.begin(), id.end());
    proto->add_volumeids(s);
  }

  for (auto index : volume_indexes_) {
    proto->add_volumeindexes(index);
  }
  return proto;
}

std::string HdfsBlocksMetadata::ToString() const {
  std::ostringstream oss;
  oss << "Metadata for " << block_ids_.size() << " blocks in " << block_pool_id_
      << ": ";
  auto it = block_ids_.begin();
  if (it != block_ids_.end()) {
    oss << *it;
    ++it;
  }
  for (; it != block_ids_.end(); ++it) {
    oss << "," << *it;
  }
  return oss.str();
}

}  // namespace bds::dancedn::cloudfs
