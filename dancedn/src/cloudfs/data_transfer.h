// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#pragma once

#include <algorithm>
#include <map>
#include <memory>
#include <random>
#include <set>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/concurrent/hashtable.h"
#include "byte/include/byte_log.h"
#include "byte/string/format/print.h"
#include "byte/system/timestamp.h"
#include "cloudfs/block_construction_stage.h"
#include "cloudfs/opstats/op_key.h"
#include "cloudfs/opstats/op_stats.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/thread.h"
#include "common/metrics.h"
#include "common/store_types.h"

namespace bds::dancedn::cloudfs {

class DatanodeInfo;
class ExtendedBlock;
class Thread;
class StorageType;
class TraceBaggage;
class OpKey;
class DataXceiverContext;
class DataNode;
class CachingStrategy;
class BlockConstructionStage;
class DiskDataTransferManager;
class DataTransferManager;
class DataXceiver;

/**
 *  Used for transferring a block of data.
 *  This class send a piece of data to
 *  another DataNode.
 */
class DataTransfer : public Thread,
                     public std::enable_shared_from_this<DataTransfer> {
 public:
  DataTransfer(const int32_t resident_time, std::vector<DatanodeInfo*>* targets,
               const std::vector<StorageType>& target_storage_types,
               ExtendedBlock* block, const BlockConstructionStage& stage,
               const std::string& client_name, DataNode* datanode);
  ~DataTransfer();
  // virtual for ut
  virtual exceptions::Exception MainRun();
  void Run() override;
  void SetTraceBaggage(const TraceBaggage& trace_baggage);
  DiskDataTransferManager* GetDiskDataTransferManager();
  void SetDiskDataTransferManager(DiskDataTransferManager* manager);
  std::string GetExtendedBlockString();
  void SetXceiver(std::shared_ptr<DataXceiver> xceiver);
  void SetPriority(bytestore::IOPriority pri) {
    priority_ = pri;
  }
  bytestore::IOPriority GetPriority() {
    return priority_;
  }
  uint64_t Id() {
    return id_;
  }

 private:
  std::shared_ptr<DataTransfer> GetPtr() {
    return shared_from_this();
  }
  std::string CurUser();

 private:
  std::shared_ptr<DataXceiver> xceiver_;
  int32_t resident_time_;
  std::vector<DatanodeInfo*> targets_;
  std::vector<StorageType> target_storage_types_;
  ExtendedBlock* block_;
  BlockConstructionStage stage_;
  std::string client_name_;
  bool is_datanode_;
  CachingStrategy* caching_strategy_;
  DataXceiverContext* context_;
  DataNode* datanode_;
  OpKey op_key_;
  bytestore::IOPriority priority_{bytestore::PRIORITY_ELASTIC};
  uint64_t id_;
  DiskDataTransferManager* disk_data_transfer_manager_;
  int64_t start_time_us_;
};

class DiskDataTransferManager {
 public:
  DiskDataTransferManager(bytestore::DiskId disk_id, StorageType type,
                          DataTransferManager* data_transfer_manager);
  ~DiskDataTransferManager() {}

  void Enqueue(std::shared_ptr<DataTransfer> replicate_worker);

  std::shared_ptr<DataTransfer> Dequeue();

  void FinishDataTransfer(std::shared_ptr<DataTransfer> finished_worker);

  bool CanTransferring();

  uint32_t GetMaxTransferring();

  uint32_t GetCurTransferring();

  uint32_t IncrementCurTransferring();

  uint32_t DecrementCurTransferring();

  StorageType GetStorageType() {
    return storage_type_;
  }

  void SetMaxTransferPerDisk(uint32_t concurrency);

  bytestore::Tags GetDiskTags();

  // for ut
  int GetWaitingNum() {
    return waiting_queue_.Size();
  }

  static bytestore::MediaType StorageType2MediaType(StorageType storage_type);

  void CancelAllWaitingTask();

 private:
  io::ConcurrentUniqueQueue<std::string, std::shared_ptr<DataTransfer>>
      waiting_queue_;
  byte::Atomic<uint32_t> cur_transferring_;
  byte::Atomic<uint32_t> max_transferring_;
  bytestore::DiskId disk_id_;
  StorageType storage_type_;
  DataTransferManager* data_transfer_manager_;
};

/*
The function of this class is similar to ThreadManager, but if we use
ThreadManager directly, the following problems will occur:
1. The count is not zero after Stop() and Join():
If we judge that the count is satisfied then AddWorker() and increment,
we will encounter the problem that the count is not zero after Stop() and
Join(). Because Join() will wait for the end of DataTransfer, and the
DataTransfer will dequeue a next DataTransfer from waiting queue to AddWorker()
and increment before the end. It causes UT problems.
2. The speed limit may not take effect:
If we judge that the count is satisfied then AddWorker(), and increment when
the DataTransfer actually starts, it will encounter a situation where the
production speed of worker queue is greater than the consumption speed,
resulting in the failure of the speed limit.
*/
class DataTransferManager : public Thread {
 private:
  enum Type : uint8_t { DELETE, ADD };
  struct TaskComparer {
    // Sort with priority and id, if priority is equal, smaller id's task is in
    // front.
    bool operator()(const std::pair<DataTransferManager::Type,
                                    std::shared_ptr<DataTransfer>>& lhs,
                    const std::pair<DataTransferManager::Type,
                                    std::shared_ptr<DataTransfer>>& rhs) {
      if (lhs.second->GetPriority() == rhs.second->GetPriority()) {
        return lhs.second->Id() > rhs.second->Id();
      }
      return lhs.second->GetPriority() > rhs.second->GetPriority();
    }
  };

 public:
  DataTransferManager() {}
  ~DataTransferManager();

  void AddDiskDataTransferManager(bytestore::DiskId disk_id,
                                  StorageType storage_type);

  bool AddDataTransferRequest(bytestore::DiskId disk_id,
                              std::shared_ptr<DataTransfer> replicate_worker);

  void FinishDataTransfer(std::shared_ptr<DataTransfer> finished_worker);

  void Run();

  void AddWorker(std::shared_ptr<DataTransfer> worker);

  void DeleteWorker(std::shared_ptr<DataTransfer> worker);

  void SetMaxReplicateConcurrencyPerDisk(StorageType storage_type,
                                         uint32_t value);

  void CancelAllWaitingTask();

  // for ut
  int GetDiskDataTransferManagerNum() {
    return disk_data_transfer_manager_map_.Size();
  }

  int GetTotalTransferringNum() {
    return transferring_set_.size();
  }

  uint32_t GetCurTransferring(bytestore::DiskId disk_id);

  int GetWaitingNum(bytestore::DiskId disk_id);

  std::map<bytestore::DiskId, uint32_t> GetMaxReplicateConcurrency();

 private:
  void Cleanup();

 private:
  byte::concurrent::HashTable<bytestore::DiskId, DiskDataTransferManager*>
      disk_data_transfer_manager_map_;
  io::ConcurrentPriorityQueue<
      std::pair<DataTransferManager::Type, std::shared_ptr<DataTransfer>>,
      DataTransferManager::TaskComparer>
      request_queue_;
  // used to record the ongoing workers
  // they should stop, join and delete when clean up
  std::set<std::shared_ptr<DataTransfer>> transferring_set_;
};

}  // namespace bds::dancedn::cloudfs
