// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include "cloudfs/client_io.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/io/address.h"
#include "cloudfs/message/rpc_request_message.h"
#include "cloudfs/proto/ProtobufRpcEngine.pb.h"

namespace google {
namespace protobuf {
class Message;
}
}  // namespace google

namespace bds::dancedn::cloudfs {

class InterDatanodeRpc {
 public:
  InterDatanodeRpc(ClientIO* io, const io::IPAddress& addr, uint32_t timeout)
      : io_(io), addr_(addr), timeout_(timeout) {}

  ~InterDatanodeRpc() {
    delete io_;
  }

  exceptions::Exception Call(io::Method method,
                             const google::protobuf::Message* r,
                             google::protobuf::Message** res);

  exceptions::Exception Call(io::Method method, message::RpcRequestMessage* req,
                             message::RpcResponseMessage** res);
  io::IPAddress GetAddr() const {
    return addr_;
  }

 public:
  static const char PROTOCOL_NAME[];
  static const uint32_t PROTOCOL_VERSION;

 private:
  ::cloudfs::RequestHeaderProto* CreateRequestHeader(io::Method method_id);

 private:
  ClientIO* io_;
  io::IPAddress addr_;
  uint32_t timeout_;
};

}  // namespace bds::dancedn::cloudfs
