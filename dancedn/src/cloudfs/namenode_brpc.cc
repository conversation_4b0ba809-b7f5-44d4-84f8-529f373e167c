// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include <memory>
#include <unordered_map>
#include <utility>

#include "byte/include/byte_log.h"
#include "byte/string/format/print.h"
#include "cloudfs/client_io.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/io/address.h"
#include "cloudfs/io/message_request.h"
#include "cloudfs/message/rpc_response_message.h"
#include "cloudfs/namenode_rpc.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/proto/DatanodeProtocol.pb.h"
#include "cloudfs/proto/ProtobufRpcEngine.pb.h"
#include "cloudfs/proto/RpcHeader.pb.h"
#include "cloudfs/store/ufs/remote_store.h"
#include "cloudfs/store/ufs/tos_info.h"

namespace bds::dancedn::cloudfs {

// const char NamenodeRpc::PROTOCOL_NAME[] =
//         "org.apache.hadoop.hdfs.server.protocol.DatanodeProtocol";
// const uint32_t NamenodeRpc::PROTOCOL_VERSION = 1;
// const char NamenodeRpc::NAMENODE_PROTOCOL_NAME[] =
//         "org.apache.hadoop.hdfs.server.protocol.NamenodeProtocol";

extern const std::unordered_map<std::string, exceptions::E> ExceptionsMap;
namespace {

class NamenodeSyncBrpc : public NamenodeRpc {
 public:
  NamenodeSyncBrpc(std::unique_ptr<ClientIO> io, const std::string& ip,
                   int port, uint32_t timeout)
      : io_(std::move(io)),
        ip_(ip),
        port_(port),
        call_id_(0),
        timeout_(timeout) {}

  ~NamenodeSyncBrpc() {}

  exceptions::Exception Register(
      const ::cloudfs::datanode::RegisterDatanodeRequestProto* r,
      ::cloudfs::datanode::RegisterDatanodeResponseProto** res) override {
    auto* response = new ::cloudfs::datanode::RegisterDatanodeResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }

    auto e = io_->SendViaBrpc(
        io::Method::Register, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *res = static_cast<::cloudfs::datanode::RegisterDatanodeResponseProto*>(
        response);
    return exceptions::Exception();
  }

  exceptions::Exception SendHeartbeat(
      const ::cloudfs::datanode::HeartbeatRequestProto* r,
      ::cloudfs::datanode::HeartbeatResponseProto** res) override {
    auto* response = new ::cloudfs::datanode::HeartbeatResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }

    auto e = io_->SendViaBrpc(
        io::Method::SendHeartbeat, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *res = response;
    return exceptions::Exception();
  }

  exceptions::Exception BlockReport(
      const ::cloudfs::datanode::BlockReportRequestProto* r,
      ::cloudfs::datanode::BlockReportResponseProto** res) override {
    auto* response = new ::cloudfs::datanode::BlockReportResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }

    auto e = io_->SendViaBrpc(
        io::Method::BlockReport, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *res =
        static_cast<::cloudfs::datanode::BlockReportResponseProto*>(response);
    return exceptions::Exception();
  }

  exceptions::Exception BlockReceivedAndDeleted(
      const ::cloudfs::datanode::BlockReceivedAndDeletedRequestProto* r)
      override {
    auto* response =
        new ::cloudfs::datanode::BlockReceivedAndDeletedResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto e = io_->SendViaBrpc(
        io::Method::BlockReceivedAndDeleted, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    delete response;
    if (!e.OK()) {
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    return exceptions::Exception();
  }

  exceptions::Exception ErrorReport(
      const ::cloudfs::datanode::ErrorReportRequestProto* r) override {
    auto* response = new ::cloudfs::datanode::ErrorReportResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto e = io_->SendViaBrpc(
        io::Method::ErrorReport, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    delete response;
    if (!e.OK()) {
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    return exceptions::Exception();
  }

  exceptions::Exception VersionRequest(
      const ::cloudfs::VersionRequestProto* r, NameSpaceInfo** info,
      RemoteBlockInfo** remote_block_info) override {
    auto* response = new ::cloudfs::VersionResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto e = io_->SendViaBrpc(
        io::Method::VersionRequest, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);

    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *info = NameSpaceInfo::ParseProto(response);
    if (response->has_remoteblockinfo()) {
      *remote_block_info =
          RemoteBlockInfo::ParseProto(response->remoteblockinfo());
      LOG(INFO) << "parse remote info from remote_block_info";
    }
    if (*remote_block_info == nullptr && response->has_tosinfo()) {
      // this case is that: NN is old-version but DN is new-version
      *remote_block_info = TOSInfo::ParseProto(response->tosinfo());
      LOG(INFO) << "parse remote info from tos_info";
    }
    if (*remote_block_info == nullptr) {
      // HACK for local ns_type
      *remote_block_info = &TOSInfo::DUMMY_TOS_INFO;
      LOG(INFO) << "use dummy as remote_block_info";
    }

    if (*info == nullptr || *remote_block_info == nullptr) {
      delete response;
      return exceptions::Exception(exceptions::E::kFalseException,
                                   "Parse Version Response Proto failed");
    }
    return exceptions::Exception();
  }

  exceptions::Exception ReportBadBlocks(
      const ::cloudfs::datanode::ReportBadBlocksRequestProto* r) override {
    auto* response = new ::cloudfs::datanode::ReportBadBlocksResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto e = io_->SendViaBrpc(
        io::Method::ReportBadBlocks, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);

    delete response;
    if (!e.OK()) {
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    return exceptions::Exception();
  }

  exceptions::Exception CommitBlockSynchronization(
      const ::cloudfs::datanode::CommitBlockSynchronizationRequestProto* r)
      override {
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto* response =
        new ::cloudfs::datanode::CommitBlockSynchronizationResponseProto;
    auto e = io_->SendViaBrpc(
        io::Method::CommitBlockSynchronization, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    delete response;
    if (!e.OK()) {
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    return exceptions::Exception();
  }

  exceptions::Exception GetBlocks(
      const ::cloudfs::namenode::GetBlocksRequestProto* r,
      ::cloudfs::namenode::GetBlocksResponseProto** res) override {
    auto* response = new ::cloudfs::namenode::GetBlocksResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }

    auto e = io_->SendViaBrpc(
        io::Method::GetBlocks, address.get(), NAMENODE_PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *res = static_cast<::cloudfs::namenode::GetBlocksResponseProto*>(response);
    return exceptions::Exception();
  }

  exceptions::Exception Acquire(
      const ::cloudfs::datanode::AcquireRequest* r,
      ::cloudfs::datanode::AcquireResponse** res) override {
    auto* response = new ::cloudfs::datanode::AcquireResponse;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }

    auto e = io_->SendViaBrpc(io::Method::Acquire, address.get(), PROTOCOL_NAME,
                              static_cast<const google::protobuf::Message*>(r),
                              static_cast<google::protobuf::Message*>(response),
                              timeout_);
    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *res = static_cast<::cloudfs::datanode::AcquireResponse*>(response);
    return exceptions::Exception();
  }

  exceptions::Exception GetNamespaces(
      const ::cloudfs::datanode::GetNamespacesRequestProto* r,
      ::cloudfs::datanode::GetNamespacesResponseProto** res) override {
    auto* response = new ::cloudfs::datanode::GetNamespacesResponseProto;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }

    auto e = io_->SendViaBrpc(
        io::Method::GetNamespaces, address.get(), PROTOCOL_NAME,
        static_cast<const google::protobuf::Message*>(r),
        static_cast<google::protobuf::Message*>(response), timeout_);
    if (!e.OK()) {
      delete response;
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }
    *res = response;
    return exceptions::Exception();
  }

 private:
  exceptions::Exception Call(enum cloudfs::io::Method method,
                             const google::protobuf::Message* r,
                             google::protobuf::Message** res) {
    auto rh = CreateRequestHeader(method);
    message::RpcResponseMessage* ret = nullptr;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto e = io_->SendRequest(method, address.get(), rh, r, &ret, timeout_);
    delete rh;
    if (!e.OK()) {
      LOG(ERROR) << "send " << cloudfs::io::MethodToString(method)
                 << " to namenode failed exception : " << e.ToString()
                 << " address: " << address->ToString();
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }

    if (ret->RpcHeader()->status() !=
        ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS) {
      auto e = ParseHeaderException(ret->RpcHeader());
      delete ret;
      return e;
    }

    auto body = ret->ReleaseBody();

    delete ret;
    *res = body;
    return exceptions::Exception();
  }

  exceptions::Exception CallNamenode(enum cloudfs::io::Method method,
                                     const google::protobuf::Message* r,
                                     google::protobuf::Message** res) {
    auto rh = CreateNamenodeRequestHeader(method);
    message::RpcResponseMessage* ret = nullptr;
    auto&& address = NamenodeRpc::GetIPAddressCache()->Get(ip_, port_);
    if (address == nullptr) {
      return exceptions::Exception(
          exceptions::kIOException,
          byte::StringPrint("Parse hostname %s and port %d failed", ip_,
                            port_));
    }
    auto e = io_->SendRequest(method, address.get(), rh, r, &ret, timeout_);
    delete rh;
    if (!e.OK()) {
      LOG(ERROR) << "send " << cloudfs::io::MethodToString(method)
                 << " to namenode failed exception : " << e.ToString()
                 << " address: " << address->ToString();
      NamenodeRpc::GetIPAddressCache()->MarkFailed(ip_, port_);
      return e;
    }

    if (ret->RpcHeader()->status() !=
        ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS) {
      auto e = ParseHeaderException(ret->RpcHeader());
      LOG(ERROR) << "parse header exception:" << e.ToString();
      delete ret;
      return e;
    }

    auto body = ret->ReleaseBody();

    delete ret;
    *res = body;
    return exceptions::Exception();
  }

  exceptions::Exception ParseHeaderException(
      const ::cloudfs::RpcResponseHeaderProto* header) {
    auto e = exceptions::Exception::FromString(header->exceptionclassname());
    if (e == exceptions::kFalseException) {
      if (header->has_errormsg()) {
        return exceptions::Exception(e, "Unknown exception " +
                                            header->exceptionclassname() +
                                            ": " + header->errormsg());
      }
      return exceptions::Exception(e, "Unknown exception " +
                                          header->exceptionclassname() +
                                          ": no error message");
    }

    if (header->has_errormsg()) {
      return exceptions::Exception(e, header->errormsg());
    }

    return exceptions::Exception(e);
  }

  ::cloudfs::RequestHeaderProto* CreateRequestHeader(io::Method method) {
    auto proto = new ::cloudfs::RequestHeaderProto();
    proto->set_methodname(io::MethodToString(method));
    proto->set_clientprotocolversion(PROTOCOL_VERSION);
    proto->set_declaringclassprotocolname(PROTOCOL_NAME);
    return proto;
  }

  ::cloudfs::RequestHeaderProto* CreateNamenodeRequestHeader(
      io::Method method) {
    auto proto = new ::cloudfs::RequestHeaderProto();
    proto->set_methodname(io::MethodToString(method));
    proto->set_clientprotocolversion(PROTOCOL_VERSION);
    proto->set_declaringclassprotocolname(NAMENODE_PROTOCOL_NAME);
    return proto;
  }

 private:
  std::unique_ptr<ClientIO> io_;
  std::string ip_;
  int port_;
  uint32_t call_id_;
  uint32_t timeout_;
};

}  // namespace

NamenodeRpc* NewNamenodeSyncBrpc(std::unique_ptr<ClientIO> io,
                                 const std::string& ip, int port,
                                 uint32_t timeout) {
  return new NamenodeSyncBrpc(std::move(io), ip, port, timeout);
}

}  // namespace bds::dancedn::cloudfs
