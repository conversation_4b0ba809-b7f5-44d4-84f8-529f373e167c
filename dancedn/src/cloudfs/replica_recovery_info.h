// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "cloudfs/block.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/replica_state.h"

namespace bds::dancedn::cloudfs {

class ReplicaRecoveryInfo : public Block {
 public:
  ReplicaRecoveryInfo() {}
  ReplicaRecoveryInfo(uint64_t block_id, uint64_t disk_len, uint64_t gs,
                      ReplicaState state);
  ~ReplicaRecoveryInfo() {}

  ReplicaState GetOriginalReplicaState() const {
    return original_state_;
  }

  void SetStorageUuid(const std::string& storage_uuid) {
    storage_uuid_ = storage_uuid;
  }

  std::string GetStorageUuid() const {
    return storage_uuid_;
  }

  exceptions::Exception ToProto(::cloudfs::BlockProto* proto) const {
    return Block::ToProto(proto);
  }

  static ReplicaRecoveryInfo* ParseProto(const ::cloudfs::BlockProto& proto) {
    Block* blk = Block::ParseProto(proto);
    return static_cast<ReplicaRecoveryInfo*>(blk);
  }

 private:
  ReplicaState original_state_;
  std::string storage_uuid_;
};

}  // namespace bds::dancedn::cloudfs
