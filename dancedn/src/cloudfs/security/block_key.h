// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#ifndef BLOCK_KEY_H_
#define BLOCK_KEY_H_

#pragma once

#include <cstdint>
#include <string>
#include <utility>
#include <vector>

#include "cloudfs/proto/hdfs.pb.h"

namespace bds::dancedn::cloudfs {

class BlockKey {
 public:
  BlockKey() = default;

  BlockKey(uint64_t id, uint64_t expire_date, std::string value)
      : id_(id), expire_date_(expire_date), value_(std::move(value)) {}

  uint64_t GetId() const {
    return id_;
  }

  uint64_t GetExpireDate() const {
    return expire_date_;
  }

  std::string GetValue() const {
    return value_;
  }

  bool IsEmpty() const {
    return id_ == 0 && expire_date_ == 0 && value_.empty();
  }

  static BlockKey ParseProto(const ::cloudfs::BlockKeyProto& block_key_proto);

  static std::vector<BlockKey> ParseProtos(
      const ::google::protobuf::RepeatedPtrField<::cloudfs::BlockKeyProto>&
          block_key_protos);

 private:
  uint64_t id_{0};
  uint64_t expire_date_{0};
  std::string value_{""};
};
}  // namespace bds::dancedn::cloudfs

#endif
