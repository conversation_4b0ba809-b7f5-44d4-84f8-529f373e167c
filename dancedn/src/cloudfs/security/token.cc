// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#include "cloudfs/security/token.h"

#include <string>

#include "cloudfs/security/block_token_identifier.h"
#include "cloudfs/security/block_token_secret_manager.h"

namespace bds::dancedn::cloudfs {

Token::Token(
    const std::shared_ptr<BlockTokenIdentifier>& block_token_identifier,
    const std::shared_ptr<BlockTokenSecretManager>&
        block_token_secret_manager) {
  password_ =
      block_token_secret_manager->CreatePassword(block_token_identifier);
  identifier_ = block_token_identifier->GetBytes();
  kind_ = block_token_identifier->GetKind();
  service_ = "";
}

void Token::ToProto(::cloudfs::TokenProto* token_proto) const {
  token_proto->set_identifier(identifier_);
  token_proto->set_password(password_);
  token_proto->set_kind(kind_);
  token_proto->set_service(service_);
}

Token Token::ParseProto(const ::cloudfs::TokenProto& token_proto) {
  return Token(token_proto.identifier(), token_proto.password(),
               token_proto.kind(), token_proto.service());
}

std::vector<Token> Token::ParseProtos(
    const ::google::protobuf::RepeatedPtrField<::cloudfs::TokenProto>&
        token_protos) {
  std::vector<Token> tokens;
  for (const auto& token_proto : token_protos) {
    auto token = ParseProto(token_proto);
    tokens.emplace_back(token);
  }
  return tokens;
}

std::string Token::ToString() const {
  std::string res = "Token(identifier:";
  res += identifier_ + ", password:";
  res += password_ + ", kind:";
  res += kind_ + ", service:";
  res += service_ + ')';
  return res;
}

}  // namespace bds::dancedn::cloudfs
