// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#ifndef TOKEN_H_
#define TOKEN_H_

#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "cloudfs/proto/Security.pb.h"

namespace bds::dancedn::cloudfs {

class BlockTokenSecretManager;
class BlockTokenIdentifier;

class Token {
 public:
  Token() = default;

  Token(std::string identifier, std::string password, std::string kind,
        std::string service)
      : identifier_(std::move(identifier)),
        password_(std::move(password)),
        kind_(std::move(kind)),
        service_(std::move(service)) {}

  Token(const std::shared_ptr<BlockTokenIdentifier>& block_token_identifier,
        const std::shared_ptr<BlockTokenSecretManager>&
            block_token_secret_manager);

  std::string GetIdentifer() const {
    return identifier_;
  }

  std::string GetPassword() const {
    return password_;
  }

  std::string GetKind() const {
    return kind_;
  }

  std::string GetService() const {
    return service_;
  }

  bool IsEmpty() const {
    return identifier_.empty() && password_.empty() && kind_.empty() &&
           service_.empty();
  }

  std::string ToString() const;

  void ToProto(::cloudfs::TokenProto* token_proto) const;

  static Token ParseProto(const ::cloudfs::TokenProto& token_proto);

  static std::vector<Token> ParseProtos(
      const ::google::protobuf::RepeatedPtrField<::cloudfs::TokenProto>&
          token_protos);

 private:
  std::string identifier_;
  std::string password_;
  std::string kind_;
  std::string service_;
};

}  // namespace bds::dancedn::cloudfs

#endif
