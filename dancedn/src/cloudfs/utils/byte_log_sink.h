// Copyright (c) 2021-present, ByteDance Inc. All rights reserved.

#pragma once

#include <sstream>

#include "butil/logging.h"
#include "butil/strings/string_piece.h"
#include "byte/include/byte_log.h"

namespace bds::dancedn::cloudfs {

// redirect brpc log to byte log
class ByteLogSink : public logging::LogSink {
 public:
  ByteLogSink() {}
  ~ByteLogSink() {}
  // Called when a log is ready to be written out.
  // Returns true to stop further processing.
  bool OnLogMessage(int severity, const char* file, int line,
                    const butil::StringPiece& log_content) override {
    byte::LogLevel level;
    switch (severity) {
      case logging::BLOG_VERBOSE: level = byte::LOG_LEVEL_DEBUG; break;
      case logging::BLOG_INFO:
      case logging::BLOG_NOTICE: level = byte::LOG_LEVEL_INFO; break;
      case logging::BLOG_WARNING: level = byte::LOG_LEVEL_WARNING; break;
      case logging::BLOG_ERROR: level = byte::LOG_LEVEL_ERROR; break;
      case logging::BLOG_FATAL: level = byte::LOG_LEVEL_FATAL; break;
      default: level = byte::LOG_LEVEL_ERROR;
    }
    if (level >= byte::GetMinLogLevel()) {
      std::stringstream output;
      for (auto ch = log_content.begin(); ch != log_content.end(); ch++) {
        output << ch;
      }
      byte::LogMessager(file, line, level).stream()
          << "[BRPC] " << output.str();
    }
    return true;
  }
};

}  // namespace bds::dancedn::cloudfs
