// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once
#ifndef OPERATION_H_
#define OPERATION_H_

#include <string>

namespace bds::dancedn::cloudfs {

enum class Operation {
  ReadBlock,
  CopyBlock,
  WriteBlock,
  WriteBlockFromClient,
  WriteBlockInPipeline,
  RespondPacket,
  ReplaceBlock,
  TransferBlock,
  CopyBlockAcrossFederation,
  GetReplicaVisibleLength,
  GetBlocks,
  RecoverBlock,
  TransferReplica,

  // V2 start
  QueryBlockV2,
  CreateBlockV2,
  Write<PERSON>lockV2,
  SyncBlockV2,
  ReadBlockV2,
  PingBlockV2,
  Seal<PERSON>lockV2,
  FinalizeBlockV2,
  ChecksumV2,

  // background
  EvictReadCache,
  UploadRemote<PERSON>lock,
  MpUploadRemoteBlock,
  AppendRemoteBlock,
  ReadRemoteBlock,
  InvalidBlock,
  Register,
  IBR,
  HBCommand,
  Heartbeat,

  UnknownOperation
};

std::string OperationToString(const Operation& op);

}  // namespace bds::dancedn::cloudfs

#endif
