// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "cloudfs/opstats/op_stats.h"

#include <gtest/gtest.h>

#include <thread>  // NOLINT

#include "byte/byte_log/byte_log_impl.h"
#include "common/metrics.h"

namespace bds::dancedn::cloudfs {

class OpStatsTests : public ::testing::Test {
 public:
  OpStatsTests() {}

  void SetUp() override {}

  static void SetUpTestCase() {
    bytestore::metrics_internal::InitFastMetrics();
    OpStats::GetInstance().Init();
  }
  static void TearDownTestCase() {
    OpStats::GetInstance().Stop();
    OpStats::GetInstance().Join();
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(OpStatsTests, Record) {
  auto& op_stats = OpStats::GetInstance();
  int64_t t_id = 1;
  uint64_t ns_id = 101;
  uint64_t blk = 10;
  OpKey key1 = OpKey::New(t_id, ns_id, blk, "task", "user", "local", "client",
                          "path", Operation::ReplaceBlock);
  OpKey key2 = OpKey::New(t_id, ns_id, blk, "task", "user", "local", "client",
                          "path", Operation::WriteBlock);
  OpKey key3 = OpKey::New(t_id, ns_id, blk, "task", "user", "local", "client",
                          "path", Operation::ReadBlock);
  OpKey key4 = OpKey::New(t_id, ns_id, blk, "task", "user", "local", "client",
                          "path", Operation::TransferBlock);
  OpKey key5 = OpKey::New(t_id, ns_id, blk, "task", "user", "local", "client",
                          "path", Operation::TransferReplica);
  OpKey key6 = OpKey::New(t_id, ns_id, blk, "task", "user", "local", "client",
                          "path", Operation::GetReplicaVisibleLength);
  OpKey key7 =
      OpKey::New(t_id, ns_id, blk, "task", "task2", "task3", "client", "user",
                 "local", "client", "path", Operation::WriteBlockV2);

  op_stats.Record(key1, 0, 1, 0, 1000);
  op_stats.Record(key1, 0, 1, 0, 2000);
  op_stats.Record(key1, 0, 1, 0, 3000);
  op_stats.Record(key1, 0, 1, 0, 4000);
  op_stats.Record(key1, 0, 1, 0, 5000);
  op_stats.Record(key2, 0, 1, 0, 6000);
  op_stats.Record(key3, 0, 1, 0, 7000);
  op_stats.Record(key4, 0, 1, 0, 8000);
  op_stats.Record(key5, 0, 1, 0, 8000);
  op_stats.Record(key6, 0, 1, 0, 8000);
  op_stats.Record(key7, 0, 1, 0, 8000);
  usleep(1000);

  EXPECT_GE(op_stats.GetQueueSize(), 0);
}

}  // namespace bds::dancedn::cloudfs
