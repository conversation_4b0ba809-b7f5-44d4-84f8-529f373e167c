#include <iostream>

#include "byterpc/memory_id.h"
#include "byterpc/util/object_pool.h"

#include "benchmark/benchmark.h"

namespace {

using namespace byterpc;

template <size_t SIZE> class TestObject {
private:
    char val[SIZE];
};

};  // namespace

static void alloc_obj_size_8(benchmark::State& state) {
    std::vector<TestObject<8>*> vec;
    vec.resize(state.max_iterations);

    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<8>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<8>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<8>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<8>>(vec[i]);
    }
}

static void free_obj_size_8(benchmark::State& state) {
    std::vector<TestObject<8>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<8>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<8>>(vec[idx++]);
    }
}

static void alloc_obj_size_16(benchmark::State& state) {
    std::vector<TestObject<16>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<16>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<16>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<16>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<16>>(vec[i]);
    }
}

static void free_obj_size_16(benchmark::State& state) {
    std::vector<TestObject<16>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<16>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<16>>(vec[idx++]);
    }
}

static void alloc_obj_size_32(benchmark::State& state) {
    std::vector<TestObject<32>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<32>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<32>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<32>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<32>>(vec[i]);
    }
}

static void free_obj_size_32(benchmark::State& state) {
    std::vector<TestObject<32>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<32>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<32>>(vec[idx++]);
    }
}

static void alloc_obj_size_40(benchmark::State& state) {
    std::vector<TestObject<40>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<40>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<40>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<40>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<40>>(vec[i]);
    }
}

static void free_obj_size_40(benchmark::State& state) {
    std::vector<TestObject<40>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<40>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<40>>(vec[idx++]);
    }
}

static void alloc_obj_size_64(benchmark::State& state) {
    std::vector<TestObject<64>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<64>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<64>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<64>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<64>>(vec[i]);
    }
}

static void free_obj_size_64(benchmark::State& state) {
    std::vector<TestObject<64>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<64>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<64>>(vec[idx++]);
    }
}

static void alloc_obj_size_128(benchmark::State& state) {
    std::vector<TestObject<128>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<128>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<128>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<128>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<128>>(vec[i]);
    }
}

static void free_obj_size_128(benchmark::State& state) {
    std::vector<TestObject<128>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<128>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<128>>(vec[idx++]);
    }
}

static void alloc_obj_size_256(benchmark::State& state) {
    std::vector<TestObject<256>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<256>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<256>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<256>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<256>>(vec[i]);
    }
}

static void free_obj_size_256(benchmark::State& state) {
    std::vector<TestObject<256>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<256>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<256>>(vec[idx++]);
    }
}

static void alloc_obj_size_512(benchmark::State& state) {
    std::vector<TestObject<512>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<512>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<512>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<512>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<512>>(vec[i]);
    }
}

static void free_obj_size_512(benchmark::State& state) {
    std::vector<TestObject<512>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<512>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<512>>(vec[idx++]);
    }
}

static void alloc_obj_size_1024(benchmark::State& state) {
    std::vector<TestObject<1024>*> vec;
    vec.resize(state.max_iterations);
    for (size_t i = 0; i < state.max_iterations; ++i) {
        auto* obj = util::get_tls_object<TestObject<1024>>();
        vec[i] = obj;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        util::return_tls_object<TestObject<1024>>(vec[i]);
    }
    vec.clear();
    vec.resize(state.max_iterations);
    size_t idx = 0;

    for (auto _ : state) {
        auto* obj = util::get_tls_object<TestObject<1024>>();
        vec[idx++] = obj;
    }

    // clear resource
    for (size_t i = 0; i < vec.size(); i++) {
        util::return_tls_object<TestObject<1024>>(vec[i]);
    }
}

static void free_obj_size_1024(benchmark::State& state) {
    std::vector<TestObject<1024>*> vec;
    vec.resize(state.max_iterations);
    // prepare resource
    for (size_t i = 0; i < vec.size(); i++) {
        auto* obj = util::get_tls_object<TestObject<1024>>();
        vec[i] = obj;
    }

    size_t idx = 0;
    for (auto _ : state) {
        util::return_tls_object<TestObject<1024>>(vec[idx++]);
    }
}

BENCHMARK(alloc_obj_size_8)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_8)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_16)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_16)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_32)->Iterations(10000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_32)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_40)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_40)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_64)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_64)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_128)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_128)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_256)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_256)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_512)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_512)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK(alloc_obj_size_1024)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);
BENCHMARK(free_obj_size_1024)->Iterations(10000000)->Repetitions(20)->ReportAggregatesOnly(true);

BENCHMARK_MAIN();
