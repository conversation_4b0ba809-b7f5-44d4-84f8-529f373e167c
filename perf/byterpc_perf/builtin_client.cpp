// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <thread>

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/rpc.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/timestamp.h"
#include "proto/builtin_service.pb.h"

#include "absl/memory/memory.h"
#include "byte/stats/histogram.h"

DEFINE_int32(thread_num, 4, "Number of threads to send requests");
DEFINE_int32(attachment_size, 4096, "use attachment");
DEFINE_int32(num_size, 100, "Number of int in request");
DEFINE_int32(str_size, 10, "Number of string in request");
DEFINE_string(str, "abc", "Content of string in request");
DEFINE_int32(timeout_ms, 2000, "rpc timeout in ms");
DEFINE_int32(connect_timeout_ms, 1000, "connect timeout in ms");
DEFINE_uint32(protocol, 1, "1: PROTOCOL_BAIDU_STD; 2: PROTOCOL_BYTE_STD");
DEFINE_string(remote_addr, "0.0.0.0:8888", "Remote server address");
DEFINE_bool(use_byte_express, false, "enable rdma or not");
DEFINE_bool(use_tarzan, false, "enable tarzan or not");
DEFINE_uint32(depth, 1, "Number of inflight rpc");
DEFINE_uint64(rpc_num, 10000, "Number of requests per thread");

namespace byterpc {
DECLARE_int32(byterpc_tarzan_worker_num);
}  // namespace byterpc

class RpcCall {
public:
    RpcCall(std::vector<char>&& ch, byterpc::loop_type_t loop_type = byterpc::LOOP_UNTIL_QUIT)
        : _finished(false),
          _loop_type(loop_type),
          _builder(),
          _req(new byterpc::proto::PingRequest),
          _stub(),
          _channel(),
          _controller(),
          _count(0),
          _inflight(0),
          _ch(move(ch)) {
        _resp = new byterpc::proto::PingResponse[FLAGS_depth];
        _controller = new byterpc::Controller*[FLAGS_depth];
    }

    ~RpcCall() {
        delete _attachment;
        delete[] _resp;
        delete[] _controller;
    }

    bool Finished() const {
        return _finished;
    }

    void Init() {
        byterpc::Builder::ChannelOptions options;
        options._rpc_timeout_ms = FLAGS_timeout_ms;
        options._connect_timeout_ms = FLAGS_connect_timeout_ms;
        options._trans_type = byterpc::TYPE_KERNEL_TCP;
        if (FLAGS_use_tarzan) {
            options._trans_type = byterpc::TYPE_USERSPACE_TCP;
        }
        if (FLAGS_use_byte_express) {
            options._trans_type = byterpc::TYPE_RDMA;
        }
        _channel = _builder.BuildChannel(FLAGS_remote_addr, options);
        BYTERPC_CHECK(_channel);

        if (FLAGS_attachment_size > 0) {
            _attachment = new byterpc::IOBuf();
            _attachment->append(_ch.data(), FLAGS_attachment_size);
        }

        _stub.reset(new byterpc::proto::ping_Stub(_channel.get()));
    }

    void Run() {
        _start_timestamp = byterpc::util::TimeStamp::Now();
        for (uint32_t i = 0; i < FLAGS_depth; ++i) {
            IssueRPC(i);
        }
    }

    void IssueRPC(uint32_t index) {
        uint64_t start_timestamp = byterpc::util::TimeStamp::Now();
        _controller[index] = _builder.CreateSessionController(
            static_cast<byterpc::ProtocolType>(FLAGS_protocol), FLAGS_timeout_ms * 1000);

        if (FLAGS_attachment_size > 0) {
            _controller[index]->InstallOutgoingAttachment(*_attachment);
        }
        _controller[index]->SetLogId(_count);

        google::protobuf::Closure* done =
            ::byterpc::NewCallback<RpcCall, RpcCall*, uint32_t, uint64_t>(
                this, &RpcCall::Reset, index, start_timestamp);

        _stub->default_method(_controller[index], _req.get(), &_resp[index], done);
        ++_inflight;
        ++_count;
    }

    void Reset(uint32_t index, uint64_t start_timestamp) {
        --_inflight;
        _resp[index].Clear();
        if (_controller[index]->Failed()) {
            BYTERPC_LOG(WARNING) << "RPC call failed, error code: "
                                 << _controller[index]->ErrorCode()
                                 << ", error text: " << _controller[index]->ErrorText()
                                 << " _count = " << _count;
        }
        _hist.Add(byterpc::util::TimeStamp::DurationToUs(byterpc::util::TimeStamp::Now() -
                                                         start_timestamp));

        if (_count < FLAGS_rpc_num) {
            IssueRPC(index);
        } else if (0 == _inflight) {
            uint64_t comsumed_us = byterpc::util::TimeStamp::DurationToUs(
                byterpc::util::TimeStamp::Now() - _start_timestamp);
            BYTERPC_LOG(INFO) << "Qps is: " << FLAGS_rpc_num * 1000000 / comsumed_us;
            BYTERPC_LOG(INFO) << "Avg latency is: " << _hist.Average();
            BYTERPC_LOG(INFO) << "99th latency is: " << _hist.Percentile(99.0);
            BYTERPC_LOG(INFO) << "Median latency is: " << _hist.Median();
            BYTERPC_LOG(INFO) << "Client exit";
            // Signal to event loop that it can quit now;
            if (_loop_type == byterpc::LOOP_UNTIL_QUIT) {
                byterpc::ExecCtx::QuitLoop();
            } else {
                _finished = true;
            }
        }
    }

private:
    bool _finished;
    byterpc::loop_type_t _loop_type;
    byterpc::Builder _builder;
    std::unique_ptr<byterpc::proto::PingRequest> _req;
    byterpc::proto::PingResponse* _resp;
    std::unique_ptr<byterpc::proto::ping_Stub> _stub;
    std::shared_ptr<byterpc::Builder::Channel> _channel;
    byterpc::Controller** _controller;
    byte::HistogramImpl _hist;
    uint64_t _start_timestamp;
    uint32_t _count;
    uint32_t _inflight;
    std::vector<char> _ch;
    byterpc::IOBuf* _attachment;
};

static void StartClient(std::unique_ptr<RpcCall> call) {
    if (FLAGS_use_tarzan) {
        {
            byterpc::ExecCtx ctx(byterpc::LOOP_IF_POSSIBLE);
            call->Init();
            call->Run();
        }
        while (!call->Finished()) {
            byterpc::ExecCtx::LoopOnce();
        }
    } else {
        byterpc::ExecCtx ctx(byterpc::LOOP_UNTIL_QUIT);
        call->Init();
        call->Run();
        byterpc::ExecCtx::LoopUntilQuit();
    }
}

// Server: ./perf_server --start_builtin_server=true ...
int main(int argc, char* argv[]) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);
    BYTERPC_LOG(INFO) << "thread_num is: " << FLAGS_thread_num
                      << "; attachment_size is: " << FLAGS_attachment_size
                      << "; num_size is: " << FLAGS_num_size << "; str_size is: " << FLAGS_str_size
                      << "; str is: " << FLAGS_str;

    byterpc::InitOptions init_opt(false, false, "byterpc_test");
    if (FLAGS_use_tarzan) {
        byterpc::FLAGS_byterpc_tarzan_worker_num = FLAGS_thread_num;
        init_opt._init_utcp = true;
    } else if (FLAGS_use_byte_express)
        init_opt._init_rdma = true;
    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to init tarzan or rdma";
        return -1;
    }

    byterpc::loop_type_t loop_type =
        FLAGS_use_tarzan ? byterpc::LOOP_IF_POSSIBLE : byterpc::LOOP_UNTIL_QUIT;
    std::vector<std::unique_ptr<std::thread>> pids;
    std::vector<std::unique_ptr<RpcCall>> calls;
    for (int i = 0; i < FLAGS_thread_num; i++) {
        std::vector<char> ch(FLAGS_attachment_size, 'a');
        std::unique_ptr<RpcCall> call(new RpcCall(std::move(ch), loop_type));
        calls.push_back(std::move(call));
    }
    for (int i = 0; i < FLAGS_thread_num; i++) {
        std::unique_ptr<std::thread> th(new std::thread(StartClient, std::move(calls[i])));
        pids.push_back(std::move(th));
    }
    for (int i = 0; i < FLAGS_thread_num; i++) {
        pids[i]->join();
    }
    return 0;
}
