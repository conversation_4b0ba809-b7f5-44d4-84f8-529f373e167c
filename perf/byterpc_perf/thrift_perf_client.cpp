// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <cstring>
#include <memory>
#include <thread>
#include <vector>

#include "echo_types.h"

#include "byterpc/protocol/thrift/thrift_message.h"
#include "byterpc/thread/ev_thread_helper.h"
#include "byterpc/thread/polling_thread_helper.h"
#include "byterpc/util/timestamp.h"

#include "byte/stats/histogram.h"
#include "byte/string/algorithm.h"
#include "byte/string/number.h"

DEFINE_int32(thread_num, 4, "Number of threads to send requests");
DEFINE_int32(thread_num_per_port, 1, "Number of thread per port");
DEFINE_string(bind_cpu_id_list, "", "bind cpu id, if have, must equal to thread_num");
DEFINE_int32(attachment_size, 4096, "use attachment");
DEFINE_int32(timeout_ms, 2000, "rpc timeout in ms");
DEFINE_int32(connect_timeout_ms, 1000, "connect timeout in ms");
DEFINE_uint32(crc_mode, 0, "0: NONE 1: FULL 2: META_PB");
DEFINE_string(remote_addr, "0.0.0.0:18888", "Remote server address, multiple addr split by ,");
DEFINE_string(local_ip, "", "Local client ip address, multiple ip split by ,");
DEFINE_bool(use_byte_express, false, "enable rdma or not");
DEFINE_bool(use_tarzan, false, "enable tarzan or not");
DEFINE_bool(parse_hostname_to_ipv4, true, "parse hostname to ipv4 or ipv6");
DEFINE_uint32(depth, 1, "Number of inflight rpc");
DEFINE_uint64(rpc_num, 1000000, "Number of requests per thread");
DEFINE_bool(use_polling_mode, true, "rdma or tcp use polling mode or event triger mode");
DEFINE_bool(enable_histogram, true, "enable histogram or not");
DEFINE_bool(enable_connect_with_buildchannel, true, "enable connect when build channel");

namespace byterpc {
DECLARE_int32(byterpc_tarzan_worker_num);
DECLARE_int32(byterpc_metric_thread_num);
}  // namespace byterpc

template <typename T> inline T* get_pointer(T* p) {
    return p;
}

template <typename Class, typename Pointer, typename Arg1, typename Arg2>
class MyClosure : public ::google::protobuf::Closure {
public:
    typedef void (Class::*MethodType)(Arg1 arg1, Arg2 arg2);

    MyClosure(const Pointer& object, MethodType method) : object_(object), method_(method) {}
    ~MyClosure() {}

    void Run() {
        (get_pointer(object_)->*method_)(arg1_, arg2_);
    }

    Arg1 arg1_;
    Arg2 arg2_;

private:
    Pointer object_;
    MethodType method_;
};

class RpcCall {
public:
    using callback_t = MyClosure<RpcCall, RpcCall*, uint32_t, uint64_t>;
    RpcCall(byterpc::ThreadBase* th,
            const std::vector<byterpc::util::EndPoint> endpoints,
            int port_offset,
            const std::vector<byterpc::util::EndPoint> local_ips)
        : _builder(),
          _remote_eps(),
          _local_ips(local_ips),
          _thrift_req(new example::EchoRequest),
          _thrift_resp(),
          _th(th),
          _thrift_stub(),
          _channel(),
          _controller(),
          _rpc_done(nullptr),
          _timeout_us(FLAGS_timeout_ms * 1000),
          _crc_mode(static_cast<byterpc::CrcMode>(FLAGS_crc_mode)),
          _hist(),
          _start_timestamp(0),
          _count(0),
          _inflight(0),
          _attachment(nullptr) {
        _rpc_done = (callback_t*)new char[sizeof(callback_t) * FLAGS_depth];
        for (uint32_t i = 0; i < FLAGS_depth; i++) {
            new (&_rpc_done[i]) callback_t(this, &RpcCall::Reset);
        }
        for (size_t i = 0; i < endpoints.size(); ++i) {
            if (!endpoints[i].is_extended()) {
                _remote_eps.emplace_back(endpoints[i].ip, endpoints[i].port + port_offset);
            } else {
                std::string new_uds_path = FLAGS_remote_addr + "_" + std::to_string(port_offset);
                byterpc::util::EndPoint ep;
                if (0 != byterpc::util::str2endpoint(new_uds_path.c_str(), &ep)) {
                    BYTERPC_LOG(FATAL) << "UDS create addr failed";
                }
                _remote_eps.emplace_back(ep);
            }
        }

        _thrift_resp.resize(FLAGS_depth);
        _controller.resize(FLAGS_depth);
    }

    ~RpcCall() {
        delete[] reinterpret_cast<char*>(_rpc_done);
    }

    void Init() {
        byterpc::Builder::ChannelOptions options;
        options._rpc_timeout_ms = FLAGS_timeout_ms;
        options._connect_timeout_ms = FLAGS_connect_timeout_ms;
        options._trans_type = byterpc::TYPE_KERNEL_TCP;
        options._hostname_to_ipv4 = FLAGS_parse_hostname_to_ipv4;
        if (FLAGS_enable_connect_with_buildchannel) {
            options._build_connect_option = byterpc::Builder::ConnectOptions::ASYNC_CONNECT;
        }
        if (FLAGS_use_tarzan) {
            options._trans_type = byterpc::TYPE_USERSPACE_TCP;
        } else if (FLAGS_use_byte_express) {
            options._trans_type = byterpc::TYPE_RDMA;
        }

        BYTERPC_CHECK(_remote_eps.size() == 1 && _local_ips.size() <= 1);
        options._enable_fallback = false;
        byterpc::util::EndPoint local_ip =
            _local_ips.empty() ? byterpc::util::EndPoint() : _local_ips[0];
        _channel = _builder.BuildChannel(_remote_eps[0], local_ip, options);
        BYTERPC_CHECK(_channel);
        BYTERPC_LOG(INFO) << "Build channel successful: "
                          << " remote_side=" << _channel->ServerAddress()
                          << ", local_side=" << _channel->ClientAddress()
                          << ", trans_type=" << _channel->GetTransportType();

        _thrift_req->__set_data(std::string(FLAGS_attachment_size, 'a'));
        _thrift_stub = std::make_unique<byterpc::ThriftStub>(_channel);
    }

    void Run() {
        // _start_timestamp will be set after first rpc completed.
        for (uint32_t i = 0; i < FLAGS_depth; ++i) {
            IssueRPC(i);
        }
    }

    void IssueRPC(uint32_t index) {
        uint64_t start_timestamp = byterpc::util::TimeStamp::Now();
        byterpc::Builder::ControllerOptions opt{byterpc::PROTOCOL_THRIFT, _timeout_us, _crc_mode};
        _controller[index] = _builder.CreateSessionController(opt);
        BYTERPC_CHECK(_controller[index] != nullptr);
        _controller[index]->SetLogId(_count);

        if (UNLIKELY(_count == FLAGS_depth)) {
            _start_timestamp = start_timestamp;
        }

        ++_inflight;
        ++_count;

        _rpc_done[index].arg1_ = index;
        _rpc_done[index].arg2_ = start_timestamp;

        _thrift_stub->CallMethod(
            "Echo", _controller[index], _thrift_req.get(), &_thrift_resp[index], &_rpc_done[index]);
    }

    void Reset(uint32_t index, uint64_t start_timestamp) {
        --_inflight;
        uint64_t rpc_id = _controller[index]->LogId();
        if (_controller[index]->Failed()) {
            BYTERPC_LOG(WARNING) << "RPC call failed, error code: "
                                 << _controller[index]->ErrorCode()
                                 << ", error text: " << _controller[index]->ErrorText()
                                 << " rpc_id = " << rpc_id;
        }

        if (FLAGS_enable_histogram && LIKELY(rpc_id >= FLAGS_depth)) {
            _hist.Add(byterpc::util::TimeStamp::DurationToUs(byterpc::util::TimeStamp::Now() -
                                                             start_timestamp));
        }

        if (_count < FLAGS_rpc_num) {
            IssueRPC(index);
        } else if (0 == _inflight) {
            Quit();
        }
    }

    void Quit() {
        EchoOutput();
        _th->Stop();
        delete this;
    }

    void EchoOutput() {
        const uint64_t consumed_us = byterpc::util::TimeStamp::DurationToUs(
            byterpc::util::TimeStamp::Now() - _start_timestamp);
        BYTERPC_LOG(INFO) << "Qps is: " << (FLAGS_rpc_num - FLAGS_depth) * 1000000 / consumed_us;
        BYTERPC_LOG(INFO) << "Avg latency is: " << _hist.Average();
        BYTERPC_LOG(INFO) << "99th latency is: " << _hist.Percentile(99.0);
        BYTERPC_LOG(INFO) << "999th latency is: " << _hist.Percentile(99.9);
        BYTERPC_LOG(INFO) << "9999th latency is: " << _hist.Percentile(99.99);
        BYTERPC_LOG(INFO) << "max latency is: " << _hist.Percentile(100.0);
        BYTERPC_LOG(INFO) << "Median latency is: " << _hist.Median();
        BYTERPC_LOG(INFO) << "Client exit";
    }

private:
    byterpc::Builder _builder;
    std::vector<byterpc::util::EndPoint> _remote_eps;
    std::vector<byterpc::util::EndPoint> _local_ips;
    std::unique_ptr<example::EchoRequest> _thrift_req;
    std::vector<example::EchoResponse> _thrift_resp;
    byterpc::ThreadBase* _th;
    std::unique_ptr<byterpc::ThriftStub> _thrift_stub;
    std::shared_ptr<byterpc::Builder::Channel> _channel;
    std::vector<byterpc::Controller*> _controller;
    callback_t* _rpc_done;
    int64_t _timeout_us;
    byterpc::CrcMode _crc_mode;
    byte::HistogramImpl _hist;
    uint64_t _start_timestamp;
    uint32_t _count;
    uint32_t _inflight;
    std::unique_ptr<byterpc::IOBuf> _attachment;
};

static void StartClient(RpcCall* call) {
    call->Init();
    call->Run();
}

int main(int argc, char* argv[]) {
    // Because the first rpc contain the cost of connection building,
    // we need filter it out.
    // if we only fire one rpc, we may get divided by zero error.
    BYTERPC_CHECK(FLAGS_rpc_num >= 2);

    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);
    BYTERPC_LOG(INFO) << "thread_num is: " << FLAGS_thread_num << "; iodepth is: " << FLAGS_depth
                      << "; attachment_size is: " << FLAGS_attachment_size;

    // parse bind_cpu_id_list
    std::vector<std::string> cpu_ids;
    byte::SplitString(FLAGS_bind_cpu_id_list, ",", &cpu_ids);
    if (cpu_ids.size() && static_cast<int32_t>(cpu_ids.size()) != FLAGS_thread_num) {
        BYTERPC_LOG(ERROR) << "Cpu id number is not equal to thread num";
        return -1;
    }

    // parse remote_addr
    std::vector<std::string> remote_addr_strs;
    std::vector<byterpc::util::EndPoint> endpoints;
    byte::SplitString(FLAGS_remote_addr, ",", &remote_addr_strs);
    for (size_t i = 0; i < remote_addr_strs.size(); ++i) {
        byterpc::util::EndPoint ep;
        if (byterpc::util::str2endpoint(remote_addr_strs[i].c_str(), &ep) != 0) {
            BYTERPC_LOG(ERROR) << "Invalid remote_addr=" << remote_addr_strs[i];
            return -1;
        }
        // check uds path, it should be only 1 item in FLAGS_remote_addr
        if (ep.is_extended() && 1 != remote_addr_strs.size()) {
            BYTERPC_LOG(ERROR) << "UDS mode FLAGS_remote_addr should only include 1 item";
            return -1;
        }
        endpoints.push_back(ep);
    }
    BYTERPC_CHECK(!endpoints.empty());

    // parse local_ip
    std::vector<std::string> local_ip_strs;
    std::vector<byterpc::util::EndPoint> local_ips;
    byte::SplitString(FLAGS_local_ip, ",", &local_ip_strs);
    for (size_t i = 0; i < local_ip_strs.size(); ++i) {
        byterpc::util::EndPoint ep;
        if (byterpc::util::str2ip(local_ip_strs[i].c_str(), &ep.ip) != 0) {
            BYTERPC_LOG(ERROR) << "Invalid local_ip=" << local_ip_strs[i];
            return -1;
        }
        local_ips.push_back(ep);
    }

    // Set BE span_size to 2MB, enable large payload allocate
    GFLAGS_NAMESPACE::SetCommandLineOption("byterpc_byte_express_span_size", "2097152");

    // Global Init
    byterpc::InitOptions init_opt(false, false, "byterpc_test");
    if (FLAGS_use_tarzan) {
        byterpc::FLAGS_byterpc_tarzan_worker_num = FLAGS_thread_num;
        init_opt._init_utcp = true;
    } else if (FLAGS_use_byte_express) {
        init_opt._init_rdma = true;
    }

    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to init tarzan or rdma";
        return -1;
    }

    byterpc::loop_type_t loop_type =
        FLAGS_use_polling_mode ? byterpc::LOOP_IF_POSSIBLE : byterpc::LOOP_UNTIL_QUIT;
    byterpc::TransportType transport = byterpc::TYPE_KERNEL_TCP;
    if (FLAGS_use_tarzan) {
        transport = byterpc::TYPE_USERSPACE_TCP;
    } else if (FLAGS_use_byte_express) {
        transport = byterpc::TYPE_RDMA;
    }

    // Start Threads
    std::vector<std::unique_ptr<byterpc::ThreadBase>> ths;
    for (int i = 0; i < FLAGS_thread_num; i++) {
        std::unique_ptr<byterpc::ThreadBase> th;
        byterpc::ThreadOptions th_options;
        if (loop_type == byterpc::LOOP_IF_POSSIBLE) {
            th.reset(new byterpc::PollingThreadHelper());
        } else {
            th.reset(new byterpc::EvThreadHelper());
            if (transport == byterpc::TYPE_USERSPACE_TCP) {
                BYTERPC_LOG(ERROR) << "tarzan do not support event-trigger.";
                return -1;
            }
            th_options._transport = transport;
        }
        if (!cpu_ids.empty()) {
            byte::StringToNumber(cpu_ids[i], &th_options._cpu_id);
            th_options._thread_name = "rpc_th" + cpu_ids[i];
        }
        th->Init(th_options);
        th->Start();
        ths.push_back(std::move(th));
    }

    // send first rpc
    for (int i = 0; i < FLAGS_thread_num; i++) {
        int port_offset = i / FLAGS_thread_num_per_port;
        RpcCall* call = new RpcCall(ths[i].get(), endpoints, port_offset, local_ips);
        Closure<void>* closure = NewClosure(&StartClient, call);
        ths[i]->Invoke(closure);
    }

    for (size_t i = 0; i < ths.size(); i++) {
        ths[i]->Join();
    }

    return 0;
}
