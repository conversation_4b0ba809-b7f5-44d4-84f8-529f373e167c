// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/exceptions.h"
#include "hdfs/io/message_request.h"

namespace google {
namespace protobuf {
class Message;
}  // namespace protobuf
}  // namespace google

namespace bytestore {
namespace chunkserver {
namespace hdfs {

namespace io {
class IOBuf;
class IPAddress;
class Connection;
class Wrapper;
class Context;
}  // namespace io

namespace message {
class RpcResponseMessage;
}

class RPCMessage;

class ClientIO {
 public:
  ClientIO();
  ~ClientIO();

  bool Start();
  void Stop();
  void Wait();

  exceptions::Exception SendRequest(
      io::Method method, io::IPAddress* address,
      const ::google::protobuf::Message* request_header,
      const ::google::protobuf::Message* request,
      message::RpcResponseMessage** message, uint32_t timeout);

 private:
  std::string SerializeRPCHeader(int call_id);
  std::string SerializeConnectionContext();

  io::IOBuf* SerializeRequest(const ::google::protobuf::Message* rh,
                              const ::google::protobuf::Message* r);

  void WriteHandShake(io::IOBuf* buf);

  void WriteIpcConnection(io::IOBuf* buf);

  int DecodeRpcMessageSync(io::Connection* conn, io::Context* ctx,
                           int total_len, io::Wrapper** w);

  int32_t NextCallId();

  int GetConnection(io::IPAddress* address, uint32_t timeout,
                    io::Connection** conn);

  int WriteMessage(io::IPAddress* address, io::IOBuf* buf, uint32_t timeout,
                   io::Context* ctx, void** res);

 private:
  uint32_t call_id_;
  std::string client_id_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
