// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/exceptions.h"
#include "hdfs/ha_service_state.h"
#include "hdfs/proto/DatanodeProtocol.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class NNHAStatusHeartbeat {
 public:
  explicit NNHAStatusHeartbeat(HAServiceState state, int64_t txid);
  NNHAStatusHeartbeat();
  ~NNHAStatusHeartbeat() {}
  HAServiceState GetState() const {
    return state_;
  }
  int64_t GetTxid() const {
    return txid_;
  }
  static NNHAStatusHeartbeat ParseProto(
      const hadoop::hdfs::datanode::NNHAStatusHeartbeatProto& proto);

 private:
  HAServiceState state_;
  int64_t txid_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
