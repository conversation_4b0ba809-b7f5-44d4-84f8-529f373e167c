// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "byte/concurrent/hashtable.h"
#include "byte/concurrent/mutex.h"
#include "hdfs/block.h"
#include "hdfs/replica_info.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

using byte::concurrent::HashTable;
using ReplicaInfoPtr = std::shared_ptr<ReplicaInfo>;

class ReplicaMap {
 public:
  ReplicaMap();
  ~ReplicaMap();

  ReplicaInfoPtr Get(const std::string& blockpool_id,
                     const uint64_t& block_id) const;

  ReplicaInfoPtr Get(const std::string& blockpool_id, const Block& block) const;

  void Add(const std::string& blockpool_id, ReplicaInfoPtr replica_info);

  ReplicaInfoPtr Remove(const std::string& blockpool_id, const Block* block);

  ReplicaInfoPtr Remove(const std::string& blockpool_id,
                        const uint64_t& block_id);

  // just return if blockpool is not in this map
  void RemoveBlockPool(const std::string& blockpool_id);

  void GetReplicasFromBlockPool(const std::string& bpid,
                                std::vector<ReplicaInfoPtr>*) const;

  // return removed replicas
  std::vector<ReplicaInfoPtr> RemoveByDiskId(const std::string& bpid,
                                             uint32_t disk_id);

  void GetBlockPoolList(std::vector<std::string>* block_pool_list) const;

 private:
  std::unordered_map<std::string, HashTable<uint64_t, ReplicaInfoPtr>> map_;
  mutable byte::LiteRWLock bp_map_mutex_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
