// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "hdfs/hdfs_background_task_manager.h"

#include "byte/system/timestamp.h"
#include "common/periodical_task_executor.h"
#include "gflags/gflags.h"

DECLARE_uint32(bytestore_hdfs_background_task_manager_interval_seconds);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

BackgroundTaskManager::BackgroundTaskManager()
    : BackgroundTaskManager(
          1000ULL * 1000 *
          FLAGS_bytestore_hdfs_background_task_manager_interval_seconds) {}

BackgroundTaskManager::BackgroundTaskManager(uint64_t schedule_interval_us)
    : byte::PeriodicalThread("dn_bg_task_mgr") {
  SetInterval(schedule_interval_us);
}

BackgroundTaskManager::~BackgroundTaskManager() {}

void BackgroundTaskManager::AddTask(const std::string& name, bool is_once,
                                    int64_t interval_us,
                                    std::function<void()> callback) {
  byte::MutexLocker locker(&mutex_);
  BackgroundTask task;
  task.name_ = name;
  task.is_once_ = is_once;
  task.interval_us_ = interval_us;
  task.callback_ = callback;
  task.last_run_us_ = byte::GetCurrentTimeInUs();
  if (is_once) {
    once_tasks_.push(task);
  } else {
    tasks_.push_back(task);
  }
}

void BackgroundTaskManager::Execute() {
  byte::MutexLocker locker(&mutex_);
  LOG(DEBUG) << "back ground task manager is executing";
  HandleOnceTasks();
  HandleRepeatedTasks();
}

void BackgroundTaskManager::HandleOnceTasks() {
  while (!once_tasks_.empty()) {
    BackgroundTask earliest_task = once_tasks_.top();
    int64_t now = byte::GetCurrentTimeInUs();
    if (now < earliest_task.last_run_us_ + earliest_task.interval_us_) return;
    once_tasks_.pop();
    LOG(DEBUG) << "background once task:" << earliest_task.name_
               << " is running";
    earliest_task.callback_();
  }
}

void BackgroundTaskManager::HandleRepeatedTasks() {
  for (auto& task : tasks_) {
    int64_t now = byte::GetCurrentTimeInUs();
    if (now >= task.last_run_us_ + task.interval_us_) {
      LOG_EVERY_SECONDS(INFO, 300, 10)
          << "background repeated task:" << task.name_ << " is running";
      task.callback_();
      task.last_run_us_ = now;
    }
  }
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
