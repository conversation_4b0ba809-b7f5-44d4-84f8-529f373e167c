// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#ifndef WRITABLE_UTIL_H_
#define WRITABLE_UTIL_H_

#pragma once

#include <string>

namespace bytestore {
namespace chunkserver {
namespace hdfs {

int32_t DecodeVIntSize(const char& value);

bool IsNegativeVInt(const char& value);

bool ReadByte(const std::string& stream, int* position, char* result);

// reference from hadoop hdfs WritableUtils.java
bool ReadVLong(const std::string& stream, int* position, int64_t* result);

bool ReadVInt(const std::string& stream, int* position, int32_t* result);

bool ReadVIntInRange(const std::string& stream, int* position, int32_t* result,
                     const int32_t& lower, const int32_t& upper);

bool ReadInt(const std::string& stream, int* positon, int32_t* result);

bool ReadString(const std::string& stream, int* position, std::string* result);

bool ReadVString(const std::string& stream, int* postion, std::string* result);

std::string WriteByte(char c);

std::string WriteVLong(int64_t number);

std::string WriteVInt(int32_t number);

std::string Write(int32_t b);

std::string WriteInt(int32_t number);

std::string WriteString(const std::string& s);

std::string WriteVString(const std::string& str);

std::string StrToHexString(const std::string& str);

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore

#endif
