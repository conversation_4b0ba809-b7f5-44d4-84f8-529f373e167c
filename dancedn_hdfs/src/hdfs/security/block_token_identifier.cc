// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#include "hdfs/security/block_token_identifier.h"

#include "byte/include/byte_log.h"
#include "hdfs/security/writable_utils.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

std::string AccessModeToStr(const AccessMode& access_mode) {
  if (access_mode == AccessMode::READ) {
    return "READ";
  } else if (access_mode == AccessMode::WRITE) {
    return "WRITE";
  } else if (access_mode == AccessMode::COPY) {
    return "COPY";
  } else if (access_mode == AccessMode::REPLACE) {
    return "REPLACE";
  } else {
    return "UNKNOWN";
  }
}

const char* BlockTokenIdentifier::KIND_NAME = "HDFS_BLOCK_TOKEN";

std::string BlockTokenIdentifier::ToString() const {
  std::string res =
      "block_token_identifier (expireDate=" + std::to_string(GetExpireDate()) +
      ", keyId=" + std::to_string(GetKeyId()) + ", userId=" + GetUserId() +
      ", blockPoolId=" + GetBpid() +
      ", blockId=" + std::to_string(GetBlockId());
  std::string access_mode = ", access modes=";
  for (const auto& mode : access_modes_) {
    access_mode += mode + ',';
  }
  if (!access_modes_.empty()) {
    access_mode.pop_back();
  }
  return res + access_mode + ')';
}

bool BlockTokenIdentifier::Equals(
    const BlockTokenIdentifier& block_token_identifier) const {
  if (&block_token_identifier == this) {
    return true;
  }
  return block_token_identifier.expire_date_ == expire_date_ &&
         block_token_identifier.key_id_ == key_id_ &&
         block_token_identifier.user_id_ == user_id_ &&
         block_token_identifier.block_pool_id_ == block_pool_id_ &&
         block_token_identifier.block_id_ == block_id_ &&
         block_token_identifier.access_modes_ == access_modes_;
}

bool BlockTokenIdentifier::ReadFields(const std::string& stream) {
  LOG(DEBUG) << "receive token identifier bytes:" << StrToHexString(stream);
  int position = 0;
  auto success = ReadVLong(stream, &position, &expire_date_);
  if (!success) {
    LOG(WARNING) << "failed to call ReadVLong for expire_data,"
                 << "current status:" << ToString();
    return false;
  }
  success = ReadVInt(stream, &position, &key_id_);
  if (!success) {
    LOG(WARNING) << "failed to call ReadVInt for key_id,"
                 << "current status:" << ToString();
    return false;
  }
  success = ReadString(stream, &position, &user_id_);
  if (!success) {
    LOG(WARNING) << "failed to call ReadString for user_id,"
                 << "current status:" << ToString();
    return false;
  }
  success = ReadString(stream, &position, &block_pool_id_);
  if (!success) {
    LOG(WARNING) << "failed to call ReadString for bp_id,"
                 << "current status:" << ToString();
    return false;
  }
  int64_t temp_block_id = 0;
  success = ReadVLong(stream, &position, &temp_block_id);
  if (!success) {
    LOG(WARNING) << "failed to call ReadVLong for block_id,"
                 << "current status:" << ToString();
    return false;
  }
  if (temp_block_id < 0) {
    LOG(WARNING) << "error! get minus block_id,"
                 << "current status:" << ToString();
    return false;
  }
  block_id_ = static_cast<uint64_t>(temp_block_id);
  int32_t len = 0;
  // maybe ReadVIntInRange?
  success = ReadVInt(stream, &position, &len);
  if (!success) {
    LOG(WARNING) << "failed to call ReadVInt for access_modes length,"
                 << "current status:" << ToString();
    return false;
  }
  /// simplified from java version hdfs, so maybe problem?
  /// (lwj)byte array unit test passed
  for (int32_t i = 0; i < len; ++i) {
    std::string temp_result;
    success = ReadVString(stream, &position, &temp_result);
    if (!success) {
      LOG(WARNING) << "failed to call ReadString for access_mode,"
                   << "current status:" << ToString();
      return false;
    }
    access_modes_.emplace_back(temp_result);
  }
  return true;
}

std::string BlockTokenIdentifier::GetBytes() const {
  auto res = WriteVLong(expire_date_);
  res += WriteVInt(key_id_);
  res += WriteString(user_id_);
  res += WriteString(block_pool_id_);
  res += WriteVLong(block_id_);
  res += WriteVInt(access_modes_.size());
  /// simplified from java version hdfs, so maybe problem?
  /// (lwj)byte array unit test passed
  for (const auto& mode : access_modes_) {
    res += WriteVString(mode);
  }
  return res;
}

bool BlockTokenIdentifier::IsEmpty() const {
  return expire_date_ == 0 && key_id_ == 0 && user_id_.empty() &&
         block_pool_id_.empty() && block_id_ == 0 && access_modes_.empty();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
