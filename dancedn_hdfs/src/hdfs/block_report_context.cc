// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/block_report_context.h"

#include "hdfs/proto/DatanodeProtocol.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

exceptions::Exception BlockReportContext::ToProto(
    hadoop::hdfs::datanode::BlockReportContextProto* proto) const {
  proto->set_totalrpcs(total_rpcs_);
  proto->set_currpc(current_rpc_);
  proto->set_id(report_id_);
  return exceptions::Exception();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
