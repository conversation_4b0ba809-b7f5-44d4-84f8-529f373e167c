// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "byte/base/atomic.h"
#include "hdfs/io/ring_buffer.h"
#include "hdfs/thread.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

template <class T>
class Actor : public bytestore::chunkserver::hdfs::Thread {
  static_assert(!std::is_class<T>::value,
                "Actor<T>, only support basic type and pointer");

 public:
  explicit Actor(const std::function<void(T)>& closure, int mailbox,
                 const std::string& name = "");
  ~Actor();

  void Stop();

  int Length() {
    return length_;
  }

  void Tell(T message);

 private:
  bool Pick(T* message);
  void Run() override;

  template <
      typename TT = void,
      typename std::enable_if<!std::is_pointer<T>::value, TT>::type* = nullptr>
  void Cleanup() {}

  template <
      typename TT = void,
      typename std::enable_if<std::is_pointer<T>::value, TT>::type* = nullptr>
  void Cleanup() {
    T item;
    while (mailbox_.TryPop(&item)) {
      delete item;
    }
  }

 private:
  io::RingBuffer<T> mailbox_;
  std::function<void(T)> closure_;
  byte::Mutex tell_mutex_;
  ConditionVariable tell_cond_;
  byte::Mutex pick_mutex_;
  ConditionVariable pick_cond_;
  int length_;
};

template <class T>
Actor<T>::Actor(const std::function<void(T)>& closure, int mailbox,
                const std::string& name)
    : mailbox_(mailbox),
      closure_(closure),
      tell_mutex_(),
      tell_cond_(&tell_mutex_),
      pick_mutex_(),
      pick_cond_(&pick_mutex_),
      length_(0) {}

template <class T>
Actor<T>::~Actor() {
  Stop();
  Cleanup();
}

template <class T>
void Actor<T>::Run() {
  T item;
  while (Pick(&item)) {
    closure_(item);
    __sync_fetch_and_add(&length_, -1);
  }
}

template <class T>
void Actor<T>::Stop() {
  Thread::Stop();
  {
    byte::MutexLocker guard(&tell_mutex_);
    tell_cond_.Broadcast();
  }
  {
    byte::MutexLocker guard(&pick_mutex_);
    pick_cond_.Broadcast();
  }
}

template <class T>
void Actor<T>::Tell(T message) {
  {
    byte::MutexLocker guard(&tell_mutex_);
    while (!mailbox_.TryPush(message)) {
      tell_cond_.WaitUtil(
          [this]() {
            return IsStopped() || !mailbox_.Full();
          },
          0);
      if (IsStopped()) return;
    }
    byte::AtomicIncrement(&length_);
  }
  {
    byte::MutexLocker guard(&pick_mutex_);
    pick_cond_.Signal();
  }
}

template <class T>
bool Actor<T>::Pick(T* message) {
  {
    byte::MutexLocker lock(&pick_mutex_);
    while (!mailbox_.TryPop(message)) {
      pick_cond_.WaitUtil(
          [this]() {
            return IsStopped() || !mailbox_.Empty();
          },
          0);
      if (IsStopped()) return false;
    }
  }
  {
    byte::MutexLocker guard(&tell_mutex_);
    tell_cond_.Signal();
  }

  return true;
}

// ==================== ActorGroup =====================

template <class T, class Picker>
class ActorGroup {
  static_assert(!std::is_class<T>::value,
                "ActorGroup<T>, only support basic type and pointer");

 public:
  explicit ActorGroup(const std::function<void(T)>& fn, int n, int mailbox,
                      const std::string& name);
  ~ActorGroup();

  void Start();
  void Stop();

  void Tell(T message);
  int NumPending() const;

 private:
  Picker picker_;
  std::vector<Actor<T>*> actors_;
};

template <class T, class Picker>
ActorGroup<T, Picker>::ActorGroup(const std::function<void(T)>& fn, int n,
                                  int mailbox, const std::string& name)
    : picker_() {
  actors_.reserve(n);
  for (int i = 0; i < n; i++) {
    auto actor = new Actor<T>(fn, mailbox, name + "-" + std::to_string(i));
    actor->SetThreadPoolIndex(i);
    actors_.emplace_back(actor);
  }
}

template <class T, class Picker>
ActorGroup<T, Picker>::~ActorGroup() {
  Stop();
  for (size_t i = 0; i < actors_.size(); i++) {
    delete actors_[i];
  }
}

template <class T, class Picker>
void ActorGroup<T, Picker>::Start() {
  for (size_t i = 0; i < actors_.size(); i++) {
    actors_[i]->Start();
  }
}

template <class T, class Picker>
void ActorGroup<T, Picker>::Stop() {
  for (size_t i = 0; i < actors_.size(); i++) {
    actors_[i]->Stop();
  }
  for (size_t i = 0; i < actors_.size(); i++) {
    actors_[i]->Join();
  }
}

template <class T, class Picker>
void ActorGroup<T, Picker>::Tell(T message) {
  picker_.Next(actors_, message)->Tell(message);
}

template <class T, class Picker>
int ActorGroup<T, Picker>::NumPending() const {
  int n = 0;
  for (size_t i = 0; i < actors_.size(); i++) {
    n += actors_[i]->Length();
  }
  return n;
}

template <class T>
class RoundRobinPicker {
 public:
  RoundRobinPicker() : index_(0) {}
  Actor<T>* Next(const std::vector<Actor<T>*>& actors, const T) {
    uint32_t index = __sync_fetch_and_add(&index_, 1);
    return actors[index % actors.size()];
  }

 private:
  uint32_t index_;
};

template <class T>
class HashPicker {
 public:
  HashPicker() {}
  Actor<T>* Next(const std::vector<Actor<T>*>& actors, const T message) {
    return actors[message->HashCode() % actors.size()];
  }
};

template <class T>
class BalancePicker {
  // in multi-producer, maybe performance is worse than roundrobin
 public:
  BalancePicker() : last_index_(0) {}
  Actor<T>* Next(const std::vector<Actor<T>*>& actors, const T) {
    int index = last_index_;
    int min_index = last_index_;
    int min_length = actors[last_index_]->Length();
    do {
      int length = actors[index]->Length();
      if (length < min_length) {
        min_index = index;
        min_length = length;
      }
      index = (index + 1) % actors.size();
    } while (index != last_index_);

    last_index_ = (min_index + 1) % actors.size();
    return actors[min_index];
  }

 private:
  int last_index_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
