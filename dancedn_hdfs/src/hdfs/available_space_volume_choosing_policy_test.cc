// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/available_space_volume_choosing_policy.h"

#include <future>  // NOLINT
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "chunkserver/device_management.h"
#include "chunkserver/disk.h"
#include "chunkserver/disk_loader.h"
#include "chunkserver/env.h"
#include "common/async_thread.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "hdfs/exceptions.h"
#include "hdfs/mocks.h"
#include "hdfs/round_robin_volume_choosing_policy.h"

DECLARE_double(bytestore_hdfs_dn_balanced_space_preference_fraction_key);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class AvailableSpaceVolumeChoosingPolicyTests : public ::testing::Test {
 public:
  void SetUp() override {
    metrics_internal::InitFastMetrics();
    storage_ = std::make_shared<MockStore>();
    EXPECT_CALL(*storage_, GetDiskIoutil(::testing::_))
        .WillRepeatedly(::testing::Return(0.0));
    EXPECT_CALL(*storage_, AcquireActivity(::testing::_, ::testing::_))
        .WillRepeatedly(
            ::testing::DoAll(::testing::SetArgPointee<1>(true),
                             ::testing::Return(exceptions::Exception())));
    dn_interface_ = std::make_shared<MockDatanodeInterface>();
    EXPECT_CALL(*dn_interface_, GetFreeSizeInBytes(::testing::_, ::testing::_))
        .WillRepeatedly([](uint32_t id, uint64_t* size) {
          *size = id * 100 * 1024 * 1024 * 1024L;
          return BYTESTORE_OK;
        });
    work_dir_ = "./AvailableSpaceVolumeTestDir/";
    EXPECT_TRUE(
        local_fs_.CreateDir(std::string(work_dir_), byte::CreateOptions())
            .ok());
  }

  void TearDown() override {
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    local_fs_.DeleteDir(std::string(work_dir_), delete_options);
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  std::shared_ptr<MockStore> storage_{nullptr};
  std::shared_ptr<MockDatanodeInterface> dn_interface_{nullptr};
  std::string work_dir_;
  byte::LocalFileSystem local_fs_;
};

TEST_F(AvailableSpaceVolumeChoosingPolicyTests, BigBlockSize) {
  auto available_policy = std::make_shared<AvailableSpaceVolumeChoosingPolicy>(
      storage_.get(), dn_interface_);
  std::vector<Disk*> volumes;
  std::unique_ptr<AsyncThreadPool> bg_thread_pool(new AsyncThreadPool());
  bool ok = bg_thread_pool->Init(AsyncThreadPoolOptions());
  EXPECT_EQ(ok, true);
  ok = bg_thread_pool->Start();
  EXPECT_EQ(ok, true);
  std::unique_ptr<Env> env(new Env(work_dir_));
  const int kMaxDisk = 5;
  for (int i = 0; i < kMaxDisk; ++i) {
    DiskOptions options;
    options.disk_id_ = static_cast<DiskId>(i + 1);
    options.config_disk_nums_ = kMaxDisk;
    options.config_.disk_id_ = i;
    options.config_.disk_type_ = CDT_SATA_HDD;
    options.chunk_system_type_ = TYPE_KERNEL_CHUNK_SYSTEM;
    options.cpt_thread_ = bg_thread_pool->BaseThread();
    options.cpt_bg_thread_ = bg_thread_pool->BaseThread();
    options.disk_check_thread_ = bg_thread_pool->BaseThread();
    options.task_thread_pool_ = env->GetTaskThreadPool();
    options.callback_thread_pool_ = env->GetCallbackThreadPool();
    auto volume = new DiskImpl(options);
    volumes.emplace_back(volume);
  }

  DiskLoader loader(volumes);
  Errorcode load_rc = loader.Load(false);
  EXPECT_EQ(load_rc, BYTESTORE_OK);

  Disk* ans = nullptr;
  uint64_t block_size = 400 * 1024 * 1024 * 1024L;
  auto e = available_policy->ChooseVolume(StorageType::DISK, volumes,
                                          block_size, &ans);
  EXPECT_TRUE(e.OK());
  EXPECT_EQ(ans->GetDiskId(), 5);
  block_size = 500 * 1024 * 1024 * 1024L;
  e = available_policy->ChooseVolume(StorageType::DISK, volumes, block_size,
                                     &ans);
  EXPECT_FALSE(e.OK());
  for (auto volume : volumes) {
    volume->Stop();
    delete volume;
  }
}

TEST_F(AvailableSpaceVolumeChoosingPolicyTests, SmallBlockSize) {
  auto available_policy = std::make_shared<AvailableSpaceVolumeChoosingPolicy>(
      storage_.get(), dn_interface_);
  std::vector<Disk*> volumes;
  std::unique_ptr<AsyncThreadPool> bg_thread_pool(new AsyncThreadPool());
  bool ok = bg_thread_pool->Init(AsyncThreadPoolOptions());
  EXPECT_EQ(ok, true);
  ok = bg_thread_pool->Start();
  EXPECT_EQ(ok, true);
  std::unique_ptr<Env> env(new Env(work_dir_));
  const int kMaxDisk = 8;
  for (int i = 0; i < kMaxDisk; ++i) {
    DiskOptions options;
    options.disk_id_ = static_cast<DiskId>(i + 1);
    options.config_disk_nums_ = kMaxDisk;
    options.config_.disk_id_ = i;
    options.config_.disk_type_ = CDT_SATA_HDD;
    options.chunk_system_type_ = TYPE_KERNEL_CHUNK_SYSTEM;
    options.cpt_thread_ = bg_thread_pool->BaseThread();
    options.cpt_bg_thread_ = bg_thread_pool->BaseThread();
    options.disk_check_thread_ = bg_thread_pool->BaseThread();
    options.task_thread_pool_ = env->GetTaskThreadPool();
    options.callback_thread_pool_ = env->GetCallbackThreadPool();
    auto volume = new DiskImpl(options);
    volumes.emplace_back(volume);
  }

  DiskLoader loader(volumes);
  Errorcode load_rc = loader.Load(false);
  EXPECT_EQ(load_rc, BYTESTORE_OK);

  std::unordered_set<DiskId> chosen_volumes;
  Disk* ans = nullptr;
  uint64_t block_size = 40 * 1024 * 1024 * 1024L;
  for (int i = 0; i < 3000; ++i) {
    auto random_key = available_policy->GetCurrentRandomNumber();
    if (random_key <
        FLAGS_bytestore_hdfs_dn_balanced_space_preference_fraction_key) {
      // choose volumes with more free space
      auto e = available_policy->ChooseVolume(StorageType::DISK, volumes,
                                              block_size, &ans);
      EXPECT_TRUE(e.OK());
      EXPECT_GE(ans->GetDiskId(), 5);
    } else {
      auto e = available_policy->ChooseVolume(StorageType::DISK, volumes,
                                              block_size, &ans);
      EXPECT_TRUE(e.OK());
      EXPECT_LE(ans->GetDiskId(), 4);
    }
    chosen_volumes.insert(ans->GetDiskId());
  }
  EXPECT_EQ(chosen_volumes.size(), 8);
  for (auto volume : volumes) {
    volume->Stop();
    delete volume;
  }
}

TEST_F(AvailableSpaceVolumeChoosingPolicyTests, MiddleBlockSize) {
  auto available_policy = std::make_shared<AvailableSpaceVolumeChoosingPolicy>(
      storage_.get(), dn_interface_);
  std::vector<Disk*> volumes;
  std::unique_ptr<AsyncThreadPool> bg_thread_pool(new AsyncThreadPool());
  bool ok = bg_thread_pool->Init(AsyncThreadPoolOptions());
  EXPECT_EQ(ok, true);
  ok = bg_thread_pool->Start();
  EXPECT_EQ(ok, true);
  std::unique_ptr<Env> env(new Env(work_dir_));
  const int kMaxDisk = 8;
  for (int i = 0; i < kMaxDisk; ++i) {
    DiskOptions options;
    options.disk_id_ = static_cast<DiskId>(i + 1);
    options.config_disk_nums_ = kMaxDisk;
    options.config_.disk_id_ = i;
    options.config_.disk_type_ = CDT_SATA_HDD;
    options.chunk_system_type_ = TYPE_KERNEL_CHUNK_SYSTEM;
    options.cpt_thread_ = bg_thread_pool->BaseThread();
    options.cpt_bg_thread_ = bg_thread_pool->BaseThread();
    options.disk_check_thread_ = bg_thread_pool->BaseThread();
    options.task_thread_pool_ = env->GetTaskThreadPool();
    options.callback_thread_pool_ = env->GetCallbackThreadPool();
    auto volume = new DiskImpl(options);
    volumes.emplace_back(volume);
  }

  DiskLoader loader(volumes);
  Errorcode load_rc = loader.Load(false);
  EXPECT_EQ(load_rc, BYTESTORE_OK);

  std::unordered_set<DiskId> chosen_volumes;
  Disk* ans = nullptr;
  uint64_t block_size = 300 * 1024 * 1024 * 1024L;
  for (int i = 0; i < 3000; ++i) {
    auto random_key = available_policy->GetCurrentRandomNumber();
    if (random_key <
        FLAGS_bytestore_hdfs_dn_balanced_space_preference_fraction_key) {
      // choose volumes with more free space
      auto e = available_policy->ChooseVolume(StorageType::DISK, volumes,
                                              block_size, &ans);
      EXPECT_TRUE(e.OK());
      EXPECT_GE(ans->GetDiskId(), 5);
    } else {
      auto e = available_policy->ChooseVolume(StorageType::DISK, volumes,
                                              block_size, &ans);
      EXPECT_TRUE(e.OK());
      EXPECT_EQ(ans->GetDiskId(), 4);
    }
    chosen_volumes.insert(ans->GetDiskId());
  }
  EXPECT_EQ(chosen_volumes.size(), 5);
  for (auto volume : volumes) {
    volume->Stop();
    delete volume;
  }
}

TEST_F(AvailableSpaceVolumeChoosingPolicyTests, ChooseSSD) {
  auto available_policy = std::make_shared<AvailableSpaceVolumeChoosingPolicy>(
      storage_.get(), dn_interface_);
  std::vector<Disk*> volumes;
  std::unique_ptr<AsyncThreadPool> bg_thread_pool(new AsyncThreadPool());
  bool ok = bg_thread_pool->Init(AsyncThreadPoolOptions());
  EXPECT_EQ(ok, true);
  ok = bg_thread_pool->Start();
  EXPECT_EQ(ok, true);
  std::unique_ptr<Env> env(new Env(work_dir_));
  const int kMaxDisk = 8;
  for (int i = 0; i < kMaxDisk; ++i) {
    DiskOptions options;
    options.disk_id_ = static_cast<DiskId>(i + 1);
    options.config_disk_nums_ = kMaxDisk;
    options.config_.disk_id_ = i;
    options.config_.disk_type_ = CDT_NVME_SSD;
    options.chunk_system_type_ = TYPE_KERNEL_CHUNK_SYSTEM;
    options.cpt_thread_ = bg_thread_pool->BaseThread();
    options.cpt_bg_thread_ = bg_thread_pool->BaseThread();
    options.disk_check_thread_ = bg_thread_pool->BaseThread();
    options.task_thread_pool_ = env->GetTaskThreadPool();
    options.callback_thread_pool_ = env->GetCallbackThreadPool();
    auto volume = new DiskImpl(options);
    volumes.emplace_back(volume);
  }

  DiskLoader loader(volumes);
  Errorcode load_rc = loader.Load(false);
  EXPECT_EQ(load_rc, BYTESTORE_OK);

  std::unordered_set<DiskId> chosen_volumes;
  Disk* ans = nullptr;
  uint64_t block_size = 300 * 1024 * 1024 * 1024L;
  for (int i = 0; i < 3000; ++i) {
    auto random_key = available_policy->GetCurrentRandomNumber();
    if (random_key <
        FLAGS_bytestore_hdfs_dn_balanced_space_preference_fraction_key) {
      // choose volumes with more free space
      auto e = available_policy->ChooseVolume(StorageType::SSD, volumes,
                                              block_size, &ans);
      EXPECT_TRUE(e.OK());
      EXPECT_GE(ans->GetDiskId(), 5);
    } else {
      auto e = available_policy->ChooseVolume(StorageType::SSD, volumes,
                                              block_size, &ans);
      EXPECT_TRUE(e.OK());
      EXPECT_EQ(ans->GetDiskId(), 4);
    }
    chosen_volumes.insert(ans->GetDiskId());
  }
  EXPECT_EQ(chosen_volumes.size(), 5);
  for (auto volume : volumes) {
    volume->Stop();
    delete volume;
  }
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
