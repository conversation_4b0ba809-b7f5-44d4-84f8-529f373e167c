// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "hdfs/hdfs_background_task_manager.h"

#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "hdfs/datanode.h"

DECLARE_uint32(bytestore_hdfs_background_task_manager_interval_seconds);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

using BackgroundTask = BackgroundTaskManager::BackgroundTask;
using earlier = BackgroundTaskManager::earlier;

class HdfsBackgroundTaskManagerTests : public ::testing::Test {
 public:
  HdfsBackgroundTaskManagerTests() {}

  ~HdfsBackgroundTaskManagerTests() {}

  void SetUp() {
    FLAGS_bytestore_hdfs_background_task_manager_interval_seconds = 1;
  }

  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

void TestTaskExecute() {
  byte::ThisThread::SleepInMs(500);
  LOG(INFO) << "test task fnished.";
}

TEST_F(HdfsBackgroundTaskManagerTests, OnceTask) {
  int64_t now = byte::GetCurrentTimeInUs();
  // the expected time
  BackgroundTask task1;
  task1.name_ = "task1";
  task1.is_once_ = true;
  task1.interval_us_ = 100 * 1000;
  task1.last_run_us_ = now;
  BackgroundTask task2;
  task2.name_ = "task2";
  task2.is_once_ = true;
  task2.interval_us_ = 300 * 1000;
  task2.last_run_us_ = now - 100 * 1000;
  BackgroundTask task3;
  task3.name_ = "task3";
  task3.is_once_ = true;
  task3.interval_us_ = 300 * 1000;
  task3.last_run_us_ = now;
  ASSERT_TRUE(task1 < task2);
  ASSERT_TRUE(task2 < task3);

  std::priority_queue<BackgroundTask, std::vector<BackgroundTask>, earlier>
      once_tasks;
  once_tasks.push(task2);
  once_tasks.push(task3);
  once_tasks.push(task1);
  ASSERT_EQ(once_tasks.size(), 3);
  BackgroundTask earliest_task = once_tasks.top();
  ASSERT_EQ(earliest_task.name_, "task1");
  once_tasks.pop();
  ASSERT_EQ(once_tasks.size(), 2);
  earliest_task = once_tasks.top();
  ASSERT_EQ(earliest_task.name_, "task2");
  once_tasks.pop();
  ASSERT_EQ(once_tasks.size(), 1);
  earliest_task = once_tasks.top();
  ASSERT_EQ(earliest_task.name_, "task3");
  once_tasks.pop();
  ASSERT_TRUE(once_tasks.empty());
}

TEST_F(HdfsBackgroundTaskManagerTests, LifeCycle) {
  std::unique_ptr<BackgroundTaskManager> bg_task_manager(
      new BackgroundTaskManager());
  // task interval 1.5s, manager interval 1s
  bg_task_manager->AddTask("test_task", false, 1.5 * 1000 * 1000,
                           std::bind(&TestTaskExecute));
  LOG(INFO) << "back ground task manager is running";
  bg_task_manager->Start();
  // wait for task finished
  byte::ThisThread::SleepInMs(2800);
  bg_task_manager->Stop();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
