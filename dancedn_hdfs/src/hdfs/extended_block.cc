// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/extended_block.h"

#include <arpa/inet.h>

#include <vector>

#include "byte/string/format/print.h"
#include "common/memory_pool.h"
#include "hdfs/block.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

ExtendedBlock::ExtendedBlock() : pool_id_(""), block_(nullptr) {}

ExtendedBlock::ExtendedBlock(const std::string& pool_id, uint64_t block_id)
    : pool_id_(pool_id), block_(new Block(block_id, 0, 0)) {}

ExtendedBlock::ExtendedBlock(const std::string& pool_id, const Block* block)
    : pool_id_(pool_id), block_(block->Clone()) {}

ExtendedBlock::ExtendedBlock(const std::string& pool_id, uint64_t block_id,
                             uint64_t len, uint64_t gs)
    : pool_id_(pool_id), block_(new Block(block_id, len, gs)) {}

ExtendedBlock::ExtendedBlock(const ExtendedBlock& b) {
  pool_id_ = b.pool_id_;
  block_ = b.block_->Clone();
}

ExtendedBlock::~ExtendedBlock() {
  delete block_;
}

bool ExtendedBlock::Equals(const ExtendedBlock* b) {
  return block_->Equals(b->block_) && pool_id_ == b->pool_id_;
}

ExtendedBlock* ExtendedBlock::Clone() const {
  return new ExtendedBlock(pool_id_, block_);
}

ExtendedBlock* ExtendedBlock::GetChecksumBlock() const {
  ExtendedBlock* eb = new ExtendedBlock();
  eb->pool_id_ = pool_id_;
  eb->block_ = block_->GetChecksumBlock();
  return eb;
}

uint64_t ExtendedBlock::GetNumBytes() const {
  return block_->GetNumBytes();
}

void ExtendedBlock::SetNumBytes(uint64_t bytes) {
  block_->SetNumBytes(bytes);
}

std::string ExtendedBlock::ToString() const {
  return byte::StringPrint("%s:%s", pool_id_, block_->ToString());
}

ExtendedBlock* ExtendedBlock::ParseProto(
    const hadoop::hdfs::ExtendedBlockProto& proto) {
  return new ExtendedBlock(proto.poolid(), proto.blockid(), proto.numbytes(),
                           proto.generationstamp());
}

ExtendedBlock* ExtendedBlock::ParseProto(
    const hadoop::hdfs::ExtendedBlockProto& proto, MemoryPool* mem_pool) {
  auto b =
      mem_pool->New<ExtendedBlock>(proto.poolid(), proto.blockid(),
                                   proto.numbytes(), proto.generationstamp());
  return b;
}

exceptions::Exception ExtendedBlock::ToProto(
    hadoop::hdfs::ExtendedBlockProto* proto) const {
  proto->set_poolid(pool_id_);
  proto->set_blockid(block_->GetBlockID());
  proto->set_numbytes(block_->GetNumBytes());
  proto->set_generationstamp(block_->GetGS());
  return exceptions::Exception();
}

void ExtendedBlock::SetBlockPoolID(const std::string& bpid) {
  pool_id_ = bpid;
}

uint64_t ExtendedBlock::GetBlockID() const {
  return block_->GetBlockID();
}

void ExtendedBlock::SetBlockID(uint64_t block_id) {
  block_->SetBlockID(block_id);
}

uint64_t ExtendedBlock::GetGS() const {
  return block_->GetGS();
}

void ExtendedBlock::SetGS(uint64_t gs) {
  block_->SetGS(gs);
}

bool ExtendedBlock::IsChecksum() {
  return block_->IsChecksum();
}

bool ExtendedBlock::GetChunkIdMeta(ChunkIdMeta* chunk_id_meta) const {
  std::string bpid = GetBlockPoolID();
  auto piece = byte::StringPiece(bpid);
  std::vector<std::string> elems;
  byte::SplitStringByAnyOf(piece, "-", &elems);
  if (elems.size() == 4 && elems.front() == "BP") {
    chunk_id_meta->block_id_ = GetBlockID();
    chunk_id_meta->random_ = strtoul(elems.at(1).c_str(), nullptr, 0);
    auto x = inet_addr(elems.at(2).c_str());
    chunk_id_meta->ip_ = ntohl(x);
    chunk_id_meta->timestamp_ = strtoull(elems.at(3).c_str(), nullptr, 0);
    chunk_id_meta->is_checksum_ = block_->IsChecksum();
    return true;
  }
  return false;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
