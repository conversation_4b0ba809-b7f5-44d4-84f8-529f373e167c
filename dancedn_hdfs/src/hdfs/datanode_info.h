// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <memory>
#include <string>
#include <vector>

#include "hdfs/exceptions.h"
#include "hdfs/node.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DatanodeID;

// org.apache.hadoop.hdfs.protocol.DatanodeInfo.java
class DatanodeInfo : public Node {
 public:
  enum AdminStates {
    NORMAL = 0,
    DECOMMISSION_INPROGRESS = 1,
    DECOMMISSIONED = 2,
  };

  static std::string AdminStatesToString(AdminStates state);
  hadoop::hdfs::DatanodeInfoProto_AdminState AdminStatesToProto() const;
  static AdminStates AdminStatesParseProto(
      const hadoop::hdfs::DatanodeInfoProto_AdminState& state);

 public:
  explicit DatanodeInfo(std::shared_ptr<DatanodeID> datanode);
  DatanodeInfo(std::shared_ptr<DatanodeID> datanode,
               const std::string& location);
  DatanodeInfo(std::shared_ptr<DatanodeID> datanode,
               const std::string& location, uint64_t capacity,
               uint64_t dfs_used, uint64_t remaining, uint64_t block_pool_used,
               uint64_t last_update, uint32_t xceiver_count,
               AdminStates admin_state);
  ~DatanodeInfo();

  DatanodeInfo* Clone() const;

  // use shared_ptr
  // DatanodeID* mutable_datanode_id() const { return datanode_id_; }

  std::shared_ptr<DatanodeID> GetDatanodeID() const {
    return datanode_id_;
  }

  std::string GetSoftwareVersion() const {
    return software_version_;
  }
  void SetSoftwareVersion(const std::string& software_version) {
    software_version_ = software_version;
  }

  const std::vector<std::string>& GetDependentHostnames() const {
    return dependent_hostnames_;
  }
  void SetDependentHostnames(
      const std::vector<std::string>& dependent_hostnames) {
    dependent_hostnames_ = dependent_hostnames;
  }

  void SetXceiverCount(uint32_t xceiver_count) {
    xceiver_count_ = xceiver_count;
  }

  uint64_t GetCapacity() const {
    return capacity_;
  }
  void SetCapacity(uint64_t capacity) {
    capacity_ = capacity;
  }
  uint64_t GetDfsUsed() const {
    return dfs_used_;
  }
  void SetDfsUsed(uint64_t dfs_used) {
    dfs_used_ = dfs_used;
  }
  uint64_t GetNonDfsUsed() const {
    return non_dfs_used_;
  }
  void SetNonDfsUsed(uint64_t non_dfs_used) {
    non_dfs_used_ = non_dfs_used;
  }
  uint64_t GetRemaining() const {
    return remaining_;
  }
  void SetRemaining(uint64_t remaining) {
    remaining_ = remaining;
  }
  uint64_t GetBlockPoolUsed() const {
    return block_pool_used_;
  }
  void SetBlockPoolUsed(uint64_t block_pool_used) {
    block_pool_used_ = block_pool_used;
  }
  uint64_t GetLastUpdate() const {
    return last_update_;
  }
  void SetLastUpdate(uint64_t update_time) {
    last_update_ = update_time;
  }
  uint32_t GetXceiverCount() const {
    return xceiver_count_;
  }
  uint64_t GetAvgIOUtilMs() const {
    return avg_io_util_ms_;
  }
  void SetAvgIOUtilMs(uint64_t ms) {
    avg_io_util_ms_ = ms;
  }

  AdminStates GetAdminState() const {
    return admin_state_;
  }

  //  implement Node
  std::string GetNetworkLocation() const override {
    return location_;
  }
  bool SetNetworkLocation(const std::string& location) override;

  std::string GetName() const override;

  Node* GetParent() const override {
    return parent_;
  }
  void SetParent(Node* parent) override {
    parent_ = parent;
  }

  std::string GetPath() const override {
    return path_;
  }
  void UpdatePath();

  int GetLevel() const override {
    return level_;
  }
  void SetLevel(int level) override {
    level_ = level;
  }

  void StartDecommission();
  void StopDecommission();
  void SetDecommissioned();
  bool IsDecommissionInProgress() const;
  bool IsDecommissioned() const;
  static DatanodeInfo* ParseProto(const hadoop::hdfs::DatanodeInfoProto& proto);
  exceptions::Exception ToProto(hadoop::hdfs::DatanodeInfoProto* proto) const;

  std::string ToString() const;

 private:
  std::shared_ptr<DatanodeID> datanode_id_;
  uint64_t capacity_;
  uint64_t dfs_used_;
  uint64_t non_dfs_used_;
  uint64_t remaining_;
  uint64_t block_pool_used_;
  uint64_t last_update_;
  uint32_t xceiver_count_;
  uint64_t avg_io_util_ms_;

  AdminStates admin_state_;
  std::string software_version_;
  std::vector<std::string> dependent_hostnames_;

  // for Node
  int level_;
  Node* parent_;
  std::string path_;
  std::string location_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
