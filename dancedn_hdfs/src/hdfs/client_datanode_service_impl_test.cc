// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/client_datanode_service_impl.h"

#include <deque>
#include <fstream>
#include <iostream>
#include <memory>
#include <set>
#include <unordered_map>
#include <vector>

#include "byte/io/local_filesystem.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "hdfs/block.h"
#include "hdfs/block_local_path_info.h"
#include "hdfs/block_pool_actor.h"
#include "hdfs/block_pool_manager.h"
#include "hdfs/block_pool_service.h"
#include "hdfs/constants.h"
#include "hdfs/data_transfer_throttler.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_config.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/datanode_storage.h"
#include "hdfs/directory_scanner.h"
#include "hdfs/extended_block.h"
#include "hdfs/finalized_replica.h"
#include "hdfs/hdfs_blocks_metadata.h"
#include "hdfs/message/get_block_local_path_info_message.h"
#include "hdfs/message/get_blocks_message.h"
#include "hdfs/message/get_hdfs_block_locations_message.h"
#include "hdfs/message/get_replica_visible_length_message.h"
#include "hdfs/message/get_replica_visible_length_v2_message.h"
#include "hdfs/message/init_replica_recovery_message.h"
#include "hdfs/message/refresh_namenodes_message.h"
#include "hdfs/message/rpc_request_message.h"
#include "hdfs/message/rpc_response_message.h"
#include "hdfs/message/shutdown_datanode_message.h"
#include "hdfs/message/trigger_block_report_message.h"
#include "hdfs/message/update_replica_under_recovery_message.h"
#include "hdfs/mocks.h"
#include "hdfs/namespace_info.h"
#include "hdfs/proto/ClientNamenodeProtocol.pb.h"
#include "hdfs/proto/InterDatanodeProtocol.pb.h"
#include "hdfs/proto/ProtobufRpcEngine.pb.h"
#include "hdfs/proto/RpcHeader.pb.h"
#include "hdfs/recovering_block.h"
#include "hdfs/replica_in_pipeline.h"
#include "hdfs/replica_info.h"
#include "hdfs/replica_recovery_info.h"
#include "hdfs/replica_state.h"
#include "hdfs/store.h"

namespace {
// Prepare configuration contents; should be saved as an individual file in
// future.
static const std::string& k_test_content = R"(
{
    "BlockReportInterval": 60,
    "InitialBlockReportDelay": 300,
    "MinimunNameNodeVersion": "2.6.0",
    "DeleteReportInterval": 50,
    "HeartBeatInterval": 70,
    "FailoverBlockReportDelay": 80,
    "BlockReportSplitThreshold": 99,
    "SocketKeepaliveTimeout": 120,
    "SocketTimeout": 120,
    "IOFileBufferSize":4096,
    "EstimateBlockSize": 51200,
    "RestartReplicaExpiry": 333,
    "DatanodeSlowIoWarningThresholdMs": 334,
    "SyncBehindWrites": false,
    "GetSyncBehindWriteInBackground": true
})";

static const std::string& k_test_nn_content = R"(
{
    "Namenodes": {
         "ns0": {
            "nn1":"5061",
            "nn0":"8081"
         },
         "ns1": {
            "nn3":"5061"
         }
    },
    "Version": "1.0.0.1"
})";

static const char BLOCK_POOL_ID[] = "BP-2026362776-192.168.6.101-1546064706393";
static const char EXCEPTION_BLOCK_POOL_ID[] =
    "BP-2026362776-192.168.6.101-6666666666666";
static const char LOCAL_BLOCK_PATH[] = "/data01/yarn/dndata/subdir01";
static const char LOCAL_META_PATH[] = "/data01/yarn/dndata/meta";
static const char STORAGE_UUID[] = "1234565667";
static const char EXCEPTION_STORAGE_UUID[] = "666666666";
static const char DATANODE_UUID[] = "1234";

static const int32_t CALL_ID = 1;
static const char CLIENT_ID[] = "my client";
static const int32_t RETRY_COUNT = -1;

static const uint64_t FINALIZED_REPLICA_BLOCK_ID = 123456;
static const uint64_t TEMPORARY_REPLICA_BLOCK_ID = 123457;

static const int64_t FINALIZED_REPLICA_LENGTH = 128;
static const int64_t TEMPORARY_REPLICA_LENGTH = -1;

static const uint64_t IO_EXCEPTION_BLOCK_ID = 654321;
static const char IO_EXCEPTION_ERR[] = "generation stamp error";
static const char IO_EXCEPTION_ERROR_MESSAGE[] =
    "java.io.IOException: generation stamp error";
static const char IO_EXCEPTION_CLASS_NAME[] = "java.io.IOException";

static const uint64_t REPLICA_NOT_FOUND_EXCEPTION_BLOCK_ID = 654322;
static const char REPLICA_NOT_FOUND_EXCEPTION_ERR[] =
    "Cannot append to a non-existent replica";
static const char REPLICA_NOT_FOUND_EXCEPTION_ERROR_MESSAGE[] =
    "org.apache.hadoop.hdfs.server.datanode.ReplicaNotFoundException: "
    "Cannot append to a non-existent replica";
static const char REPLICA_NOT_FOUND_EXCEPTION_CLASS_NAME[] =
    "org.apache.hadoop.hdfs.server.datanode.ReplicaNotFoundException";

static const char NULLPOINT_EXCEPTION_CLASS_NAME[] =
    "java.lang.NullPointerException";
static const char STORAGE_NOT_INITIAL_ERROR_MESSAGE[] =
    "java.lang.NullPointerException: Storage not yet initialized";

static const char UNSUPPORTED_OPERATION_EXCEPTION_CLASS_NAME[] =
    "java.lang.UnsupportedOperationException";
static const char UNSUPPORTED_OPERATION_EXCEPTION_ERROR_MESSAGE[] =
    "java.lang.UnsupportedOperationException: "
    "Datanode#GetHdfsBlocksMetadata is not enabled in datanode config";

static const char ILLEGAL_ARGUMENT_EXCEPTION_CLASS_NAME[] =
    "java.lang.IllegalArgumentException";
static const char ILLEGAL_ARGUMENT_EXCEPTION_ERROR_MESSAGE[] =
    "java.lang.IllegalArgumentException: "
    "BUG: StorageType not found, type=7";

static const char RECOVERY_IN_PROGRESS_EXCEPTION_CLASS_NAME[] =
    "org.apache.hadoop.hdfs.protocol.RecoveryInProgressException";

static const char CORRUPTED_VOLUME_MAP[] =
    "java.io.IOException: VolumeMap is corrupted";

static const char bp_id[] = "tbpi";
static const uint64_t blk_gs0 = 1;
static const uint64_t blk_gs1 = 2;
static const uint64_t blk_id = 20190828;
static const uint64_t blk_len = 4096;
std::unique_ptr<bytestore::chunkserver::hdfs::ExtendedBlock> test_block(
    new bytestore::chunkserver::hdfs::ExtendedBlock(bp_id, blk_id, blk_len,
                                                    blk_gs0));

// DatanodeIDs
static const char ip_addr[] = "127.0.0.1";
static const char hostname[] = "localhost";
static const char uuid[] = "duuid001";
static const int xfer_port = 5060;
static const int info_port = 5061;
static const int ipc_port = 5062;
std::shared_ptr<bytestore::chunkserver::hdfs::DatanodeID> local_dn_id(
    new bytestore::chunkserver::hdfs::DatanodeID(ip_addr, hostname, uuid,
                                                 xfer_port, info_port, ipc_port,
                                                 0));

static const char ip_addr1[] = "127.0.0.1";
static const char hostname1[] = "remote";
static const char uuid1[] = "duuid002";
std::shared_ptr<bytestore::chunkserver::hdfs::DatanodeID> remote_dn_id1(
    new bytestore::chunkserver::hdfs::DatanodeID(ip_addr1, hostname1, uuid1,
                                                 xfer_port, info_port, ipc_port,
                                                 0));

static const char ip_addr2[] = "127.0.0.1";
static const char hostname2[] = "remote";
static const char uuid2[] = "duuid002";
std::shared_ptr<bytestore::chunkserver::hdfs::DatanodeID> remote_dn_id2(
    new bytestore::chunkserver::hdfs::DatanodeID(ip_addr2, hostname2, uuid2,
                                                 xfer_port, info_port, ipc_port,
                                                 0));

std::vector<bytestore::chunkserver::hdfs::DatanodeInfo*> dn_infos = {
    new bytestore::chunkserver::hdfs::DatanodeInfo(local_dn_id),
    new bytestore::chunkserver::hdfs::DatanodeInfo(remote_dn_id1),
    new bytestore::chunkserver::hdfs::DatanodeInfo(remote_dn_id2)};

std::unique_ptr<bytestore::chunkserver::hdfs::RecoveringBlock> rb(
    new bytestore::chunkserver::hdfs::RecoveringBlock(test_block->Clone(),
                                                      dn_infos, blk_gs1));

static const char sid[] = "suuid";

}  // namespace

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class MockStorage : public MockStore {
 public:
  MockStorage() : uuid_(), storage_("mock") {}
  ~MockStorage() {}

  std::string GetDatanodeUUID() const {
    return uuid_;
  }

  StorageInfo* GetBPStorage(const std::string& pool_id) const {
    return nullptr;
  }

  void SetUUID(const std::string& uuid) {
    uuid_ = uuid;
  }

  exceptions::Exception GetReplicaVisibleLength(const ExtendedBlock& block,
                                                int64_t* res) const {
    uint64_t block_id = block.GetBlock()->GetBlockID();
    switch (block_id) {
      case IO_EXCEPTION_BLOCK_ID:
        return exceptions::Exception(exceptions::kIOException,
                                     IO_EXCEPTION_ERR);
      case REPLICA_NOT_FOUND_EXCEPTION_BLOCK_ID:
        return exceptions::Exception(exceptions::kReplicaNotFoundException,
                                     REPLICA_NOT_FOUND_EXCEPTION_ERR);
      case FINALIZED_REPLICA_BLOCK_ID: *res = FINALIZED_REPLICA_LENGTH; break;
      case TEMPORARY_REPLICA_BLOCK_ID: *res = TEMPORARY_REPLICA_LENGTH; break;
    }
    return exceptions::Exception();
  }

  exceptions::Exception GetReplicaVisibleLengthV2(
      const ExtendedBlock& block, int64_t* res, int32_t* replica_state) const {
    uint64_t block_id = block.GetBlock()->GetBlockID();
    switch (block_id) {
      case IO_EXCEPTION_BLOCK_ID:
        return exceptions::Exception(exceptions::kIOException,
                                     IO_EXCEPTION_ERR);
      case FINALIZED_REPLICA_BLOCK_ID:
        *res = FINALIZED_REPLICA_LENGTH;
        *replica_state = ReplicaState::FINALIZED;
        break;
    }
    return exceptions::Exception();
  }

  exceptions::Exception GetBlockLocalPathInfo(const ExtendedBlock& block,
                                              BlockLocalPathInfo* res) const {
    if (block.GetBlock()->GetBlockID() == IO_EXCEPTION_BLOCK_ID) {
      return exceptions::Exception(exceptions::kIOException, IO_EXCEPTION_ERR);
    }
    res->SetBlock(block);
    res->SetBlockPath(LOCAL_BLOCK_PATH);
    res->SetMetaPath(LOCAL_META_PATH);
    return exceptions::Exception();
  }

  exceptions::Exception GetHdfsBlocksMetadata(
      const std::string& pool_id, const std::vector<uint64_t>& block_ids,
      HdfsBlocksMetadata* res) const {
    if (block_ids.size() == 0) {
      return exceptions::Exception(exceptions::kIOException, IO_EXCEPTION_ERR);
    }
    res->SetBlockPoolId(pool_id);
    res->SetBlockIds(block_ids);
    std::vector<std::vector<uint8_t>> volume_ids;
    volume_ids.push_back({65, 66, 67});
    volume_ids.push_back({68, 69, 70});
    volume_ids.push_back({71, 72, 73});
    res->SetVolumeIds(volume_ids);

    std::vector<uint32_t> volume_indexes;
    for (size_t i = 0; i < block_ids.size(); i++) {
      volume_indexes.emplace_back(i % 3);
    }
    res->SetVolumeIndexes(volume_indexes);
    return exceptions::Exception();
  }

  std::vector<std::shared_ptr<FinalizedReplica>>
  GetFinalizedBlocksOnPersistentStorage(
      const std::string& block_pool_id) const {
    std::vector<std::shared_ptr<FinalizedReplica>> replicas;
    if (block_pool_id == EXCEPTION_BLOCK_POOL_ID) {
      for (int i = 0; i < 3; i++) {
        ExtendedBlock block(block_pool_id, 123456 + i, 128, 123456 + i);
        auto replica = std::make_shared<FinalizedReplica>(
            &block, EXCEPTION_STORAGE_UUID, false, false);
        replicas.push_back(replica);
      }
      return replicas;
    }

    for (int i = 0; i < 3; i++) {
      ExtendedBlock block(block_pool_id, 123456 + i, 128, 123456 + i);
      auto replica = std::make_shared<FinalizedReplica>(&block, STORAGE_UUID,
                                                        false, false);
      replicas.push_back(replica);
    }
    return replicas;
  }

  StorageType GetStorageType(const std::string& storage_uuid) const {
    if (storage_uuid == EXCEPTION_STORAGE_UUID) {
      return StorageType::NULLTYPE;
    }
    return StorageType::DISK;
  }

  exceptions::Exception InitReplicaRecovery(const RecoveringBlock* rblock,
                                            ReplicaRecoveryInfo** rinfo) {
    Block* blk = rblock->GetBlock()->GetBlock();
    *rinfo = new ReplicaRecoveryInfo(blk->GetBlockID(), blk->GetNumBytes(),
                                     blk->GetGS(), ReplicaState::FINALIZED);
    return exceptions::Exception();
  }

  exceptions::Exception UpdateReplicaUnderRecovery(
      const ExtendedBlock* old_block, uint64_t recovery_id, uint64_t new_length,
      std::string* storage_id) {
    *storage_id = sid;
    return exceptions::Exception();
  }

 private:
  std::string uuid_;
  DatanodeStorage storage_;
};

class ClientDatanodeServiceImplTests : public ::testing::Test {
 public:
  ClientDatanodeServiceImplTests() {}
  ~ClientDatanodeServiceImplTests() {}

  hadoop::common::RpcRequestHeaderProto* CreateRpcHeader() {
    // construct rpc request header proto
    auto rpc_header = new hadoop::common::RpcRequestHeaderProto;
    rpc_header->set_callid(CALL_ID);
    rpc_header->set_clientid(CLIENT_ID);
    rpc_header->set_retrycount(RETRY_COUNT);
    auto baggage = rpc_header->add_baggages();
    baggage->set_name("user");
    baggage->set_value("name");
    return rpc_header;
  }

  void SetUp() {}
  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLength) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // finalized replica
  // construct rpc request header proto
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();
  auto request_header = new hadoop::common::RequestHeaderProto();

  // construct getReplicaVisibleLengthRequestProto
  auto request_body = new hadoop::hdfs::GetReplicaVisibleLengthRequestProto();
  auto block = new hadoop::hdfs::ExtendedBlockProto();
  block->set_blockid(FINALIZED_REPLICA_BLOCK_ID);
  request_body->set_allocated_block(block);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getReplicaVisibleLength");
  auto m = static_cast<message::GetReplicaVisibleLengthMessage*>(message);
  m->RpcHeader(rpc_header);
  m->RequestHeader(request_header);
  m->SetRequestBody(request_body);
  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::GetReplicaVisibleLengthResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->length(), FINALIZED_REPLICA_LENGTH);

  delete response;

  // temporary replica
  // construct rpc request header proto
  rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();
  request_header = new hadoop::common::RequestHeaderProto();

  // construct getReplicaVisibleLengthRequestProto
  request_body = new hadoop::hdfs::GetReplicaVisibleLengthRequestProto();
  block = new hadoop::hdfs::ExtendedBlockProto();
  block->set_blockid(TEMPORARY_REPLICA_BLOCK_ID);
  request_body->set_allocated_block(block);

  // construct RpcRequestMessage
  message = impl.NewRequest("getReplicaVisibleLength");
  m = static_cast<message::GetReplicaVisibleLengthMessage*>(message);
  m->RpcHeader(rpc_header);
  m->RequestHeader(request_header);
  m->SetRequestBody(request_body);

  response = impl.CallMethod(m);
  resp_header = response->RpcHeader();
  resp_body =
      static_cast<const hadoop::hdfs::GetReplicaVisibleLengthResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  auto len = resp_body->length();
  auto length = *reinterpret_cast<int64_t*>(&len);
  EXPECT_EQ(length, TEMPORARY_REPLICA_LENGTH);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLengthV2) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // finalized replica
  // construct rpc request header proto
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();
  auto request_header = new hadoop::common::RequestHeaderProto();

  // construct getReplicaVisibleLengthRequestProto
  auto request_body = new hadoop::hdfs::GetReplicaVisibleLengthV2RequestProto();
  auto block = new hadoop::hdfs::ExtendedBlockProto();
  block->set_blockid(FINALIZED_REPLICA_BLOCK_ID);
  request_body->set_allocated_block(block);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getReplicaVisibleLengthV2");
  auto m = static_cast<message::GetReplicaVisibleLengthV2Message*>(message);
  m->RpcHeader(rpc_header);
  m->RequestHeader(request_header);
  m->SetRequestBody(request_body);
  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::GetReplicaVisibleLengthV2ResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->length(), FINALIZED_REPLICA_LENGTH);
  EXPECT_EQ(resp_body->state(), ReplicaState::FINALIZED);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLengthWithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // ReplicaNotFoundException
  auto ex_rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  auto ex_block = new hadoop::hdfs::ExtendedBlockProto();
  ex_block->set_blockid(REPLICA_NOT_FOUND_EXCEPTION_BLOCK_ID);
  auto ex_request_body =
      new hadoop::hdfs::GetReplicaVisibleLengthRequestProto();
  ex_request_body->set_allocated_block(ex_block);
  auto ex_message = impl.NewRequest("getReplicaVisibleLength");
  auto ex_m = static_cast<message::GetReplicaVisibleLengthMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);
  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);

  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            REPLICA_NOT_FOUND_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(),
            REPLICA_NOT_FOUND_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // IOException
  ex_rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  ex_block = new hadoop::hdfs::ExtendedBlockProto();
  ex_block->set_blockid(IO_EXCEPTION_BLOCK_ID);
  ex_request_body = new hadoop::hdfs::GetReplicaVisibleLengthRequestProto();
  ex_request_body->set_allocated_block(ex_block);
  ex_message = impl.NewRequest("getReplicaVisibleLength");
  ex_m = static_cast<message::GetReplicaVisibleLengthMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);

  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLengthV2WithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // IOException
  auto ex_rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  auto ex_block = new hadoop::hdfs::ExtendedBlockProto();
  ex_block->set_blockid(IO_EXCEPTION_BLOCK_ID);
  auto ex_request_body =
      new hadoop::hdfs::GetReplicaVisibleLengthV2RequestProto();
  ex_request_body->set_allocated_block(ex_block);
  auto ex_message = impl.NewRequest("getReplicaVisibleLengthV2");
  auto ex_m =
      static_cast<message::GetReplicaVisibleLengthV2Message*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);

  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, RefreshNamenodes) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  auto bpm = new BlockPoolManager(datanode.get());
  bpm->StartActorManager();
  datanode->SetStorage(mock_storage);
  datanode->SetBlockPoolManager(bpm);

  // construct datanode config
  gflags::FlagSaver saver;
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file));

  datanode->SetDataNodeConfig(conf);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  // constuct RefreshNamenodesRequestProto
  auto request_body = new hadoop::hdfs::RefreshNamenodesRequestProto();

  // construct RpcRequestMessage
  auto message = impl.NewRequest("refreshNamenodes");
  auto m = static_cast<message::RefreshNamenodesMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  sleep(1);
  auto resp_header = response->RpcHeader();
  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  delete response;
  datanode->Stop();
  datanode->Join();
  // delete test file
  local_fs.DeleteDir(work_dir, delete_options);
}

TEST_F(ClientDatanodeServiceImplTests, GetBlockLocalPathInfo) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct GetBlockLocalPathInfoRequestProto
  auto request_body = new hadoop::hdfs::GetBlockLocalPathInfoRequestProto();
  auto block = new ExtendedBlock(BLOCK_POOL_ID, 123456, 128, 341021);
  auto block_proto = new hadoop::hdfs::ExtendedBlockProto();
  block->ToProto(block_proto);
  request_body->set_allocated_block(block_proto);
  delete block;

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getBlockLocalPathInfo");
  auto m = static_cast<message::GetBlockLocalPathInfoMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::GetBlockLocalPathInfoResponseProto*>(
          response->Body());
  auto resp_block = resp_body->block();

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(resp_block.poolid(), BLOCK_POOL_ID);
  EXPECT_EQ(resp_body->localpath(), LOCAL_BLOCK_PATH);
  EXPECT_EQ(resp_body->localmetapath(), LOCAL_META_PATH);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetBlockLocalPathInfoWithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  impl.SetDataNode(datanode.get());

  // NullPointException
  auto ex_rpc_header = CreateRpcHeader();

  // construct GetBlockLocalPathInfoRequestProto
  auto ex_request_body = new hadoop::hdfs::GetBlockLocalPathInfoRequestProto();
  auto ex_block = new ExtendedBlock(BLOCK_POOL_ID, 123456, 128, 341021);
  auto ex_block_proto = new hadoop::hdfs::ExtendedBlockProto();
  ex_block->ToProto(ex_block_proto);
  ex_request_body->set_allocated_block(ex_block_proto);
  delete ex_block;

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("getBlockLocalPathInfo");
  auto ex_m = static_cast<message::GetBlockLocalPathInfoMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            NULLPOINT_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), STORAGE_NOT_INITIAL_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // IOException
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);

  // create rpc header
  ex_rpc_header = CreateRpcHeader();

  // construct GetBlockLocalPathInfoRequestProto
  ex_request_body = new hadoop::hdfs::GetBlockLocalPathInfoRequestProto();
  ex_block =
      new ExtendedBlock(BLOCK_POOL_ID, IO_EXCEPTION_BLOCK_ID, 128, 341021);
  ex_block_proto = new hadoop::hdfs::ExtendedBlockProto();
  ex_block->ToProto(ex_block_proto);
  ex_request_body->set_allocated_block(ex_block_proto);
  delete ex_block;

  // construct RpcRequestMessage
  ex_message = impl.NewRequest("getBlockLocalPathInfo");
  ex_m = static_cast<message::GetBlockLocalPathInfoMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, GetHdfsBlockLocations) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  datanode->SetGetHdfsBlockLocationsEnable(true);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct GetHdfsBlocksMetadataProto
  auto request_body = new hadoop::hdfs::GetHdfsBlockLocationsRequestProto();
  request_body->add_blockids(123456);
  request_body->add_blockids(123457);
  request_body->add_blockids(123458);
  request_body->set_blockpoolid(BLOCK_POOL_ID);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getHdfsBlockLocations");
  auto m = static_cast<message::GetHdfsBlockLocationsMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::GetHdfsBlockLocationsResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  EXPECT_TRUE(resp_body != nullptr);
  EXPECT_EQ(resp_body->volumeids(0), "ABC");
  EXPECT_EQ(resp_body->volumeids(1), "DEF");
  EXPECT_EQ(resp_body->volumeindexes(0), 0);
  EXPECT_EQ(resp_body->volumeindexes(1), 1);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetHdfsBlockLocationsWithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  impl.SetDataNode(datanode.get());

  // NullPointException
  auto ex_rpc_header = CreateRpcHeader();

  // construct GetHdfsBlockLocationsResponseProto
  auto ex_request_body = new hadoop::hdfs::GetHdfsBlockLocationsRequestProto();

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("getHdfsBlockLocations");
  auto ex_m = static_cast<message::GetHdfsBlockLocationsMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            NULLPOINT_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), STORAGE_NOT_INITIAL_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // UnsupportedOperationException
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);

  ex_rpc_header = CreateRpcHeader();

  // construct GetHdfsBlockLocationsResponseProto
  ex_request_body = new hadoop::hdfs::GetHdfsBlockLocationsRequestProto();

  // construct RpcRequestMessage
  ex_message = impl.NewRequest("getHdfsBlockLocations");
  ex_m = static_cast<message::GetHdfsBlockLocationsMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            UNSUPPORTED_OPERATION_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(),
            UNSUPPORTED_OPERATION_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // IOException
  datanode->SetGetHdfsBlockLocationsEnable(true);

  ex_rpc_header = CreateRpcHeader();

  // construct GetHdfsBlockLocationsResponseProto
  ex_request_body = new hadoop::hdfs::GetHdfsBlockLocationsRequestProto();

  // construct RpcRequestMessage
  ex_message = impl.NewRequest("getHdfsBlockLocations");
  ex_m = static_cast<message::GetHdfsBlockLocationsMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, ShutdwonDatanode) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct ShutdwonDatanodeRequestProto
  auto request_body = new hadoop::hdfs::ShutdownDatanodeRequestProto();
  request_body->set_forupgrade(true);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("shutdownDatanode");
  auto m = static_cast<message::ShutdownDatanodeMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, TriggerBlockReport) {
  ClientDatanodeServiceImpl impl;
  DataNode* datanode = new DataNode();
  auto mock_storage = new MockStorage();
  auto bpm = new BlockPoolManager(datanode);
  bpm->StartActorManager();
  datanode->SetStorage(mock_storage);
  datanode->SetBlockPoolManager(bpm);

  // construct datanode config
  gflags::FlagSaver saver;
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file));

  datanode->SetDataNodeConfig(conf);
  impl.SetDataNode(datanode);
  datanode->RefreshNamenodes();
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  for (auto bps : threads) {
    for (auto actor : bps->GetActors()) {
      EXPECT_EQ(actor->GetSendImmediateIBR(), false);
    }
  }

  // construct rpc request header
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  // construct TriggerBlockReportProto
  auto rpc_body = new hadoop::hdfs::TriggerBlockReportRequestProto();
  rpc_body->set_incremental(true);

  // construct RpcRequstMessage
  auto message = impl.NewRequest("triggerBlockReport");
  auto m = static_cast<message::TriggerBlockReportMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(rpc_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  for (auto bps : threads) {
    for (auto actor : bps->GetActors()) {
      EXPECT_EQ(actor->GetSendImmediateIBR(), true);
    }
  }

  datanode->Stop();
  datanode->Join();
  delete datanode;

  delete response;

  // delete test file
  local_fs.DeleteDir(work_dir, delete_options);
}

TEST_F(ClientDatanodeServiceImplTests, GetBlocks) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  DataNode* datanode = new DataNode();
  datanode->SetStorage(mock_storage);
  std::shared_ptr<DatanodeID> datanode_id =
      std::make_shared<DatanodeID>("::", "", DATANODE_UUID, 0, 0, 0, 0);
  datanode->SetDatanodeID(datanode_id);

  auto bpm = new BlockPoolManager(datanode);
  bpm->StartActorManager();
  datanode->SetBlockPoolManager(bpm);

  // construct datanode config
  gflags::FlagSaver saver;
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file));

  datanode->SetDataNodeConfig(conf);
  impl.SetDataNode(datanode);
  datanode->RefreshNamenodes();
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct GetBlocksRequestProto
  auto request_body = new hadoop::hdfs::GetBlocksRequestProto();
  request_body->set_bpid(BLOCK_POOL_ID);
  request_body->set_size(257);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getBlocks");
  auto m = static_cast<message::GetBlocksMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body = static_cast<const hadoop::hdfs::GetBlocksResponseProto*>(
      response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  EXPECT_TRUE(resp_body != nullptr);
  auto blocks = resp_body->blocks();
  EXPECT_EQ(blocks.blocks_size(), 3);
  auto block = blocks.blocks(0);
  EXPECT_EQ(block.datanodeuuids(0), DATANODE_UUID);
  EXPECT_EQ(block.storageuuids(0), STORAGE_UUID);
  EXPECT_EQ(block.storagetypes(0), hadoop::hdfs::StorageTypeProto::DISK);
  auto b = block.block();
  EXPECT_EQ(b.numbytes(), 128);

  datanode->Stop();
  datanode->Join();
  delete datanode;

  delete response;

  // delete test file
  local_fs.DeleteDir(work_dir, delete_options);
}

TEST_F(ClientDatanodeServiceImplTests, GetBlocksWithException) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  DataNode* datanode = new DataNode();
  datanode->SetStorage(mock_storage);
  std::shared_ptr<DatanodeID> datanode_id =
      std::make_shared<DatanodeID>("::", "", DATANODE_UUID, 0, 0, 0, 0);
  datanode->SetDatanodeID(datanode_id);

  auto bpm = new BlockPoolManager(datanode);
  bpm->StartActorManager();
  datanode->SetBlockPoolManager(bpm);

  // construct datanode config
  gflags::FlagSaver saver;
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file));

  datanode->SetDataNodeConfig(conf);
  impl.SetDataNode(datanode);
  datanode->RefreshNamenodes();
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  // construct rpc request header
  auto ex_rpc_header = CreateRpcHeader();

  // construct GetBlocksRequestProto
  auto ex_request_body = new hadoop::hdfs::GetBlocksRequestProto();
  ex_request_body->set_bpid(EXCEPTION_BLOCK_POOL_ID);
  ex_request_body->set_size(257);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("getBlocks");
  auto ex_m = static_cast<message::GetBlocksMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  // IllegalArgumentException
  datanode->SetGetHdfsBlockLocationsEnable(true);
  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(ex_resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            ILLEGAL_ARGUMENT_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(),
            ILLEGAL_ARGUMENT_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  datanode->Stop();
  datanode->Join();
  delete datanode;

  local_fs.DeleteDir(work_dir, delete_options);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, InitReplicaRecovery) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto rblock = new hadoop::hdfs::RecoveringBlockProto();
  rb->ToProto(rblock);
  auto ex_request_body = new hadoop::hdfs::InitReplicaRecoveryRequestProto();
  ex_request_body->set_allocated_block(rblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("initReplicaRecovery");
  auto ex_m = static_cast<message::InitReplicaRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::InitReplicaRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_NE(resp_body, nullptr);
  auto flag = resp_body->replicafound();
  EXPECT_TRUE(flag);
  auto state = resp_body->state();
  EXPECT_EQ(state, ReplicaState::FINALIZED);
  auto block = resp_body->block();
  EXPECT_EQ(block.blockid(), blk_id);
  EXPECT_EQ(block.genstamp(), blk_gs0);
  EXPECT_EQ(block.numbytes(), blk_len);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, ReplicaNotFound) {
  ClientDatanodeServiceImpl impl;
  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                           ::testing::Return(exceptions::Exception())));
  DataNode datanode;
  datanode.SetStorage(mock_storage);
  impl.SetDataNode(&datanode);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto rblock = new hadoop::hdfs::RecoveringBlockProto();
  rb->ToProto(rblock);
  auto ex_request_body = new hadoop::hdfs::InitReplicaRecoveryRequestProto();
  ex_request_body->set_allocated_block(rblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("initReplicaRecovery");
  auto ex_m = static_cast<message::InitReplicaRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::InitReplicaRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_NE(resp_body, nullptr);
  auto flag = resp_body->replicafound();
  EXPECT_FALSE(flag);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, RecoveryInProgressException) {
  ClientDatanodeServiceImpl impl;
  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(
          exceptions::Exception(exceptions::E::kRecoveryInProgressException)));
  DataNode datanode;
  datanode.SetStorage(mock_storage);
  impl.SetDataNode(&datanode);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto rblock = new hadoop::hdfs::RecoveringBlockProto();
  rb->ToProto(rblock);
  auto ex_request_body = new hadoop::hdfs::InitReplicaRecoveryRequestProto();
  ex_request_body->set_allocated_block(rblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("initReplicaRecovery");
  auto ex_m = static_cast<message::InitReplicaRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::InitReplicaRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(resp_header->exceptionclassname(),
            RECOVERY_IN_PROGRESS_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(resp_body, nullptr);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, UpdateReplicaUnderRecovery) {
  ClientDatanodeServiceImpl impl;
  MockStorage* mock_storage = new MockStorage();
  MockDataNode mock_dn;
  EXPECT_CALL(mock_dn, NotifyNamenodeReceivedBlock(::testing::_, "", sid))
      .WillRepeatedly(::testing::Return());
  mock_dn.SetStorage(mock_storage);
  impl.SetDataNode(&mock_dn);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto eblock = new hadoop::hdfs::ExtendedBlockProto();
  test_block->ToProto(eblock);
  auto ex_request_body =
      new hadoop::hdfs::UpdateReplicaUnderRecoveryRequestProto();
  ex_request_body->set_allocated_block(eblock);
  ex_request_body->set_newlength(blk_len);
  ex_request_body->set_recoveryid(blk_gs1);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("updateReplicaUnderRecovery");
  auto ex_m =
      static_cast<message::UpdateReplicaUnderRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::UpdateReplicaUnderRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_NE(resp_body, nullptr);
  auto storage_id = resp_body->storageuuid();
  EXPECT_EQ(storage_id, sid);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, CorruptedVolumeMap) {
  ClientDatanodeServiceImpl impl;
  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, UpdateReplicaUnderRecovery(::testing::_, blk_gs1,
                                                        blk_len, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(""),
                           ::testing::Return(exceptions::Exception())));
  DataNode datanode;
  datanode.SetStorage(mock_storage);
  impl.SetDataNode(&datanode);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto eblock = new hadoop::hdfs::ExtendedBlockProto();
  test_block->ToProto(eblock);
  auto ex_request_body =
      new hadoop::hdfs::UpdateReplicaUnderRecoveryRequestProto();
  ex_request_body->set_allocated_block(eblock);
  ex_request_body->set_newlength(blk_len);
  ex_request_body->set_recoveryid(blk_gs1);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("updateReplicaUnderRecovery");
  auto ex_m =
      static_cast<message::UpdateReplicaUnderRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const hadoop::hdfs::UpdateReplicaUnderRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            hadoop::common::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(resp_header->errordetail(),
            hadoop::common::
                RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(resp_header->errormsg(), CORRUPTED_VOLUME_MAP);
  EXPECT_EQ(resp_body, nullptr);

  delete response;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
