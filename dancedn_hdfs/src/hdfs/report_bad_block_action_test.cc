// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/report_bad_block_action.h"

#include <functional>
#include <memory>
#include <set>
#include <unordered_map>
#include <vector>

#include "gmock/gmock-actions.h"
#include "gtest/gtest.h"
#include "hdfs/constants.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/exceptions.h"
#include "hdfs/extended_block.h"
#include "hdfs/mocks.h"
#include "hdfs/storage_type.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class ReportBadBlockActionTests : public ::testing::Test {
 public:
  ReportBadBlockActionTests() {}

  ~ReportBadBlockActionTests() {}

  void SetUp() {
    client = new MockNamenodeClient();
    std::shared_ptr<DatanodeID> datanode_id(
        new DatanodeID("127.0.0.1", "test host name", "", 1101, 1102, 1103, 0));
    StorageInfo* storage_info = new StorageInfo(
        DATANODE_LAYOUT_VERSION, 1, "cluster-time", 1, NodeType::DATA_NODE);

    dn_registration = new DatanodeRegistration(datanode_id, storage_info,
                                               DATANODE_SOFTWARE_VERSION);
  }

  void TearDown() {
    delete client;
    delete dn_registration;
  }

  static void SetUpTestCase() {}

  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  std::shared_ptr<ReportBadBlockAction> BuildReportBadBlockAction(
      ExtendedBlock* block, const std::string& storage_uuid,
      const StorageType& storage_type) {
    return std::make_shared<ReportBadBlockAction>(block->Clone(), storage_uuid,
                                                  storage_type);
  }

 public:
  MockNamenodeClient* client;
  DatanodeRegistration* dn_registration;
};

TEST_F(ReportBadBlockActionTests, ReportTo) {
  ExtendedBlock block("BP-1-1", 1);
  std::string storage_uuid = "storage_uuid";
  StorageType storage_type(StorageType::TYPE_ID_SSD, false);

  EXPECT_CALL(*client, ReportBadBlocks)
      .Times(3)
      .WillOnce(::testing::Return(exceptions::Exception()))
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kStandbyException)));

  // Case 1: dn_registration is null
  std::shared_ptr<ReportBadBlockAction> action =
      BuildReportBadBlockAction(&block, storage_uuid, storage_type);
  exceptions::Exception e = action->ReportTo(client, nullptr);
  ASSERT_EQ(e.GetE(), exceptions::kNoException);

  // Case 2: the response when call namenode is kNoException
  action = BuildReportBadBlockAction(&block, storage_uuid, storage_type);
  e = action->ReportTo(client, dn_registration);
  ASSERT_EQ(e.GetE(), exceptions::kNoException);

  // Case 3: the response when call namenode is kIOException
  action = BuildReportBadBlockAction(&block, storage_uuid, storage_type);
  e = action->ReportTo(client, dn_registration);
  ASSERT_EQ(e.GetE(), exceptions::kBPServiceActorActionException);

  // Case 4: the response when call namenode is kStandbyException
  action = BuildReportBadBlockAction(&block, storage_uuid, storage_type);
  e = action->ReportTo(client, dn_registration);
  ASSERT_EQ(e.GetE(), exceptions::kNoException);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
