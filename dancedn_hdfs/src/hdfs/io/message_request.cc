// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/io/message_request.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

std::string MethodToString(enum Method method) {
  switch (method) {
    case Method::VersionRequest: return "versionRequest";
    case Method::ReportBadBlocks: return "reportBadBlocks";
    case Method::ErrorReport: return "errorReport";
    case Method::CommitBlockSynchronization:
      return "commitBlockSynchronization";
    case Method::Register: return "registerDatanode";
    case Method::SendHeartbeat: return "sendHeartbeat";
    case Method::BlockReport: return "blockReport";
    case Method::BlockReceivedAndDeleted: return "blockReceivedAndDeleted";
    case Method::InitReplicaRecovery: return "initReplicaRecovery";
    case Method::UpdateReplicaUnderRecovery:
      return "updateReplicaUnderRecovery";
    case Method::GetBlocks: return "getBlocks";
  }

  return "Unknown";
}

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
