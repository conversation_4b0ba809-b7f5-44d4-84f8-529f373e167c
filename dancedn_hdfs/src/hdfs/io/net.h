// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include "hdfs/io/address.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

class Net {
 public:
  Net() = default;
  ~Net() = default;

  static int CreateTcpSocket(bool v6);

  static void EnableNonBlock(int fd, bool enabled);
  static void EnableCloexec(int fd, bool enabled);
  static void EnableReuseAddr(int fd, int enabled);
  static void EnableReusePort(int fd, int enabled);
  static void SetRecvTimeout(int fd, int timeout);
  static void SetSendTimeout(int fd, int timeout);
  static void EnableNodelay(int fd, int enabled);
};

struct NetFlags {
 public:
  NetFlags()
      : non_block(false),
        reuse_addr(false),
        reuse_port(false),
        no_delay(false) {}

  ~NetFlags() {}

  void Apply(int fd) const;

 public:
  bool non_block;
  bool reuse_addr;
  bool reuse_port;
  bool no_delay;
};

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
