// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/io/io_worker.h"

#include <arpa/inet.h>

#include <functional>
#include <memory>

#include "byte/byte_log/byte_log_impl.h"
#include "byte/concurrent/count_down_latch.h"
#include "gtest/gtest.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/example.h"
#include "hdfs/io/handle_context.h"
#include "hdfs/io/io.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/io/net.h"
#include "hdfs/io/wrapper.h"
#include "hdfs/util.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

static const char localhost[] = "127.0.0.1";
static const uint16_t lport = 5011;
static IPAddress address;

static const uint8_t signal = 128;

class MockClient : public ExampleClient {
 public:
  explicit MockClient(const IPAddress& addr) : ExampleClient(addr) {}

  bool SendOneByte() {
    return ExampleClient::SendMessage(&signal, 1) == IO_OK;
  }

  bool SendZeros(int len) {
    uint8_t* zeros = new uint8_t[len];
    memset(zeros, 0, len);
    int flag = ExampleClient::SendMessage(zeros, len);
    delete[] zeros;
    return flag == IO_OK;
  }

  bool ReceiveAck() {
    return ExampleClient::ReceiveAck(&signal, 1);
  }
};

class IOWorkerTests : public ::testing::Test {
 public:
  IOWorkerTests()
      : conn_no_(-1), decode_no_(-1), read_no_(-1), close_no_(-1), seq_no_(0) {}
  ~IOWorkerTests() {}

  void SetUp() override {
    IPAddress::Parse(&address, localhost, lport);
    worker_ = std::make_unique<IOWorker>();
    EXPECT_TRUE(worker_->Init());
    EXPECT_TRUE(worker_->IsStopped());

    event_io_ = std::make_unique<EventIO>(0);
    event_io_->MaxChunkEachRead(1);
    worker_->SetEventIO(event_io_.get());

    IPAddress::Parse(&saddr_, localhost, lport);
  }

  void TearDown() override {
    if (worker_ != nullptr) {
      worker_->Stop();
      worker_->Join();
    }
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  void SetInitConnFn(std::function<void(Connection*)> fn) {
    event_io_->ConnectionInitializer(fn);
  }

  int No() {
    return ++seq_no_;
  }

 public:
  int conn_no_;
  int decode_no_;
  int read_no_;
  int close_no_;
  IPAddress saddr_;
  std::unique_ptr<IOWorker> worker_;
  std::unique_ptr<EventIO> event_io_;

 private:
  int seq_no_;
};

TEST_F(IOWorkerTests, TwoRound) {
  // server
  byte::CountDownLatch latch(7);
  SetInitConnFn([this, &latch](Connection* conn) {
    auto ctx = new HandleContext();
    ctx->ConnectedHandle([this, &latch](IOWorker* worker, Connection* conn) {
         conn_no_ = No();
         latch.CountDown();
         return IO_OK;
       })
        ->DisconnectedHandle(
            [this, &latch](IOWorker* worker, Connection* conn) {
              close_no_ = No();
              // latch.CountDown();
              return IO_OK;
            })
        ->DecodeHandle([this, &latch](Connection* conn, Wrapper** w) {
          decode_no_ = No();
          latch.CountDown();
          IOChunk* chunk = conn->ReadBuf()->Front();
          if (!chunk->EnoughDataLength(1)) return IO_AGAIN;
          uint8_t sign = chunk->ReadFixed8BE();
          if (sign != signal) return IO_ERR;
          uint8_t* data = new uint8_t(sign);
          *w = new Wrapper(reinterpret_cast<void*>(data), [](void* ptr) {
            delete reinterpret_cast<decltype(data)>(ptr);
          });
          return IO_OK;
        })
        ->ReadHandle(
            [this, &latch](IOWorker* worker, Connection* conn, void* data) {
              read_no_ = No();
              uint8_t* ack = reinterpret_cast<uint8_t*>(data);
              IOChunk* chunk = new IOChunk(1);
              chunk->WriteBytes(ack, 1);
              conn->WriteBuf()->Append(chunk);
              latch.CountDown();
              delete ack;
              return IO_OK;
            });
    conn->Handle(ctx);
  });
  EXPECT_EQ(worker_->Listen(saddr_), IO_OK);
  worker_->Start();
  EXPECT_FALSE(worker_->IsStopped());

  // client
  auto client_ptr = std::make_unique<MockClient>(saddr_);
  EXPECT_TRUE(client_ptr->Inited());
  EXPECT_TRUE(client_ptr->SendOneByte());
  EXPECT_TRUE(client_ptr->ReceiveAck());
  EXPECT_TRUE(client_ptr->SendOneByte());
  EXPECT_TRUE(client_ptr->ReceiveAck());

  latch.Wait();
  EXPECT_EQ(conn_no_, 1);
  EXPECT_EQ(decode_no_, 7);
  EXPECT_EQ(read_no_, 6);
  EXPECT_LT(close_no_, 0);
}

TEST_F(IOWorkerTests, InvalidConnection) {
  // server
  byte::CountDownLatch latch(2);
  SetInitConnFn([this, &latch](Connection* conn) {
    auto ctx = new HandleContext();
    ctx->ConnectedHandle([this, &latch](IOWorker* worker, Connection* conn) {
         conn_no_ = No();
         latch.CountDown();
         return IO_ERR;
       })
        ->DisconnectedHandle(
            [this, &latch](IOWorker* worker, Connection* conn) {
              close_no_ = No();
              latch.CountDown();
              return IO_OK;
            })
        ->DecodeHandle([this, &latch](Connection* conn, Wrapper** w) {
          decode_no_ = No();
          latch.CountDown();
          return IO_OK;
        })
        ->ReadHandle(
            [this, &latch](IOWorker* worker, Connection* conn, void* data) {
              read_no_ = No();
              conn->MaskDelayClose();
              latch.CountDown();
              return IO_OK;
            });
    conn->Handle(ctx);
  });
  EXPECT_EQ(worker_->Listen(saddr_), IO_OK);
  worker_->Start();
  EXPECT_FALSE(worker_->IsStopped());

  // client
  auto client_ptr = std::make_unique<MockClient>(saddr_);
  EXPECT_TRUE(client_ptr->Inited());

  latch.Wait();
  EXPECT_EQ(conn_no_, 1);
  EXPECT_LT(decode_no_, 0);
  EXPECT_LT(read_no_, 0);
  EXPECT_EQ(close_no_, 2);
}

TEST_F(IOWorkerTests, BrokenConnection) {
  // server
  byte::CountDownLatch latch(2);
  SetInitConnFn([this, &latch](Connection* conn) {
    auto ctx = new HandleContext();
    ctx->ConnectedHandle([this, &latch](IOWorker* worker, Connection* conn) {
         conn_no_ = No();
         latch.CountDown();
         return IO_OK;
       })
        ->DisconnectedHandle(
            [this, &latch](IOWorker* worker, Connection* conn) {
              close_no_ = No();
              latch.CountDown();
              return IO_OK;
            })
        ->DecodeHandle([this, &latch](Connection* conn, Wrapper** w) {
          decode_no_ = No();
          latch.CountDown();
          return IO_OK;
        })
        ->ReadHandle(
            [this, &latch](IOWorker* worker, Connection* conn, void* data) {
              read_no_ = No();
              conn->MaskDelayClose();
              latch.CountDown();
              return IO_OK;
            });
    conn->Handle(ctx);
  });
  EXPECT_EQ(worker_->Listen(saddr_), IO_OK);
  worker_->Start();
  EXPECT_FALSE(worker_->IsStopped());

  // client
  auto client_ptr = std::make_unique<MockClient>(saddr_);
  EXPECT_TRUE(client_ptr->Inited());
  client_ptr->Close();
  latch.Wait();
  EXPECT_EQ(conn_no_, 1);
  EXPECT_LT(decode_no_, 0);
  EXPECT_LT(read_no_, 0);
  EXPECT_EQ(close_no_, 2);
}

TEST_F(IOWorkerTests, DecodeFailure) {
  // server
  byte::CountDownLatch latch(3);
  SetInitConnFn([this, &latch](Connection* conn) {
    auto ctx = new HandleContext();
    ctx->ConnectedHandle([this, &latch](IOWorker* worker, Connection* conn) {
         conn_no_ = No();
         latch.CountDown();
         return IO_OK;
       })
        ->DisconnectedHandle(
            [this, &latch](IOWorker* worker, Connection* conn) {
              close_no_ = No();
              latch.CountDown();
              return IO_OK;
            })
        ->DecodeHandle([this, &latch](Connection* conn, Wrapper** w) {
          decode_no_ = No();
          latch.CountDown();
          return IO_ERR;
        })
        ->ReadHandle(
            [this, &latch](IOWorker* worker, Connection* conn, void* data) {
              read_no_ = No();
              conn->MaskDelayClose();
              latch.CountDown();
              return IO_OK;
            });
    conn->Handle(ctx);
  });
  EXPECT_EQ(worker_->Listen(saddr_), IO_OK);
  worker_->Start();
  EXPECT_FALSE(worker_->IsStopped());

  // client
  auto client_ptr = std::make_unique<MockClient>(saddr_);
  EXPECT_TRUE(client_ptr->Inited());
  EXPECT_TRUE(client_ptr->SendOneByte());

  latch.Wait();
  EXPECT_EQ(conn_no_, 1);
  EXPECT_EQ(decode_no_, 2);
  EXPECT_LT(read_no_, 0);
  EXPECT_EQ(close_no_, 3);
}

TEST_F(IOWorkerTests, WaitForLength) {
  // server
  byte::CountDownLatch latch(4);
  SetInitConnFn([this, &latch](Connection* conn) {
    auto ctx = new HandleContext();
    ctx->ConnectedHandle([this, &latch](IOWorker* worker, Connection* conn) {
         conn_no_ = No();
         latch.CountDown();
         return IO_OK;
       })
        ->DisconnectedHandle(
            [this, &latch](IOWorker* worker, Connection* conn) {
              close_no_ = No();
              latch.CountDown();
              return IO_OK;
            })
        ->DecodeHandle([this, &latch](Connection* conn, Wrapper** w) {
          decode_no_ = No();
          IOChunk* chunk = conn->ReadBuf()->Front();
          if (!chunk->EnoughDataLength(10)) return IO_AGAIN;
          *w = new Wrapper(
              reinterpret_cast<void*>(chunk->Data()), [](void* ptr) {
                delete reinterpret_cast<decltype(chunk->Data())>(ptr);
              });
          latch.CountDown();
          return IO_OK;
        })
        ->ReadHandle(
            [this, &latch](IOWorker* worker, Connection* conn, void* data) {
              read_no_ = No();
              conn->MaskDelayClose();
              latch.CountDown();
              return IO_OK;
            });
    conn->Handle(ctx);
    conn->ReadChunkSize(1);
  });
  EXPECT_EQ(worker_->Listen(saddr_), IO_OK);
  worker_->Start();

  // client
  auto client_ptr = std::make_unique<MockClient>(saddr_);
  EXPECT_TRUE(client_ptr->Inited());
  EXPECT_TRUE(client_ptr->SendZeros(10));
  latch.Wait();
  EXPECT_EQ(conn_no_, 1);
  EXPECT_EQ(decode_no_, 4);
  EXPECT_EQ(read_no_, 5);
  EXPECT_EQ(close_no_, 6);
}

TEST_F(IOWorkerTests, ProcessFailure) {
  // server
  byte::CountDownLatch latch(4);
  SetInitConnFn([this, &latch](Connection* conn) {
    auto ctx = new HandleContext();
    ctx->ConnectedHandle([this, &latch](IOWorker* worker, Connection* conn) {
         conn_no_ = No();
         latch.CountDown();
         return IO_OK;
       })
        ->DisconnectedHandle(
            [this, &latch](IOWorker* worker, Connection* conn) {
              close_no_ = No();
              latch.CountDown();
              return IO_OK;
            })
        ->DecodeHandle([this, &latch](Connection* conn, Wrapper** w) {
          decode_no_ = No();
          IOChunk* chunk = conn->ReadBuf()->Front();
          if (!chunk->EnoughDataLength(1)) return IO_AGAIN;
          uint8_t sign = chunk->ReadFixed8BE();
          if (sign != signal) return IO_ERR;
          uint8_t* data = new uint8_t(sign);
          *w = new Wrapper(reinterpret_cast<void*>(data), [](void* ptr) {
            delete reinterpret_cast<decltype(data)>(ptr);
          });
          latch.CountDown();
          return IO_OK;
        })
        ->ReadHandle(
            [this, &latch](IOWorker* worker, Connection* conn, void* data) {
              read_no_ = No();
              delete reinterpret_cast<uint8_t*>(data);
              latch.CountDown();
              return IO_ERR;
            });
    conn->Handle(ctx);
    conn->ReadChunkSize(1);
  });
  EXPECT_EQ(worker_->Listen(saddr_), IO_OK);
  worker_->Start();

  // client
  auto client_ptr = std::make_unique<MockClient>(saddr_);
  EXPECT_TRUE(client_ptr->Inited());
  EXPECT_TRUE(client_ptr->SendOneByte());
  latch.Wait();
  EXPECT_EQ(conn_no_, 1);
  EXPECT_EQ(decode_no_, 2);
  EXPECT_EQ(read_no_, 3);
  EXPECT_EQ(close_no_, 4);
}

// TODO(cyt): io_worker as client

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
