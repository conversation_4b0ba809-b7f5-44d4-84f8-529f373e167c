// Copyright (c) 2018, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "byte/concurrent/mutex.h"
#include "hdfs/exceptions.h"
#include "hdfs/replica_info.h"
#include "hdfs/replica_state.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class Block;
class Store;
class DataXceiver;
class ExtendedBlock;

class ReplicaInPipeline : public ReplicaInfo {
 public:
  ReplicaInPipeline() {}
  ReplicaInPipeline(const std::string& bpid, uint64_t block_id, uint64_t len,
                    uint64_t gs, const std::string& storage_uuid,
                    bool is_transient, bool checksum_enabled,
                    uint16_t disk_id = 0,
                    PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaInPipeline(const std::string& bpid, uint64_t block_id, uint64_t gs,
                    const std::string& storage_uuid, bool is_transient,
                    bool checksum_enabled, uint16_t disk_id = 0,
                    PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaInPipeline(const ExtendedBlock& block, const std::string& storage_uuid,
                    bool is_transient, bool checksum_enabled,
                    uint16_t disk_id = 0,
                    PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaInPipeline(const ReplicaInPipeline& from);
  explicit ReplicaInPipeline(std::shared_ptr<ReplicaInPipeline> from);
  virtual ~ReplicaInPipeline() {}

  ReplicaState GetState() const override {
    return ReplicaState::TEMPORARY;
  }

  int64_t GetVisibleLength() const override {
    return -1;
  }

  virtual uint64_t GetBytesAcked() const {
    return bytes_acked_;
  }

  uint64_t GetBytesOnDisk() const override {
    return bytes_on_disk_;
  }

  virtual void SetBytesAcked(uint64_t bytes_acked) {
    bytes_acked_ = bytes_acked;
  }

  virtual void SetDiskDataLen(uint64_t data_length) {
    bytes_on_disk_ = data_length;
  }

  virtual uint64_t GetDiskDataLen() const {
    return bytes_on_disk_;
  }

  std::string ToString() const override;

  // thread-safe stop writer and set writer
  exceptions::Exception ResetWriter(DataXceiver* writer, uint32_t timeout);

  bool ShouldStopWriter();

  void SetWriter(DataXceiver* writer);

 private:
  exceptions::Exception StopWriter(uint32_t timeout);

 private:
  uint64_t bytes_acked_;
  uint64_t bytes_on_disk_;
  std::shared_ptr<DataXceiver> writer_;
  byte::Mutex writer_mutex_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
