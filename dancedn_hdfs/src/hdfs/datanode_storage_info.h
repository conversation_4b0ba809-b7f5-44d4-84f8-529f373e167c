// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <vector>

#include "hdfs/datanode_storage.h"
#include "hdfs/storage_type.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DatanodeStorage;
class DatanodeDescriptor;
class DatanodeInfo;
class StorageReport;

class DatanodeStorageInfo {
 public:
  static std::vector<DatanodeInfo*> ToDatanodeInfos(
      const std::vector<DatanodeStorageInfo*> storages);

  static std::vector<std::string> ToStorageIDs(
      const std::vector<DatanodeStorageInfo*> storages);

  static std::vector<StorageType> ToStorageTypes(
      const std::vector<DatanodeStorageInfo*> storages);

 public:
  DatanodeStorageInfo(const DatanodeDescriptor* dn, const DatanodeStorage* s);
  DatanodeStorageInfo(const DatanodeDescriptor* dn, std::string storage_id,
                      StorageType storage_type, DatanodeStorage::State state);
  ~DatanodeStorageInfo();

  DatanodeStorageInfo* Clone() const;

  DatanodeDescriptor* GetDatanodeDescriptor() const {
    return dn_;
  }
  std::string GetStorageID() const {
    return storage_id_;
  }
  StorageType GetStorageType() const {
    return storage_type_;
  }
  DatanodeStorage::State GetState() const {
    return state_;
  }
  void SetState(DatanodeStorage::State state) {
    state_ = state;
  }

  void UpdateFromStorage(const DatanodeStorage* storage);

  void ReceivedHeartbeat(const StorageReport* report);

  std::string ToString() const;

  uint64_t GetNumBlocks() const {
    return num_blocks_;
  }

 private:
  DatanodeDescriptor* dn_;
  std::string storage_id_;
  StorageType storage_type_;
  DatanodeStorage::State state_;

  uint64_t capacity_;
  uint64_t dfs_used_;
  uint64_t non_dfs_used_;
  uint64_t remaining_;
  uint64_t block_pool_used_;
  uint64_t num_blocks_;  // modified by namenode
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
