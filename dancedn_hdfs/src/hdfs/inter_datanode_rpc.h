// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include "hdfs/client_io.h"
#include "hdfs/exceptions.h"
#include "hdfs/io/address.h"
#include "hdfs/proto/ProtobufRpcEngine.pb.h"

namespace google {
namespace protobuf {
class Message;
}
}  // namespace google

namespace hadoop {
namespace common {
class RpcResponseHeaderProto;
}
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class InterDatanodeRpc {
 public:
  InterDatanodeRpc(ClientIO* io, const io::IPAddress& addr, uint32_t timeout)
      : io_(io), addr_(addr), timeout_(timeout) {}

  ~InterDatanodeRpc() {
    delete io_;
  }

  exceptions::Exception Call(io::Method method,
                             const google::protobuf::Message* r,
                             google::protobuf::Message** res);

 public:
  static const char PROTOCOL_NAME[];
  static const uint32_t PROTOCOL_VERSION;

 private:
  hadoop::common::RequestHeaderProto* CreateRequestHeader(io::Method method_id);

 private:
  ClientIO* io_;
  io::IPAddress addr_;
  uint32_t timeout_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
