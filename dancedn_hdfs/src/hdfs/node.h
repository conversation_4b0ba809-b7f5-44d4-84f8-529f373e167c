// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class Node {
 public:
  Node() {}
  virtual ~Node() {}

  virtual std::string GetNetworkLocation() const = 0;

  virtual bool SetNetworkLocation(const std::string& location) = 0;

  virtual std::string GetName() const = 0;

  virtual Node* GetParent() const = 0;

  virtual void SetParent(Node* parent) = 0;

  virtual std::string GetPath() const = 0;

  virtual int GetLevel() const = 0;

  virtual void SetLevel(int level) = 0;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
