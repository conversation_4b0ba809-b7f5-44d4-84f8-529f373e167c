// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/block_sender.h"

#include <unistd.h>

#include <algorithm>
#include <memory>
#include <utility>

#include "byte/include/assert.h"
#include "byte/string/format/print.h"
#include "byte/system/timestamp.h"
#include "byte/util/scope_guard.h"
#include "common/media_flags.h"
#include "gflags/gflags.h"
#include "hdfs/block.h"
#include "hdfs/data_checksum.h"
#include "hdfs/data_transfer_throttler.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_interface.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/extended_block.h"
#include "hdfs/generation_stamp.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/opstats/op_stats.h"
#include "hdfs/packet_header.h"
#include "hdfs/proto/hdfs.pb.h"
#include "hdfs/replica.h"
#include "hdfs/replica_being_written.h"
#include "hdfs/replica_state.h"
#include "hdfs/store.h"

#define RECORD_PACKET_METRICS()                                 \
  int64_t start_time_us = byte::GetCurrentTimeInUs();           \
  byte::ScopeGuard metric_guard([this, start_time_us]() {       \
    METRICS_hdfs_send_packet_latency_us->GetMetric(tags_)->Set( \
        byte::GetCurrentTimeInUs() - start_time_us);            \
  });

DECLARE_uint32(bytestore_hdfs_sender_wait_period);
DECLARE_uint32(bytestore_hdfs_io_file_buffer_size);
DECLARE_MEDIA_FLAG_uint32(bytestore_hdfs_slow_io_warning_threshold_ms);
DECLARE_uint32(bytestore_hdfs_max_chunk_size);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_bool(bytestore_hdfs_xceiver_tags_with_user);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DECLARE_COUNTER_METRICS_POOL(hdfs_send_throttler_wait_num);
DECLARE_HISTOGRAM_METRICS_POOL(hdfs_send_throttler_wait_time_ms);
DECLARE_HISTOGRAM_METRICS_POOL(hdfs_send_packet_latency_us);
DECLARE_HISTOGRAM_METRICS_POOL(hdfs_block_sender_read_disk_latency_us);
DECLARE_HISTOGRAM_METRICS_POOL(hdfs_block_sender_send_packet_latency_us);

const uint64_t BlockSender::LONG_READ_THRESHOLD_BYTES = 256 * 1024;
const uint64_t BlockSender::META_FILE_HEADER_SIZE = 7;

BlockSender::BlockSender(ExtendedBlock* block, uint64_t start_offset,
                         uint64_t length, bool verify_checksum,
                         bool send_checksum, DataNode* datanode,
                         CachingStrategy* caching_strategy)
    : block_(block),
      sent_entire_byte_range_(false),
      verify_checksum_(verify_checksum),
      generate_checksum_from_mem_(false),
      dn_(datanode),
      checksum_(nullptr),
      last_disk_data_length_(0),
      op_key_(),
      disk_(nullptr),
      ref_(nullptr),
      wait_time_ms_for_throttle_(0) {}

BlockSender::~BlockSender() {
  // TODO(livexmm) dropCacheBehind and cancleReadahead
  // delete block_ by data_xceiver
  // delete dn_ by DataNode
}

exceptions::Exception BlockSender::CreateBlockSender(
    std::shared_ptr<Thread> thread, ExtendedBlock* block, uint64_t start_offset,
    int64_t length, bool verify_checksum, bool send_checksum,
    DataNode* datanode, CachingStrategy* caching_strategy, std::string user,
    int user_priority, BlockSender** block_sender) {
  LOG(DEBUG) << "create block sender, block:" << block->ToString()
             << " start_offset:" << start_offset << " length:" << length
             << " verify_checksum:" << verify_checksum
             << " send_checksum:" << send_checksum;
  std::unique_ptr<BlockSender> sender(
      new BlockSender(block, start_offset, length, verify_checksum,
                      send_checksum, datanode, caching_strategy));
  sender->thread_ = thread;
  sender->user_ = user;
  sender->user_priority_ = user_priority;
  if (sender->verify_checksum_ && !send_checksum) {
    return exceptions::Exception(
        exceptions::E::kIllegalArgumentException,
        "If verifying checksum, currently must also send it.");
  }
  // TODO(livexmm) initialize cachingStrategy
  int64_t replica_visible_length = 0;
  std::string block_pool_id = block->GetBlockPoolID();
  uint64_t block_id = block->GetBlock()->GetBlockID();
  std::shared_ptr<ReplicaInfo> replica =
      datanode->GetStorage()->GetReplica(block_pool_id, block_id);
  if (replica == nullptr) {
    return exceptions::Exception(exceptions::E::kReplicaNotFoundException,
                                 block->ToString());
  }
  replica_visible_length = replica->GetVisibleLength();
  DiskId disk_id = replica->GetDiskId();
  sender->disk_ = datanode->GetStorage()->GetDisk(disk_id);
  if (sender->disk_ == nullptr) {
    std::string msg =
        byte::StringPrint("Disk id not found while check files of %s",
                          replica->GetBlock()->ToString());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->datanode_slow_log_threshold_ms_ =
      MFLAGS(bytestore_hdfs_slow_io_warning_threshold_ms,
             sender->disk_->GetMediaType());
  std::string media_str = MediaTypeToShortString(sender->disk_->GetMediaType());
  sender->media_throttler_ =
      datanode->GetStorage()->GetMediaThrottler(media_str);
  if (FLAGS_bytestore_hdfs_xceiver_tags_with_user) {
    sender->tags_ = {
        {"user", sender->user_},
        {"user_priority", byte::IntegerToString(sender->user_priority_)},
        {"bpid", block->GetBlockPoolID()},
        {"media_type", media_str},
        {"disk_id", byte::IntegerToString(sender->disk_->GetDiskId())}};
  } else {
    sender->tags_ = {
        {"media_type", media_str},
        {"disk_id", byte::IntegerToString(sender->disk_->GetDiskId())}};
  }
  sender->ref_.reset(new DiskRef(sender->disk_));
  if (datanode->GetStorage()->IsVolumeClosed(sender->disk_)) {
    std::string msg = byte::StringPrint("Disk closed while check files of %s",
                                        replica->GetBlock()->ToString());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  uint64_t last_disk_data_length = 0;
  if (replica->GetState() == ReplicaState::RBW) {
    LOG(DEBUG) << "RBW wait for Minlength start_offset:" << start_offset
               << " length:" << length;
    auto rbw = std::dynamic_pointer_cast<ReplicaBeingWritten>(replica);
    auto e = WaitForMinLength(rbw, start_offset + length);
    if (!e.OK()) {
      return e;
    }
    last_disk_data_length = rbw->GetDiskDataLen();
  }
  if (replica->GetGS() < block->GetGS()) {
    std::string msg =
        "Replica genstamp < block genstamp, block=" + block->ToString() +
        ", replica=" + replica->ToString();
    return exceptions::Exception(exceptions::E::kIOException, msg);
  } else if (replica->GetGS() > block->GetGS()) {
    block->SetGS(replica->GetGS());
  }
  if (replica_visible_length < 0) {
    std::string msg = "Replica is not readable, block=" + block->ToString() +
                      ", replica=" + replica->ToString();
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  /*
   * | checksum_mode | meta_file_exist | operation | |       0       |  True |
   * read checksum from file and send       | |       0       |  False | send
   * TYPE_NULL checksum                | |     Not 0     |  True           |
   * read checksum from file and send       | |     Not 0     |  False |
   * generate TYPE_CRC32C checksum and send |
   */
  std::unique_ptr<DataChecksum> csum;
  if ((verify_checksum || send_checksum) && replica->ChecksumEnabled()) {
    DataChecksum* ret_csum = nullptr;
    auto e = datanode->GetStorage()->ReadDiskChecksum(block, &ret_csum);
    csum.reset(ret_csum);
    if (csum == nullptr) {
      LOG(WARNING) << "Could not find metadata file for " << block->ToString();
    }
  }
  if (csum == nullptr) {
    // The number of bytes per checksum here determines the alignment
    // of reads: we always start reading at a checksum chunk boundary,
    // even if the checksum type is NULL. So, choosing too big of a value
    // would risk sending too much unnecessary data. 512 (1 disk sector)
    // is likely to result in minimal extra IO.
    sender->generate_checksum_from_mem_ =
        FLAGS_bytestore_hdfs_checksum_mode > 0 && send_checksum;
    if (sender->generate_checksum_from_mem_) {
      csum.reset(
          DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 4096));
    } else {
      csum.reset(DataChecksum::NewDataChecksum(DataChecksum::TYPE_NULL, 512));
    }
  }

  // If chunkSize is very large, then the metadata file is mostly corrupted.
  // It is almost impossible for DanceDN, so we just return error;
  auto size = csum->GetBytesPerChecksum();
  uint32_t limit_size = FLAGS_bytestore_hdfs_max_chunk_size;
  if (size > limit_size && size > replica_visible_length) {
    std::string msg =
        byte::StringPrint("invalid checksum %s", csum->ToString());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->chunk_size_ = size;
  sender->checksum_ = std::move(csum);
  sender->checksum_size_ = sender->checksum_->GetChecksumSize();
  length = length < 0 ? replica_visible_length : length;

  uint64_t end = last_disk_data_length != 0 ? last_disk_data_length
                                            : replica->GetBytesOnDisk();
  LOG(DEBUG) << "chunk_size:" << size
             << " checksum_size:" << sender->checksum_size_
             << " length:" << length
             << " replica_bytes_on_disk:" << replica->GetBytesOnDisk()
             << " end:" << end << " start_offset:" << start_offset
             << " should generate checksum: "
             << sender->generate_checksum_from_mem_;
  if (start_offset < 0 || start_offset > end || (length + start_offset) > end) {
    std::string msg = byte::StringPrint(
        "Offset %ld and lenght %ld don't match block %s ( blockLen %ld )",
        start_offset, length, block->ToString(), end);
    std::shared_ptr<DatanodeRegistration> reg;
    auto e = datanode->GetDNRegistrationForBP(block->GetBlockPoolID(), &reg);
    if (!e.OK()) {
      return e;
    }
    LOG(WARNING) << reg->ToString() << ":sendBlock() : " << msg;
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->offset_ = start_offset - (start_offset % sender->chunk_size_);
  if (length >= 0) {
    // Ensure end_offset points to end of chunk
    uint64_t tmp_len = start_offset + length;
    if (tmp_len % sender->chunk_size_ != 0) {
      tmp_len += (sender->chunk_size_ - tmp_len % sender->chunk_size_);
    }
    if (tmp_len < end) {
      // will use on-disk checksum here since the end is a stable chunk
      end = tmp_len;
    } else if (last_disk_data_length != 0) {
      // last chunk is changing
      sender->last_disk_data_length_ = last_disk_data_length;
    }
  }

  LOG(DEBUG) << "sender_offset:" << sender->offset_
             << " sender_end_offset:" << end << " start_offset:" << start_offset
             << " length:" << length << " chunk_size:" << sender->chunk_size_;

  sender->end_offset_ = end;
  sender->seqno_ = 0;
  *block_sender = sender.release();
  return exceptions::Exception();
}

exceptions::Exception BlockSender::WaitForMinLength(
    const std::shared_ptr<ReplicaBeingWritten>& rbw, uint64_t len) {
  for (uint8_t i = 0; i < 30 && rbw->GetBytesOnDisk() < len; i++) {
    usleep(FLAGS_bytestore_hdfs_sender_wait_period);
  }
  uint64_t bytes_on_disk = rbw->GetBytesOnDisk();
  if (bytes_on_disk < len) {
    return exceptions::Exception(
        exceptions::E::kIOException,
        byte::StringPrint("Need %d bytes, but only %d bytes available", len,
                          bytes_on_disk));
  }
  return exceptions::Exception();
}

io::IOChunk* BlockSender::PreallocIOChunk(uint32_t max_chunks) {
  uint32_t datalen =
      std::min((uint32_t)(end_offset_ - offset_), chunk_size_ * max_chunks);
  uint32_t num_chunks = NumberOfChunks(datalen);
  uint32_t checksum_data_len = num_chunks * checksum_size_;
  uint32_t packet_len = datalen + checksum_data_len + 4;
  uint32_t total_len = packet_len + PacketHeader::PKT_MAX_HEADER_LEN;
  return new io::IOChunk(total_len, true);
}

exceptions::Exception BlockSender::SendBlock(io::Connection* conn,
                                             uint64_t* read_len) {
  if (conn == nullptr || conn->IsClosed()) {
    return exceptions::Exception(exceptions::E::kIOException,
                                 "out stream is null or close");
  }
  initial_offset_ = offset_;
  uint64_t total_read = 0;
  // Avoid the situation where read_len is not assigned a value in case of a
  // failure.
  byte::ScopeGuard read_len_guard([&]() {
    *read_len = total_read;
  });

  // TODO(caibingfeng) if (isLongRead() && blockinfd != nullptr)
  manageOsCache();
  // uint64_t start_time = byte::GetCurrentTimeInMs();

  uint32_t pkt_buf_size = PacketHeader::PKT_MAX_HEADER_LEN;
  uint32_t max_chunks_per_packet = std::max(
      (uint32_t)1, NumberOfChunks(FLAGS_bytestore_hdfs_io_file_buffer_size));
  pkt_buf_size += (chunk_size_ + checksum_size_) * max_chunks_per_packet;
  LOG(DEBUG) << "pkt_buf_size:" << pkt_buf_size
             << " PKT_MAX_HEADER_LEN:" << PacketHeader::PKT_MAX_HEADER_LEN
             << " max_chunks_per_packet:" << max_chunks_per_packet
             << " chunk_size:" << chunk_size_
             << " checksum_size:" << checksum_size_
             << " end_offset:" << end_offset_ << " offset:" << offset_;

  io::IOChunk* chunk = PreallocIOChunk(max_chunks_per_packet);

  while (end_offset_ > offset_) {
    manageOsCache();
    int64_t start_time = byte::GetCurrentTimeInMs();
    uint64_t len = 0;
    auto e = SendPacket(conn, max_chunks_per_packet, chunk, &len);
    if (!e.OK()) {
      LOG(ERROR) << "send packet failed block:" << block_->ToString()
                 << " send len:" << len << " offset:" << offset_
                 << " total_read:" << total_read << " seqno:" << seqno_;
      chunk->AlwaysDestroy();
      return e;
    }
    offset_ += len;
    total_read += len + (NumberOfChunks(len) * checksum_size_);
    seqno_++;
    LOG(DEBUG) << "send len:" << len << " offset:" << offset_
               << " total_read:" << total_read << " seqno:" << seqno_;
    if (!op_key_.isEmpty()) {
      OpStats::GetInstance().AddBytes(op_key_, len);
      OpStats::GetInstance().SaveLatency(
          op_key_, byte::GetCurrentTimeInMs() - start_time);
    }
  }

  uint64_t len = 0;
  auto e = SendPacket(conn, max_chunks_per_packet, chunk, &len);
  if (!e.OK()) {
    LOG(ERROR) << "send last packet failed block:" << block_->ToString()
               << " send len:" << len << " offset:" << offset_
               << " total_read:" << total_read << " seqno:" << seqno_;
    chunk->AlwaysDestroy();
    return e;
  }
  sent_entire_byte_range_ = true;

  chunk->AlwaysDestroy();
  return exceptions::Exception();
}

exceptions::Exception BlockSender::SendPacket(io::Connection* conn,
                                              uint32_t max_chunks,
                                              io::IOChunk* chunk,
                                              uint64_t* len) {
  RECORD_PACKET_METRICS();
  uint32_t data_len =
      std::min((uint32_t)(end_offset_ - offset_), chunk_size_ * max_chunks);
  uint32_t num_chunks = NumberOfChunks(data_len);
  uint32_t checksum_data_len = num_chunks * checksum_size_;
  uint32_t packet_len = data_len + checksum_data_len + 4;
  uint32_t total_len = packet_len + PacketHeader::PKT_MAX_HEADER_LEN;
  bool last_data_packet = offset_ + data_len == end_offset_ && data_len > 0;
  LOG(DEBUG) << "date_len:" << data_len << " num_chunks:" << num_chunks
             << " checksum_data_len:" << checksum_data_len
             << " packet_len:" << packet_len << " total_len:" << total_len
             << " last_data_packet:" << last_data_packet;
  // The packet buffer is organized as follows:
  // _______HHHHCCCCD?D?D?D?
  //        ^   ^
  //        |   \ checksumOff
  //        \ headerOff
  // _ padding, since the header is variable-length
  // H = header and length prefixes
  // C = checksums
  // D? = data, if transferTo is false.

  io::IOBuf* buf = conn->WriteBuf();
  int head_len;
  auto e = WritePacketHeader(chunk, data_len, packet_len, &head_len);
  if (!e.OK()) {
    LOG(ERROR) << "WritePacketHeader error exceptions : " << e.ToString();
    return e;
  }
  int headerOff = chunk->Length() - head_len;
  uint32_t checksum_off = chunk->Length();
  uint64_t csum_file_off = (offset_ / chunk_size_) * checksum_size_;
  LOG(DEBUG) << "headeroff:" << headerOff << " head_len:" << head_len
             << " checksum_off:" << checksum_off
             << " csum_file_off:" << csum_file_off;
  if (generate_checksum_from_mem_) {
    chunk->IncrLength(checksum_data_len);
  } else if (checksum_size_ > 0) {
    auto e = dn_->GetStorage()->ReadChecksum(block_, chunk, csum_file_off,
                                             checksum_data_len);
    if (!e.OK()) {
      LOG(WARNING)
          << "Could not read or failed to veirfy checksum for data at offset "
          << offset_ << " for block " << block_->ToString() << e.ToString();
      return e;
    }
    // Different from java version:
    // To simplify the process of writing, we do not keep rbw's last checksum in
    // memory, so we need recalc last checksum instead of get it from memory
    // (see below)
  }
  uint32_t data_off = checksum_off + checksum_data_len;
  LOG(DEBUG) << "data_off:" << data_off << " offset:" << offset_
             << " data_len:" << data_len;

  // if (!transferto)

  // int64_t before_read = byte::GetCurrentTimeInNs();
  // when sending last empty packet, no need to read from storage
  if (data_len > 0) {
    uint64_t begin_time_us = byte::GetCurrentTimeInUs();
    e = dn_->GetStorage()->ReadBlock(block_, chunk, offset_, data_len);
    if (!e.OK()) {
      LOG(ERROR) << "Read block from storage failed error exceptions : "
                 << e.ToString();
      return e;
    }
    uint64_t duration_us = byte::GetCurrentTimeInUs() - begin_time_us;
    METRICS_hdfs_block_sender_read_disk_latency_us->GetMetric(tags_)->Set(
        duration_us);
    if (duration_us / 1000 > datanode_slow_log_threshold_ms_) {
      LOG(WARNING) << "Slow BlockSender read data from disk cost:"
                   << duration_us / 1000
                   << "ms (threshold=" << datanode_slow_log_threshold_ms_
                   << "ms), block: " << block_->ToString() << ", volume: "
                   << dn_->GetStorage()->GetVolumePath(disk_->GetDiskId());
    }
  }
  if (checksum_size_ > 0) {
    if (generate_checksum_from_mem_) {
      if (data_len > 0) {
        LOG(DEBUG) << "Generating checksum for block " << block_->ToString()
                   << " seqno " << seqno_;
        uint32_t gen_csum_off = checksum_off;
        uint64_t gen_data_off = data_off;
        for (uint32_t gen_seqno = 0; gen_seqno < num_chunks; gen_seqno++) {
          uint32_t gen_chunk_size =
              std::min(chunk_size_, (uint32_t)(chunk->Length() - gen_data_off));
          checksum_->Reset();
          checksum_->Update(chunk->Data(), gen_data_off, gen_chunk_size);
          chunk->ReplaceFixed32BE(gen_csum_off, checksum_->GetValue());
          gen_csum_off += checksum_size_;
          gen_data_off += gen_chunk_size;
        }
      }
    } else {
      // update checksum for rbw when block has checksum file and last chunk is
      // changing
      if (last_data_packet && last_disk_data_length_ != 0 &&
          (last_disk_data_length_ % chunk_size_) != 0) {
        BYTE_ASSERT_EQ(checksum_size_, (uint32_t)4);
        uint32_t partial_chunk_size = last_disk_data_length_ % chunk_size_;
        uint32_t last_chunk_off = data_off + data_len - partial_chunk_size;
        checksum_->Reset();
        checksum_->Update(chunk->Data(), last_chunk_off, partial_chunk_size);
        uint32_t offset = checksum_off + checksum_data_len - checksum_size_;
        chunk->ReplaceFixed32BE(offset, checksum_->GetValue());
      }
    }
  }

  // int64_t after_read = byte::GetCurrentTimeInNs();
  if (verify_checksum_) {
    auto e = checksum_->VerifyChecksum(chunk->Data() + data_off, data_len,
                                       chunk->Data() + checksum_off);
    if (!e.OK()) {
      LOG(ERROR) << "VerifyChecksum error exceptions is : " << e.ToString();
      return e;
    }
  }

  chunk->IncrOffset(headerOff);
  buf->Append(chunk);
  // TODO(caibingfeng)  if (transferto)
  uint64_t begin_time_us = byte::GetCurrentTimeInUs();
  auto flag = conn->Write(true);
  if (flag != IO_OK) {
    LOG(ERROR) << "write failed, flag:" << flag;
    return exceptions::Exception(exceptions::E::kIOException,
                                 "write packet failed");
  }
  uint64_t duration_us = byte::GetCurrentTimeInUs() - begin_time_us;
  METRICS_hdfs_block_sender_send_packet_latency_us->GetMetric(tags_)->Set(
      duration_us);
  if (duration_us / 1000 > datanode_slow_log_threshold_ms_) {
    LOG(WARNING) << "Slow BlockSender read data to remote cost:"
                 << duration_us / 1000
                 << "ms (threshold=" << datanode_slow_log_threshold_ms_
                 << "ms), block: " << block_->ToString() << ", to remote "
                 << conn->Address().ToString();
  }

  if (dn_->IsThroughputThrottleEnabled() && user_priority_ >= 0 &&
      media_throttler_ != nullptr) {
    int64_t wait_time_ms = media_throttler_->Throttle(
        thread_, ThrottleType::READ, user_priority_, packet_len);
    if (wait_time_ms > 0) {
      wait_time_ms_for_throttle_ += wait_time_ms;
      METRICS_hdfs_send_throttler_wait_num->GetMetric(tags_)->Increment();
      METRICS_hdfs_send_throttler_wait_time_ms->GetMetric(tags_)->Set(
          wait_time_ms);
    }
  }
  // int64_t after_write = byte::GetCurrentTimeInNs();
  // LOG(INFO) << "before_read:" << before_read << " after_read:" << after_read
  // << " after_write:"
  //  << after_write << " read cost:" << (after_read - before_read) << " write
  //  cost:"
  //  << (after_write - after_read);
  *len = data_len;
  return exceptions::Exception();
}

void BlockSender::manageOsCache() {
  // TODO(livexmm) manageOsCache
}

// bool BlockSender::IsLongRead() const {
//     return (end_offset_ - initial_offset_) > LONG_READ_THRESHOLD_BYTES;
// }

uint32_t BlockSender::NumberOfChunks(uint64_t datalen) {
  return static_cast<uint32_t>((datalen + chunk_size_ - 1) / chunk_size_);
}

exceptions::Exception BlockSender::WritePacketHeader(io::IOChunk* chunk,
                                                     uint32_t data_len,
                                                     uint32_t packet_len,
                                                     int* head_len) {
  auto header = PacketHeader::CreatePacketHeader(
      packet_len, offset_, seqno_, (data_len == 0), data_len, false);
  int size = header->GetSerializedSize();
  chunk->IncrLength(PacketHeader::PKT_MAX_HEADER_LEN - size);
  header->PutInChunk(chunk);
  delete header;
  *head_len = size;
  return exceptions::Exception();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
