// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "hdfs/data_transfer.h"

#include <algorithm>
#include <random>
#include <sstream>
#include <utility>

#include "byte/util/scope_guard.h"
#include "chunkserver/chunkserver.h"
#include "common/media_flags.h"
#include "gflags/gflags.h"
#include "hdfs/block.h"
#include "hdfs/block_pool_actor.h"
#include "hdfs/block_pool_manager.h"
#include "hdfs/block_pool_service.h"
#include "hdfs/block_report_options.h"
#include "hdfs/block_sender.h"
#include "hdfs/blocks_with_locations.h"
#include "hdfs/caching_strategy.h"
#include "hdfs/chunkserver_store.h"
#include "hdfs/constants.h"
#include "hdfs/data_pipeline_context.h"
#include "hdfs/data_xceiver.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_command.h"
#include "hdfs/datanode_config.h"
#include "hdfs/datanode_control_io.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/datanode_sender.h"
#include "hdfs/datanode_stream_server.h"
#include "hdfs/disk_stat_summary.h"
#include "hdfs/extended_block.h"
#include "hdfs/finalized_replica.h"
#include "hdfs/io/address.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/io/net.h"
#include "hdfs/recovering_block.h"
#include "hdfs/replica_recovery_info.h"
#include "hdfs/replica_state.h"
#include "hdfs/security/block_pool_token_secret_manager.h"
#include "hdfs/security/block_token_secret_manager.h"
#include "hdfs/security/token.h"
#include "hdfs/util.h"

DECLARE_uint32(bytestore_hdfs_socket_timeout);
DECLARE_uint32(bytestore_hdfs_socket_write_timeout);
DECLARE_uint32(bytestore_hdfs_socket_write_timeout_extension);
DECLARE_MEDIA_FLAG_uint32(bytestore_hdfs_max_replicate_concurrency_per_disk);
DECLARE_string(bytestore_hdfs_default_user);
DECLARE_bool(bytestore_hdfs_xceiver_tags_with_user);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DECLARE_COUNTER_METRICS_POOL(hdfs_xmits_read_throughput);
DECLARE_GUAGE_METRICS_POOL(hdfs_replicate_in_progress_per_disk);

DataTransfer::DataTransfer(const int32_t resident_time,
                           std::vector<DatanodeInfo*>* targets,
                           const std::vector<StorageType>& target_storage_types,
                           ExtendedBlock* block,
                           const BlockConstructionStage& stage,
                           const std::string& client_name, DataNode* datanode)
    : xceiver_(nullptr),
      resident_time_(resident_time),
      target_storage_types_(target_storage_types),
      block_(block),
      stage_(stage),
      client_name_(client_name),
      datanode_(datanode),
      op_key_(OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                         client_name.empty() ? Operation::TransferReplica
                                             : Operation::TransferBlock)),
      disk_data_transfer_manager_(nullptr) {
  is_datanode_ = client_name.empty();
  for (size_t i = 0; i < targets->size(); i++) {
    targets_.emplace_back((*targets)[i]);
    (*targets)[i] = nullptr;
  }
  std::ostringstream os;
  auto it = target_storage_types.begin();
  os << "[";
  if (it != target_storage_types.end()) {
    os << it->ToString();
    it++;
  }
  for (; it != target_storage_types.end(); it++) {
    os << ", " << it->ToString();
  }
  os << "]";
  LOG(DEBUG) << "DataTransfer: " << block->ToString()
             << " (numBytes = " << block->GetBlock()->GetNumBytes() << ")"
             << ", stage = " << stage.ToString()
             << ", clientName = " << client_name
             << ", targetStorageTypes = " << os.str();
  context_ = DataPipelineContext::NewDefaultContext();
  caching_strategy_ = CachingStrategy::NewDefaultStrategy();
  auto op = client_name.empty()
                ? DataNodeStreamServer::Operation::TransferReplica
                : DataNodeStreamServer::Operation::TransferBlock;
  datanode_->GetDataNodeStreamServer()->AddXceiverOperation(
      block_->GetBlockPoolID(), op);
}

DataTransfer::~DataTransfer() {
  auto op = client_name_.empty()
                ? DataNodeStreamServer::Operation::TransferReplica
                : DataNodeStreamServer::Operation::TransferBlock;
  datanode_->GetDataNodeStreamServer()->DeleteXceiverOperation(
      block_->GetBlockPoolID(), op);
  for (auto target : targets_) {
    delete target;
  }
  delete block_;
  delete caching_strategy_;
  delete context_;
  // datanode_ deleted by DataNode
}

void DataTransfer::MainRun() {
  // bp_reg deleted by BlockPoolService
  std::shared_ptr<DatanodeRegistration> bp_reg;
  auto e = datanode_->GetDNRegistrationForBP(block_->GetBlockPoolID(), &bp_reg);
  if (!e.OK()) {
    LOG(WARNING) << "GetDNRegistrationForBP failed";
    return;
  }

  datanode_->IncXmitsInProcess(is_datanode_);
  byte::ScopeGuard xmits_guard([this] {
    datanode_->DecXmitsInProcess(is_datanode_);
  });
  const bool is_client = client_name_.length() > 0;

  BYTE_ASSERT(targets_.size() > 0);

  LOG(DEBUG) << "Connecting to datanode: "
             << targets_[0]->GetDatanodeID()->GetXferAddr().ToString();
  io::IPAddress address = targets_[0]->GetDatanodeID()->GetXferAddr();
  int fd = io::Net::CreateTcpSocket(address.IsIpv6());
  if (fd == -1) {
    LOG(ERROR) << "create tcp client socket failed";
    return;
  }

  io::NetFlags flags;
  flags.no_delay = true;
  flags.Apply(fd);

  auto conn = io::NewClientConnection(address.Index(), fd, address);
  std::unique_ptr<io::Connection> conn_deleter(conn);
  int ret = conn->Connect();
  if (ret != IO_OK) {
    LOG(WARNING) << "Exception occur when connectint to datanode "
                 << targets_[0]->GetDatanodeID()->GetXferAddr().ToString();
    return;
  }
  LOG(DEBUG) << "connect success";

  uint32_t read_timeout = FLAGS_bytestore_hdfs_socket_timeout * targets_.size();
  uint32_t write_timeout = FLAGS_bytestore_hdfs_socket_write_timeout +
                           FLAGS_bytestore_hdfs_socket_write_timeout_extension *
                               (targets_.size() - 1);
  io::Net::SetSendTimeout(fd, read_timeout);
  io::Net::SetRecvTimeout(fd, write_timeout);

  BlockSender* block_sender = nullptr;
  std::string user = CurUser();
  int user_priority = datanode_->GetUserPriority(user);
  // if pipeline recovery, current thread is a DataXceiver
  // else if replicate, current thread is a DataTransfer
  std::shared_ptr<Thread> cur_thread = xceiver_;
  if (cur_thread == nullptr) {
    cur_thread = GetPtr();
  }
  e = BlockSender::CreateBlockSender(
      cur_thread, block_, 0, block_->GetNumBytes(), false, true, datanode_,
      caching_strategy_, user, user_priority, &block_sender);
  std::unique_ptr<BlockSender> block_sender_deleter(block_sender);
  if (!e.OK()) {
    LOG(WARNING) << "TransferBlock fail when CreateBlockSender.\n"
                 << e.ToString();

    datanode_->CheckDiskErrorAsync();
    return;
  }
  block_sender->SetOpKey(op_key_);
  LOG(DEBUG) << "create block sender success";

  Token token;
  auto bp_token_secr_manager = datanode_->GetBlockPoolTokenSecretManager();
  if (!datanode_->IsBpInAccessAllowList(block_->GetBlockPoolID()) &&
      datanode_->IsBlockTokenEnabled() &&
      bp_token_secr_manager->IsBlockPoolRegistered(block_->GetBlockPoolID())) {
    std::vector<AccessMode> modes{AccessMode::WRITE};
    auto token_sp = bp_token_secr_manager->GenerateToken(block_, modes);
    if (token_sp == nullptr) {
      LOG(ERROR) << "failed to call GenerateToken for bp:"
                 << block_->GetBlockPoolID() << " In DataTransfer";
    } else {
      token = *token_sp;
    }
  }
  DatanodeInfo src_node(bp_reg->GetDatanodeID());
  DatanodeSender sender(context_->GetIoPriorityOptions(), conn);
  TraceBaggage traceBaggage;
  if (is_client) {
    sender.SetTraceBaggage(context_->GetTraceBaggage());
  } else {
    traceBaggage.Put("user", user);
    sender.SetTraceBaggage(traceBaggage);
  }
  if (sender.WriteBlock(
          block_, target_storage_types_[0], resident_time_, token, client_name_,
          targets_, target_storage_types_, &src_node, stage_, 0, 0, 0, 0,
          block_sender->GetChecksum(), caching_strategy_, false, "", 0,
          hadoop::hdfs::IOPriority::PRIORITY_ELASTIC) != IO_OK) {
    LOG(WARNING) << "TransferBlock fail when WriteBlock to datanode "
                 << targets_[0]->GetDatanodeID()->ToString();
    datanode_->CheckDiskErrorAsync();
    return;
  }
  LOG(DEBUG) << "block sender write block success";

  uint64_t sended = 0;
  e = block_sender->SendBlock(conn, &sended);
  Tags tags;
  if (FLAGS_bytestore_hdfs_xceiver_tags_with_user) {
    tags = {{"user", user},
            {"user_priority", byte::IntegerToString(user_priority)},
            {"bpid", block_->GetBlockPoolID()},
            {"is_datanode", is_datanode_ ? "true" : "false"}};
  } else {
    tags = {{"is_datanode", is_datanode_ ? "true" : "false"}};
  }
  METRICS_hdfs_xmits_read_throughput->GetMetric(tags)->Add(sended);
  if (!e.OK()) {
    LOG(WARNING) << "TransferBlock fail when SendBlock to datanode "
                 << targets_[0]->GetDatanodeID()->ToString() << "\n"
                 << e.ToString();

    datanode_->CheckDiskErrorAsync();
    return;
  }

  LOG(INFO) << "DataTransfer: Transmitted " << block_->ToString()
            << " (numBytes=" << block_->GetNumBytes() << ") to"
            << targets_[0]->GetDatanodeID()->ToString() << " user:" << user
            << " user_priority:" << byte::IntegerToString(user_priority) << ", "
            << ThrottleUtil::GetThrottleLog(
                   block_sender->GetWaitTimeMsForThrottle());

  // if is_client, read ack
  if (is_client) {
    auto close_ack = new hadoop::hdfs::DNTransferAckProto();
    if (ReadStatusProto(conn, close_ack) != IO_OK) {
      LOG(WARNING) << "DataTransfer: Prase close ack fail when transfer "
                   << block_->ToString() << " to "
                   << targets_[0]->GetDatanodeID()->ToString();

      datanode_->CheckDiskErrorAsync();
      delete close_ack;
      return;
    }
    LOG(DEBUG) << "DataTransfer: close-ack = " << close_ack->DebugString();
    if (close_ack->status() != hadoop::hdfs::Status::SUCCESS) {
      std::ostringstream os;
      os << "Bad connect ack, targes=[";
      auto iter = targets_.begin();
      if (iter != targets_.end()) {
        os << (*iter)->GetDatanodeID()->ToString();
        iter++;
      }
      for (; iter != targets_.end(); iter++) {
        os << ", " << (*iter)->GetDatanodeID()->ToString();
      }
      os << "]";

      LOG(WARNING) << bp_reg->ToString() << ":Failed to transfer "
                   << block_->ToString() << " to "
                   << targets_[0]->GetDatanodeID()->ToString()
                   << " due to: " << os.str();

      datanode_->CheckDiskErrorAsync();
      ret = IO_ERR;
    }
    delete close_ack;
    OpStats::GetInstance().IncrQps(op_key_);
  }
}

void DataTransfer::Run() {
  LOG(INFO) << "data transfer is running, is_datanode:" << is_datanode_
            << " block:" << block_->ToString();
  MainRun();
  if (disk_data_transfer_manager_ != nullptr) {
    disk_data_transfer_manager_->FinishDataTransfer(GetPtr());
  }
}

void DataTransfer::SetTraceBaggage(const TraceBaggage& trace_baggage) {
  context_->SetTraceBaggage(trace_baggage);
}

std::string DataTransfer::CurUser() {
  return context_ == nullptr ? FLAGS_bytestore_hdfs_default_user
                             : context_->GetUser(true);
}

DiskDataTransferManager* DataTransfer::GetDiskDataTransferManager() {
  return disk_data_transfer_manager_;
}

void DataTransfer::SetDiskDataTransferManager(DiskDataTransferManager* ddts) {
  disk_data_transfer_manager_ = ddts;
}

std::string DataTransfer::GetExtendedBlockString() {
  return block_->ToString();
}

void DataTransfer::SetXceiver(std::shared_ptr<DataXceiver> xceiver) {
  xceiver_ = xceiver;
}

DiskDataTransferManager::DiskDataTransferManager(
    DiskId disk_id, StorageType type,
    DataTransferManager* data_transfer_manager)
    : disk_id_(disk_id),
      storage_type_(type),
      data_transfer_manager_(data_transfer_manager) {
  state_ = NORMAL;
  cur_transferring_ = 0;
  max_transferring_ = MFLAGS(bytestore_hdfs_max_replicate_concurrency_per_disk,
                             StorageType2MediaType(type));
}

void DiskDataTransferManager::Enqueue(
    std::shared_ptr<DataTransfer> replicate_worker) {
  std::string extended_block_str = replicate_worker->GetExtendedBlockString();
  if (IsClosed()) {
    LOG(INFO) << "enqueue data transfer request failed due to closed disk, "
              << " current num of transferring thread:"
              << cur_transferring_.Value()
              << " disk_id:" << byte::IntegerToString(disk_id_)
              << " storage_type:" << storage_type_.ToString()
              << " block:" << extended_block_str;
    return;
  }
  if (waiting_queue_.Push(extended_block_str, replicate_worker)) {
    LOG(INFO) << "enqueue data transfer request success,"
              << " current num of transferring thread:"
              << cur_transferring_.Value()
              << " disk_id:" << byte::IntegerToString(disk_id_)
              << " storage_type:" << storage_type_.ToString()
              << " block:" << extended_block_str;
  } else {
    LOG(INFO) << "enqueue data transfer request failed due to redundant block,"
              << " current num of transferring thread:"
              << cur_transferring_.Value()
              << " disk_id:" << byte::IntegerToString(disk_id_)
              << " storage_type:" << storage_type_.ToString()
              << " block:" << extended_block_str;
  }
}

std::shared_ptr<DataTransfer> DiskDataTransferManager::Dequeue() {
  std::shared_ptr<DataTransfer> next_worker = nullptr;
  if (!IsClosed() && !waiting_queue_.Empty()) {
    next_worker = waiting_queue_.Front();
    waiting_queue_.Pop();
  }
  return next_worker;
}

void DiskDataTransferManager::FinishDataTransfer(
    std::shared_ptr<DataTransfer> finished_worker) {
  METRICS_hdfs_replicate_in_progress_per_disk->GetMetric(GetDiskTags())
      ->Set(DecrementCurTransferring());
  data_transfer_manager_->FinishDataTransfer(finished_worker);
}

bool DiskDataTransferManager::CanTransferring() {
  return !IsClosed() && cur_transferring_.Value() < max_transferring_.Value();
}

uint32_t DiskDataTransferManager::GetMaxTransferring() {
  return max_transferring_.Value();
}

uint32_t DiskDataTransferManager::GetCurTransferring() {
  return cur_transferring_.Value();
}

uint32_t DiskDataTransferManager::IncrementCurTransferring() {
  return ++cur_transferring_;
}

uint32_t DiskDataTransferManager::DecrementCurTransferring() {
  return --cur_transferring_;
}

void DiskDataTransferManager::SetMaxTransferPerDisk(uint32_t concurrency) {
  max_transferring_ = concurrency;
}

Tags DiskDataTransferManager::GetDiskTags() {
  Tags disk_tags = {{"media_type", MediaTypeToShortString(
                                       StorageType2MediaType(storage_type_))},
                    {"disk_id", byte::IntegerToString(disk_id_)}};
  return disk_tags;
}

MediaType DiskDataTransferManager::StorageType2MediaType(
    StorageType storage_type) {
  switch (storage_type.GetType()) {
    case StorageType::TYPE_ID_SSD: return MediaType::TYPE_NVME_SSD;
    default: return MediaType::TYPE_SATA_HDD;
  }
}

void DiskDataTransferManager::Close() {
  state_ = CLOSED;
  waiting_queue_.Clear();
}

void DiskDataTransferManager::Open(StorageType type) {
  state_ = NORMAL;
  storage_type_ = type;
  max_transferring_ = MFLAGS(bytestore_hdfs_max_replicate_concurrency_per_disk,
                             StorageType2MediaType(type));
}

DataTransferManager::~DataTransferManager() {
  Stop();
  Join();
  disk_data_transfer_manager_map_.ForEach(
      [](DiskId disk_id, DiskDataTransferManager* ddtm) {
        delete ddtm;
      });
}

void DataTransferManager::AddDiskDataTransferManager(DiskId disk_id,
                                                     StorageType storage_type) {
  DiskDataTransferManager* ddtm = nullptr;
  if (!disk_data_transfer_manager_map_.Get(disk_id, ddtm)) {
    LOG(INFO) << "add disk data transfer manager, disk_id:" << disk_id;
    ddtm = new DiskDataTransferManager(disk_id, storage_type, this);
    disk_data_transfer_manager_map_.Put(disk_id, ddtm);
  } else if (ddtm->IsClosed()) {
    LOG(INFO) << "open disk data transfer manager, disk_id:" << disk_id;
    ddtm->Open(storage_type);
  } else {
    LOG(INFO) << "disk data transfer manager is already opened, disk_id:"
              << disk_id;
  }
}

void DataTransferManager::CloseDiskDataTransferManager(DiskId disk_id) {
  DiskDataTransferManager* ddtm = nullptr;
  if (disk_data_transfer_manager_map_.Get(disk_id, ddtm)) {
    if (!ddtm->IsClosed()) {
      LOG(INFO) << "close disk data transfer manager, disk_id:" << disk_id;
      ddtm->Close();
    } else {
      LOG(INFO) << "disk data transfer manager is already closed, disk_id:"
                << disk_id;
    }
  } else {
    LOG(INFO) << "disk data transfer manager not found, disk_id:" << disk_id;
  }
}

bool DataTransferManager::GetDiskDataTransferManager(
    DiskId disk_id, DiskDataTransferManager** ddtm) {
  if (!disk_data_transfer_manager_map_.Get(disk_id, *ddtm)) {
    return false;
  }
  return true;
}

bool DataTransferManager::AddDataTransferRequest(
    DiskId disk_id, std::shared_ptr<DataTransfer> replicate_worker) {
  // check whether the DiskDataTransferManager exists
  DiskDataTransferManager* ddtm;
  if (!disk_data_transfer_manager_map_.Get(disk_id, ddtm)) {
    return false;
  }
  replicate_worker->SetDiskDataTransferManager(ddtm);
  AddWorker(replicate_worker);
  return true;
}

void DataTransferManager::FinishDataTransfer(
    std::shared_ptr<DataTransfer> finished_worker) {
  DeleteWorker(finished_worker);
}

void DataTransferManager::Run() {
  while (!IsStopped()) {
    WaitUtil(
        [this]() {
          return !request_queue_.Empty() || IsStopped();
        },
        0);
    while (!request_queue_.Empty()) {
      auto item = request_queue_.Front();
      request_queue_.Pop();

      std::shared_ptr<DataTransfer> worker = item.second;
      DiskDataTransferManager* ddtm = worker->GetDiskDataTransferManager();
      switch (item.first) {
        case DataTransferManager::Type::ADD:
          if (ddtm->CanTransferring()) {
            transferring_set_.emplace(worker);
            METRICS_hdfs_replicate_in_progress_per_disk
                ->GetMetric(ddtm->GetDiskTags())
                ->Set(ddtm->IncrementCurTransferring());
            worker->Start();
          } else {
            ddtm->Enqueue(worker);
          }
          break;
        case DataTransferManager::Type::DELETE:
          worker->Stop();
          worker->Join();
          transferring_set_.erase(worker);
          // finished worker will be destructed automatically
          std::shared_ptr<DataTransfer> next_worker = ddtm->Dequeue();
          if (next_worker != nullptr) {
            AddWorker(next_worker);
          }
          break;
      }
    }
  }
  Cleanup();
}

void DataTransferManager::AddWorker(std::shared_ptr<DataTransfer> worker) {
  request_queue_.Push(std::make_pair(DataTransferManager::Type::ADD, worker));
  Signal();
}

void DataTransferManager::DeleteWorker(std::shared_ptr<DataTransfer> worker) {
  request_queue_.Push(
      std::make_pair(DataTransferManager::Type::DELETE, worker));
  Signal();
}

void DataTransferManager::Cleanup() {
  // all started workers, whether finished or not, are in transferring_set_
  for (std::shared_ptr<DataTransfer> transferring_worker : transferring_set_) {
    transferring_worker->Stop();
    transferring_worker->Join();
    // Finished worker already produce a DELETE request
  }
  transferring_set_.clear();
  // At this point, all of started workers has called stop and join.
  // All data transfer workers are either not started or have finished,
  // They can be destructed automatically with that not started
}

void DataTransferManager::SetMaxReplicateConcurrencyPerDisk(
    StorageType storage_type, uint32_t value) {
  disk_data_transfer_manager_map_.ForEach(
      [&](DiskId disk_id, DiskDataTransferManager* ddtm) {
        if (ddtm->GetStorageType().GetType() == storage_type.GetType()) {
          ddtm->SetMaxTransferPerDisk(value);
        }
      });
}

// for ut
uint32_t DataTransferManager::GetCurTransferring(DiskId disk_id) {
  DiskDataTransferManager* ddtm;
  if (!disk_data_transfer_manager_map_.Get(disk_id, ddtm)) {
    return -1;
  }
  return ddtm->GetCurTransferring();
}

int DataTransferManager::GetWaitingNum(DiskId disk_id) {
  DiskDataTransferManager* ddtm;
  if (!disk_data_transfer_manager_map_.Get(disk_id, ddtm)) {
    return -1;
  }
  return ddtm->GetWaitingNum();
}

std::map<DiskId, uint32_t> DataTransferManager::GetMaxReplicateConcurrency() {
  std::map<DiskId, uint32_t> disk_id_to_con;
  disk_data_transfer_manager_map_.ForEach(
      [&](DiskId disk_id, DiskDataTransferManager* ddtm) {
        disk_id_to_con.emplace(disk_id, ddtm->GetMaxTransferring());
      });
  return disk_id_to_con;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
