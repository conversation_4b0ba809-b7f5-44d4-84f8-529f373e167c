// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "hdfs/data_transfer_throttler.h"

#include "common/media_flags.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "hdfs/datanode.h"
#include "hdfs/mocks.h"

DECLARE_uint32(bytestore_hdfs_user_priority_num);
DECLARE_int32(bytestore_hdfs_user_default_priority);
DECLARE_int64(bytestore_hdfs_data_transfer_throttler_period_ms);
DECLARE_MEDIA_FLAG_int64(bytestore_hdfs_write_throughput_threshold_per_disk_p0);
DECLARE_MEDIA_FLAG_int64(bytestore_hdfs_write_throughput_threshold_per_disk_p1);
DECLARE_MEDIA_FLAG_int64(bytestore_hdfs_write_throughput_threshold_per_disk_p2);
DECLARE_MEDIA_FLAG_int64(bytestore_hdfs_read_throughput_threshold_per_disk_p0);
DECLARE_MEDIA_FLAG_int64(bytestore_hdfs_read_throughput_threshold_per_disk_p1);
DECLARE_MEDIA_FLAG_int64(bytestore_hdfs_read_throughput_threshold_per_disk_p2);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DataTransferThrottlerTests : public ::testing::Test {
 public:
  DataTransferThrottlerTests() {}

  ~DataTransferThrottlerTests() {}

  void SetUp() {}

  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(DataTransferThrottlerTests, CreateMediaThrottler) {
  std::shared_ptr<MockDataNode> dn = std::make_shared<MockDataNode>();
  std::shared_ptr<ThrottleConfig> tc = dn->GetThrottleConfig();
  // ssd disk throttler
  StorageType ssd = StorageType::SSD;
  uint32_t ssd_disk_num = 2;
  std::shared_ptr<MediaThrottler> mt_ssd =
      MediaThrottler::Create(ssd, ssd_disk_num, tc);
  // ssd write
  auto mt_ssd_write_period_p0 = mt_ssd->GetPeriod(ThrottleType::WRITE, 0);
  if (mt_ssd_write_period_p0 >= 0) {
    ASSERT_EQ(mt_ssd_write_period_p0,
              tc->get_data_transfer_throttler_period_());
  }
  auto mt_ssd_write_period_p1 = mt_ssd->GetPeriod(ThrottleType::WRITE, 1);
  if (mt_ssd_write_period_p1 >= 0) {
    ASSERT_EQ(mt_ssd_write_period_p1,
              tc->get_data_transfer_throttler_period_());
  }
  auto mt_ssd_write_period_p2 = mt_ssd->GetPeriod(ThrottleType::WRITE, 2);
  if (mt_ssd_write_period_p2 >= 0) {
    ASSERT_EQ(mt_ssd_write_period_p2,
              tc->get_data_transfer_throttler_period_());
  }
  auto mt_ssd_write_throughput_threshold_p0 =
      mt_ssd->GetBandwidth(ThrottleType::WRITE, 0);
  if (mt_ssd_write_throughput_threshold_p0 >= 0) {
    ASSERT_EQ(mt_ssd_write_throughput_threshold_p0,
              ssd_disk_num * tc->get_write_throughput_threshold_p0_(ssd));
  }
  auto mt_ssd_write_throughput_threshold_p1 =
      mt_ssd->GetBandwidth(ThrottleType::WRITE, 1);
  if (mt_ssd_write_throughput_threshold_p1 >= 0) {
    ASSERT_EQ(mt_ssd_write_throughput_threshold_p1,
              ssd_disk_num * tc->get_write_throughput_threshold_p1_(ssd));
  }
  auto mt_ssd_write_throughput_threshold_p2 =
      mt_ssd->GetBandwidth(ThrottleType::WRITE, 2);
  if (mt_ssd_write_throughput_threshold_p2 >= 0) {
    ASSERT_EQ(mt_ssd_write_throughput_threshold_p2,
              ssd_disk_num * tc->get_write_throughput_threshold_p2_(ssd));
  }
  // ssd read
  auto mt_ssd_read_period_p0 = mt_ssd->GetPeriod(ThrottleType::READ, 0);
  if (mt_ssd_read_period_p0 >= 0) {
    ASSERT_EQ(mt_ssd_read_period_p0, tc->get_data_transfer_throttler_period_());
  }
  auto mt_ssd_read_period_p1 = mt_ssd->GetPeriod(ThrottleType::READ, 1);
  if (mt_ssd_read_period_p1 >= 0) {
    ASSERT_EQ(mt_ssd_read_period_p1, tc->get_data_transfer_throttler_period_());
  }
  auto mt_ssd_read_period_p2 = mt_ssd->GetPeriod(ThrottleType::READ, 2);
  if (mt_ssd_read_period_p2 >= 0) {
    ASSERT_EQ(mt_ssd_read_period_p2, tc->get_data_transfer_throttler_period_());
  }
  auto mt_ssd_write_read_threshold_p0 =
      mt_ssd->GetBandwidth(ThrottleType::READ, 0);
  if (mt_ssd_write_read_threshold_p0 >= 0) {
    ASSERT_EQ(mt_ssd_write_read_threshold_p0,
              ssd_disk_num * tc->get_read_throughput_threshold_p0_(ssd));
  }
  auto mt_ssd_write_read_threshold_p1 =
      mt_ssd->GetBandwidth(ThrottleType::READ, 1);
  if (mt_ssd_write_read_threshold_p1 >= 0) {
    ASSERT_EQ(mt_ssd_write_read_threshold_p1,
              ssd_disk_num * tc->get_read_throughput_threshold_p1_(ssd));
  }
  auto mt_ssd_write_read_threshold_p2 =
      mt_ssd->GetBandwidth(ThrottleType::READ, 2);
  if (mt_ssd_write_read_threshold_p2 >= 0) {
    ASSERT_EQ(mt_ssd_write_read_threshold_p2,
              ssd_disk_num * tc->get_read_throughput_threshold_p2_(ssd));
  }
  // hdd disk throttler
  StorageType hdd = StorageType::DISK;
  uint32_t hdd_disk_num = 24;
  std::shared_ptr<MediaThrottler> mt_hdd =
      MediaThrottler::Create(hdd, hdd_disk_num, dn->GetThrottleConfig());
  // hdd write
  auto mt_hdd_write_period_p0 = mt_ssd->GetPeriod(ThrottleType::WRITE, 0);
  if (mt_hdd_write_period_p0 >= 0) {
    ASSERT_EQ(mt_hdd_write_period_p0,
              tc->get_data_transfer_throttler_period_());
  }
  auto mt_hdd_write_period_p1 = mt_ssd->GetPeriod(ThrottleType::WRITE, 1);
  if (mt_hdd_write_period_p1 >= 0) {
    ASSERT_EQ(mt_hdd_write_period_p1,
              tc->get_data_transfer_throttler_period_());
  }
  auto mt_hdd_write_period_p2 = mt_ssd->GetPeriod(ThrottleType::WRITE, 2);
  if (mt_hdd_write_period_p2 >= 0) {
    ASSERT_EQ(mt_hdd_write_period_p2,
              tc->get_data_transfer_throttler_period_());
  }
  auto mt_hdd_write_throughput_threshold_p0 =
      mt_hdd->GetBandwidth(ThrottleType::WRITE, 0);
  if (mt_hdd_write_throughput_threshold_p0 >= 0) {
    ASSERT_EQ(mt_hdd_write_throughput_threshold_p0,
              hdd_disk_num * tc->get_write_throughput_threshold_p0_(hdd));
  }
  auto mt_hdd_write_throughput_threshold_p1 =
      mt_hdd->GetBandwidth(ThrottleType::WRITE, 1);
  if (mt_hdd_write_throughput_threshold_p1 >= 0) {
    ASSERT_EQ(mt_hdd_write_throughput_threshold_p1,
              hdd_disk_num * tc->get_write_throughput_threshold_p1_(hdd));
  }
  auto mt_hdd_write_throughput_threshold_p2 =
      mt_hdd->GetBandwidth(ThrottleType::WRITE, 2);
  if (mt_hdd_write_throughput_threshold_p2 >= 0) {
    ASSERT_EQ(mt_hdd_write_throughput_threshold_p2,
              hdd_disk_num * tc->get_write_throughput_threshold_p2_(hdd));
  }
  // hdd read
  auto mt_hdd_read_period_p0 = mt_ssd->GetPeriod(ThrottleType::READ, 0);
  if (mt_hdd_read_period_p0 >= 0) {
    ASSERT_EQ(mt_hdd_read_period_p0, tc->get_data_transfer_throttler_period_());
  }
  auto mt_hdd_read_period_p1 = mt_ssd->GetPeriod(ThrottleType::READ, 1);
  if (mt_hdd_read_period_p1 >= 0) {
    ASSERT_EQ(mt_hdd_read_period_p1, tc->get_data_transfer_throttler_period_());
  }
  auto mt_hdd_read_period_p2 = mt_ssd->GetPeriod(ThrottleType::READ, 2);
  if (mt_hdd_read_period_p2 >= 0) {
    ASSERT_EQ(mt_hdd_read_period_p2, tc->get_data_transfer_throttler_period_());
  }
  auto mt_hdd_read_throughput_threshold_p0 =
      mt_hdd->GetBandwidth(ThrottleType::READ, 0);
  if (mt_hdd_read_throughput_threshold_p0 >= 0) {
    ASSERT_EQ(mt_hdd_read_throughput_threshold_p0,
              hdd_disk_num * tc->get_read_throughput_threshold_p0_(hdd));
  }
  auto mt_hdd_read_throughput_threshold_p1 =
      mt_hdd->GetBandwidth(ThrottleType::READ, 1);
  if (mt_hdd_read_throughput_threshold_p1 >= 0) {
    ASSERT_EQ(mt_hdd_read_throughput_threshold_p1,
              hdd_disk_num * tc->get_read_throughput_threshold_p1_(hdd));
  }
  auto mt_hdd_read_throughput_threshold_p2 =
      mt_hdd->GetBandwidth(ThrottleType::READ, 2);
  if (mt_hdd_read_throughput_threshold_p2 >= 0) {
    ASSERT_EQ(mt_hdd_read_throughput_threshold_p2,
              hdd_disk_num * tc->get_read_throughput_threshold_p2_(hdd));
  }
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
