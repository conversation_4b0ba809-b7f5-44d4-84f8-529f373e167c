// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/directory_scanner.h"

#include <memory>

#include "byte/base/closure.h"
#include "byte/concurrent/timer_manager.h"
#include "byte/include/byte_log.h"
#include "chunkserver/chunk_store/chunk_store.h"
#include "chunkserver/disk.h"
#include "chunkserver/modules/delete_manager.h"
#include "common/async_thread.h"
#include "common/media_flags.h"
#include "common/memory_pool_repo.h"
#include "common/metrics.h"
#include "common/timer.h"
#include "common/utility.h"
#include "gflags/gflags.h"
#include "hdfs/chunkserver_store.h"

DECLARE_int32(bytestore_hdfs_directory_scanner_concurrency);
DECLARE_uint32(bytestore_hdfs_directory_scanner_interval_time_ms);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DirectoryScanner::DirectoryScanner(Store* storage)
    : storage_(storage), stopping_(true) {
  run_thread_.reset(new AsyncThreadImpl());
  BYTE_ASSERT(run_thread_->Init("directory_scanner_thread"));
  lock_.reset(new byte::Mutex);
}

DirectoryScanner::~DirectoryScanner() {}

void DirectoryScanner::Start() {
  if (!run_thread_->Start()) {
    LOG(ERROR) << log_prefix_ << "start schedule thread failed.";
    return;
  }
  timer_callback_.reset(NewPermanentClosure(this, &DirectoryScanner::execute));
  timer_.reset(run_thread_->CreateTimer());
  // generate a random interval in ms for the first round
  unsigned int seed = time(NULL);
  uint32_t first_interval_ms =
      1000 *
      (rand_r(&seed) %
           (FLAGS_bytestore_hdfs_directory_scanner_interval_time_ms / 1000) +
       1);
  LOG(INFO) << log_prefix_ << "Start, first_interval:" << first_interval_ms
            << "ms";
  timer_->Init(Timer::Mode::ONCE, 1000ULL * first_interval_ms,
               timer_callback_.get());
  timer_->StartInThread(run_thread_.get());
  stopping_ = false;
}

void DirectoryScanner::Stop() {
  LOG(INFO) << log_prefix_ << "Stop";
  stopping_ = true;
  if (timer_.get()) {
    timer_->StopInThread(run_thread_.get());
  }
  if (!run_thread_->Stop()) {
    LOG(WARNING) << log_prefix_ << "stop schedule thread failed.";
  }
}

void DirectoryScanner::execute() {
  BYTE_ASSERT(GetCurrentThread() == run_thread_.get());

  if (stopping_.Value()) {
    LOG(INFO) << log_prefix_ << "Reject to execute because of stopping";
    return;
  }

  disk_directory_queue_ = storage_->GetDisks();
  if (disk_directory_queue_.empty()) {
    LOG(WARNING) << log_prefix_ << "disk_directory_queue_ is empty!";
    // reschedule for the next round directory scan
    reschedule();
    return;
  }

  int scan_depth = FLAGS_bytestore_hdfs_directory_scanner_concurrency;
  if (scan_depth == -1) {
    scan_depth = disk_directory_queue_.size();
  }
  std::vector<std::thread> scan_threads;
  for (int i = 0; i < scan_depth; ++i) {
    scan_threads.push_back(
        std::thread(&DirectoryScanner::Scan, this, i, scan_depth));
  }
  for (auto& th : scan_threads) {
    th.join();
  }

  // reschedule for the next round directory scan
  reschedule();
}

bool DirectoryScanner::Next(DiskDirectory* dd) {
  byte::Mutex::Locker guard(lock_.get());
  if (disk_directory_queue_.empty()) {
    return false;
  }
  *dd = disk_directory_queue_.front();
  disk_directory_queue_.pop_front();
  return true;
}

void DirectoryScanner::Scan(int index, int scan_depth) {
  while (true) {
    DiskDirectory dd;
    if (!Next(&dd)) {
      LOG(INFO) << log_prefix_ << "there is no more disk. scanner_id:" << index
                << " scan_depth:" << scan_depth;
      break;
    }
    Disk* disk = dd.disk_;
    if (disk == nullptr) {
      LOG(WARNING) << log_prefix_ << "disk is nullptr. scanner_id:" << index
                   << " scan_depth:" << scan_depth;
      continue;
    }
    if (storage_->IsVolumeClosed(disk)) {
      LOG(WARNING) << log_prefix_ << "disk is closed. scanner_id:" << index
                   << " scan_depth:" << scan_depth
                   << " disk_id:" << disk->GetDiskId();
      continue;
    }
    StorageDirectory sd = dd.sd_;
    ChunkStore* chunk_store = disk->GetChunkStore();
    LOG(INFO) << log_prefix_
              << "Start scan directory from disk. disk_id:" << disk->GetDiskId()
              << " scanner_id:" << index << " scan_depth:" << scan_depth;
    int64_t start_us = byte::GetCurrentTimeInUs();
    ChunkIteratorOptions options;
    std::unique_ptr<ChunkIterator> iter(chunk_store->NewChunkIterator(options));
    DeleteManager* delete_manager = disk->GetDeleteManager();
    for (; iter->Valid(); iter->Next()) {
      StorageMeta chunk_meta;
      iter->Current(&chunk_meta);
      ChunkId chunk_id = chunk_meta.chunk_id_;
      ChunkIdMeta* chunk_id_meta =
          reinterpret_cast<ChunkIdMeta*>(reinterpret_cast<char*>(&chunk_id));
      BlockMeta* block_meta = reinterpret_cast<BlockMeta*>(chunk_meta.xattrs_);
      LOG(DEBUG) << log_prefix_ << "currrent chunk_id:" << chunk_id.ToString()
                 << " bpid:" << chunk_id_meta->GetBlockPoolId()
                 << " length:" << chunk_meta.meta_.length_
                 << " gs:" << block_meta->gs_
                 << " disk_id:" << disk->GetDiskId();
      // Filter1: checkpoint chunk.
      if (IsInternalChunk(chunk_id)) {
        continue;
      }

      // Filter2: invisible chunk.
      if (chunk_meta.meta_.invisible_ == 1) {
        LOG(DEBUG) << log_prefix_ << "Omit invisible chunk. "
                   << "chunk_meta:" << chunk_meta.meta_.ToString();
        continue;
      }

      // Filter3: replicating chunk.
      if (chunk_meta.meta_.replicating_ == 1) {
        LOG(DEBUG) << log_prefix_ << "Omit replicating chunk. "
                   << "chunk_meta:" << chunk_meta.meta_.ToString();
        continue;
      }

      // Filter4: deleting chunk.
      if (delete_manager->IsChunkDeleting(chunk_id)) {
        continue;
      }

      int fin = block_meta->fin_;
      int tmp = block_meta->tmp_;
      uint64_t gs = block_meta->gs_;
      uint64_t block_id = chunk_id_meta->block_id_;

      std::unique_ptr<ExtendedBlock> block(
          new ExtendedBlock(chunk_id_meta->GetBlockPoolId(), block_id,
                            chunk_meta.meta_.length_, gs));
      ReplicaState replica_state;
      if (fin == 1) {  // finalized
        replica_state = ReplicaState::FINALIZED;
      } else if (tmp == 0) {  // rbw
        replica_state = ReplicaState::RBW;
      } else {  // tmp
        replica_state = ReplicaState::TEMPORARY;
      }

      // directory scanner only cares about blocks in FINALIZED status
      if (replica_state != ReplicaState::FINALIZED) {
        continue;
      }

      // corrupt chunk
      if (chunk_meta.meta_.status_ == 1) {
        auto e = storage_->CheckAndUpdate(block.get(), replica_state,
                                          disk->GetDiskId(), true);
        if (!e.OK()) {
          LOG(ERROR) << log_prefix_
                     << "check and update failed, exception:" << e.ToString();
        }
        continue;
      }

      std::shared_ptr<ReplicaInfo> replica_info = storage_->GetReplica(
          block->GetBlockPoolID(), block->GetBlock()->GetBlockID());
      if (replica_info == nullptr) {
        auto e = storage_->CheckAndUpdate(block.get(), replica_state,
                                          disk->GetDiskId(), false);
        if (!e.OK()) {
          LOG(ERROR) << log_prefix_
                     << "check and update failed, exception:" << e.ToString();
        }
      } else {
        std::string replica_storage_uuid = replica_info->GetStorageUuid();
        if (storage_->GetStorageType(sd.storage_uuid_)
                .Equals(StorageType::SSD) &&
            storage_->GetStorageType(replica_storage_uuid)
                .Equals(StorageType::DISK)) {
          auto e = storage_->CheckAndUpdate(block.get(), replica_state,
                                            disk->GetDiskId(), false);
          if (!e.OK()) {
            LOG(ERROR) << log_prefix_
                       << "check and update failed, exception:" << e.ToString();
          }
        }
      }
    }

    LOG(INFO) << log_prefix_ << "scan directory from disk finished."
              << " disk_id:" << disk->GetDiskId() << " scanner_id:" << index
              << " cost_us:"
              << GetDiffTime(start_us, byte::GetCurrentTimeInUs());
  }
}

void DirectoryScanner::reschedule() {
  if (stopping_.Value()) {
    LOG(INFO) << log_prefix_ << "Reject to reschedule because of stopping";
    return;
  }

  uint32_t interval_ms =
      FLAGS_bytestore_hdfs_directory_scanner_interval_time_ms;
  LOG(INFO) << log_prefix_ << "reschedule, interval:" << interval_ms << "ms";
  timer_->Init(Timer::Mode::ONCE, 1000ULL * interval_ms, timer_callback_.get());
  timer_->StartInThread(run_thread_.get());
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
