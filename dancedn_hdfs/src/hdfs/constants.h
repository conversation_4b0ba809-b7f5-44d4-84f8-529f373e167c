// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once
#include <string>

namespace bytestore {
namespace chunkserver {
namespace hdfs {

enum NodeType {
  NAME_NODE = 0,
  DATA_NODE = 1,
  JOURNAL_NODE = 2,
};

enum StreamType : uint8_t {
  WRITE_BLOCK = 80,
  READ_BLOCK = 81,
  READ_METADATA = 82,
  REPLACE_BLOCK = 83,
  COPY_BLOCK = 84,
  BLOCK_CHECKSUM = 85,
  TRANSFER_BLOCK = 86,
  REQUEST_SHORT_CIRCUIT_FDS = 87,
  RELEASE_SHORT_CIRCUIT_FDS = 88,
  REQUEST_SHORT_CIRCUIT_SHM = 89,
  READ_BLOCK_VIA_UPSTREAM = 90,
  COPY_BLOCK_ACROSS_FEDERATION = 91,
};

constexpr static int DATANODE_LAYOUT_VERSION = -60;
constexpr static char DATANODE_SOFTWARE_VERSION[] = "2.6.0";
constexpr static char DISK_ERROR_STRING[] = "Possible disk error: ";
constexpr static uint8_t DISK_ERROR_LENGTH = 22;

constexpr static uint8_t DATANODE_STREAM_VERSION = 28;
constexpr static uint8_t RPC_CURRENT_VERSION = 9;
constexpr static int RPC_SERVICE_CLASS_DEFAULT = 0;

constexpr static int AUTHORIZATION_FAILED_CALL_ID = -1;
constexpr static int INVALID_CALL_ID = -2;
constexpr static int CONNECTION_CONTEXT_CALL_ID = -3;
constexpr static int PING_CALL_ID = -4;

class HdfsVersion {
 public:
  constexpr static const char* HDFS_CURRENT_VERSION = "2.6.0";
};
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
