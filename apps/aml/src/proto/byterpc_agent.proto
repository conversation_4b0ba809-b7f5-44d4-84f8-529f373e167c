syntax="proto2";

package byterpc_agent.proto;

option cc_generic_services = true;

message PrepareRequest {
    optional uint32 task_num = 1;
    optional uint32 timeout_ms = 2;
    repeated uint64 prepare_data_zc_lengths = 3;
}

message PrepareResponse {
    optional uint64 session_id = 1;
}

message DataTransferRequest {
    optional uint64 session_id = 1;
    optional uint32 task_id = 2;
    repeated uint64 send_data_lengths = 3;
}

message DataTransferResponse {
    repeated uint64 send_data_lengths = 1;
}

service DataTransferService {
    rpc Prepare(PrepareRequest) returns (PrepareResponse);
    rpc DataTransfer(DataTransferRequest) returns (DataTransferResponse);
}
