#ifndef _CONFIG_H_
#define _CONFIG_H_

#include <mutex>
#include <functional>
#include "cloudfs.h"
#include "logger.h"

enum class filesystem_mode {
  HDFS,
  ACC,
};

enum class acc_write_mode {
  THROUGH,
  ASYNC,
};

class config {
 public:
  template <typename T>
  struct config_default {
    T* variable;
    const char* key;
    T value;
    std::function<void(const char*, T const&)> check;
  };

  filesystem_mode get_filesystem_mode() const {
    return fs_mode_;
  }

  void set_filesystem_mode(const char* str) {
    parse_filesystem_mode(str);
  }

  int get_max_retry_on_connection() {
    return max_retry_on_connection_;
  }

  acc_write_mode get_acc_write_mode() const {
    return this->acc_write_mode_;
  }

  void set_acc_write_mode() {
    parse_acc_write_mode(acc_write_mode_str_.c_str());
  }

  std::string get_acc_append_file_pattern() const {
    return this->acc_append_file_pattern_;
  }

  std::string get_log_severity() const {
    return this->log_severity_;
  }

  bool get_is_pread() const {
    return this->is_pread_;
  }

  void set_config(cfsBuilder* bld, const std::string& key,
                  const std::string& value);
  bool parse_config(cfsBuilder* bld);

  bool get_is_adaptive_pread() const {
    return this->is_adaptive_pread_;
  }

  void set_is_adaptive_pread(bool is_adaptive_pread) {
    this->is_adaptive_pread_ = is_adaptive_pread;
  }

  bool get_is_fio_test() const {
    return this->is_fio_test;
  }

  void set_is_fio_test(bool is_fio) {
    this->is_fio_test = is_fio;
  }

  int get_pread_adaptive_threshold() {
    return this->pread_adaptive_threshold;
  }

  int get_open_retry_timeout_ms() const {
    return this->open_retry_timeout_ms;
  }

  int get_log_file_clear_interval_minute() const {
    return this->log_file_clear_interval;
  }

  void set_open_retry_timeout_ms(int timeout) {
    this->open_retry_timeout_ms = timeout;
  }

  bool get_is_limit_file_number() {
    return this->is_limit_file_number_;
  }

 private:
  void parse_filesystem_mode(const char* mode_str) {
    if (strcasecmp(mode_str, "hdfs") == 0) {
      fs_mode_ = filesystem_mode::HDFS;
    } else if (strcasecmp(mode_str, "acc") == 0) {
      fs_mode_ = filesystem_mode::ACC;
    } else {
      CFS_LOG(ERROR, "Unexpected filesystem mode %s, use HDFS", mode_str);
      fs_mode_ = filesystem_mode::HDFS;
    }
  }

  void parse_acc_write_mode(const char* mode_str) {
    if (strcasecmp(mode_str, "async") == 0) {
      acc_write_mode_ = acc_write_mode::ASYNC;
    } else if (strcasecmp(mode_str, "through") == 0) {
      acc_write_mode_ = acc_write_mode::THROUGH;
    } else {
      CFS_LOG(ERROR, "Unexpected acc write mode %s, use ASYNC", mode_str);
      acc_write_mode_ = acc_write_mode::ASYNC;
    }
  }

 private:
  std::once_flag init_config_flag_;

  filesystem_mode fs_mode_;

  int max_retry_on_connection_;

  std::string acc_write_mode_str_;
  acc_write_mode acc_write_mode_;

  std::string acc_append_file_pattern_;

  std::string log_severity_;
  bool is_pread_;
  bool is_adaptive_pread_;
  bool is_fio_test;
  int pread_adaptive_threshold;
  bool is_limit_file_number_;

  int open_retry_timeout_ms;  // the timeout of retry when open, -1 means always
                              // retry
  int log_file_clear_interval;
};

extern config root_config;

#endif /* _CONFIG_H_ */
