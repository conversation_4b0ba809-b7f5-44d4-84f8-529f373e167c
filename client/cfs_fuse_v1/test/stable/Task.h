#ifndef _FUSE_TEST_STABLE_TASK_H_
#define _FUSE_TEST_STABLE_TASK_H_

#include "Utils.h"

class Task {
 public:
  Task(const std::string& prefix, int depth)
      : prefix(prefix), depth(depth), idx(-1), tmp(false) {}
  Task(const std::string& prefix, int depth, int idx)
      : prefix(prefix), depth(depth), idx(idx), tmp(false) {}
  Task(const std::string& prefix, int depth, int idx, bool tmp)
      : prefix(prefix), depth(depth), idx(idx), tmp(tmp) {}
  virtual void Run() = 0;

  std::string ToString();
  void Delay();

 protected:
  std::string prefix;
  int depth;
  int idx;
  bool tmp;
};

class StatTask : Task {
 public:
  StatTask(const std::string& prefix, int depth, int idx, bool tmp)
      : Task(prefix, depth, idx, tmp) {}
  virtual void Run() override;

 private:
  std::string GetPath();
};

class ReadTask : Task {
 public:
  ReadTask(const std::string& prefix, int depth, int idx, bool tmp)
      : Task(prefix, depth, idx, tmp) {}
  virtual void Run() override;

 private:
  std::string GetPath();
};

class RandomReadTask : Task {
 public:
  RandomReadTask(const std::string& prefix, int depth, int idx, bool tmp)
      : Task(prefix, depth, idx, tmp) {}
  virtual void Run() override;

 private:
  std::string GetPath();
  int RandomRreadInner(std::string path, int fd, std::size_t fileSize,
                       std::size_t alreadyReadOff, unsigned char* buffer,
                       std::vector<std::pair<int, int>> bufferRangeArr);
  std::vector<std::pair<int, int>> GenerateBufferRange(int bufferSize);
};

class WriteTask : Task {
 public:
  WriteTask(const std::string& prefix, int depth, int idx, bool tmp)
      : Task(prefix, depth, idx, tmp) {}
  virtual void Run() override;

 private:
  std::string GetPath();
};

class MkdirTask : Task {
 public:
  MkdirTask(const std::string& prefix, int depth, int idx)
      : Task(prefix, depth, idx) {}
  virtual void Run() override;

 private:
  std::string GetPath();
  std::string GetTmpPath();
};

class MoveTask : Task {
 public:
  MoveTask(const std::string& prefix, int depth, int idx, ObjectType type)
      : Task(prefix, depth, idx), type(type) {}
  virtual void Run() override;

 private:
  ObjectType type;
};

class DeleteTask : Task {
 public:
  DeleteTask(const std::string& prefix, int depth) : Task(prefix, depth) {}
  virtual void Run() override;

 private:
  std::string GetPath();
  ObjectType type;
};

class TaskRunner {
 public:
  TaskRunner() = default;
  void Run();

 private:
  std::string GetPrefix();
};

#endif /* _FUSE_TEST_STABLE_TASK_H_ */
