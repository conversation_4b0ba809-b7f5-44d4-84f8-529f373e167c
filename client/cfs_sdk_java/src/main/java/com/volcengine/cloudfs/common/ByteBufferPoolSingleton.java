package com.volcengine.cloudfs.common;

import java.util.Objects;

import org.apache.hadoop.conf.Configuration;

import com.volcengine.cloudfs.fs.cfs.ConfigKeys;

/**
 * Singleton factory for managing IO and metadata buffer pools
 */
public class ByteBufferPoolSingleton {
  private volatile static CfsByteBufferPool ioByteBufferPool;
  private volatile static CfsByteBufferPool metaByteBufferPool;
  private ByteBufferPoolSingleton (){}


  public static CfsByteBufferPool getIOByteBufferPool() {
    return getIOByteBufferPool(null);
  }

  /**
   * Get singleton instance for IO operations buffer pool
   * @param conf Hadoop configuration
   * @return Thread-safe buffer pool instance with configured capacity
   */
  public static CfsByteBufferPool getIOByteBufferPool(Configuration conf) {
    if (Objects.isNull(ioByteBufferPool)) {
      synchronized (ByteBufferPoolSingleton.class) {
        if (Objects.isNull(ioByteBufferPool)) {
          long maxCapacity = ConfigKeys.CFS_IO_BUFFER_POOL_MAX_BYTES_DEFAULT;
          if (Objects.nonNull(conf)) {
            maxCapacity = conf.getLong(ConfigKeys.CFS_IO_BUFFER_POOL_MAX_BYTES, ConfigKeys.CFS_IO_BUFFER_POOL_MAX_BYTES_DEFAULT);
          }
          ioByteBufferPool = new CfsByteBufferPool(maxCapacity, true);
        }
      }
    }
    return ioByteBufferPool;
  }

  public static CfsByteBufferPool getMetaByteBufferPool(Configuration conf) {
    if (Objects.isNull(metaByteBufferPool)) {
      synchronized (ByteBufferPoolSingleton.class) {
        if (Objects.isNull(metaByteBufferPool)) {
          long maxCapacity = ConfigKeys.CFS_META_BUFFER_POOL_MAX_BYTES_DEFAULT;
          if (Objects.nonNull(conf)) {
            maxCapacity = conf.getLong(ConfigKeys.CFS_META_BUFFER_POOL_MAX_BYTES, ConfigKeys.CFS_META_BUFFER_POOL_MAX_BYTES_DEFAULT);
          }
          metaByteBufferPool = new CfsByteBufferPool(maxCapacity, true);
        }
      }
    }
    return metaByteBufferPool;
  }
}
