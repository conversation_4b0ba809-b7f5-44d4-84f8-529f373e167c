package com.volcengine.cloudfs.utils;

import org.apache.hadoop.fs.Path;
import org.apache.hadoop.ipc.protobuf.RpcHeaderProtos;

import java.io.File;
import java.util.Objects;

public class Utils {
  public static String parsePath(Path path) {
    if (Objects.isNull(path)) {
      throw new IllegalArgumentException("Request path is null");
    }
    return path.toUri().getPath();
  }

  public static File findMatchingFile(File directory, String prefix, String suffix) {
    if (directory.exists() && directory.isDirectory()) {
      File[] files = directory.listFiles();

      if (files != null) {
        for (File file : files) {
          if (file.isFile() && file.getName().startsWith(prefix) && file.getName().endsWith(suffix)) {
            return file;
          }
        }
      }
    }
    return null;
  }
}
