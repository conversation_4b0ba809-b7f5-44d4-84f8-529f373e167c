package com.volcengine.cloudfs.handler;

import java.io.IOException;
import java.nio.ByteBuffer;

import org.apache.hadoop.hdfs.protocol.HdfsFileStatus;

import com.volcengine.cloudfs.fbs.GetFileStatusRequestObj;
import com.volcengine.cloudfs.fbs.GetFileStatusRespObj;
import com.volcengine.cloudfs.fs.coder.GetFileStatusDecoder;
import com.volcengine.cloudfs.fs.coder.GetFileStatusEncoder;
import com.volcengine.cloudfs.fs.common.FilesystemContext;
import com.volcengine.cloudfs.jni.NativeResponse;

public class GetFileStatusHandler extends RequestHandler<GetFileStatusRequestObj, GetFileStatusRespObj, HdfsFileStatus> {
  private final String path;

  public GetFileStatusHandler(FilesystemContext clientContext, String path) {
    super(clientContext);
    this.path = path;
  }

  @Override
  protected GetFileStatusRequestObj constructParams() {
    GetFileStatusRequestObj requestObj = new GetFileStatusRequestObj();
    requestObj.setPath(path);
    return requestObj;
  }

  @Override
  protected ByteBuffer encodeParams(GetFileStatusRequestObj request) throws IOException {
    this.encoder = new GetFileStatusEncoder(request, clientContext.getConf());
    return this.encoder.encode();
  }

  @Override
  protected NativeResponse callNative(ByteBuffer encodeBuffer) throws IOException {
    return nativeFileSystem.getFileStatus(encodeBuffer);
  }

  @Override
  protected GetFileStatusRespObj decodeResponse(NativeResponse nativeResp) throws IOException {
    this.decoder = new GetFileStatusDecoder(nativeResp);
    return (GetFileStatusRespObj) decoder.decode();
  }

  @Override
  protected HdfsFileStatus constructResponse(GetFileStatusRespObj protoResp) {
    return new HdfsFileStatus.Builder()
        .length(protoResp.getLength())
        .isdir(protoResp.getIsDir())
        .owner(protoResp.getOwner())
        .group(protoResp.getGroup())
        .replication(protoResp.getBlockReplication())
        .path(path.getBytes()).build();
  }

}
