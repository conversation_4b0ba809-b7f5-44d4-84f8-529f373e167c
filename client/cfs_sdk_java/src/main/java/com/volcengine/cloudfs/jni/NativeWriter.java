package com.volcengine.cloudfs.jni;

import java.io.IOException;
import java.nio.ByteBuffer;

public class NativeWriter extends NativeObject {

  public NativeWriter(long nativeWriterPtr) {
    this.nativeObjectPtr = nativeWriterPtr;
  }

  public static NativeWriter createNativeWriter(long nativeWriterPtr) {
    return new NativeWriter(nativeWriterPtr);
  }

  // Native call
  public native NativeResponse write(ByteBuffer buf) throws IOException;

  public native NativeResponse closeFile() throws IOException;

  @Override
  protected void close() {

  }
}
