#include "jni/jni_util.h"

#include <cstddef>

namespace cfs {
namespace jni {

jobject JniUtil::JNativeResult(JNIEnv* env, jobject& obj, int status,
                               const char* message) {
  return JNativeResult(env, obj, nullptr, 0, status, message);
}

jobject JniUtil::GetErrorResult(JNIEnv* env, jobject& obj) {
  const cfs_error_t* err = cfs_get_last_error();

  int status = CFS_STATUS_INTERNAL_ERROR;
  const char* msg = nullptr;
  if (err) {
    status = err->status;
    msg = err->message;
  } else {
    msg = "failed to get error";
  }
  return JniUtil::JNativeResult(env, obj, status, msg);
}

// template <typename RequestType>
// RequestType* JniUtil::VerifyProtos(JNIEnv* env, jobject obj, jobject params,
//                                jobject& error) {
//   jlong bufferCap = env->GetDirectBufferCapacity(params);
//   unsigned char* buf = (unsigned char*)env->GetDirectBufferAddress(params);
//   if (buf == nullptr) {
//     return JniUtil::JNativeResult(env, obj, CFS_STATUS_INVALID_ARGUMENT,
//                                   "Request buffer is null");
//   }

//   RequestType* requestProto = ::flatbuffers::GetRoot<RequestType>(buf);
//   ::flatbuffers::Verifier verifier(buf, bufferCap);
//   if (requestProto->Verify(verifier)) {
//     return requestProto;
//   } else {
//     error = JniUtil::JNativeResult(env, obj, CFS_STATUS_INVALID_ARGUMENT,
//                                   "Request buffer can not be verified");
//     return nullptr;
//   }
// }

jobject JniUtil::JNativeResult(JNIEnv* env, jobject& obj, void* resultAddr,
                               jlong resultCapacity, int status,
                               const char* message) {
  // 获取 NativeResult 类
  jclass nativeResultClass =
      env->FindClass("com/volcengine/cloudfs/jni/NativeResponse");
  if (!nativeResultClass) {
    return NULL;
  }

  // 获取 NativeResult 的构造方法 ID
  jmethodID constructor = env->GetMethodID(nativeResultClass, "<init>", "()V");
  if (!constructor) {
    return NULL;
  }

  // 创建 NativeResult 对象
  jobject nativeResultObject = env->NewObject(nativeResultClass, constructor);
  if (!nativeResultObject) {
    return NULL;
  }

  // 获取字段 ID
  jfieldID resultFieldID =
      env->GetFieldID(nativeResultClass, "result", "Ljava/lang/Object;");
  jfieldID statusFieldID = env->GetFieldID(nativeResultClass, "status", "I");
  jfieldID messageFieldID =
      env->GetFieldID(nativeResultClass, "message", "Ljava/lang/String;");

  if (!resultFieldID || !statusFieldID || !messageFieldID) {
    return NULL;
  }

  if (resultAddr) {
    jobject byteBuffer =
        NewDirectByteBuffer(env, obj, resultAddr, resultCapacity);
    if (!byteBuffer) {
      return NULL;
    }
    // 设置 result 字段
    env->SetObjectField(nativeResultObject, resultFieldID, byteBuffer);
  }

  // 设置 status 字段
  env->SetIntField(nativeResultObject, statusFieldID, status);

  // 创建并设置 message 字符串
  if (message) {
    jstring jmessage = env->NewStringUTF(message);
    env->SetObjectField(nativeResultObject, messageFieldID, jmessage);
  }

  return nativeResultObject;
}

jobject JniUtil::NewDirectByteBuffer(JNIEnv* env, jobject& obj, void* dataAddr,
                                     jlong capacity) {
  if (capacity > static_cast<jlong>(std::numeric_limits<jint>::max()) ||
      capacity < static_cast<jlong>(std::numeric_limits<jint>::min())) {
    std::cerr << "Capacity out of jint range" << std::endl;
    return nullptr;
  }

  jclass cls = env->GetObjectClass(obj);
  jmethodID mid =
      env->GetMethodID(cls, "allocateDirectBuffer", "(I)Ljava/nio/ByteBuffer;");

  if (mid == nullptr) {
    std::cerr << "Failed to find method allocateDirectBuffer" << std::endl;
    return nullptr;
  }

  jobject directBuffer =
      env->CallObjectMethod(obj, mid, static_cast<jint>(capacity));
  if (directBuffer == nullptr) {
    std::cerr << "Failed to allocate direct buffer" << std::endl;
    jobject byteBuffer = env->NewDirectByteBuffer(dataAddr, capacity);
    if (!byteBuffer) {
      std::cerr << "Failed to allocate direct buffer from JDK" << std::endl;
      return nullptr;
    } else {
      return byteBuffer;
    }
  } else {
    jclass byteBufferClass = env->GetObjectClass(directBuffer);
    jmethodID capacityMethodID =
        env->GetMethodID(byteBufferClass, "capacity", "()I");
    jint capacity = env->CallIntMethod(directBuffer, capacityMethodID);

    void* directBufferPtr = env->GetDirectBufferAddress(directBuffer);
    if (directBufferPtr == nullptr) {
      std::cerr << "Failed to get direct buffer pointer" << std::endl;
      return nullptr;
    }
    std::memcpy(directBufferPtr, dataAddr, capacity);
    return directBuffer;
  }
}

jobject JniUtil::JNativeResult(JNIEnv* env, jobject& obj, int64_t result,
                               int status, const char* message) {
  // 获取 NativeResult 类
  jclass nativeResultClass =
      env->FindClass("com/volcengine/cloudfs/jni/NativeResponse");
  if (!nativeResultClass) {
    return NULL;
  }

  // 获取 NativeResult 的构造方法 ID
  jmethodID constructor = env->GetMethodID(nativeResultClass, "<init>", "()V");
  if (!constructor) {
    return NULL;
  }

  // 创建 NativeResult 对象
  jobject nativeResultObject = env->NewObject(nativeResultClass, constructor);
  if (!nativeResultObject) {
    return NULL;
  }

  // 获取字段 ID
  jfieldID resultFieldID =
      env->GetFieldID(nativeResultClass, "result", "Ljava/lang/Object;");
  jfieldID statusFieldID = env->GetFieldID(nativeResultClass, "status", "I");
  jfieldID messageFieldID =
      env->GetFieldID(nativeResultClass, "message", "Ljava/lang/String;");

  if (!resultFieldID || !statusFieldID || !messageFieldID) {
    return NULL;
  }

  jclass longClass = env->FindClass("java/lang/Long");
  jmethodID longConstructor = env->GetMethodID(longClass, "<init>", "(J)V");

  jobject longObject = env->NewObject(longClass, longConstructor, result);

  // 设置 result 字段
  env->SetObjectField(nativeResultObject, resultFieldID, longObject);

  // 设置 status 字段
  env->SetIntField(nativeResultObject, statusFieldID, status);

  // 创建并设置 message 字符串
  if (message) {
    jstring jmessage = env->NewStringUTF(message);
    env->SetObjectField(nativeResultObject, messageFieldID, jmessage);
  }

  return nativeResultObject;
}

cfs_fs_handle JniUtil::ResolveFilesystem(JNIEnv* env, jobject& obj) {
  jclass thisClass = env->GetObjectClass(obj);

  jfieldID fid = env->GetFieldID(thisClass, "nativeObjectPtr", "J");
  if (fid == nullptr) {
    return nullptr;
  }

  jlong nativePtr = env->GetLongField(obj, fid);
  return reinterpret_cast<cfs_fs_handle>(nativePtr);
}

cfs_file_handle JniUtil::ResolveFileHandle(JNIEnv* env, jobject& obj) {
  jclass thisClass = env->GetObjectClass(obj);

  jfieldID fid = env->GetFieldID(thisClass, "nativeObjectPtr", "J");
  if (fid == nullptr) {
    return nullptr;
  }

  jlong nativePtr = env->GetLongField(obj, fid);
  return reinterpret_cast<cfs_file_handle>(nativePtr);
}

}  // namespace jni
}  // namespace cfs
