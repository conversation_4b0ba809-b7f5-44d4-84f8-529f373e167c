/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_volcengine_cloudfs_jni_NativeReader */

#ifndef _Included_com_volcengine_cloudfs_jni_NativeReader
#define _Included_com_volcengine_cloudfs_jni_NativeReader
#ifdef __cplusplus
extern "C" {
namespace cfs {
namespace jni {
#endif
/*
 * Class:     com_volcengine_cloudfs_jni_NativeReader
 * Method:    read
 * Signature:
 * (JLjava/nio/ByteBuffer;)Lcom/volcengine/cloudfs/jni/NativeResponse;
 */
JNIEXPORT jobject JNICALL Java_com_volcengine_cloudfs_jni_NativeReader_read(
    JNIEnv*, jobject, jlong, jobject);

/*
 * Class:     com_volcengine_cloudfs_jni_NativeReader
 * Method:    closeFile
 * Signature: ()Lcom/volcengine/cloudfs/jni/NativeResponse;
 */
JNIEXPORT jobject JNICALL
Java_com_volcengine_cloudfs_jni_NativeReader_closeFile(JNIEnv*, jobject);

#ifdef __cplusplus
}
}  // namespace jni
}  // namespace cfs
#endif
#endif
