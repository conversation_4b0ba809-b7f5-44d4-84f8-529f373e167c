# GIT_SUBMODULES_RECURSE need cmake >= 3.17
cmake_minimum_required(VERSION 3.17.0)

project("Cloudfs SDK" C CXX)

set(PACKAGE_NAME      "cloudfs-sdk")
set(PACKAGE_VERSION   "v2")
set(PACKAGE_STRING    "${PACKAGE_NAME} ${PACKAGE_VERSION}")
set(PACKAGE_TARNAME   "${PACKAGE_NAME}-${PACKAGE_VERSION}")

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# use PROJECT_SOURCE_DIR but not CMAKE_SOURCE_DIR because this proj may be
# compiled as a sub-project as another project
set(CFS_HOME ${PROJECT_SOURCE_DIR})

option(CFS_ENABLE_STATIC "Whether to build static library" OFF)
# When this option is set to true, _GLIBCXX_USE_CXX11_ABI will be set to build
# third parties.
# See 'https://gcc.gnu.org/onlinedocs/libstdc++/manual/using_dual_abi.html'
# for details.
option(CFS_DISABLE_CXX11_ABI "Whether to disable cxx11 abi and use old abi" OFF)
option(CFS_ENABLE_ASAN "Whether to enable AddressSanitizer" OFF)
option(CFS_ENABLE_MOCK "Whether to enable mock in UT and some IT" OFF)
option(CFS_BUILD_CMD "Whether to build command line tools" ON)
option(CFS_BUILD_FUSE "Whether to build FUSE" OFF)
option(CFS_BUILD_UNIT_TEST "Whether to build unit tests. CFS_ENABLE_MOCK must be ON if this option is ON" OFF)
option(CFS_BUILD_MOCK_IT "Whether to build partially-mock integration tests. CFS_ENABLE_MOCK must be ON if this option is ON" OFF)
option(CFS_BUILD_INTEGRATION_TEST "Whether to build integration tests" OFF)
option(CFS_BUILD_FUSE_TEST "Whether to build FUSE test" OFF)
option(CFS_BUILD_BENCHMARK "Whether to build benchmarks" OFF)
option(CFS_ENABLE_GCOV "Whether to enable gcov" OFF)
option(CFS_ENABLE_JEMALLOC "Use jemalloc to replace glibc-malloc" ON)
option(CFS_SHARED_JEMALLOC "Link to jemalloc shared-library, default is static-library" OFF)
option(CFS_HIDE_THIRDPARTY_SYMBOL "Hide third_party symbols in CFS shared-lib. If enabled, CFS_SHARED_JEMALLOC is ON automatically" OFF)
option(CFS_STATIC_LIB_CXX "Link libstdc++ and libgcc statically. If CFS_SHARED_JEMALLOC is ON, this option is OFF automatically" OFF)

# ===============================================
# Check the building system environment before building.
# ===============================================
if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
  message(FATAL_ERROR "Building system must be Linux!")
endif()
if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU")
  if(CMAKE_CXX_COMPILER_VERSION VERSION_LESS 8.3.0)
    message(FATAL_ERROR "GCC>=8.3.0 required!")
  endif()
else()
  message(FATAL_ERROR "Compiler Error: only GCC is supported for now!")
endif()

# ===============================================
# Check if third party is installed.
# ===============================================
if("${CFS_THIRDPARTY_ROOT}" STREQUAL "")
  # Build thirdparty on our own if the thirdparty is not built
  set(CMAKE_PREFIX_PATH ${CFS_HOME}/third/install)
  message(STATUS "Use default CFS_THIRDPARTY_ROOT: ${CMAKE_PREFIX_PATH}")
  find_library(GFLAGS_LIB_FOUND libbyterpc.a)
  if(NOT GFLAGS_LIB_FOUND)
    message(FATAL_ERROR "Third party not found. Please build third party in advance!")
  endif()
else()
  # Use user-specified thirparty
  set(CMAKE_PREFIX_PATH ${CFS_THIRDPARTY_ROOT})
  message(STATUS "Specified CFS_THIRDPARTY_ROOT: ${CFS_THIRDPARTY_ROOT}")
  find_library(GFLAGS_LIB_FOUND libbyterpc.a)
  if(NOT GFLAGS_LIB_FOUND)
    message(FATAL_ERROR "Third party not found. Please build third party in advance!")
  endif()
endif()

list(APPEND CMAKE_MODULE_PATH "${CFS_HOME}/cmakes/modules")
list(INSERT CMAKE_INCLUDE_PATH 0 ${CMAKE_PREFIX_PATH}/include)
list(INSERT CMAKE_LIBRARY_PATH 0 ${CMAKE_PREFIX_PATH}/lib)
list(INSERT CMAKE_PROGRAM_PATH 0 ${CMAKE_PREFIX_PATH}/bin)

message(STATUS "CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}")
message(STATUS "CMAKE_MODULE_PATH: ${CMAKE_MODULE_PATH}")
message(STATUS "CMAKE_INCLUDE_PATH: ${CMAKE_INCLUDE_PATH}")
message(STATUS "CMAKE_LIBRARY_PATH: ${CMAKE_LIBRARY_PATH}")
message(STATUS "CMAKE_PROGRAM_PATH: ${CMAKE_PROGRAM_PATH}")
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")

if(CFS_ENABLE_JEMALLOC)
  if(CFS_HIDE_THIRDPARTY_SYMBOL)
    message(STATUS "CFS_SHARED_JEMALLOC is force-set to ON because CFS_HIDE_THIRDPARTY_SYMBOL is enabled")
    set(CFS_SHARED_JEMALLOC ON)
  endif()
  if(CFS_SHARED_JEMALLOC)
    message(STATUS "CFS_STATIC_LIB_CXX is force-set to OFF because CFS_SHARED_JEMALLOC is enabled")
    set(CFS_STATIC_LIB_CXX OFF)
  endif()
endif()

# Must use CONFIG mode for find_package(spdlog) here because
# spdlogConfigTargets.cmake adds target_compile_definitions of
# `SPDLOG_COMPILED_LIB` to the targets who links to spdlog::spdlog.
# This definitions will use the static-built libspdlog.a but not header-only.
# See https://github.com/gabime/spdlog/blob/v1.x/example/CMakeLists.txt
# for details.
find_package(spdlog REQUIRED CONFIG)
find_package(fmt REQUIRED CONFIG)
find_package(absl REQUIRED CONFIG)
set(Boost_USE_DEBUG_LIBS OFF)
set(Boost_USE_DEBUG_RUNTIME OFF)
set(Boost_USE_STATIC_LIBS ON)
set(Boost_USE_STATIC_RUNTIME ON)
find_package(Boost REQUIRED COMPONENTS ALL CONFIG)
find_package(function2 REQUIRED CONFIG)
set(GFLAGS_USE_TARGET_NAMESPACE ON)
find_package(gflags REQUIRED CONFIG)
find_package(Jemalloc REQUIRED MODULE)
find_package(Protobuf REQUIRED MODULE)
find_package(Byte REQUIRED MODULE)
find_package(Byterpc REQUIRED MODULE)
find_package(Brpc REQUIRED MODULE)
find_package(Xxhash REQUIRED MODULE)
find_package(Openssl REQUIRED MODULE)
# TODO(dbc) NUMA is temp disabled in byterpc
# find_package(Numa REQUIRED MODULE)

# ===============================================
# Configure File before building
# ===============================================
find_package(Git REQUIRED)
# TODO(dbc) use add_custom_target instead of execute_process
execute_process(
  COMMAND ${GIT_EXECUTABLE} log -1 --format=%H
  WORKING_DIRECTORY ${CFS_HOME}
  OUTPUT_VARIABLE  CFS_GIT_HASH
  OUTPUT_STRIP_TRAILING_WHITESPACE
  )
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --abbrev-ref HEAD
  WORKING_DIRECTORY ${CFS_HOME}
  OUTPUT_VARIABLE CFS_GIT_BRANCH
  OUTPUT_STRIP_TRAILING_WHITESPACE
  )
execute_process(
  COMMAND ${GIT_EXECUTABLE} describe --tag --exact-match HEAD
  WORKING_DIRECTORY ${CFS_HOME}
  OUTPUT_VARIABLE CFS_GIT_TAG
  ERROR_VARIABLE CFS_GIT_TAG_ERR
  OUTPUT_STRIP_TRAILING_WHITESPACE
  )

# $ENV{BUILD_VERSION} is set by SCM platform by default
if(DEFINED ENV{BUILD_VERSION} AND NOT "$ENV{BUILD_VERSION}" STREQUAL "")
  set(CFS_BUILD_VERSION $ENV{BUILD_VERSION})
elseif(CFS_GIT_TAG)
  set(CFS_BUILD_VERSION ${CFS_GIT_TAG})
else()
  set(CFS_BUILD_VERSION ${CFS_GIT_HASH})
endif()
message(STATUS "CFS_BUILD_VERSION: ${CFS_BUILD_VERSION}")

# ===============================================
# Set building configurations.
# ===============================================
if("${CMAKE_BUILD_TYPE}" STREQUAL "")
  SET(CMAKE_BUILD_TYPE "RelWithDebInfo")
endif()

set(CFS_BUILD_TYPE ${CMAKE_BUILD_TYPE})
configure_file("${CFS_HOME}/include/cloudfs2/version.h.in" "${CFS_HOME}/include/cloudfs2/version.h" @ONLY)

list(APPEND CFS_EXE_DEP_LIBS
  ${BRPC_LIBRARY}
  ${JEMALLOC_LIBRARY}
  spdlog::spdlog
  fmt::fmt
  stdc++fs
  pthread
  dl
)

list(APPEND CFS_DEP_LIBS
  ${BYTERPC_LIBRARIES}
  ${BYTE_LIBRARY}
  ${BRPC_LIBRARY}
  ${PROTOBUF_LIBRARY}
  ${XXHASH_LIBRARY}
  ${OPENSSL_LIBRARY}
  spdlog::spdlog
  fmt::fmt
  gflags::gflags_static
  absl::strings
  absl::str_format
  absl::time
  absl::synchronization
  # absl::flat_hash_set
  Boost::url
  Boost::thread
  # ${NUMA_LIBRARY}
  pthread
  dl
  )

if(CFS_ENABLE_JEMALLOC)
  list(APPEND CFS_DEP_LIBS
    ${JEMALLOC_LIBRARY}
    )
endif()

# -lstdc++fs is needed for gcc < 9.1. For clang, it should be -lc++fs
if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU" AND
    CMAKE_CXX_COMPILER_VERSION VERSION_LESS 9.1)
  list(APPEND CFS_DEP_LIBS stdc++fs)
endif()

include(cmakes/cpu_features.cmake)
message(STATUS "Enabled CPU Options: ${COMPILER_FLAGS}")

set(CXX_COMMON_FLAGS "-Wall -Werror -Wextra -Wno-interference-size -fPIC -fno-omit-frame-pointer ${COMPILER_FLAGS}")
if(CFS_DISABLE_CXX11_ABI)
  set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -D_GLIBCXX_USE_CXX11_ABI=0")
endif()

set(COMMON_LINKER_FLAGS "-no-pie")
if(CFS_STATIC_LIB_CXX)
  set(COMMON_LINKER_FLAGS "${COMMON_LINKER_FLAGS} -static-libstdc++ -static-libgcc")
endif()

if(CFS_ENABLE_ASAN)
  set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -fsanitize=address,undefined")
  set(COMMON_LINKER_FLAGS "${COMMON_LINKER_FLAGS} -fsanitize=address,undefined")
endif()

if(CFS_ENABLE_GCOV)
  set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} --coverage -fno-inline -fno-inline-small-functions -fno-default-inline")
  set(COMMON_LINKER_FLAGS "${COMMON_LINKER_FLAGS} --coverage")
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${CXX_COMMON_FLAGS}")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${COMMON_LINKER_FLAGS}")
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} ${COMMON_LINKER_FLAGS}")

if(CFS_HIDE_THIRDPARTY_SYMBOL)
  set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--version-script=${CFS_HOME}/hide_symbol.map")
endif()

message(STATUS "CMAKE_CXX_FLAGS: ${CMAKE_CXX_FLAGS}")
message(STATUS "CMAKE_EXE_LINKER_FLAGS: ${CMAKE_EXE_LINKER_FLAGS}")
message(STATUS "CMAKE_SHARED_LINKER_FLAGS: ${CMAKE_SHARED_LINKER_FLAGS}")

include_directories(SYSTEM ${CMAKE_INCLUDE_PATH})

macro(cfs_add_executable)
  cmake_parse_arguments(
    cfs_exec                    # prefix
    ""                          # <options>
    "NAME"                      # <one_value_args>
    "SOURCES;OBJECTS;LIBRARIES" # <multi_value_args>
    ${ARGN}
    )

  add_executable(${cfs_exec_NAME}
    ${cfs_exec_SOURCES}
    ${cfs_exec_OBJECTS}
    )

  target_include_directories(${cfs_exec_NAME}
    PRIVATE ${CFS_HOME}/include
    PRIVATE ${CFS_HOME}/src
    PRIVATE ${PROTO_DST_PATH}
    )

  target_link_libraries(${cfs_exec_NAME}
    ${cfs_exec_LIBRARIES}
    )
endmacro()

if((CFS_BUILD_UNIT_TEST OR CFS_BUILD_MOCK_IT) AND (NOT CFS_ENABLE_MOCK))
  message(FATAL_ERROR "CFS_ENABLE_MOCK must be ON when CFS_BUILD_UNIT_TEST/CFS_BUILD_MOCK_IT is ON")
endif()
if((CFS_BUILD_INTEGRATION_TEST OR CFS_BUILD_BENCHMARK) AND CFS_ENABLE_MOCK)
  message(FATAL_ERROR "CFS_ENABLE_MOCK must be OFF when CFS_BUILD_INTEGRATION_TEST/CFS_BUILD_BENCHMARK is ON")
endif()

# enable_testing must be placed before add_subdirectory(src)
if(CFS_BUILD_UNIT_TEST OR CFS_BUILD_MOCK_IT OR CFS_BUILD_INTEGRATION_TEST OR CFS_BUILD_FUSE_TEST OR CFS_ENABLE_MOCK)
  enable_testing()
  find_package(GTest REQUIRED MODULE)
  if(CFS_ENABLE_MOCK)
    list(APPEND CFS_DEP_LIBS GTest::gmock)
    add_compile_definitions(CFS_MOCK_ENABLED)
  endif()
endif()

add_subdirectory(src)

# test/CMakeLists.txt will test CFS_BUILD_UNIT_TEST, CFS_BUILD_INTEGRATION_TEST
# and CFS_BUILD_BENCHMARK internally
add_subdirectory(test)

if(CFS_BUILD_CMD)
  add_subdirectory(cmd)
endif()
if(CFS_BUILD_FUSE)
  add_subdirectory(fuse)
endif()

install(FILES ${CMAKE_CURRENT_LIST_DIR}/conf/credential.example DESTINATION conf)
