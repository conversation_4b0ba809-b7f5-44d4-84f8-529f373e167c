#include "common/net_util.h"

#include <arpa/inet.h>
#include <gflags/gflags.h>
#include <httplib.h>

#include <boost/asio/ip/address.hpp>
#include <boost/asio/ip/network_v4.hpp>
#include <boost/asio/ip/network_v6.hpp>
#include <boost/url/encode.hpp>
#include <boost/url/parse.hpp>
#include <boost/url/rfc/unreserved_chars.hpp>

#include "common/logger.h"

DECLARE_string(cfs_expect_dn_subnet);

namespace cfs {
namespace internal {

// The unreserved chars must be same with NNProxy. See
// https://code.byted.org/inf/gonnproxy/blob/cfs_master/pkg/auth/context/context.go#L334
static constexpr boost::urls::grammar::lut_chars kUnReservedChars =
    boost::urls::unreserved_chars + boost::urls::grammar::lut_chars(":@");
static constexpr boost::urls::grammar::lut_chars kVolcAuthUnReservedChars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._*";
static constexpr struct in_addr kIp4AddrAny = {INADDR_ANY};
static struct in6_addr kIp6AddrAny = in6addr_any;
static constexpr char kNICDelim = ',';
static std::once_flag g_init_dn_subnet_flag;
// Both g_expect_dn_subnet_v4 and g_expect_dn_subnet_range_v4 must be a global
// variable because g_expect_dn_subnet_range_v4 only stores iterators (refs) to
// g_expect_dn_subnet_v4.
static boost::asio::ip::network_v4 g_expect_dn_subnet_v4;
static boost::asio::ip::address_v4_range g_expect_dn_subnet_range_v4;
static boost::asio::ip::network_v6 g_expect_dn_subnet_v6;
static boost::asio::ip::address_v6_range g_expect_dn_subnet_range_v6;
static bool g_expect_dn_ipv6{false};
static bool g_all_ipv6_valid{false};
// true if the user-provided expect_dn_subnet is invalia. In this case,
// IsExpectDNIp will always return false.
static bool g_invalid_dn_subnet{false};

static void InitExpectDNSubnet() {
  CFSLOG(INFO, "Init expect_dn_subnet: {}", FLAGS_cfs_expect_dn_subnet);
  boost::system::error_code ec4;
  g_expect_dn_subnet_v4 =
      boost::asio::ip::make_network_v4(FLAGS_cfs_expect_dn_subnet, ec4);
  if (!ec4.failed()) {
    g_expect_dn_subnet_range_v4 = g_expect_dn_subnet_v4.hosts();
    g_expect_dn_ipv6 = false;
    return;
  }
  CFSLOG(INFO,
         "ExpectDNSubnet is not ipv4 format, try parsing in ipv6 format: {}",
         FLAGS_cfs_expect_dn_subnet);
  boost::system::error_code ec6;
  g_expect_dn_subnet_v6 =
      boost::asio::ip::make_network_v6(FLAGS_cfs_expect_dn_subnet, ec6);
  if (ec6.failed()) {
    CFSLOG(ERROR,
           "ExpectDNSubnet=`{}`is neither ipv4 nor ipv6 format, all DNs will "
           "be rejected!",
           FLAGS_cfs_expect_dn_subnet);
    g_invalid_dn_subnet = true;
    return;
  }
  g_expect_dn_ipv6 = true;
  g_expect_dn_subnet_range_v6 = g_expect_dn_subnet_v6.hosts();

  // NOTE: boost network_v6_range has a bug that not able to represent "::/0".
  // See https://github.com/boostorg/boost/issues/883 for details
  if (g_expect_dn_subnet_range_v6.empty()) {
    g_all_ipv6_valid = true;
  }
}

bool NetUtil::IsIpv4(const std::string& str) noexcept {
  boost::system::error_code ec;
  [[maybe_unused]] auto addr = boost::asio::ip::make_address_v4(str, ec);
  return !ec.failed();
}

bool NetUtil::IsIpv6(const std::string& str) noexcept {
  boost::system::error_code ec;
  [[maybe_unused]] auto addr = boost::asio::ip::make_address_v6(str, ec);
  return !ec.failed();
}

// This function is ported from byterpc util
bool NetUtil::IsRealIpv6(const in6_addr& ip) noexcept {
  return (ntohl(ip.s6_addr32[2]) != 0x0000ffff) || (ip.s6_addr32[0] != 0) ||
         (ip.s6_addr32[1] != 0);
}

bool NetUtil::IsValidIp(const std::string& str) noexcept {
  boost::system::error_code ec;
  [[maybe_unused]] auto addr = boost::asio::ip::make_address(str, ec);
  if (ec.failed()) {
    return false;
  }
  return true;
}

Result<std::variant<sockaddr_in, sockaddr_in6>> NetUtil::DoHostname2Ip(
    const std::string& host, int32_t sock_type, int32_t flags, int32_t family) {
  if (CFS_UNLIKELY(host.empty())) {
    return Status(CFS_ERR_INVALID_PARAMETER, "Empty hostname to convert to ip");
  }

  struct addrinfo* result = nullptr;
  struct addrinfo hints;
  memset(&hints, 0, sizeof(struct addrinfo));
  hints.ai_family = family;
  hints.ai_socktype = sock_type;
  hints.ai_flags = flags;
  int32_t res = getaddrinfo(host.c_str(), nullptr, &hints, &result);
  if (res != 0) {
    CFSDLOG(ERROR,
            "Fail to DoHostname2Ip, host={}, sock_type={:#x}, flags={:#x}, "
            "family={:#x}, err={}",
            host, sock_type, flags, family, res);
    return Status(CFS_ERR_INVALID_PARAMETER, "Fail to convert hostname to ip");
  }

  std::variant<sockaddr_in, sockaddr_in6> ret;
  if (result->ai_family == AF_INET) {
    CFS_DCHECK_EQ(result->ai_addr->sa_family, AF_INET);
    ret = *(reinterpret_cast<sockaddr_in*>(result->ai_addr));
  } else {
    CFS_CHECK_EQ(result->ai_family, AF_INET6);
    CFS_DCHECK_EQ(result->ai_addr->sa_family, AF_INET6);
    ret = *(reinterpret_cast<sockaddr_in6*>(result->ai_addr));
  }

  freeaddrinfo(result);
  return Result<std::variant<sockaddr_in, sockaddr_in6>>(std::move(ret));
}

Result<std::variant<sockaddr_in, sockaddr_in6>> NetUtil::Hostname2Ip(
    const std::string& host, int32_t sock_type) {
  return DoHostname2Ip(host, sock_type, AI_ADDRCONFIG, AF_UNSPEC);
}

// This function is ported from byterpc util.
static in6_addr Ipv4ToIpv6(const in_addr& v4) {
  in6_addr v6;
  v6.s6_addr32[0] = 0x00000000;
  v6.s6_addr32[1] = 0x00000000;
  v6.s6_addr32[2] = htonl(0xffff);
  v6.s6_addr32[3] = v4.s_addr;
  return v6;
}

Result<in6_addr> NetUtil::Hostname2Ip6(const std::string& host,
                                       int32_t sock_type, bool prefer_ipv6) {
  if (prefer_ipv6) {
    auto res =
        DoHostname2Ip(host, sock_type, AI_ADDRCONFIG | AI_V4MAPPED, AF_INET6);
    if (!res.IsOk()) {
      return std::move(res).GetError();
    }
    CFS_CHECK(std::holds_alternative<sockaddr_in6>(res.GetValue()));
    return std::get<sockaddr_in6>(res.GetValue()).sin6_addr;
  } else {
    // prefer ipv4
    auto res = DoHostname2Ip(host, sock_type, AI_ADDRCONFIG, AF_INET);
    if (!res.IsOk()) {
      return std::move(res).GetError();
    }
    CFS_CHECK(std::holds_alternative<sockaddr_in>(res.GetValue()));
    return Ipv4ToIpv6(std::get<sockaddr_in>(res.GetValue()).sin_addr);
  }
}

std::string NetUtil::Ip4ToStr(const struct in_addr& addr) {
  char ip_str[INET_ADDRSTRLEN];
  const char* dst = inet_ntop(AF_INET, &addr, ip_str, INET_ADDRSTRLEN);
  if (dst == nullptr) {
    CFSLOG(ERROR, "Fail to parse inet4 addr to str, errno={}", errno);
    ip_str[0] = '\0';
  }
  return std::string(ip_str);
}

std::string NetUtil::Ip6ToStr(const struct in6_addr& addr) {
  char ip_str[INET6_ADDRSTRLEN];
  const char* dst = inet_ntop(AF_INET6, &addr, ip_str, INET6_ADDRSTRLEN);
  if (dst == nullptr) {
    CFSLOG(ERROR, "Fail to parse inet6 addr to str, errno={}", errno);
    ip_str[0] = '\0';
  }
  return std::string(ip_str);
}

bool NetUtil::IsExpectDNIp(const std::string& str) noexcept {
  std::call_once(g_init_dn_subnet_flag, []() {
    InitExpectDNSubnet();
  });
  if (CFS_UNLIKELY(g_invalid_dn_subnet)) {
    return false;
  }
  boost::system::error_code ec;
  if (g_expect_dn_ipv6) {
    auto addr6 = boost::asio::ip::make_address_v6(str, ec);
    if (ec.failed()) {
      // CFSDLOG(DEBUG, "Invalid ipv6 in IsExpectDNIp, ip={}", str);
      return false;
    }
    if (g_all_ipv6_valid) {
      return true;
    }
    return g_expect_dn_subnet_range_v6.find(addr6) !=
           g_expect_dn_subnet_range_v6.end();
  } else {
    auto addr4 = boost::asio::ip::make_address_v4(str, ec);
    if (ec.failed()) {
      // CFSDLOG(DEBUG, "Invalid ipv4 in IsExpectDNIp, ip={}", str);
      return false;
    }
    return g_expect_dn_subnet_range_v4.find(addr4) !=
           g_expect_dn_subnet_range_v4.end();
  }
}

Result<boost::urls::url_view> NetUtil::ParseUrl(std::string_view str) noexcept {
  auto rv = boost::urls::parse_absolute_uri(str);
  if (rv) {
    return std::move(rv.value());
  } else {
    return Status(CFS_ERR_INVALID_PARAMETER);
  }
}

std::string NetUtil::PctEncode(const std::string_view& s) noexcept {
  boost::urls::encoding_opts opts;
  opts.space_as_plus = true;
  return boost::urls::encode(s, kUnReservedChars, opts);
}

std::string NetUtil::PctEncodeVolcAuth(const std::string_view& s) noexcept {
  boost::urls::encoding_opts opts;
  opts.space_as_plus = true;
  return boost::urls::encode(s, kVolcAuthUnReservedChars, opts);
}

Result<std::string> NetUtil::SimpleGetUrl(const std::string& url) noexcept {
  if (url.empty()) {
    return Status(CFS_ERR_INVALID_PARAMETER, "Empty string to curl");
  }
  auto uri_res = ParseUrl(url);
  if (!uri_res.IsOk()) {
    return uri_res.GetError();
  }
  auto& uri_view = uri_res.GetValue();
  httplib::Client cli(uri_view.host());
  cli.set_connection_timeout(2);  // 2s
  cli.set_read_timeout(2, 0);     // 2 seconds
  cli.set_write_timeout(2, 0);    // 2 seconds
  auto res = cli.Get(uri_view.path());
  if (res) {
    if (res->status == httplib::StatusCode::OK_200) {
      return res->body;
    } else {
      CFSLOG(ERROR, "Fail to GET url={}, status_code={}", url,
             httplib::status_message(res->status));
    }
  } else {
    CFSLOG(ERROR, "Fail to GET url={}, err={}", url,
           httplib::to_string(res.error()));
  }
  return Status(CFS_ERR_INTERNAL_ERROR, "Fail to SimpleGetUrl");
}

}  // namespace internal
}  // namespace cfs
