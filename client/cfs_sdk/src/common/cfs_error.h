#pragma once

#include <cstdint>

namespace cfs {

enum Errorcode : int32_t {
  CFS_OK = 0,
#define DECLARE_CFS_ERRORCODE(name, errorcode) CFS_ERR_##name = errorcode,
#include "common/cfs_errorcode.inc"
#undef DECLARE_CFS_ERRORCODE
  CFS_UNKNOWN_ERR = INT32_MIN,
};

const char* GetErrorString(Errorcode errorcode);

// The `format_as` function is used for fmtlib to print 'enum Errorcode'
auto inline format_as(Errorcode e) {
  return GetErrorString(e);
}

}  // namespace cfs
