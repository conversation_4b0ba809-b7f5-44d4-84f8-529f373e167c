#pragma once

#include <algorithm>
#include <string>
#include <vector>

#include "common/logger.h"

namespace cfs {
namespace internal {

// NOTE only support RightOutliersFinder yet
enum OutlierPolicy {
  // outliers are samples whose value > absolute_base_value_
  POLICY_ABSOLUTE = 0,
  // outliers are samples whose nearly-step-size > accumelated_avg[i] *
  // (relative_ratio_ - 1)
  POLICY_ACC_AVG_RELA,
  // outliers are samples whose value > median * relative_ratio_
  POLICY_MEDIAN_RELA,
  // outliers are samples whose value > q3 + iqr * relative_ratio_
  // q1 = p25, q3 = p75, iqr = q3 - q1
  POLICY_IQR
};

inline std::string ToString(OutlierPolicy policy) {
  switch (policy) {
    // NOTE user should ensure that sample count is large enough to get
    // meaningful outliers
    case POLICY_ABSOLUTE: return "ABSOLUTE";
    case POLICY_ACC_AVG_RELA: return "ACC_AVG_RELA";
    case POLICY_MEDIAN_RELA: return "MEDIAN_RELA";
    case POLICY_IQR: return "IQR";
    default: break;
  }
  return std::to_string(policy);
}

// RightOutliersFinder is a algorithm to find right outliers in samples using
// different policy.
//
// Find() can be called many times to find outliers using different policy on
// the same samples.
//
// User should ensure that sample count is large enough to get meaningful
// outliers according to "Law of Large Numbers".
template <typename K>
class RightOutliersFinder {
 public:
  struct Sample {
    K key_;
    uint64_t value_;

    Sample(const K& key, uint64_t value) : key_(key), value_(value) {}
    bool operator<(const Sample& rhs) const {
      return key_ < rhs.key_;
    }
    bool operator==(const Sample& rhs) const {
      return key_ == rhs.key_;
    }
  };

  struct Parameter {
    OutlierPolicy policy_;
    uint64_t absolute_base_value_;
    double relative_ratio_;

    Parameter()
        : policy_(POLICY_ABSOLUTE),
          absolute_base_value_(UINT64_MAX),
          relative_ratio_(0.0) {}

    Parameter(OutlierPolicy policy, uint64_t absolute_base_value,
              double relative_ratio)
        : policy_(policy),
          absolute_base_value_(absolute_base_value),
          relative_ratio_(relative_ratio) {}
  };

  struct SampleResult {
    // POLICY_ABSOLUTE:     rela_base_value_ = 0
    // POLICY_ACC_AVG_RELA: rela_base_value_ = accumelated_avg
    // POLICY_MEDIAN_RELA:  rela_base_value_ = median
    // POLICY_IQR:          rela_base_value_ = q3, iqr_ = iqr
    double rela_base_value_;
    uint64_t iqr_;
    std::vector<Sample> outliers_;

    SampleResult() : rela_base_value_(0), iqr_(0) {}
  };

 public:
  explicit RightOutliersFinder(const std::vector<Sample>& samples)
      : samples_(samples), sorted_(false) {}

  RightOutliersFinder(const std::vector<Sample>& samples, bool sorted_aesc)
      : samples_(samples), sorted_(sorted_aesc) {}

  int Find(const Parameter& para, SampleResult* res);

 private:
  uint32_t minSampleCount(OutlierPolicy policy) const;
  int findByAbosolute(const Parameter& para, SampleResult* res);
  int findByAccAvgRelaRatio(const Parameter& para, SampleResult* res);
  int findByMedianRelaRatio(const Parameter& para, SampleResult* res);
  int findByIQR(const Parameter& para, SampleResult* res);

  // helpers
  void sortIfNecessary();
  uint32_t midIndex(uint32_t l, uint32_t r) const {
    return l + (r - l) / 2;
  }

 private:
  std::vector<Sample> samples_;
  bool sorted_;
};

template <typename K>
int RightOutliersFinder<K>::Find(const Parameter& para, SampleResult* res) {
  *res = SampleResult();
  if (samples_.size() < minSampleCount(para.policy_)) {
    return -1;
  }
  switch (para.policy_) {
    case POLICY_ABSOLUTE: return findByAbosolute(para, res);
    case POLICY_ACC_AVG_RELA: return findByAccAvgRelaRatio(para, res);
    case POLICY_MEDIAN_RELA: return findByMedianRelaRatio(para, res);
    case POLICY_IQR: return findByIQR(para, res);
    default: CFSDLOG(ERROR, "not support policy:{}", ToString(para.policy_));
  }
  return -1;
}

template <typename K>
uint32_t RightOutliersFinder<K>::minSampleCount(OutlierPolicy policy) const {
  switch (policy) {
    // NOTE user should ensure that sample count is large enough to get
    // meaningful outliers
    case POLICY_ABSOLUTE: return 1;
    case POLICY_ACC_AVG_RELA: return 2;
    case POLICY_MEDIAN_RELA: return 3;
    case POLICY_IQR: return 4;
    default: CFSDLOG(ERROR, "not support policy:{}", ToString(policy));
  }
  return UINT32_MAX;
}

template <typename K>
int RightOutliersFinder<K>::findByAbosolute(const Parameter& para,
                                            SampleResult* res) {
  for (uint32_t index = 0; index < samples_.size(); ++index) {
    if (samples_[index].value_ > para.absolute_base_value_) {
      res->outliers_.emplace_back(samples_[index]);
    }
  }
  return 0;
}

template <typename K>
int RightOutliersFinder<K>::findByAccAvgRelaRatio(const Parameter& para,
                                                  SampleResult* res) {
  // NOTE ratio < 1.0 would not find any outliers
  if (para.relative_ratio_ < 1.0) {
    return -1;
  }

  sortIfNecessary();

  std::vector<double> accumelated_sums;
  accumelated_sums.resize(samples_.size());
  accumelated_sums[0] = samples_[0].value_;
  for (uint32_t i = 1; i < samples_.size(); ++i) {
    accumelated_sums[i] = accumelated_sums[i - 1] + samples_[i].value_;
  }
  uint32_t index = (samples_.size() + 1) / 2;
  for (; index < samples_.size(); ++index) {
    double rela_avg = accumelated_sums[index - 1] / index;
    if (samples_[index].value_ > para.relative_ratio_ * rela_avg) {
      res->rela_base_value_ = rela_avg;
      break;
    }
  }

  res->outliers_.reserve(samples_.size() - index);
  for (; index < samples_.size(); ++index) {
    res->outliers_.emplace_back(samples_[index]);
  }
  return 0;
}

template <typename K>
int RightOutliersFinder<K>::findByMedianRelaRatio(const Parameter& para,
                                                  SampleResult* res) {
  // NOTE ratio < 1.0 would not find any outliers
  if (para.relative_ratio_ < 1.0) {
    return -1;
  }

  sortIfNecessary();

  uint32_t mid_index = midIndex(0, samples_.size() - 1);
  uint64_t median = samples_[mid_index].value_;
  double max_value = para.relative_ratio_ * median;
  uint32_t index = mid_index;
  for (; index < samples_.size(); ++index) {
    if (samples_[index].value_ > max_value) {
      break;
    }
  }

  if (index == samples_.size()) {
    return 0;
  }
  res->rela_base_value_ = median;
  res->outliers_.reserve(samples_.size() - index);
  for (; index < samples_.size(); ++index) {
    res->outliers_.emplace_back(samples_[index]);
  }
  return 0;
}

template <typename K>
int RightOutliersFinder<K>::findByIQR(const Parameter& para,
                                      SampleResult* res) {
  sortIfNecessary();

  uint32_t p50_index = midIndex(0, samples_.size() - 1);
  uint32_t p25_index = midIndex(0, p50_index);
  uint32_t p75_index = midIndex(p50_index, samples_.size() - 1);
  uint64_t q1 = samples_[p25_index].value_;
  uint64_t q3 = samples_[p75_index].value_;
  uint64_t iqr = q3 - q1;
  double max_value = q3 + para.relative_ratio_ * iqr;
  uint32_t index = p75_index;
  for (; index < samples_.size(); ++index) {
    if (samples_[index].value_ > max_value) {
      break;
    }
  }

  if (index == samples_.size()) {
    return 0;
  }
  res->rela_base_value_ = q3;
  res->iqr_ = iqr;
  res->outliers_.reserve(samples_.size() - index);
  for (; index < samples_.size(); ++index) {
    res->outliers_.emplace_back(samples_[index]);
  }
  return 0;
}

template <typename K>
void RightOutliersFinder<K>::sortIfNecessary() {
  if (sorted_) {
    return;
  }
  std::sort(samples_.begin(), samples_.end(),
            [](const Sample& lhs, const Sample& rhs) {
              return lhs.value_ < rhs.value_;
            });
  sorted_ = true;
}

}  // namespace internal
}  // namespace cfs