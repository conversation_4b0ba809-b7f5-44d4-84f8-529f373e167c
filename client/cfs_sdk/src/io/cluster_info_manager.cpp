#include "io/cluster_info_manager.h"

#include <gflags/gflags.h>

#include "cluster_info_manager.h"
#include "common/hash_util.h"
#include "common/logger.h"
#include "common/random_util.h"
#include "common/string_util.h"
#include "io/io_executor.h"

DECLARE_uint32(cfs_get_cluster_info_interval_s);
DECLARE_bool(cfs_enable_slow_node_management);
DECLARE_bool(cfs_enable_hpc);
DECLARE_string(cfs_filesystem_endpoint_ip);
DECLARE_bool(cfs_filesystem_endpoint_resolve_by_dns);

namespace cfs {
namespace internal {

ClusterInfoManager* ClusterInfoManager::GetInstance() {
  static ClusterInfoManager instance;
  return &instance;
}

ClusterInfoManager::~ClusterInfoManager() {
  if (!stopped_.load(std::memory_order_acquire)) {
    Stop();
  }
}

void ClusterInfoManager::updateClusterInfo(
    const std::vector<DatanodeInfo>& dn_vec) {
  if (dn_vec.size() == 0) {
    return;
  }
  std::unique_lock<std::shared_mutex> write_lk(dn_info_map_mutex_);
  datanode_info_map_.clear();
  for (const auto& dn : dn_vec) {
    datanode_info_map_[dn.GetIpAddr()] = dn;
  }
}

void ClusterInfoManager::Init(FileSystem* fs, const std::string& fs_uri,
                              int32_t port) {
  std::string host_or_ip = fs_uri;
  if (!FLAGS_cfs_filesystem_endpoint_resolve_by_dns) {
    host_or_ip = FLAGS_cfs_filesystem_endpoint_ip;
  }
  nn_client_ = std::make_shared<NamenodeClient>(fs);
  nn_client_->Init(host_or_ip, port);
}

void ClusterInfoManager::Start() {
  if (!FLAGS_cfs_enable_slow_node_management || FLAGS_cfs_enable_hpc) {
    return;
  }
  stopped_.store(false);
  GetClusterInfo();
}

void ClusterInfoManager::Stop() {
  if (!FLAGS_cfs_enable_slow_node_management || FLAGS_cfs_enable_hpc) {
    return;
  }
  stopped_.store(true);
}

void ClusterInfoManager::GetClusterInfo() {
  if (stopped_.load()) {
    CFSLOG(WARN, "Cluster info manager is stopped");
    return;
  }

  CFSLOG(DEBUG, "Add GetClusterInfoTask to IOExecutor, interval: {}",
         FLAGS_cfs_get_cluster_info_interval_s);
  // Get cluster info
  IOExecutor::GetMetaExecutor()->AddTask([this]() {
    nn_client_->GetDatanodeReport(
        ::cloudfs::DatanodeReportTypeProto::LIVE,
        [this](Result<std::vector<DatanodeInfo>> res) {
          if (!res.IsOk()) {
            CFSLOG(ERROR, "GetDatanodeReport failed, err: {}",
                   res.GetError().ToString());
          } else {
            auto& dn_vec = res.GetValue();
            this->updateClusterInfo(dn_vec);
          }
          // Add delay task to get cluster info again
          IOExecutor::GetMetaExecutor()->AddDelayTask(
              [this]() {
                this->GetClusterInfo();
              },
              FLAGS_cfs_get_cluster_info_interval_s * 1000);
        });
  });
}

uint64_t ClusterInfoManager::GetDataNodeCount() {
  std::shared_lock<std::shared_mutex> read_lk(dn_info_map_mutex_);
  return datanode_info_map_.size();
}

bool ClusterInfoManager::IsExist(const std::string& ip_addr) {
  std::shared_lock<std::shared_mutex> read_lk(dn_info_map_mutex_);
  auto itr = datanode_info_map_.find(ip_addr);
  if (itr != datanode_info_map_.end()) {
    return true;
  }
  return false;
}

bool ClusterInfoManager::GetDatanodeInfo(const std::string& ip_addr,
                                         DatanodeInfo* node_info) {
  std::shared_lock<std::shared_mutex> read_lk(dn_info_map_mutex_);
  auto itr = datanode_info_map_.find(ip_addr);
  if (itr != datanode_info_map_.end()) {
    *node_info = itr->second;
    return true;
  }
  return false;
}

}  // namespace internal
}  // namespace cfs
