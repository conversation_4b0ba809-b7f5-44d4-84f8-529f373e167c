#include "metric/metric_sdk/metric_server_emitter.h"

#include <arpa/inet.h>

#include "common/datetime_util.h"
#include "common/endian.h"
#include "common/logger.h"
#include "common/net_util.h"

namespace cfs {
namespace internal {

static constexpr std::string_view kMetricType = "ts_store";
static constexpr std::string_view kMetricMsgType = "emit";
static constexpr uint32_t kFlushMetricMaxRetryNum = 5;
static constexpr int64_t kMetricSockTimeoutMs = 100;
// max payload size for UDP
static constexpr int32_t kMetricMaxSendBufSize = 65507;

static std::string EncodeTags(const std::vector<MetricTag>& common_tags,
                              const std::map<std::string, std::string>& tags) {
  std::string str;
  for (const auto& tag : common_tags) {
    str.append(tag.key_);
    str.push_back('=');
    str.append(tag.val_);
    str.push_back('|');
  }
  for (const auto& tag : tags) {
    str.append(tag.first);
    str.push_back('=');
    str.append(tag.second);
    str.push_back('|');
  }
  if (!str.empty()) {
    str.pop_back();  // Remove the last '|'
  }
  return str;
}

bool MetricServerEmitter::MetricRequest::Append(
    const std::vector<std::string_view>& record) {
  CFS_DCHECK(!IsFinalized());
  // For now, the record is represented as a 6-string-tuple:
  //   (msg_type, metric_type, name, value, tags, timestamp)
  CFS_DCHECK_EQ(record.size(), 6);

  size_t old_size = buf_.size();
  size_t record_sz = record.size();
  if (record_sz <= 0xf) {
    buf_.push_back(0x90 | record_sz);
  } else if (record_sz <= 0xffff) {
    buf_.push_back(0xdc);
    PutBigEndian<uint16_t, std::string>(record_sz, &buf_);
  } else if (record_sz <= 0xffffffffL) {
    buf_.push_back(0xdd);
    PutBigEndian<uint32_t, std::string>(record_sz, &buf_);
  } else {
    CFSLOG(FATAL, "Too large array");
  }
  for (const auto& s : record) {
    AppendString(s);
  }
  if (buf_.size() < kMetricMaxSendBufSize) {
    ++num_elements_;
    return true;
  } else {
    buf_.resize(old_size);
    return false;
  }
}

void MetricServerEmitter::MetricRequest::Finalize() {
  CFS_DCHECK(!IsFinalized());

  if (num_elements_ <= 0xf) {
    // Set offset_ to 4 to skip the first unused 4 bytes.
    offset_ = 4;
    buf_[4] = 0x90 | num_elements_;
  } else if (num_elements_ <= 0xffff) {
    // Set offset_ to 2 to skip the first unused 2 bytes.
    offset_ = 2;
    buf_[2] = 0xdc;
    PutBigEndian<uint16_t, char>(num_elements_, &buf_[3]);
  } else if (num_elements_ <= 0xffffffffL) {
    offset_ = 0;
    buf_[0] = 0xdd;
    PutBigEndian<uint32_t, char>(num_elements_, &buf_[1]);
  } else {
    CFSLOG(FATAL, "Too many elements when serialize MetrcRequest");
  }
}

void MetricServerEmitter::MetricRequest::AppendString(
    const std::string_view& s) {
  size_t len = s.size();
  if (len <= 0x1f) {
    buf_.push_back(0xa0 | static_cast<uint8_t>(len));
  } else if (len <= 0xff) {
    buf_.push_back(0xd9);
    buf_.push_back(static_cast<uint8_t>(len));
  } else if (len <= 0xffff) {
    buf_.push_back(0xda);
    PutBigEndian<uint16_t>(len, &buf_);
  } else if (len <= 0xffffffffL) {
    buf_.push_back(0xdb);
    PutBigEndian<uint32_t>(len, &buf_);
  } else {
    CFSLOG(FATAL, "Too large string in MsgPack format");
  }
  buf_.append(s.data(), s.size());
}

bool MetricServerEmitter::Prepare() {
  if (prepared_) {
    // Prepared success before. No need to prepare again.
    return prepared_;
  }

  memset(&sockaddr_, 0, sizeof(sockaddr_));
  sockaddr_ptr_ = reinterpret_cast<sockaddr*>(&sockaddr_.unix_domain_);

  if (!conf_.unix_domain.empty()) {
    CFSLOG(INFO, "Initing MetricServerEmitter with unix_domain={}",
           conf_.unix_domain);
    CFS_CHECK(conf_.tcp_addr.empty());
    sockaddr_.unix_domain_.sun_family = AF_UNIX;
    constexpr auto sun_cap = sizeof(sockaddr_.unix_domain_.sun_path);
    static_assert(sun_cap > 1);
    strncpy(sockaddr_.unix_domain_.sun_path, conf_.unix_domain.c_str(),
            sun_cap - 1);
    sockaddr_.unix_domain_.sun_path[sun_cap - 1] = '\0';
    sock_len_ = SUN_LEN(&sockaddr_.unix_domain_);
    prepared_ = true;
    return prepared_;
  }

  CFSLOG(INFO, "Initing MetricServerEmitter with tcp_addr={}, port={}",
         conf_.tcp_addr, conf_.tcp_port);
  if (conf_.tcp_addr.empty() || conf_.tcp_port <= 0) {
    CFSLOG(WARN, "Invalid MetricServer endpoint, tcp_addr={}, port={}",
           conf_.tcp_addr, conf_.tcp_port);
    return prepared_;
  }
  if (inet_pton(AF_INET, conf_.tcp_addr.c_str(), &sockaddr_.ip4_ep_.sin_addr) ==
      1) {
    sockaddr_.ip4_ep_.sin_family = AF_INET;
    sockaddr_.ip4_ep_.sin_port = htons(static_cast<uint16_t>(conf_.tcp_port));
    sock_len_ = sizeof(sockaddr_.ip4_ep_);
    prepared_ = true;
    return prepared_;
  }
  if (inet_pton(AF_INET6, conf_.tcp_addr.c_str(),
                &sockaddr_.ip6_ep_.sin6_addr) == 1) {
    sockaddr_.ip6_ep_.sin6_family = AF_INET6;
    sockaddr_.ip6_ep_.sin6_port = htons(static_cast<uint16_t>(conf_.tcp_port));
    sock_len_ = sizeof(sockaddr_.ip6_ep_);
    prepared_ = true;
    return prepared_;
  }

  // Not ipv4 or ipv6. Treat address as url.
  auto res = NetUtil::Hostname2Ip(conf_.tcp_addr, SOCK_STREAM);
  if (!res.IsOk()) {
    CFSLOG(WARN, "Invalid metrics server endpoint, tcp_addr={}, port={}",
           conf_.tcp_addr, conf_.tcp_port);
    return prepared_;
  }
  if (std::holds_alternative<sockaddr_in>(res.GetValue())) {
    sockaddr_.ip4_ep_ = std::get<sockaddr_in>(res.GetValue());
    sockaddr_.ip4_ep_.sin_port = htons(static_cast<uint16_t>(conf_.tcp_port));
    sock_len_ = sizeof(sockaddr_.ip4_ep_);
  } else {
    CFS_CHECK(std::holds_alternative<sockaddr_in6>(res.GetValue()));
    sockaddr_.ip6_ep_ = std::get<sockaddr_in6>(std::move(res).GetValue());
    sockaddr_.ip6_ep_.sin6_port = htons(static_cast<uint16_t>(conf_.tcp_port));
    sock_len_ = sizeof(sockaddr_.ip6_ep_);
  }
  prepared_ = true;
  return prepared_;
}

MetricServerEmitter::~MetricServerEmitter() {
  if (sock_ >= 0) {
    Close();
  }
}

bool MetricServerEmitter::Emit(const std::string& name, int64_t value,
                               const MetricId& id, int64_t time_sec) {
  return DoEmit(name, std::to_string(value), id, time_sec);
}

bool MetricServerEmitter::Emit(const std::string& name, double value,
                               const MetricId& id, int64_t time_sec) {
  return DoEmit(name, std::to_string(value), id, time_sec);
}

bool MetricServerEmitter::DoEmit(const std::string& name,
                                 const std::string& value, const MetricId& id,
                                 int64_t time_sec) {
  auto tags_str = EncodeTags(id.GetCommonTags(), id.GetTags());
  auto time_str = std::to_string(time_sec);
  std::vector<std::string_view> record;
  record.emplace_back(kMetricMsgType);
  record.emplace_back(kMetricType);
  record.emplace_back(name);
  record.emplace_back(value);
  record.emplace_back(tags_str);
  record.emplace_back(time_str);
  // CFSDLOG(INFO, "Emit one metric: {}, {}, {}", name, value, tags_str);
  if (!req_.Append(record)) {
    if (!Flush()) {
      return false;
    }
    if (!req_.Append(record)) {
      CFSLOG(WARN,
             "Fail to append metric record because it is too large. Drop this "
             "record. metric_name={}",
             name);
      return true;
    }
  }
  if (req_.IsFull()) {
    if (!Flush()) {
      return false;
    }
  }
  return true;
}

bool MetricServerEmitter::Flush() {
  if (req_.IsEmpty()) {
    return true;
  }
  req_.Finalize();
  bool ret = true;
  uint32_t tried_num = 0;
  // CFSDLOG(DEBUG, "Flushing {} bytes data to metric server", req_.GetSize());
  while (!Send(req_.GetData(), req_.GetSize(), MSG_DONTWAIT)) {
    tried_num++;
    if (tried_num >= kFlushMetricMaxRetryNum) {
      CFSLOG(ERROR,
             "Fail to send {} bytes metric data to metric server after "
             "retrying {} times. Drop current request. Last errno={}",
             req_.GetSize(), kFlushMetricMaxRetryNum, errno);
      ret = false;
      break;
    }
  }
  req_.Reset();
  return ret;
}

bool MetricServerEmitter::Send(const void* data, size_t len, int flags) {
  if (CFS_LIKELY(sock_ >= 0)) {
    if (CFS_LIKELY(send(sock_, data, len, flags) >= 0)) {
      return true;
    }
    if (errno == EAGAIN || errno == EWOULDBLOCK) {
      return false;
    }
    Close();
  }
  if (!Connect()) {
    return false;
  }
  if (send(sock_, data, len, flags) == -1) {
    if (errno != EAGAIN && errno != EWOULDBLOCK) {
      Close();
    }
    return false;
  }
  return true;
}

bool MetricServerEmitter::Connect() {
  // Don't connect to the server too frequently.
  auto now = DatetimeUtil::GetNowTimeMs();
  if (now < last_connect_time_ms_ + kMetricSockTimeoutMs) {
    std::this_thread::sleep_until(
        std::chrono::time_point<std::chrono::system_clock>(
            std::chrono::milliseconds(last_connect_time_ms_ +
                                      kMetricSockTimeoutMs)));
  }
  last_connect_time_ms_ = now;

  sock_ = socket(sockaddr_ptr_->sa_family, SOCK_DGRAM, 0);
  if (sock_ < 0) {
    CFSLOG(ERROR, "Failed to create socket, errno={}", errno);
    return false;
  }
  timeval tv;
  tv.tv_sec = 0;
  tv.tv_usec = 1000 * kMetricSockTimeoutMs;
  if (setsockopt(sock_, SOL_SOCKET, SO_SNDTIMEO, &tv, sizeof(tv))) {
    CFSLOG(ERROR, "Fail to set the metric sending timeout, errno={}", errno);
    Close();
    return false;
  }
  if (setsockopt(sock_, SOL_SOCKET, SO_SNDBUF, &kMetricMaxSendBufSize,
                 sizeof(int32_t))) {
    CFSLOG(ERROR, "Fail to set the metric max send buf size, errno={}", errno);
    Close();
    return false;
  }

  if (connect(sock_, sockaddr_ptr_, sock_len_) != 0) {
    CFSLOG(ERROR, "Fail to connect to metric socket, errno={}", errno);
    Close();
    return false;
  }
  CFSLOG(INFO, "Success to connect to metric socket");
  return true;
}

void MetricServerEmitter::Close() {
  CFS_DCHECK_GE(sock_, 0);
  if (close(sock_) < 0) {
    CFSLOG(ERROR, "Fail to close sock to metric server, errno={}", errno);
  }
  CFSLOG(INFO, "Success to close sock to metric server");
  sock_ = -1;
}

}  // namespace internal
}  // namespace cfs
