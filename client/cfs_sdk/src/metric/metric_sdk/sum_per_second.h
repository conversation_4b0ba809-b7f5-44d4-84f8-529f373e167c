#pragma once

// metric_sdk will use bvar, which will use butil::scoped_lock, which will use
// glog in debug-mode.
// byte::thread_pool will use bytelog.
// glog and bytelog conflict with each other. So we must compile bvar in
// release mode.
#ifndef NDEBUG
#define NDEBUG
#include <bvar/reducer.h>
#include <bvar/window.h>
#undef NDEBUG
#else
#include <bvar/reducer.h>
#include <bvar/window.h>
#endif

#include <cstdint>

namespace cfs {
namespace internal {

class SumPerSecond final {
 public:
  explicit SumPerSecond(int64_t window_sec)
      : sum_(), sum_window_(&sum_, window_sec) {}

  double GetValue() const {
    auto val = sum_window_.get_value();
    return static_cast<double>(val) / sum_window_.window_size();
  }

  void Increase(int64_t n = 1) {
    sum_ << n;
  }

  void Decrease(int64_t n = 1) {
    sum_ << -n;
  }

 private:
  bvar::Adder<int64_t> sum_;
  bvar::Window<bvar::Adder<int64_t>> sum_window_;
};

}  // namespace internal
}  // namespace cfs
