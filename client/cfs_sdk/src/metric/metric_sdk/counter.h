#pragma once

// metric_sdk will use bvar, which will use butil::scoped_lock, which will use
// glog in debug-mode.
// byte::thread_pool will use bytelog.
// glog and bytelog conflict with each other. So we must compile bvar in
// release mode.
#ifndef NDEBUG
#define NDEBUG
#include <bvar/reducer.h>
#undef NDEBUG
#else
#include <bvar/reducer.h>
#endif

#include <cstdint>

namespace cfs {
namespace internal {

class Counter final {
 public:
  explicit Counter(int64_t val = 0) {
    val_ << val;
  }

  int64_t GetValue() const {
    return val_.get_value();
  }

  void Increase(int64_t n = 1) {
    val_ << n;
  }

  void Decrease(int64_t n = 1) {
    val_ << -n;
  }

 private:
  bvar::Adder<int64_t> val_;
};

}  // namespace internal
}  // namespace cfs
