#include "cloudfs2/cloudfs2.hpp"
#include "common/datetime_util.h"
#include "impl/cfs_impl.h"

namespace cfs {

using DatetimeUtil = ::cfs::internal::DatetimeUtil;
using CfsTrace = ::cfs::internal::CfsTrace;

int32_t ListDirPolicy(cfs_fs_handle fs, std::vector<CfsDirPolicy>* policies) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  int32_t res = CfsListWriteBackPolicy(fs, policies, trid);
  std::string tr_path;
  CfsTrace::AddTrace("CLI_API_LISTDIRPOLICY", trid, tr_path, start_us,
                     end_us - start_us, res >= 0);
  return res;
}

int32_t ListSyncPolicy(cfs_fs_handle fs, std::vector<CfsDirPolicy>* policies) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  int32_t res = CfsListSyncPolicy(fs, policies, trid);
  std::string tr_path;
  CfsTrace::AddTrace("CLI_API_LISTSYNCPOLICY", trid, tr_path, start_us,
                     end_us - start_us, res >= 0);
  return res;
}

int32_t MkdirExt(cfs_fs_handle fs, const std::string& path, mode_t mode,
                 bool create_parent, const cfs_ttl_t& ttl,
                 cfs_write_back_policy wb) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  int32_t res = CfsMkdirExt(fs, path, mode, create_parent, ttl, wb, trid);
  CfsTrace::AddTrace("CLI_API_MKDIREXT", trid, path, start_us,
                     end_us - start_us, res >= 0);
  return res;
}

std::string LoadCache(cfs_fs_handle fs, const std::string& path, bool recursive,
                      bool load_metadata, bool load_data, int replica_num) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  std::string res = CfsLoad(fs, path.c_str(), recursive, load_metadata,
                            load_data, replica_num, trid);
  CfsTrace::AddTrace("CLI_API_LOAD", trid, path, start_us, end_us - start_us,
                     !res.empty());
  return res;
}

std::string FreeCache(cfs_fs_handle fs, const std::string& path, bool recursive,
                      bool free_metadata) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  std::string res = CfsFree(fs, path.c_str(), recursive, free_metadata, trid);
  CfsTrace::AddTrace("CLI_API_FREE", trid, path, start_us, end_us - start_us,
                     !res.empty());
  return res;
}

int32_t LookupJob(cfs_fs_handle fs, const std::string& job_id,
                  LookupJobRsp* resp) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  int32_t res = CfsLookupJob(fs, job_id.c_str(), resp, trid);
  CfsTrace::AddTrace("CLI_API_LOOKUP_JOB", trid, job_id, start_us,
                     end_us - start_us, res >= 0);
  return res;
}

int32_t CancelJob(cfs_fs_handle fs, const std::string& job_id) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us);
  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  int32_t res = CfsCancelJob(fs, job_id.c_str(), trid);
  CfsTrace::AddTrace("CLI_API_LOOKUP_JOB", trid, job_id, start_us,
                     end_us - start_us, res >= 0);
  return res;
}

}  // namespace cfs
