#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"
#include "impl/cfs_impl.h"  // access FileSystem::GetFileInfo
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"

DECLARE_uint64(cfs_write_block_size);
DECLARE_bool(cfs_create_with_add_blk);

DEFINE_string(parent_dir, "/parent/dir", "must exist");
DEFINE_string(test_dir, "create_with_addblk_it",
              "must not exist under parent_dir");

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::Gt;
using ::testing::Ne;
using ::testing::NiceMock;
using ::testing::Return;

class CreateWithAddBlkMockIT : public testing::Test {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_write_block_size = 16 * 1024 * 1024;
    FLAGS_cfs_create_with_add_blk = true;
    fs_ = ::cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
    // Make sure parent_dir exists
    auto* finfo = ::cfs_get_file_info(fs_, FLAGS_parent_dir.c_str());
    ASSERT_NE(finfo, nullptr);
    ASSERT_EQ(finfo->type, CFS_FILE_DIR);
    ASSERT_EQ(::cfs_free_file_info(fs_, finfo, 1), 0);

    test_dir_ = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    auto exist_res = ::cfs_exist(fs_, test_dir_.c_str());
    ASSERT_GE(exist_res, 0);
    if (exist_res > 0) {
      ASSERT_EQ(::cfs_delete(fs_, test_dir_.c_str(), true), 0);
    }
    ASSERT_EQ(::cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);

    MockUtil::SetDefaultMockReturnVal();
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(::cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {}

  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
  static std::string test_dir_;
};

cfs_fs* CreateWithAddBlkMockIT::fs_ = nullptr;
std::string CreateWithAddBlkMockIT::test_dir_;

TEST_F(CreateWithAddBlkMockIT, 1BlkNonEmpty) {
  std::string path1 = test_dir_ + "/1BlkNonEmpty";
  NiceMock<MockRpcStub> mock;
  // Because the LocatedBlock is already in CreateResponseProto, so no AddBlock
  // should be issued.
  EXPECT_CALL(mock, AddBlock(_, _, _, _, _)).Times(0);
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = 2,
      .force = false};
  cfs_file* fd = ::cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);

  constexpr uint64_t kBufSize = 1024 * 128;
  std::array<char, kBufSize> buf;
  buf.fill('1');
  int64_t written_size = ::cfs_write(fd, buf.data(), buf.size());
  ASSERT_EQ(written_size, static_cast<int64_t>(kBufSize));
  ASSERT_EQ(::cfs_close(fd), 0);
  RpcMock::GetInstance()->SetMockStub(nullptr);
}

TEST_F(CreateWithAddBlkMockIT, 1BlkEmpty) {
  std::string path1 = test_dir_ + "/1BlkEmpty";
  NiceMock<MockRpcStub> mock;
  EXPECT_CALL(mock, AddBlock(_, _, _, _, _)).Times(0);
  EXPECT_CALL(mock, AbandonBlock(_, _, _, _, _)).Times(0);
  EXPECT_CALL(mock, Complete(path1, Ne(0), _, 0, _))
      .Times(1)
      .WillOnce(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = 2,
      .force = false};
  cfs_file* fd = ::cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);

  // close file without writing any data. The LocatedBlock in CreateRsp is also
  // not AbandonBlock-ed.
  ASSERT_EQ(::cfs_close(fd), 0);

  // The empty blocks should not appear
  auto get_res = fs_->fs_handle->GetFileInfo(path1, true, false, -1);
  ASSERT_TRUE(get_res.IsOk());
  auto& finfo = get_res.GetValue();
  EXPECT_EQ(finfo.GetLength(), 0);
  EXPECT_TRUE(finfo.GetLocatedBlocks().GetBlocks().empty());

  RpcMock::GetInstance()->SetMockStub(nullptr);
}

TEST_F(CreateWithAddBlkMockIT, 2BlkNonEmpty) {
  std::string path1 = test_dir_ + "/2BlkNonEmpty";
  NiceMock<MockRpcStub> mock;
  EXPECT_CALL(mock, AddBlock(_, _, _, _, _))
      .Times(1)
      .WillOnce(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  EXPECT_CALL(mock, AbandonBlock(_, _, _, _, _)).Times(0);
  EXPECT_CALL(mock, Complete(path1, Ne(0), _, Gt(0), _))
      .Times(1)
      .WillOnce(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = 2,
      .force = false};
  cfs_file* fd = ::cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);

  constexpr uint64_t kBufLen = 1024 * 128;
  std::array<char, kBufLen> buf;
  buf.fill('1');
  uint64_t finish_len = 0;
  while (finish_len < FLAGS_cfs_write_block_size + kBufLen) {
    int64_t write_res1 = ::cfs_write(fd, buf.data(), kBufLen);
    EXPECT_EQ(write_res1, static_cast<int64_t>(kBufLen));
    finish_len += kBufLen;
  }
  ASSERT_EQ(::cfs_close(fd), 0);
  RpcMock::GetInstance()->SetMockStub(nullptr);
}

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}