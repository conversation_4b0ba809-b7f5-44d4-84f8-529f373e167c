set(lib_name cli11)
ExternalProject_Add(
  ${lib_name}
  URL https://github.com/CLIUtils/CLI11/archive/refs/tags/v2.3.2.tar.gz
  URL_HASH MD5=b80cb645dee25982110b068b426363ff
  DOWNLOAD_NAME cli11-2.3.2.tar.gz
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DCLI11_PRECOMPILED=ON
    -DCLI11_BUILD_TESTS=OFF
    -DCLI11_BUILD_EXAMPLES=OFF
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
  )
