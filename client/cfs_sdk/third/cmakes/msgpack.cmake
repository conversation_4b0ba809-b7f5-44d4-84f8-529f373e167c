set(lib_name msgpack)
ExternalProject_Add(
    ${lib_name}
    URL https://github.com/msgpack/msgpack-c/releases/download/cpp-3.0.1/msgpack-3.0.1.tar.gz
    URL_HASH MD5=d933875f5d30dd6756c214d7dcb05fac
    DOWNLOAD_NAME msgpack-3.0.1.tar.gz
    PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
    DOWNLOAD_DIR ${DOWNLOAD_DIR}
    BUILD_IN_SOURCE FALSE
    CMAKE_ARGS
      ${common_cmake_args}
      -DMSGPACK_CXX17=ON
      -DMSGPACK_BUILD_EXAMPLES=OFF
      -DMSGPACK_BUILD_TESTS=OFF
      -DMSGPACK_ENABLE_SHARED=OFF
    BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
    INSTALL_COMMAND make -s install
    LOG_CONFIGURE TRUE
    LOG_BUILD TRUE
    LOG_INSTALL TRUE
)
