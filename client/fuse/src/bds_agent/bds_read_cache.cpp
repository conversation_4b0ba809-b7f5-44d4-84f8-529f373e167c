#include <errno.h>
#include <fcntl.h>
#include <gflags/gflags.h>
#include <linux/futex.h>
#include <sys/mman.h>
#include <sys/syscall.h>
#include <time.h>
#include <unistd.h>

#include <cstdio>
#include <cstring>
#include <iostream>

#include "bds_cache.h"
#include "common/logger.h"
#include "common/macros.h"

extern BdsCache gBdsCache;

uint64_t OpenFileForPrefetch(const std::string& abs_path) {
  int32_t cfs_flags = 0;
  cfs_flags |= CFS_O_RDONLY;
  cfs_open_option_t opts{.flags = cfs_flags,
                         .mode = 0644,
                         .create_parent = false,
                         .replication = 0,
                         .force = false};
  cfs_file* fh = cfs_open(reinterpret_cast<cfs_fs*>(gBdsCache.fs),
                          abs_path.c_str(), &opts);
  return (uint64_t)fh;
}

void DownloadOneSmallFile(CachedFileInfo* file_info, int64_t file_size) {
  int64_t offset = 0;
  int64_t subblock_idx, blk_idx, tmp_len;
  int64_t i;
  int64_t rc1;
  BdsCacheNsync* nsync = (BdsCacheNsync*)gBdsCache.nsync;
  BDSLOG(INFO,
         "DownloadOneSmallFile filling a small file. path {}, file_size {}",
         file_info->abs_path, file_size);

  for (i = 0; i < (int64_t)file_info->subblocks.size(); i++) {
    tmp_len = MIN(file_size - offset, BDS_CACHE_SUBBLOCK_SIZE);  // per subblock
    blk_idx = file_info->subblocks[i] / BDS_CACHE_SUBBLOCKS_PER_PAGE;
    subblock_idx = file_info->subblocks[i] % BDS_CACHE_SUBBLOCKS_PER_PAGE;
    void* addr = GetBdsCacheMemOffset(blk_idx, subblock_idx);
    rc1 = cfs_pread(reinterpret_cast<cfs_file*>(file_info->prefetch_fh), addr,
                    tmp_len, offset);
    if (rc1 != tmp_len) {
      BDSLOG(ERROR, "Fail to read remote part, rc1 {} tmp_len {} err={}", rc1,
             tmp_len, cfs_get_last_error()->message);
      nsync->futex[blk_idx][subblock_idx].store(BDS_FUTEX_STATE_ERROR);
      int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
      syscall(SYS_futex, futex_addr, FUTEX_WAKE, 1000, NULL, NULL, 0);
    } else {
      // BDSLOG(INFO, "DownloadOneSmallFile filling subblock: file {} offset {}
      // length {} remain {} read from cfs success", file_info->abs_path,
      // offset, tmp_len, length);
      nsync->futex[blk_idx][subblock_idx].store(BDS_FUTEX_STATE_DATA_READY);
      int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
      syscall(SYS_futex, futex_addr, FUTEX_WAKE, 1000, NULL, NULL, 0);
    }
    offset += tmp_len;
  }
  file_info->small_file_data_ready.store(1);
  BDSLOG(INFO,
         "DownloadOneSmallFile filling done. path {}, file_size {}, subblk_num "
         "{}, first_subblock {}",
         file_info->abs_path, file_size, file_info->subblocks.size(),
         file_info->subblocks[0]);
}

void FetchSubblocksRange(CachedFileInfo* file_info,
                         const std::vector<int64_t>& subblocks, int64_t offset,
                         int64_t file_size) {
  int64_t subblock_idx, blk_idx, tmp_len;
  int64_t i;
  int64_t rc1;
  BdsCacheNsync* nsync = (BdsCacheNsync*)gBdsCache.nsync;
  // BDSLOG(INFO, "FetchSubblocksRange filling subblocks range. path {}, offset
  // {}, subblock_num {}, file_size {}", file_info->abs_path, offset,
  // subblocks.size(), file_size);

  if (subblocks.size() == 0) {
    BDSLOG(ERROR, "FetchSubblocksRange subblocks size is zero");
    return;
  }

  for (i = 0; i < (int64_t)subblocks.size(); i++) {
    tmp_len = MIN(file_size - offset, BDS_CACHE_SUBBLOCK_SIZE);  // per subblock
    blk_idx = subblocks[i] / BDS_CACHE_SUBBLOCKS_PER_PAGE;
    subblock_idx = subblocks[i] % BDS_CACHE_SUBBLOCKS_PER_PAGE;
    void* addr = GetBdsCacheMemOffset(blk_idx, subblock_idx);
    rc1 = cfs_pread(reinterpret_cast<cfs_file*>(file_info->prefetch_fh), addr,
                    tmp_len, offset);
    if (rc1 != tmp_len) {
      BDSLOG(ERROR, "Fail to read remote part, rc1 {} tmp_len {} err={}", rc1,
             tmp_len, cfs_get_last_error()->message);
      nsync->futex[blk_idx][subblock_idx].store(BDS_FUTEX_STATE_ERROR);
      int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
      syscall(SYS_futex, futex_addr, FUTEX_WAKE, 1000, NULL, NULL, 0);
    } else {
      // BDSLOG(INFO, "FetchSubblocksRange filling subblock: fh {} offset {}
      // length {} remain {} read from cfs success", fh, offset, tmp_len,
      // length);
      nsync->futex[blk_idx][subblock_idx].store(BDS_FUTEX_STATE_DATA_READY);
      int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
      syscall(SYS_futex, futex_addr, FUTEX_WAKE, 1000, NULL, NULL, 0);
    }
    offset += tmp_len;
  }
  BDSLOG(INFO,
         "FetchSubblocksRange filling done, wait for frontend to read... path "
         "{}, file_size {}, subblk_num {}",
         file_info->abs_path, file_size, subblocks.size());

  int64_t evict_blk_idx = subblocks[0] / BDS_CACHE_SUBBLOCKS_PER_PAGE;
  int64_t evict_subblock_idx = subblocks[0] % BDS_CACHE_SUBBLOCKS_PER_PAGE;
  struct timespec timeout;
  timeout.tv_sec = 2;
  timeout.tv_nsec = 0;
  int* futex_addr = (int*)(&nsync->futex[evict_blk_idx][evict_subblock_idx]);
  while (true) {
    // if interrupted system call and futex value is 0, continue to FUTEX_WAIT
    syscall(SYS_futex, futex_addr, FUTEX_WAIT, BDS_FUTEX_STATE_DATA_READY,
            timeout, NULL, 0);
    if (nsync->futex[evict_blk_idx][evict_subblock_idx].load() !=
        BDS_FUTEX_STATE_EVICT) {
      continue;
    }
    break;
  }

  for (i = 0; i < (int64_t)subblocks.size(); i++) {
    BdsCacheReleaseSubblocks(subblocks);
  }
  BDSLOG(INFO,
         "FetchSubblocksRange release subblocks done path {}, end_offset {}, "
         "file_size {}, subblk_num {}",
         file_info->abs_path, offset, file_size, subblocks.size());
}

// backend: download a block from CFS
void DownloadOneBlock(CachedFileInfo* file_info, void* block_addr,
                      int64_t length, int64_t offset, int64_t blk_idx) {
  int64_t finish_size = 0;
  int64_t subblock_idx = 0;
  BdsCacheNsync* nsync = (BdsCacheNsync*)gBdsCache.nsync;
  BDSLOG(INFO,
         "DownloadOneBlock filling a block. file {}, block_addr {}, length {}, "
         "offset {}, blk_idx {}",
         file_info->abs_path, block_addr, length, offset, blk_idx);

  while (length > 0) {
    int64_t tmp_len = MIN(length, BDS_CACHE_SUBBLOCK_SIZE);  // per subblock
    void* addr = (void*)((char*)(block_addr) + finish_size);
    int64_t rc1 = cfs_pread(reinterpret_cast<cfs_file*>(file_info->prefetch_fh),
                            addr, tmp_len, offset);
    if (rc1 != tmp_len) {
      BDSLOG(ERROR, "Fail to read remote part, rc1 {} tmp_len {} err={}", rc1,
             tmp_len, cfs_get_last_error()->message);
      nsync->futex[blk_idx][subblock_idx].store(BDS_FUTEX_STATE_ERROR);
      int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
      syscall(SYS_futex, futex_addr, FUTEX_WAKE, 1000, NULL, NULL, 0);
    } else {
      // BDSLOG(INFO, "DownloadOneBlock filling subblock: fh {} offset {} length
      // {} remain {} read from cfs success", fh, offset, tmp_len, length);
      nsync->futex[blk_idx][subblock_idx].store(BDS_FUTEX_STATE_DATA_READY);
      int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
      syscall(SYS_futex, futex_addr, FUTEX_WAKE, 1000, NULL, NULL, 0);
    }

    offset += tmp_len;
    length -= tmp_len;
    finish_size += tmp_len;
    subblock_idx++;
  }
  gBdsCache.blocks[blk_idx].data_ready.store(1);  // all done, set data ready
  // if (file_info->prefetch_fh_refcount.fetch_sub(1) == 1) {
  // cfs_close(reinterpret_cast<cfs_file_handle>(file_info->prefetch_fh));
  //}

  // BDSLOG(INFO, "DownloadOneBlock filling a block done. fh {}, block_addr {},
  // length {}, offset {}, blk_idx {}",
  //   fh, block_addr, length, offset, blk_idx);
}

int64_t HitRead(int64_t blk_idx, int64_t offset, int64_t size, char* buf) {
  // BDSLOG(INFOs, "HitRead from block idx {} offset {}, size {} ", blk_idx,
  // offset, size);
  int64_t length = size;
  int64_t tmp_len, blk_offset, subblock_idx, subblock_offset;
  int64_t buf_offset = 0;
  struct timespec timeout;
  timeout.tv_sec = 2;
  timeout.tv_nsec = 0;
  BdsCacheNsync* nsync = (BdsCacheNsync*)gBdsCache.nsync;

  while (length > 0) {
    blk_offset = offset % BDS_CACHE_BLOCK_SIZE;
    subblock_idx = blk_offset / BDS_CACHE_SUBBLOCK_SIZE;
    subblock_offset = subblock_idx * BDS_CACHE_SUBBLOCK_SIZE;
    tmp_len =
        MIN(length, BDS_CACHE_SUBBLOCK_SIZE - (blk_offset - subblock_offset));

    int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
    while (true) {
      // if interrupted system call and futex value is 0, continue to FUTEX_WAIT
      syscall(SYS_futex, futex_addr, FUTEX_WAIT, BDS_FUTEX_STATE_PENDING,
              &timeout, NULL, 0);
      if (nsync->futex[blk_idx][subblock_idx].load() ==
          BDS_FUTEX_STATE_PENDING) {
        continue;
      }
      break;
    }

    if (nsync->futex[blk_idx][subblock_idx].load() !=
        BDS_FUTEX_STATE_DATA_READY) {
      BDSLOG(ERROR, "HitRead err futex {}",
             nsync->futex[blk_idx][subblock_idx].load());
      return -1;
    }
    char* shm_offset = GetBdsCacheMemOffset(blk_idx, 0) + blk_offset;
    memcpy((char*)buf + buf_offset, shm_offset, tmp_len);
    // BDSLOG(INFO, "HitRead read blk idx {} from offset {} subblock blk_offset
    // {} subblock_idx {} length {} buf_offset {} success", blk_idx, offset,
    // blk_offset, subblock_idx, tmp_len, buf_offset);

    offset += tmp_len;
    length -= tmp_len;
    buf_offset += tmp_len;
  }

  return size;
}

int64_t BackendRead(CachedFileInfo* file_info, char* buf, int64_t size,
                    off_t offset, uint64_t fh) {
  BDSLOG(INFO, "BackendRead path {} length {} offset {} fh {}",
         file_info->abs_path, size, offset, fh);
  int64_t blk_offset, blk_idx_in_file, tmp_len;
  int64_t buf_offset = 0;
  int res;
  int64_t length = size;

  if (file_info->blocks.size() == 0) {
    res = cfs_pread(reinterpret_cast<cfs_file*>(fh), buf, size, offset);
    if (res < 0) {
      BDSLOG(
          ERROR,
          "ReadFromBackend: Miss read failed for file name: {}, fh:{}, res: {}",
          file_info->abs_path, fh, res);
      return -1;
    }
    return res;
  }

  while (length > 0) {
    blk_idx_in_file = offset / BDS_CACHE_BLOCK_SIZE;
    blk_offset = offset % BDS_CACHE_BLOCK_SIZE;
    tmp_len = MIN(length, BDS_CACHE_BLOCK_SIZE - blk_offset);
    int64_t blk_idx = -1;
    std::pair<int64_t, uint32_t> b = QueryFileBlock(file_info, blk_idx_in_file);
    if (b.first != -1) {
      gBdsCache.blocks[b.first].mtx.lock();
      if (b.second == gBdsCache.blocks[b.first].generation) {  // hit
        blk_idx = b.first;
        gBdsCache.blocks[blk_idx].accessed_num.fetch_add(1);
        gBdsCache.blocks[blk_idx].pending_read.fetch_add(1);
        gBdsCache.blocks[b.first].mtx.unlock();

        res = HitRead(blk_idx, offset, tmp_len, buf + buf_offset);
        if (res != tmp_len) {
          BDSLOG(ERROR,
                 "BackendRead: Hit read failed for (file name: {}, fh:{}, "
                 "res:{}, size:{}",
                 file_info->abs_path, fh, res, tmp_len);
          res = cfs_pread(reinterpret_cast<cfs_file*>(fh), buf + buf_offset,
                          tmp_len, offset);
          if (res < 0) {
            BDSLOG(ERROR,
                   "BackendRead: OriginRead failed for (file name: {}, fh:{}, "
                   "res: {}",
                   file_info->abs_path, fh, res);
            return -1;
          }
        }

        gBdsCache.blocks[blk_idx].mtx.lock();
        gBdsCache.blocks[blk_idx].pending_read.fetch_sub(1);
        gBdsCache.blocks[blk_idx].mtx.unlock();
      } else {
        gBdsCache.blocks[b.first].mtx.unlock();
      }
    }

    if (blk_idx == -1) {  // missed, read from backend
      res = cfs_pread(reinterpret_cast<cfs_file*>(fh), buf + buf_offset,
                      tmp_len, offset);
      if (res < 0) {
        BDSLOG(ERROR,
               "ReadFromBackend: Miss read failed for file name: {}, fh:{}, "
               "res: {}",
               file_info->abs_path, fh, res);
        return -1;
      }
      BDSLOG(INFO,
             "BackendRead: Miss read success for file name: {}, fh:{}, res:{}, "
             "offset: {} size:{} buf_offset {}",
             file_info->abs_path, fh, res, offset, tmp_len, buf_offset);
    }

    offset += tmp_len;
    buf_offset += tmp_len;
    length -= tmp_len;
  }
  return size;
}

int64_t HitReadSmallFile(CachedFileInfo* file_info, int64_t offset,
                         int64_t size, char* buf) {
  // BDSLOG(INFO, "FrontendReadOneSmallFile path {} offset {}, size {} ",
  // file_info->abs_path, offset, size);
  int64_t length = size;
  int64_t tmp_len, idx, subblock_offset, subblock, blk_idx, subblock_idx;
  int64_t buf_offset = 0;
  struct timespec timeout;
  timeout.tv_sec = 2;
  timeout.tv_nsec = 0;
  BdsCacheNsync* nsync = (BdsCacheNsync*)gBdsCache.nsync;

  while (length > 0) {
    idx = offset / BDS_CACHE_SUBBLOCK_SIZE;
    subblock_offset = offset % BDS_CACHE_SUBBLOCK_SIZE;
    tmp_len = MIN(length, BDS_CACHE_SUBBLOCK_SIZE - subblock_offset);

    subblock = file_info->subblocks[idx];
    blk_idx = subblock / BDS_CACHE_SUBBLOCKS_PER_BLOCK;
    subblock_idx = subblock % BDS_CACHE_SUBBLOCKS_PER_BLOCK;

    int* futex_addr = (int*)(&nsync->futex[blk_idx][subblock_idx]);
    while (true) {
      // if interrupted system call and futex value is 0, continue to FUTEX_WAIT
      syscall(SYS_futex, futex_addr, FUTEX_WAIT, BDS_FUTEX_STATE_PENDING,
              &timeout, NULL, 0);
      if (nsync->futex[blk_idx][subblock_idx].load() ==
          BDS_FUTEX_STATE_PENDING) {
        continue;
      }
      break;
    }

    if (nsync->futex[blk_idx][subblock_idx].load() !=
        BDS_FUTEX_STATE_DATA_READY) {
      BDSLOG(ERROR, "FrontendReadOneSmallFile err futex {} offset {}",
             nsync->futex[blk_idx][subblock_idx].load(), offset);
      return -1;
    }
    char* shm_offset =
        GetBdsCacheMemOffset(blk_idx, subblock_idx) + subblock_offset;
    memcpy((char*)buf + buf_offset, shm_offset, tmp_len);
    // BDSLOG(INFO, "FrontendReadOneSmallFile read offset {}, blk_idx {},
    // subblock_idx {} subblock_offset {}",
    //       offset, blk_idx, subblock_idx, subblock_offset);

    offset += tmp_len;
    length -= tmp_len;
    buf_offset += tmp_len;
  }

  return size;
}

int64_t BdsCacheFileRead(uint64_t file_id, char* buf, int64_t size,
                         int64_t read_offset, int64_t file_size, uint64_t fh) {
  int64_t res;
  CachedFileInfo* file_info = QueryAndAddFileInfo(file_id);

  // BDSLOG(INFO, "BdsCacheFileRead: file_id {}, fh {}, read_offset {},
  // file_size {}", file_id, fh, read_offset, file_size);

  int64_t length = size;
  int64_t tmp_len;
  int64_t buf_offset = 0;
  if (file_size < BDS_CACHE_BLOCK_SIZE) {  // handle small file
    int64_t end_offset =
        MIN((int64_t)file_info->subblocks.size() * BDS_CACHE_SUBBLOCK_SIZE,
            file_size);
    while (length > 0) {
      if (read_offset < end_offset) {
        tmp_len = MIN(length, end_offset - read_offset);
        res =
            HitReadSmallFile(file_info, read_offset, tmp_len, buf + buf_offset);
        if (res < 0) {
          BDSLOG(ERROR,
                 "BdsCacheFileRead small file: part 2 failed for (path: {}, "
                 "fh:{}, res: {}\n",
                 file_info->abs_path.c_str(), fh, res);
          return -1;
        }
      } else {
        tmp_len = length;
        res = cfs_pread(reinterpret_cast<cfs_file*>(fh), buf + buf_offset,
                        tmp_len, read_offset);
        if (res < 0) {
          BDSLOG(ERROR,
                 "BdsCacheFileRead small file: Miss read failed for file id: "
                 "{}, fh:{}, res: {}",
                 file_id, fh, res);
          return -1;
        }
      }

      read_offset += tmp_len;
      length -= tmp_len;
      buf_offset += tmp_len;
      if (read_offset >= file_size) {
        break;
      }
    }
    return buf_offset;
  }

  res = BackendRead(file_info, buf, size, read_offset, fh);
  if (res < 0) {
    BDSLOG(ERROR,
           "BdsCacheFileRead: part 1 failed for path={}, buf={}, tmp_len={}, "
           "off={}",
           file_info->abs_path.c_str(), fmt::ptr(buf), tmp_len, read_offset);
    return -1;
  }

  return res;
}

void OpenPrefetchFd(CachedFileInfo* file_info, const std::string& abs_path) {
  std::unique_lock<std::mutex> lk(file_info->prefetch_fh_mtx);
  if (file_info->prefetch_fh == 0) {
    file_info->prefetch_fh = OpenFileForPrefetch(abs_path);
    BDSLOG(INFO, "OpenPrefetchFd prefetch success fh {}, path {}",
           file_info->prefetch_fh, abs_path.c_str());
  }
}

// frontend: on open a readonly file
void BdsCacheReadOnlyFileOpen(uint64_t file_id, std::string abs_path,
                              int64_t file_size, uint64_t fh) {
  CachedFileInfo* file_info = QueryAndAddFileInfo(file_id);
  file_info->abs_path = abs_path;
  file_info->file_size = file_size;
  file_info->open_times.fetch_add(1);

  OpenPrefetchFd(file_info, abs_path);
  BDSLOG(INFO,
         "BdsCacheReadOnlyFileOpen path {}, file_id {}, file_size {}, fh {}, "
         "open_times {}",
         abs_path.c_str(), file_id, file_size, fh,
         file_info->open_times.load());
}

// frontend: on close a readonly file
void BdsCacheReadOnlyFileClose(uint64_t file_id, uint64_t fh) {
  CachedFileInfo* file_info = QueryFileInfo(file_id);
  if (file_info == nullptr) {
    return;
  }

  // first, close prefetch fh
  file_info->open_times.fetch_sub(1);
  file_info->prefetch_fh_mtx.lock();
  if (file_info->open_times.load() == 0) {
    for (auto& th : file_info->prefetch_threads) {
      th.join();
    }
    file_info->prefetch_threads.clear();

    if (file_info->prefetch_fh > 0) {
      cfs_close(reinterpret_cast<cfs_file_handle>(file_info->prefetch_fh));
      file_info->prefetch_fh = 0;
    }
    BDSLOG(INFO,
           "BdsCacheReadOnlyFileClose: closed prefetch_fh and prefetch threads "
           "for file {}",
           file_info->abs_path);
  }
  file_info->prefetch_fh_mtx.unlock();

  // second: release subblocks
  if (file_info->open_times.load() == 0 && file_info->subblocks.size() > 0) {
    struct timespec timeout;
    timeout.tv_sec = 2;
    timeout.tv_nsec = 0;
    int* futex_addr = (int*)(&file_info->small_file_data_ready);
    while (true) {
      // if interrupted system call and futex value is 0, continue to FUTEX_WAIT
      syscall(SYS_futex, futex_addr, FUTEX_WAIT, 0, &timeout, NULL, 0);
      if (file_info->small_file_data_ready.load() == 0) {
        BDSLOG(ERROR,
               "BdsCacheReadOnlyFileClose: Closed file file_id {} subblocks {}",
               file_id, file_info->subblocks.size());
        continue;
      }
      break;
    }
    if (file_info->small_file_data_ready.load() == 1) {
      BdsCacheReleaseSubblocks(file_info->subblocks);
    }
  }

  BDSLOG(
      INFO,
      "BdsCacheReadOnlyFileClose: Closed file file_id {}, fh {} open_times {}",
      file_id, fh, file_info->open_times.load());
}
