#pragma once

#define FUSE_USE_VERSION 35
#include <libfuse3/fuse.h>

int bds_getattr(const char* path, struct stat* stat_buf,
                struct fuse_file_info* fi);

int bds_readlink(const char* path, char* link, size_t size);

int bds_mknod(const char* path, mode_t mode, dev_t dev);

int bds_mkdir(const char* path, mode_t mode);

int bds_unlink(const char* path);

int bds_rmdir(const char* path);

int bds_symlink(const char* path, const char* link);

int bds_rename(const char* path, const char* new_path, unsigned int flags);

int bds_link(const char* path, const char* link);

int bds_chmod(const char* path, mode_t mode, struct fuse_file_info* fi);

int bds_chown(const char* path, uid_t uid, gid_t gid,
              struct fuse_file_info* fi);

int bds_truncate(const char* path, off_t off, struct fuse_file_info* fi);

int bds_open(const char* path, struct fuse_file_info* fi);

int bds_read(const char* path, char* buf, size_t size, off_t off,
             struct fuse_file_info* fi);

int bds_write(const char* path, const char* buf, size_t size, off_t off,
              struct fuse_file_info* fi);

int bds_write_buf(const char* path, struct fuse_bufvec* buf, off_t off,
                  struct fuse_file_info* fi);

int bds_statfs(const char* path, struct statvfs* stat_buf);

int bds_flush(const char* path, struct fuse_file_info* fi);

int bds_release(const char* path, struct fuse_file_info* fi);

int bds_fsync(const char* path, int datasync, struct fuse_file_info* fi);

int bds_setxattr(const char* path, const char* name, const char* value,
                 size_t size, int flags);

int bds_getxattr(const char* path, const char* name, char* value, size_t size);

int bds_listxattr(const char* path, char* list, size_t size);

int bds_removexattr(const char* path, const char* name);

int bds_opendir(const char* path, struct fuse_file_info* fi);

int bds_readdir(const char* path, void* buf, fuse_fill_dir_t filler, off_t off,
                struct fuse_file_info* fi,
                enum fuse_readdir_flags readdir_flags);

int bds_releasedir(const char* path, struct fuse_file_info* fi);

int bds_fsyncdir(const char* path, int datasync, struct fuse_file_info* fi);

void* bds_init(struct fuse_conn_info* conn, struct fuse_config* cfg);

void bds_destroy(void* userdata);

int bds_access(const char* path, int mask);

int bds_create(const char* path, mode_t mode, struct fuse_file_info* fi);

int bds_utimens(const char* path, const struct timespec tv[2],
                struct fuse_file_info* fi);

int bds_bmap(const char* path, size_t blocksize, uint64_t* idx);

int bds_ioctl(const char* path, unsigned int cmd, void* arg,
              struct fuse_file_info* fi, unsigned int flags, void* data);

int bds_poll(const char* path, struct fuse_file_info* fi,
             struct fuse_pollhandle* ph, unsigned* reventsp);

int bds_fallocate(const char* path, int mode, off_t off, off_t len,
                  struct fuse_file_info* fi);

ssize_t bds_copy_file_range(const char* path_in, struct fuse_file_info* fi_in,
                            off_t offset_in, const char* path_out,
                            struct fuse_file_info* fi_out, off_t offset_out,
                            size_t size, int flags);

off_t bds_lseek(const char* path, off_t off, int whence,
                struct fuse_file_info* fuse_file_info);
