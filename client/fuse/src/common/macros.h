#pragma once

#include <new>

#ifndef BDS_LIKELY
#define BDS_LIKELY(x) (__builtin_expect(!!(x), 1))
#endif

#ifndef BDS_UNLIKELY
#define BDS_UNLIKELY(x) (__builtin_expect(!!(x), 0))
#endif

#define BDS_PACKED __attribute__((packed))

#ifdef __cpp_lib_hardware_interference_size
constexpr size_t kCacheLineSize = std::hardware_destructive_interference_size;
#else
// 64 bytes on x86-64
constexpr size_t kCacheLineSize = 64;
#endif

#define BDS_CACHELINE_ALIGNED alignas(kCacheLineSize)
