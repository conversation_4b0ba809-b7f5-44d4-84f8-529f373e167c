#include "common/string_util.h"

#include <absl/strings/escaping.h>

#include <algorithm>

namespace bds {
namespace fuse {

std::string StringUtil::StrJoinCStr(const char** paths, uint32_t num,
                                    const std::string& delim) {
  std::string ret;
  if (paths == nullptr || num == 0) {
    return ret;
  }
  for (uint32_t i = 0; i < num; i++) {
    if (paths[i] == nullptr) {
      StrAppend(&ret, "<NULL>", delim);
    } else {
      StringUtil::StrAppend(&ret, paths[i], delim);
    }
  }
  auto old_len = ret.size();
  // remove the trailing 'delim'
  ret.resize(old_len - delim.size());
  return ret;
}

std::string StringUtil::ToHexString(const std::string_view& str, bool upper) {
  // By default absl::BytesToHexString returns a string in lower case.
  std::string ret = absl::BytesToHexString(str);
  if (upper) {
    std::transform(ret.begin(), ret.end(), ret.begin(), ::toupper);
  }
  return ret;
}

std::string StringUtil::Base64Encode(const std::string_view& str) {
  return absl::Base64Escape(str);
}

bool StringUtil::StartsWith(const std::string_view& src,
                            const std::string_view& prefix) {
  auto len = prefix.size();
  if (src.size() < len) {
    return false;
  }
  return src.substr(0, len) == prefix;
}

}  // namespace fuse
}  // namespace bds
