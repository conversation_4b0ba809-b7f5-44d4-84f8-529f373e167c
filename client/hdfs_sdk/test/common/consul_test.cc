
#include <glog/logging.h>
#include <gtest/gtest.h>
#include <stub/stub.h>

#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>

#include "client/hdfs.h"
#include "common/Logger.h"
#include "common/SessionConfig.h"
#include "common/XmlConfig.h"
#include "server/ServiceDiscovery.h"

namespace Hdfs {
namespace Internal {

static const char* kConsulEnvKey = "CPP_HDFS_NN_CONSUL_NAME";
static const char* kConfPath = "CPP_HDFS_CONF";

class ConsulTest : public testing::Test {
   protected:
    void SetUp() override {}
    void TearDown() override {}
};

struct ConsulTestObject {
    void TestEnv(const std::string& env_val_str,
                 const std::string& test_namenode, bool expect) {
        ASSERT_TRUE(setenv(kConfPath, "/tmp/fuck_not_exist", 1) == 0);
        ASSERT_TRUE(setenv(kConsulEnvKey, env_val_str.data(), 1) == 0);
        const char* test_val = std::getenv(kConsulEnvKey);
        ASSERT_TRUE(test_val != nullptr);
        ASSERT_EQ(std::string(test_val), env_val_str);

        auto bld = hdfsNewBuilder();
        hdfsBuilderSetNameNode(bld, test_namenode.data());
        auto fs = hdfsBuilderConnect(bld);
        ASSERT_EQ(fs != nullptr, expect);
        hdfsDisconnect(fs);
        hdfsFreeBuilder(bld);
    }

    void TestBuilder(
        const std::unordered_map<std::string, std::string>& keyVals,
        const std::string& test_namenode, bool expect) {
        ASSERT_TRUE(setenv(kConfPath, "/tmp/fuck_not_exist", 1) == 0);
        auto bld = hdfsNewBuilder();
        for (const auto& [key, val] : keyVals) {
            auto ret = hdfsBuilderConfSetStr(bld, key.data(), val.data());
            ASSERT_EQ(ret, 0);
        }
        hdfsBuilderSetNameNode(bld, test_namenode.data());
        auto fs = hdfsBuilderConnect(bld);
        ASSERT_EQ(fs != nullptr, expect);
        hdfsDisconnect(fs);
        hdfsFreeBuilder(bld);
    }
};

TEST_F(ConsulTest, Basic) {
    auto seeker = std::make_unique<ConsulTestObject>();

    // default is match mode
    seeker->TestEnv("", "testFS", false);
    seeker->TestEnv("", "noFS", false);
    seeker->TestEnv("", "westeros", true);
    seeker->TestBuilder({}, "noFS", false);
    seeker->TestBuilder({}, "westeros", true);
    seeker->TestBuilder(
        {{"dfs.client.nnproxy.consul.enablemode", "disable"},
         {"dfs.client.nnproxy.consul.name", "inf.hdfs.gonnproxy:boe"}},
        "testFS", false);

    // global mode but use invalid consul
    seeker->TestEnv("non_consul", "testFS", false);
    seeker->TestEnv("non_consul", "westeros", false);
    seeker->TestEnv("inf.hdfs.gonnproxy:nodc", "westeros", false);
    seeker->TestBuilder({{"dfs.client.nnproxy.consul.enablemode", "global"},
                         {"dfs.client.nnproxy.consul.name", "non_consul"}},
                        "testFS", false);

    // global mode and use valid consul
    seeker->TestEnv("inf.hdfs.gonnproxy", "testFS", true);
    seeker->TestEnv("inf.hdfs.gonnproxy", "westeros", true);
    seeker->TestEnv("inf.hdfs.gonnproxy:boe", "testFS", true);
    seeker->TestBuilder(
        {{"dfs.client.nnproxy.consul.enablemode", "global"},
         {"dfs.client.nnproxy.consul.name", "inf.hdfs.gonnproxy:boe"}},
        "testFS", true);

    // match mode: cFS is not set
    seeker->TestEnv("aFS=inf.hdfs.gonnproxy/bFs=inf.hdfs.gonnproxy", "cFS",
                    false);
    seeker->TestEnv("aFS=inf.hdfs.gonnproxy:boe/bFS=inf.hdfs.gonnproxy:boe",
                    "cFS", false);
    seeker->TestEnv(
        "aFS=inf.hdfs.gonnproxy/bFs=inf.hdfs.gonnproxy/inf.hdfs.gonnproxy",
        "cFS", false);
    seeker->TestBuilder(
        {{"dfs.client.nnproxy.consul.enablemode", "match"},
         {"dfs.client.nnproxy.consul.name.aFS", "inf.hdfs.gonnproxy"},
         {"dfs.client.nnproxy.consul.name.bFS", "inf.hdfs.gonnproxy"}},
        "cFS", false);

    // match mode: aFS and bFS are set valid
    seeker->TestEnv("aFS=inf.hdfs.gonnproxy:boe/bFS=inf.hdfs.gonnproxy:boe",
                    "aFS", true);
    seeker->TestEnv("aFS=inf.hdfs.gonnproxy:boe/bFS=inf.hdfs.gonnproxy:boe",
                    "bFS", true);
    seeker->TestEnv(
        "aFS=inf.hdfs.gonnproxy/bFS=inf.hdfs.gonnproxy/inf.hdfs.gonnproxy",
        "aFS", true);
    seeker->TestEnv(
        "aFS=inf.hdfs.gonnproxy/bFS=inf.hdfs.gonnproxy/inf.hdfs.gonnproxy",
        "bFS", true);
    seeker->TestBuilder(
        {{"dfs.client.nnproxy.consul.enablemode", "match"},
         {"dfs.client.nnproxy.consul.name.aFS", "inf.hdfs.gonnproxy"},
         {"dfs.client.nnproxy.consul.name.bFS", "inf.hdfs.gonnproxy"}},
        "aFS", true);

    // match mode: aFS and bFS are set invalid
    seeker->TestEnv("aFS=non_consul/bFS=non_consul", "aFS", false);
    seeker->TestEnv("aFS=non_consul/bFS=non_consul", "bFS", false);
}

bool Stub_ConsulServiceInitFailed() { return true; }
using fptr = bool (*)();

TEST_F(ConsulTest, ConsulService) {
    Stub stub;
    fptr ConsulService_InitFailed =
        (fptr)(&cpputil::consul::ServiceDiscovery::init_failed);
    stub.set(ConsulService_InitFailed, Stub_ConsulServiceInitFailed);
    ConsulService cs;
    std::vector<std::string> vdc;
    cs.GetConsulAddrs("", 0, vdc);
}

}  // namespace Internal
}  // namespace Hdfs

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}