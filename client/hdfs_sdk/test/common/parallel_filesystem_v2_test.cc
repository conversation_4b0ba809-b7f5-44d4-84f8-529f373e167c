
#include <folly/hash/Checksum.h>
#include <unistd.h>

#include <cstdlib>
#include <memory>
#include <random>

#include "client/Hdfs.cpp"
#include "client/filesystem/OutputStream.h"
#include "client/filesystem/ParallelInputStreamV2.h"
#include "client/filesystem/ParallelOutputStreamV2.h"
#include "client/hdfs.h"
#include "common/json.h"
#include "common/ut_common.h"
#include "gtest/gtest.h"

using namespace Hdfs::Internal;
using namespace testing;

namespace Hdfs {
// OutPutStreamOpenOption option = default_output_stream_option;
// option.parallel_io_num_ = 0;
// option.partial_file_size_ = 0;
// option.inter_type_ = IFT_NORMAL;
// option.type_ = FileType::FT_PARALLEL;
// option.stripe_unit_size_ = ONE_MB;
// option.stripe_unit_count_ = 5;

namespace Internal {
uint64_t chunk_size = 1024 * 1024;
size_t logic_file_size = 513 * chunk_size;
std::string write_data_buf;

class TestParallelFileSystem : public ::testing::Test {
   public:
    void SetUp() override {
        auto ftenv = HdfsFunctionTestEnvironment::Instance();
        ASSERT_TRUE(ftenv != nullptr);
        test_dir = ftenv->GetHdfsTestDir();
        file_path = test_dir + "test_read_write";
        ASSERT_TRUE(ftenv->EnsureHdfsTestDirReady(test_dir));
    }

    void TearDown() override {}

   protected:
    std::string test_dir;
    std::string file_path;
};

int64_t ONE_MB = 1024 * 1024;
struct MockOption {
    int64_t offset0_;
    int64_t offset1_;
    int64_t offset2_;
    int64_t offset3_;
    int64_t offset4_;
};

void CheckPreadData(hdfsFS fs, hdfsFile file, const std::string& raw_buf,
                    int64_t start_offset, int32_t length) {
    std::string pread_buffer_str;
    pread_buffer_str.resize(10 * 1024 * 1024);
    auto pread_buffer = static_cast<char*>(pread_buffer_str.data());
    int read_size = hdfsPread(fs, file, start_offset, pread_buffer, length);
    int32_t can_read_size =
        std::min(raw_buf.size(), static_cast<size_t>(start_offset + length)) -
        start_offset;
    EXPECT_EQ(read_size, can_read_size);

    char* compare_buf = const_cast<char*>(raw_buf.c_str()) + start_offset;
    EXPECT_EQ(0, memcmp(pread_buffer, compare_buf, read_size));
}

void CheckReadData(hdfsFS fs, hdfsFile file, int64_t target_read_length) {
    std::string read_buffer_str;
    read_buffer_str.resize(10 * 1024 * 1024);
    auto read_buffer = static_cast<char*>(read_buffer_str.data());

    int64_t start_offset = hdfsTell(fs, file);

    int64_t remain_size = target_read_length;
    int64_t read_size = 0;
    while (remain_size > 0) {
        int64_t once_read_size =
            hdfsRead(fs, file, read_buffer + read_size, remain_size);

        if (once_read_size <= 0) {
            break;
        }

        read_size += once_read_size;
        remain_size -= once_read_size;
    }

    int64_t current_offset = hdfsTell(fs, file);
    EXPECT_EQ(current_offset, start_offset + read_size);

    char* compare_buf =
        const_cast<char*>(write_data_buf.c_str()) + start_offset;
    EXPECT_EQ(0, memcmp(read_buffer, compare_buf, read_size));
}

TEST_F(TestParallelFileSystem, test_read_write) {
    int32_t ONE_MB = 1024 * 1024;
    write_data_buf.resize(logic_file_size);
    std::random_device rd;
    std::mt19937 g(rd());
    for (size_t i = 0; i < logic_file_size; ++i) {
        write_data_buf[i] = (g() % 128);
    }
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, file_path.c_str());
    if (info) {
        std::cout << "file exist" << std::endl;

        hdfsFreeFileInfo(info, 1);
        info = nullptr;
        hdfsDelete(fs, file_path.c_str(), true);
    }

    hdfsDelete(fs, file_path.c_str(), true);

    CreateFileOption option = GetDefaultCreateFileOption();
    uint32_t buffer_size = 128 * 1024;
    uint32_t block_size = 10 * 1024 * 1024;
    option.format = FileStorageFormat::FT_PARALLEL;
    option.stripe_unit_size = 1024 * 1024;
    option.stripe_unit_count = 5;
    auto hFile = hdfsOpenFileV3(fs, file_path.c_str(), O_WRONLY | O_CREAT,
                                buffer_size, 0, block_size, option);
    EXPECT_TRUE(hFile);

    int64_t remain_size = write_data_buf.size();
    const char* buffer1 = write_data_buf.c_str();

    int64_t write_size = 0;
    int sync_flag = 0;

    while (remain_size > 0) {
        int once_write_size =
            hdfsWrite(fs, hFile, buffer1 + write_size,
                      std::min(remain_size, int64_t(1024 * 256)));
        if (once_write_size < 0) {
            EXPECT_TRUE(false);
        }
        remain_size -= once_write_size;
        write_size += once_write_size;
        if (write_size % ONE_MB == 0) {
            ++sync_flag;
            if (sync_flag % 10 == 0) {
                hdfsHSyncAndUpdateLength(fs, hFile);
            } else {
                hdfsHSync(fs, hFile);
            }
        }
    }

    EXPECT_EQ(0, remain_size);

    hdfsFlush(fs, hFile);

    hdfs_io_context context;
    context.write_callback = [](hdfsStatus status, void* args) {};
    context.buffer = new char[10];
    context.args = new char;
    EXPECT_EQ(hdfsStatus::STATUS_UNSUPPORTED_OP,
              hdfsAsyncWrite(fs, hFile, file_path.c_str(), 10, context));
    EXPECT_EQ(hdfsStatus::STATUS_UNSUPPORTED_OP,
              hdfsAsyncFlush(fs, hFile, context));
    EXPECT_EQ(
        hdfsStatus::STATUS_UNSUPPORTED_OP,
        hdfsAsyncWriteAndFlush(fs, hFile, file_path.c_str(), 10, context));

    delete[] context.buffer;
    delete static_cast<char*>(context.args);

    EXPECT_FALSE(nullptr == hFile->getOutputStream());

    EXPECT_FALSE(hFile->getOutputStream()->isClosed());

    hdfsCloseFile(fs, hFile);

    info = hdfsGetPathInfo(fs, file_path.c_str());
    EXPECT_TRUE(info);
    EXPECT_TRUE(info->mKind == kObjectKindFile);

    hdfsFreeFileInfo(info, 1);

    hFile = hdfsOpenFile(fs, file_path.c_str(), O_RDONLY, 0, 0, 0);
    // 1. read
    {
        char buffer[1024 * 1024] = {0};
        int read_size = 0;
        size_t offset = 0;
        do {
            read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
            ASSERT_GE(read_size, 0)
                << "Failed to hdfsRead due to: " << hdfsGetLastError();
            if (read_size == 0) {
                break;
            }
            ASSERT_LE(offset + read_size, write_data_buf.size());
            EXPECT_EQ(
                0, memcmp(buffer, write_data_buf.c_str() + offset, read_size));
            offset += read_size;
        } while (read_size > 0);
        EXPECT_EQ(write_data_buf.size(), offset) << "Failed to read all data";
    }
    std::cout << "0" << std::endl;
    // 2. pread
    {
        CheckPreadData(fs, hFile, write_data_buf, ONE_MB - 1024, 2048);
        CheckPreadData(fs, hFile, write_data_buf, 0, 1022);
        CheckPreadData(fs, hFile, write_data_buf, 0, 4 * ONE_MB);
        CheckPreadData(fs, hFile, write_data_buf, 12, 4 * ONE_MB);
        CheckPreadData(fs, hFile, write_data_buf, 512 * ONE_MB, 1 * ONE_MB);
    }

    hdfsCloseFile(fs, hFile);
    hFile = hdfsOpenFile(fs, file_path.c_str(), O_RDONLY, 0, 0, 0);
    std::cout << "1" << std::endl;
    // 3. seek+read
    {
        // 3.1 read
        int64_t desire_offset = 0;
        CheckReadData(fs, hFile, ONE_MB);
        desire_offset += ONE_MB;
        int64_t offset = hdfsTell(fs, hFile);
        EXPECT_EQ(offset, desire_offset);
        std::cout << "2" << std::endl;
        // 3.2 seek
        hdfsSeek(fs, hFile, ONE_MB * 2 + 1024);
        desire_offset = ONE_MB * 2 + 1024;

        offset = hdfsTell(fs, hFile);
        EXPECT_EQ(offset, desire_offset);

        hdfsSeek(fs, hFile, ONE_MB * 3 + 1022);
        desire_offset = ONE_MB * 3 + 1022;

        offset = hdfsTell(fs, hFile);
        EXPECT_EQ(offset, desire_offset);

        // 3.3 read
        CheckReadData(fs, hFile, ONE_MB + 1023);
        desire_offset += ONE_MB + 1023;
        offset = hdfsTell(fs, hFile);
        EXPECT_EQ(offset, desire_offset);
    }

    EXPECT_FALSE(nullptr == hFile->getInputStream());

    EXPECT_FALSE(hFile->getInputStream()->isClosed());

    EXPECT_FALSE(hFile->getInputStream()->isBytecoolFile());

    EXPECT_FALSE(hFile->getInputStream()->isThirdPartyCloudeFile());

    // append file
    {
        // CreateFileOption option = GetDefaultCreateFileOption();
        // uint32_t buffer_size = 128 * 1024;
        // uint32_t block_size = 10 * 1024 * 1024;
        // option.type = FileType::FT_PARALLEL;
        // option.stripe_unit_size = 1024 * 1024;
        // option.stripe_unit_count = 5;
        // auto hFile = hdfsOpenFileV3(fs, file_path.c_str(), O_APPEND |
        // O_WRONLY,
        //                             buffer_size, 0, block_size, option);

        // EXPECT_TRUE(hFile);
        // hdfsWrite(fs, hFile, "hello", 5);
        // hdfsCloseFile(fs, hFile);

        // info = hdfsGetPathInfo(fs, file_path.c_str());
        // EXPECT_TRUE(info);
        // EXPECT_TRUE(info->mKind == kObjectKindFile);
        // EXPECT_EQ(info->mSize, 5 + write_data_buf.size());
        // hdfsFreeFileInfo(info, 1);

        // info = hdfsGetPathInfoV2(fs, file_path.c_str());
        // EXPECT_TRUE(info);
        // EXPECT_TRUE(info->mKind == kObjectKindParallelFile);
        // hdfsFreeFileInfo(info, 1);

        // int list_num = 0;
        // hdfsFileInfo* info_vec =
        //     hdfsListDirectoryV2(fs, test_dir.c_str(), &list_num);
        // int parallel_count = 0;
        // for (int i = 0; i < list_num; ++i) {
        //     if (info_vec[i].mKind == kObjectKindParallelFile) {
        //         ++parallel_count;
        //     }
        // }
        // EXPECT_TRUE(parallel_count > 0);
        // hdfsFreeFileInfo(info_vec, list_num);
    }

    // over write file
    {
        CreateFileOption option = GetDefaultCreateFileOption();
        uint32_t buffer_size = 128 * 1024;
        uint32_t block_size = 10 * 1024 * 1024;
        option.format = FileStorageFormat::FT_PARALLEL;
        option.stripe_unit_size = 1024 * 1024;
        option.stripe_unit_count = 5;
        auto hFile = hdfsOpenFileV3(fs, file_path.c_str(), O_WRONLY | O_TRUNC,
                                    buffer_size, 0, block_size, option);
        EXPECT_TRUE(hFile);
        int64_t remain_size = write_data_buf.size();
        const char* buffer1 = write_data_buf.c_str();

        int64_t write_size = 0;
        int sync_flag = 0;

        while (remain_size > 0) {
            int once_write_size =
                hdfsWrite(fs, hFile, buffer1 + write_size,
                          std::min(remain_size, int64_t(1024 * 256)));
            if (once_write_size < 0) {
                EXPECT_TRUE(false);
            }
            remain_size -= once_write_size;
            write_size += once_write_size;
            if (write_size % ONE_MB == 0) {
                ++sync_flag;
                if (sync_flag % 10 == 0) {
                    hdfsHSyncAndUpdateLength(fs, hFile);
                } else {
                    hdfsHSync(fs, hFile);
                }
            }
        }
        hdfsCloseFile(fs, hFile);

        info = hdfsGetPathInfo(fs, file_path.c_str());
        EXPECT_TRUE(info);
        EXPECT_TRUE(info->mKind == kObjectKindFile);
        EXPECT_TRUE(info->mSize == write_data_buf.size());
        hdfsFreeFileInfo(info, 1);
    }

    hdfsCloseFile(fs, hFile);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST_F(TestParallelFileSystem, test_readahead) {
    Xoroshiro128pp rng;
    uint32_t localChecksum;
    bool testFilePrepared =
        HdfsFunctionTestEnvironment::Instance()->PrepareStrippedFile(
            file_path, logic_file_size, 1024 * 1024, 5,
            [&](folly::IOBuf* buf) { rng.fill(buf); }, &localChecksum);
    ASSERT_TRUE(testFilePrepared);

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.input.performance.switch", "true");
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.output.performance.switch", "true");
    hdfsFS fs = hdfsBuilderConnect(builder);

    CreateFileOption option = GetDefaultCreateFileOption();

    // seek, tell, read
    {
        auto hFile =
            hdfsOpenFileV3(fs, file_path.c_str(), O_RDONLY, 0, 0, 0, option);
        EXPECT_TRUE(hFile);
        char buffer[1024 * 1024] = {0};

        auto tell_fun = [&]() { return hdfsTell(fs, hFile); };
        auto seek_fun = [&](int64_t offset) {
            return hdfsSeek(fs, hFile, offset);
        };
        EXPECT_EQ(0, tell_fun());
        seek_fun(1024 * 1024);
        EXPECT_EQ(1024 * 1024, tell_fun());

        seek_fun(0);
        EXPECT_EQ(0, tell_fun());

        seek_fun(1024 * 1024 * 200);
        EXPECT_EQ(1024 * 1024 * 200, tell_fun());

        auto size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read data size:" << size << std::endl;
        EXPECT_EQ(1024 * 1024 * 200 + size, tell_fun());
        hdfsCloseFile(fs, hFile);
    }

    // open and close
    {
        auto hFile =
            hdfsOpenFileV3(fs, file_path.c_str(), O_RDONLY, 0, 0, 0, option);
        EXPECT_TRUE(hFile);

        sleep(1);

        hdfsCloseFile(fs, hFile);
    }

    // read
    {
        auto hFile =
            hdfsOpenFileV3(fs, file_path.c_str(), O_RDONLY, 0, 0, 0, option);
        EXPECT_TRUE(hFile);
        uint32_t remoteChecksum = ~0U;
        char buffer[1024 * 1024] = {0};
        int read_size = 0;
        do {
            read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
            if (read_size > 0) {
                remoteChecksum = folly::crc32c((const uint8_t*)buffer,
                                               read_size, remoteChecksum);
            }
        } while (read_size > 0);
        EXPECT_EQ(localChecksum, remoteChecksum);
        hdfsCloseFile(fs, hFile);
    }

    // seek + read
    {
        auto hFile =
            hdfsOpenFileV3(fs, file_path.c_str(), O_RDONLY, 0, 0, 0, option);
        EXPECT_TRUE(hFile);
        const int kReadSize = 1024 * 1024;
        char buffer[kReadSize + 1] = {0};

        auto seek_and_read_fully = [&](int64_t pos) {
            hdfsSeek(fs, hFile, pos);
            int target_read_size = kReadSize;
            int read_size = 0;
            while (target_read_size > 0) {
                auto once_read_size =
                    hdfsRead(fs, hFile, buffer + read_size, target_read_size);
                ASSERT_GE(once_read_size, 0);
                if (once_read_size == 0) {
                    break;
                }
                target_read_size -= once_read_size;
                read_size += once_read_size;
            }
            // Align alloc iobuf and fill it with random data.
            auto iobuf = folly::IOBuf::create(kReadSize + 16);
            iobuf->append(kReadSize + 8);
            rng.seek(pos / 8 * 8);
            rng.fill(iobuf.get());
            const char* compare_buf = (const char*)iobuf->data() + pos % 8;
            EXPECT_EQ(0, memcmp(buffer, compare_buf, kReadSize));
            std::cout << "file:" << file_path << ",seek pos:" << pos
                      << std::endl;
        };
        seek_and_read_fully(0);
        seek_and_read_fully(kReadSize + 1024);
        seek_and_read_fully(kReadSize * 3);
        seek_and_read_fully(kReadSize * 4 + 1025);

        hdfsCloseFile(fs, hFile);
    }

    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST_F(TestParallelFileSystem, test_normal_read) {
    Xoroshiro128pp rng;
    uint32_t localChecksum;
    bool testFilePrepared =
        HdfsFunctionTestEnvironment::Instance()->PrepareFile(
            file_path, 10 * 1024 * 1024,
            [&](folly::IOBuf* buf) { rng.fill(buf); }, &localChecksum);
    ASSERT_TRUE(testFilePrepared);

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    auto hFile = hdfsOpenFile(fs, file_path.c_str(), O_RDONLY, 0, 0, 0);
    ASSERT_TRUE(hFile) << fmt::format("Failed to open file {} due to: {}",
                                      file_path, hdfsGetLastError());
    // 1. read
    {
        char buffer[1024 * 1024] = {0};
        int read_size = 0;
        read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read size 1 :" << read_size << std::endl;
        read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read size 2 :" << read_size << std::endl;
        read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read size 3 :" << read_size << std::endl;
    }
    hdfsCloseFile(fs, hFile);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST_F(TestParallelFileSystem, test_normal_read_with_async_close) {
    Xoroshiro128pp rng;
    uint32_t localChecksum;
    bool testFilePrepared =
        HdfsFunctionTestEnvironment::Instance()->PrepareFile(
            file_path, 10 * 1024 * 1024,
            [&](folly::IOBuf* buf) { rng.fill(buf); }, &localChecksum);
    ASSERT_TRUE(testFilePrepared);

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsBuilderConfSetStr(builder, "dfs.client.input.close.async.switch",
                          "true");
    hdfsFS fs = hdfsBuilderConnect(builder);
    auto hFile = hdfsOpenFile(fs, file_path.c_str(), O_RDONLY, 0, 0, 0);
    ASSERT_TRUE(hFile) << fmt::format("Failed to open file {} due to: {}",
                                      file_path, hdfsGetLastError());
    // 1. read
    {
        char buffer[1024 * 1024] = {0};
        int read_size = 0;
        read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read size 1 :" << read_size << std::endl;
        read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read size 2 :" << read_size << std::endl;
        read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
        std::cout << "read size 3 :" << read_size << std::endl;
    }
    hdfsCloseFile(fs, hFile);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

}  // namespace Internal
}  // namespace Hdfs
