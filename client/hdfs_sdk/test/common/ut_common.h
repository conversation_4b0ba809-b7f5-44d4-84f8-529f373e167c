// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#include <fmt/chrono.h>
#include <fmt/format.h>
#include <folly/Random.h>
#include <folly/io/IOBuf.h>
#include <gtest/gtest.h>
#include <stdint.h>

#include <chrono>
#include <string>

#include "client/hdfs.h"

const std::string unittest_root_dir = "/hdfs_client_test/cpp/unittest/";

class HdfsFunctionTestEnvironment : public ::testing::Environment {
   public:
    HdfsFunctionTestEnvironment();

    ~HdfsFunctionTestEnvironment();

    void SetUp() override;

    void TearDown() override;

    hdfsFS GetTestHdfsFileSystem() const { return fs_; }

    // Get a unique temperary test directory for each test in each round.
    std::string GetHdfsTestDir();

    bool EnsureHdfsTestDirReady(const std::string& testdir);

    bool PrepareStrippedFile(const std::string& path, int64_t fileSize,
                             int64_t stripeSize, int64_t stripeCount,
                             std::function<void(folly::IOBuf*)> rndDataFiller,
                             uint32_t* checksum = nullptr);

    bool PrepareFile(const std::string& path, int64_t fileSize,
                     std::function<void(folly::IOBuf*)> rndDataFiller,
                     uint32_t* checksum = nullptr);

    bool FillRndData(const std::string& path, hdfsFile hfile, int64_t fileSize,
                     std::function<void(folly::IOBuf*)> rndDataFiller,
                     uint32_t* checksum = nullptr);

    static HdfsFunctionTestEnvironment* Instance() { return instance_; }

    constexpr static const char* kHdfsTestRootPrefix =
        "/hdfs_client_test/cpp/unittest/";

   private:
    static HdfsFunctionTestEnvironment* instance_;

    std::string hdfsTestRoot_;
    hdfsFS fs_ = nullptr;
};

/**
 * A fast random data generator based on xoroshiro128++ algorithm.
 */
class Xoroshiro128pp {
   public:
    /**
     * Create a random generator with two random seeds.
     */
    Xoroshiro128pp(uint64_t seed1, uint64_t seed2)
        : initial{seed1, seed2}, s{seed1, seed2} {}

    Xoroshiro128pp()
        : Xoroshiro128pp(folly::Random::secureRand64(),
                         folly::Random::secureRand64()) {}

    uint64_t next() {
        uint64_t s1 = s[0];
        uint64_t s0 = s[1];
        uint64_t result = s0 + s1;

        s1 ^= s0;
        s[0] = rotl(s0, 55) ^ s1 ^ (s1 << 14);
        s[1] = rotl(s1, 36);
        wordOffset++;
        return result;
    }

    void fill(folly::IOBuf* buf) {
        auto* data = buf->writableData();
        auto length = buf->length() / 8;
        for (size_t i = 0; i < length; i++) {
            *reinterpret_cast<uint64_t*>(data + 8 * i) = next();
        }
    }

    void reset() {
        s[0] = initial[0];
        s[1] = initial[1];
    }

    void dropWord(size_t todrop) {
        for (size_t i = 0; i < todrop; i++) {
            next();
        }
    }

    void seek(size_t pos) {
        if (pos % 8 != 0) {
            throw std::invalid_argument("pos must align to 64bits.");
        }
        int64_t todrop = pos / 8 - wordOffset;
        if (todrop > 0) {
            dropWord(todrop);
        } else {
            reset();
            dropWord(pos / 8);
        }
    }

    static uint64_t rotl(uint64_t x, int k) {
        return (x << k) | (x >> (64 - k));
    }

   private:
    uint64_t initial[2];
    uint64_t s[2];
    size_t wordOffset = 0;
};
