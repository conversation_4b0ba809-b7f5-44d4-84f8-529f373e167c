#include <unistd.h>

#include <chrono>
#include <iostream>
#include <random>
#include <sstream>
#include <thread>

#include "client/hdfs.h"
#include "common/Logger.h"
#include "gtest/gtest.h"

using namespace testing;

std::string root_dir = "/hdfs_client_test/unittest2";

namespace Hdfs {
namespace Internal {

std::string generate_test_file() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::ostringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d-%H-%M-%S");

    auto seed = now.time_since_epoch().count();
    std::uniform_int_distribution<unsigned> u(0, 10000);
    std::default_random_engine e(seed);  // 生成无符号随机整数

    std::string path = root_dir + "/test_parallel_write.txt." + ss.str() + "." +
                       std::to_string(u(e));
    return path;
}

class TestParallelWrite : public ::testing::Test {
   public:
    virtual void SetUp() {}

    virtual void TearDown() {}
};

TEST(TestParallelWrite, test_create_open) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e;  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        data[i] = *std::to_string(u(e)).c_str();
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        for (int j = 0; j < 256; ++j) {
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
            EXPECT_TRUE(ret == length);
        }

        ret = hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
        EXPECT_TRUE(ret == 0);
    }
    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    std::cout << 1 << std::endl;
    // read with single input stream
    hFile = hdfsOpenFile(fs, path.c_str(), O_RDONLY, 0, 0, 0);
    std::cout << 2 << std::endl;
    EXPECT_TRUE(hFile);
    char read_buffer[data_len] = {0};
    tSize read_len = 0;
    while (read_len != data_len) {
        auto len =
            hdfsRead(fs, hFile, read_buffer + read_len, data_len - read_len);
        if (len < 0) {
            HDFSLOG(ERROR, "error {}", hdfsGetLastError());
        }
        EXPECT_TRUE(len > 0);
        read_len += len;
    }
    EXPECT_EQ(read_len, data_len);
    EXPECT_EQ(std::string(read_buffer, data_len), std::string(data, data_len));
    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    // read with parallel input stream
    hFile = hdfsOpenFileV3(fs, path.c_str(), O_RDONLY, 0, 0, 0, option);
    EXPECT_TRUE(hFile);
    memset(read_buffer, 0, data_len);
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        auto pos = hdfsFuseTell(fs, hFile, ctx);
        EXPECT_EQ(pos, i * 256 * 1024);
        for (int j = 0; j < 256; ++j) {
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            read_len = 0;

            while (read_len != length) {
                auto len =
                    hdfsFuseRead(fs, hFile, read_buffer + offset + read_len,
                                 length - read_len, ctx);
                EXPECT_TRUE(len > 0);
                read_len += len;
                auto pos = hdfsFuseTell(fs, hFile, ctx);
                EXPECT_EQ(pos, offset + read_len);
                EXPECT_EQ(std::string(read_buffer + offset, read_len),
                          std::string(data + offset, read_len));
            }
            EXPECT_EQ(read_len, length);
            EXPECT_EQ(std::string(read_buffer + offset, length),
                      std::string(data + offset, length));
        }
    }
    EXPECT_EQ(std::string(read_buffer, 256 * 1024 + 1),
              std::string(data, 256 * 1024 + 1));
    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_append_open_with_parallel_stream) {
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);

    std::string path = generate_test_file();
    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile =
        hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_APPEND, 0, 0, 0, option);
    EXPECT_TRUE(hFile == nullptr);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_write_to_finish_concate_file) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e;  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        data[i] = u(e);
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        for (int j = 0; j < 256; ++j) {
            hdfs_io_context ctx;
            ctx.channel_id = i + 1;
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
            EXPECT_TRUE(ret == length);
        }

        ret = hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
        EXPECT_TRUE(ret == 0);
    }

    // still try to write to file
    hdfs_io_context ctx;
    ctx.channel_id = 1;
    int64_t offset = 1024 * 1024;
    int64_t length = 1024;
    ret = hdfsFuseWrite(fs, hFile, data, length, ctx);
    EXPECT_TRUE(ret == -1);

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_append_to_closed_concate_file) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e;  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        data[i] = u(e);
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        for (int j = 0; j < 256; ++j) {
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
            EXPECT_TRUE(ret == length);
        }

        ret = hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
        EXPECT_TRUE(ret == 0);
    }
    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    // open with signle stream and write angine
    hFile = hdfsOpenFile(fs, path.c_str(), O_APPEND | O_WRONLY, 0, 0, 0);
    EXPECT_TRUE(hFile);

    int64_t offset = 1024 * 1024;
    int64_t length = 1024;
    ret = hdfsWrite(fs, hFile, data, length);
    EXPECT_TRUE(ret == length);

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_tell_for_write) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e;  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        data[i] = u(e);
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        for (int j = 0; j < 256; ++j) {
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
            EXPECT_TRUE(ret == length);
            auto pos = hdfsFuseTell(fs, hFile, ctx);
            EXPECT_EQ(pos, offset + length);
        }

        // wrong channel id
        ctx.channel_id = 5;
        auto pos = hdfsFuseTell(fs, hFile, ctx);
        EXPECT_EQ(pos, -1);

        ctx.channel_id = i + 1;
        ret = hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
        EXPECT_TRUE(ret == 0);
    }

    // already concat file no need to tell
    hdfs_io_context ctx;
    ctx.channel_id = 1;
    auto pos = hdfsFuseTell(fs, hFile, ctx);
    EXPECT_EQ(pos, -1);

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_tell_for_read) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e(1000);  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        int k = i % 10;
        data[i] = *std::to_string(u(e)).c_str();
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        for (int j = 0; j < 256; ++j) {
            hdfs_io_context ctx;
            ctx.channel_id = i + 1;
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
            EXPECT_TRUE(ret == length);
        }

        ret = hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
        EXPECT_TRUE(ret == 0);
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    // read with parallel stream
    char read_buffer[data_len] = {0};
    tSize read_len = 0;
    hFile = hdfsOpenFileV3(fs, path.c_str(), O_RDONLY, 0, 0, 0, option);
    EXPECT_TRUE(hFile);

    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;

        // seek to specified pos and read correct
        int read_pos = i * 256 * 1024 + 10 * 1024;
        ret = hdfsFuseSeek(fs, hFile, read_pos, ctx);
        EXPECT_TRUE(ret == 0);

        ret = hdfsFuseTell(fs, hFile, ctx);
        EXPECT_TRUE(ret == read_pos);

        // tell with wrong channel id
        ctx.channel_id = 0;
        ret = hdfsFuseTell(fs, hFile, ctx);
        EXPECT_TRUE(ret == -1);

        // tell with wrong channel id
        ctx.channel_id = 5;
        ret = hdfsFuseTell(fs, hFile, ctx);
        EXPECT_TRUE(ret == -1);
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_seek) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e(1000);  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        int k = i % 10;
        data[i] = *std::to_string(u(e)).c_str();
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = i + 1;
        for (int j = 0; j < 256; ++j) {
            hdfs_io_context ctx;
            ctx.channel_id = i + 1;
            int64_t offset = i * 256 * 1024 + j * 1024;
            int64_t length = 1024;
            ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
            EXPECT_TRUE(ret == length);
        }

        ret = hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
        EXPECT_TRUE(ret == 0);
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    // read with input stream
    hFile = hdfsOpenFile(fs, path.c_str(), O_RDONLY, 0, 0, 0);
    EXPECT_TRUE(hFile);
    char read_buffer[data_len] = {0};
    tSize read_len = 0;
    while (read_len != data_len) {
        auto len =
            hdfsRead(fs, hFile, read_buffer + read_len, data_len - read_len);
        EXPECT_TRUE(len > 0);
        read_len += len;
    }
    EXPECT_EQ(read_len, data_len);
    EXPECT_EQ(std::string(read_buffer, data_len), std::string(data, data_len));
    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    // read with parallel stream
    memset(read_buffer, 0, data_len);
    hFile = hdfsOpenFileV3(fs, path.c_str(), O_RDONLY, 0, 0, 0, option);
    EXPECT_TRUE(hFile);

    for (int i = 0; i < option.parallel_io_num; ++i) {
        hdfs_io_context ctx;
        ctx.channel_id = 5;
        int64_t read_pos = i * 256 * 1024 + 10 * 1024;

        // seek with wrong channel id
        ret = hdfsFuseSeek(fs, hFile, read_pos, ctx);
        EXPECT_TRUE(ret == -1);

        // seek with wrong channel_id
        ctx.channel_id = 0;
        ret = hdfsFuseSeek(fs, hFile, read_pos, ctx);
        EXPECT_TRUE(ret == -1);

        // seek with wrong pos
        ctx.channel_id = i + 1;
        read_pos = (i + 1) * 256 * 1024;
        ret = hdfsFuseSeek(fs, hFile, read_pos, ctx);
        EXPECT_TRUE(ret == -1);

        // seek to specified pos and read correct
        read_pos = i * 256 * 1024 + 10 * 1024;
        ret = hdfsFuseSeek(fs, hFile, read_pos, ctx);
        EXPECT_TRUE(ret == 0);

        ret = hdfsFuseTell(fs, hFile, ctx);
        EXPECT_TRUE(ret == read_pos);

        int64_t total_read_len = 0;
        for (int j = 10; j < 256; ++j) {
            int64_t offset = i * 256 * 1024 + j * 1024;
            ret = hdfsFuseSeek(fs, hFile, offset, ctx);
            EXPECT_TRUE(ret == 0);

            ret = hdfsFuseTell(fs, hFile, ctx);
            EXPECT_TRUE(ret == offset);

            int64_t length = 1024;
            read_len = 0;
            while (read_len != length) {
                ret = hdfsFuseTell(fs, hFile, ctx);
                EXPECT_TRUE(ret == (offset + read_len));
                auto len =
                    hdfsFuseRead(fs, hFile, read_buffer + offset + read_len,
                                 length - read_len, ctx);
                EXPECT_TRUE(len > 0);
                EXPECT_EQ(std::string(read_buffer + offset + read_len, len),
                          std::string(data + offset + read_len, len));
                read_len += len;
            }
            EXPECT_EQ(read_len, length);
            total_read_len += length;
        }

        int64_t read_end = (i + 1) * 256 * 1024;
        EXPECT_EQ(total_read_len, read_end - read_pos);
        EXPECT_EQ(std::string(read_buffer + read_pos, 10),
                  std::string(data + read_pos, 10));
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_parallel_write) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e(1000);  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        int k = i % 10;
        data[i] = *std::to_string(u(e)).c_str();
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    std::vector<std::thread> threads;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        threads.push_back(std::thread([&, i]() {
            hdfs_io_context ctx;
            ctx.channel_id = i + 1;
            for (int j = 0; j < 256; ++j) {
                int64_t offset = i * 256 * 1024 + j * 1024;
                int64_t length = 1024;
                ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
                EXPECT_TRUE(ret == length);
            }
            ret =
                hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
            EXPECT_TRUE(ret == 0);
        }));
    }

    for (auto& iter : threads) {
        iter.join();
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST(TestParallelWrite, test_parallel_read) {
    std::string path = generate_test_file();
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e(1000);  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        int k = i % 10;
        data[i] = *std::to_string(u(e)).c_str();
    }

    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    // write with parallel output stream
    int ret = 0;
    std::vector<std::thread> threads;
    for (int i = 0; i < option.parallel_io_num; ++i) {
        threads.push_back(std::thread([&, i]() {
            hdfs_io_context ctx;
            ctx.channel_id = i + 1;
            for (int j = 0; j < 256; ++j) {
                int64_t offset = i * 256 * 1024 + j * 1024;
                int64_t length = 1024;
                ret = hdfsFuseWrite(fs, hFile, data + offset, length, ctx);
                EXPECT_TRUE(ret == length);
            }
            ret =
                hdfsFuseWrite(fs, hFile, nullptr, 0, ctx);  // finish write flag
            EXPECT_TRUE(ret == 0);
        }));
    }

    for (auto& iter : threads) {
        iter.join();
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    char read_buffer[data_len];
    memset(read_buffer, 0, data_len);
    hFile = hdfsOpenFileV3(fs, path.c_str(), O_RDONLY, 0, 0, 0, option);
    EXPECT_TRUE(hFile);

    threads.clear();
    for (int i = 0; i < option.parallel_io_num; ++i) {
        threads.push_back(std::thread(([&, i]() {
            hdfs_io_context ctx;
            ctx.channel_id = i + 1;

            // seek to specified pos and read correct
            int64_t read_pos = i * 256 * 1024 + 10 * 1024;
            int ret = hdfsFuseSeek(fs, hFile, read_pos, ctx);
            EXPECT_TRUE(ret == 0);

            ret = hdfsFuseTell(fs, hFile, ctx);
            EXPECT_TRUE(ret == read_pos);

            int64_t total_read_len = 0;
            for (int j = 10; j < 256; ++j) {
                int64_t offset = i * 256 * 1024 + j * 1024;
                ret = hdfsFuseSeek(fs, hFile, offset, ctx);
                EXPECT_EQ(ret, 0);

                ret = hdfsFuseTell(fs, hFile, ctx);
                EXPECT_EQ(ret, offset);

                int64_t length = 1024;
                int64_t read_len = 0;
                while (read_len != length) {
                    ret = hdfsFuseTell(fs, hFile, ctx);
                    EXPECT_TRUE(ret == (offset + read_len));
                    auto len =
                        hdfsFuseRead(fs, hFile, read_buffer + offset + read_len,
                                     length - read_len, ctx);
                    EXPECT_TRUE(len > 0);
                    EXPECT_EQ(std::string(read_buffer + offset + read_len, len),
                              std::string(data + offset + read_len, len));
                    read_len += len;
                }
                EXPECT_EQ(read_len, length);
                total_read_len += length;
            }

            int64_t read_end = (i + 1) * 256 * 1024;
            EXPECT_EQ(total_read_len, read_end - read_pos);
            EXPECT_EQ(std::string(read_buffer + read_pos, 10),
                      std::string(data + read_pos, 10));
        })));
    }

    for (auto& iter : threads) {
        iter.join();
    }

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_TRUE(ret == 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

/* 当前版本先不开启在open/close文件时候，设置part文件的attr，所以这个单测可以不用开启
TEST(TestParallelWrite, test_open_delete) {
    std::string path = generate_test_file();
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    // for parallel file
    CreateFileOption option;
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY, 0, 0, 0,
option); EXPECT_TRUE(hFile);

    auto xattr = hdfsGetXAttr(fs, path.c_str(), "trusted.hdfs.parallel_io_num");
    EXPECT_TRUE(xattr);

    int32_t parallel_io_num = std::stoi(xattr->value);
    EXPECT_EQ(parallel_io_num, option.parallel_io_num);

    for (int32_t i = 0; i < parallel_io_num; ++i) {
        std::string part_file = std::string(path) + ".part" + std::to_string(i);
        EXPECT_EQ(hdfsExists(fs, part_file.c_str()), 0);
    }

    int ret = hdfsDelete(fs, path.c_str(), 0);
    EXPECT_EQ(ret, 0);

    for (int32_t i = 0; i < parallel_io_num; ++i) {
        std::string part_file = std::string(path) + ".part" + std::to_string(i);
        EXPECT_EQ(hdfsExists(fs, part_file.c_str()), -1);
    }

    // for none parallel file
    path = generate_test_file();
    info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY, 0, 0, 0);
    EXPECT_TRUE(hFile);

    xattr = hdfsGetXAttr(fs, path.c_str(), "trusted.hdfs.parallel_io_num");
    EXPECT_TRUE(xattr==nullptr);

    for (int32_t i = 0; i < parallel_io_num; ++i) {
        std::string part_file = std::string(path) + ".part" + std::to_string(i);
        EXPECT_EQ(hdfsExists(fs, part_file.c_str()), -1);
    }

    ret = hdfsDelete(fs, path.c_str(), 0);
    EXPECT_EQ(ret, 0);
}
*/

TEST(TestParallelWrite, test_open_with_exception) {
    std::string path = generate_test_file();
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    // for parallel file
    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    int ret = hdfsCloseFile(fs, hFile);
    EXPECT_EQ(ret, 0);

    // open with append flag not suported
    hFile =
        hdfsOpenFileV3(fs, path.c_str(), O_APPEND | O_WRONLY, 0, 0, 0, option);
    EXPECT_TRUE(hFile == nullptr);

    // open with single stream
    hFile = hdfsOpenFile(fs, path.c_str(), O_APPEND | O_WRONLY, 0, 0, 0);
    EXPECT_TRUE(hFile);

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_EQ(ret, 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

static void async_failed_callback(hdfsStatus status, void* args) { return; }

TEST(TestParallelWrite, test_async_write) {
    uint32_t data_len = 1024 * 1024;
    char data[data_len];
    std::uniform_int_distribution<unsigned> u(0, 9);
    std::default_random_engine e(1000);  // 生成无符号随机整数
    for (int i = 0; i < data_len; ++i) {
        int k = i % 10;
        data[i] = *std::to_string(u(e)).c_str();
    }

    std::string path = generate_test_file();
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    // for parallel file
    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 4;
    option.partial_file_size = 256 * 1024;
    auto hFile = hdfsOpenFileV3(fs, path.c_str(), O_WRONLY | O_CREAT | O_TRUNC,
                                0, 0, 0, option);
    EXPECT_TRUE(hFile);

    hdfs_io_context ctx;
    ctx.channel_id = 1;
    ctx.write_callback = &async_failed_callback;
    ctx.args = &option;

    int ret = hdfsAsyncWrite(fs, hFile, data, data_len, ctx);
    EXPECT_EQ(ret, hdfsStatus::STATUS_UNSUPPORTED_OP);

    ret = hdfsAsyncFlush(fs, hFile, ctx);
    EXPECT_EQ(ret, hdfsStatus::STATUS_UNSUPPORTED_OP);

    ret = hdfsAsyncWriteAndFlush(fs, hFile, data, data_len, ctx);
    EXPECT_EQ(ret, hdfsStatus::STATUS_UNSUPPORTED_OP);

    ret = hdfsCloseFile(fs, hFile);
    EXPECT_EQ(ret, 0);

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

hdfsFile OpenFile(hdfsFS fs, std::string path, int flags) {
    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 0;
    return hdfsOpenFileV3(fs, path.c_str(), flags, 0, 0, 0, option);
}

std::string readFile(hdfsFS fs, std::string path) {
    CreateFileOption option = GetDefaultCreateFileOption();
    option.parallel_io_num = 0;
    auto file = hdfsOpenFileV3(fs, path.c_str(), 0, 0, 0, 0, option);
    char buffer[200] = {0};
    auto size = hdfsRead(fs, file, buffer, 200);
    hdfsCloseFile(fs, file);
    return std::string(buffer, size);
}

TEST(TestParallelWrite, test_posix_like_open) {
    std::string path = generate_test_file();
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsFS fs = hdfsBuilderConnect(builder);
    hdfsFileInfo* info = hdfsGetPathInfo(fs, path.c_str());
    EXPECT_TRUE(info == nullptr);

    // file access flags O_RDONLY O_WRONLY O_RDWR
    // file creation flags O_CREAT O_EXCL(work when O_CREAT is set) O_TRUNC
    // file status O_APPEND

    // 1. O_WRONLY
    // 1.1.1 O_CREAT file not exist create file, otherwise open for write but
    // start offset is 0, (in hdfs start offset is file size)
    auto file = OpenFile(fs, path, O_WRONLY | O_CREAT);
    EXPECT_TRUE(file);
    int ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    file = OpenFile(fs, path, O_WRONLY | O_CREAT);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    // 1.1.2 O_CREAT | O_APPEND file not exist create file, otherwise append
    // file, start offset is file size
    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_APPEND);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_APPEND);
    EXPECT_TRUE(file);
    std::string data = "test_data";
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(readFile(fs, path), data);

    // 1.2.1 O_TRUNC file exist trucate to 0, otherwise failed
    file = OpenFile(fs, path, O_WRONLY | O_TRUNC);
    EXPECT_TRUE(file);
    EXPECT_EQ(readFile(fs, path), "");
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_WRONLY | O_TRUNC);
    EXPECT_FALSE(file);

    // 1.2.2 O_TRUNC | O_APPEND file not exist trucate to 0, otherwise failed
    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_APPEND);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(readFile(fs, path), data);

    file = OpenFile(fs, path, O_WRONLY | O_TRUNC | O_APPEND);
    EXPECT_TRUE(file);
    EXPECT_EQ(readFile(fs, path), "");
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_WRONLY | O_TRUNC | O_APPEND);
    EXPECT_FALSE(file);

    // 1.3.1 O_CREAT | O_EXCL file exist failed, otherwise create file
    // 1.3.2 O_CREAT | O_EXCL | O_APPEND ，O_APPEND不生效
    // 1.3.3 O_CREAT | O_EXCL | O_TRUNC ，O_TRUNC不生效
    // 1.3.4 O_CREAT | O_EXCL | O_TRUNC | O_APPEND，O_TRUNC|O_APPEND不生效
    // only test O_CREAT | O_EXCL
    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_EXCL);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_EXCL);
    EXPECT_FALSE(file);

    // 1.4.1 O_CREAT | O_TRUNC file not exist create file, otherwise trucate to
    // 0 1.4.2 O_CREAT | O_TRUNC | O_APPEND ，O_APPEND不生效 create file
    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_TRUNC);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    // trucate to 0
    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_TRUNC);
    EXPECT_TRUE(file);
    EXPECT_EQ(readFile(fs, path), "");
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    // 1.6.1 O_APPEND file not exist failed, otherwise append file
    file = OpenFile(fs, path, O_WRONLY | O_CREAT | O_TRUNC);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(readFile(fs, path), data);

    file = OpenFile(fs, path, O_WRONLY | O_APPEND);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(readFile(fs, path), data + data);

    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_WRONLY | O_APPEND);
    EXPECT_FALSE(file);
    // 2. O_RDWR
    // TODO

    // 2.1.1 O_CREAT file not exist create file, otherwise open for write but
    // start offset is 0, (in hdfs start offset is file size)
    file = OpenFile(fs, path, O_RDWR | O_CREAT);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    file = OpenFile(fs, path, O_RDWR | O_CREAT);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    // 2.1.2 O_CREAT | O_APPEND file not exist create file, otherwise append
    // file, start offset is file size
    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_APPEND);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_APPEND);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(readFile(fs, path), data);

    // 2.2.1 O_TRUNC file exist trucate to 0, otherwise failed
    file = OpenFile(fs, path, O_RDWR | O_TRUNC);
    EXPECT_TRUE(file);
    EXPECT_EQ(readFile(fs, path), "");
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_RDWR | O_TRUNC);
    EXPECT_FALSE(file);

    // 2.2.2 O_TRUNC | O_APPEND file not exist trucate to 0, otherwise failed
    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_APPEND);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(readFile(fs, path), data);

    file = OpenFile(fs, path, O_RDWR | O_TRUNC | O_APPEND);
    EXPECT_TRUE(file);
    EXPECT_EQ(readFile(fs, path), "");
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_RDWR | O_TRUNC | O_APPEND);
    EXPECT_FALSE(file);

    // 2.3.1 O_CREAT | O_EXCL file exist failed, otherwise create file
    // 2.3.2 O_CREAT | O_EXCL | O_APPEND ，O_APPEND不生效
    // 2.3.3 O_CREAT | O_EXCL | O_TRUNC ，O_TRUNC不生效
    // 2.3.4 O_CREAT | O_EXCL | O_TRUNC | O_APPEND，O_TRUNC|O_APPEND不生效
    // only test O_CREAT | O_EXCL
    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_EXCL);
    EXPECT_TRUE(file);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_EXCL);
    EXPECT_FALSE(file);

    // 2.4.1 O_CREAT | O_TRUNC file not exist create file, otherwise trucate to
    // 2.4.2 O_CREAT | O_TRUNC | O_APPEND ，O_APPEND不生效 create file
    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_TRUNC);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    // trucate to 0
    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_TRUNC);
    EXPECT_TRUE(file);
    EXPECT_EQ(readFile(fs, path), "");
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);

    // 2.6.1 O_APPEND file not exist failed, otherwise append file
    file = OpenFile(fs, path, O_RDWR | O_CREAT | O_TRUNC);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(readFile(fs, path), data);

    file = OpenFile(fs, path, O_RDWR | O_APPEND);
    EXPECT_TRUE(file);
    hdfsWrite(fs, file, data.c_str(), 9);
    ret = hdfsCloseFile(fs, file);
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(readFile(fs, path), data + data);

    hdfsDelete(fs, path.c_str(), 0);
    file = OpenFile(fs, path, O_RDWR | O_APPEND);
    EXPECT_FALSE(file);

    // 3. O_RDONLY
    // already tested

    hdfsDelete(fs, path.c_str(), 0);
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

}  // namespace Internal
}  // namespace Hdfs

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}