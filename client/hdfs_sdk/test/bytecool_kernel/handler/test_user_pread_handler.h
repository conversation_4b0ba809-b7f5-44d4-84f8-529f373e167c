#pragma once

#include <gtest/gtest.h>

#include "../utils/util.h"
#include "bytecool_kernel/handler/bytecool_handler.h"

class UserPReadHandlerTest : public testing::Test {
   protected:
    void SetUp() override {
        hdfs_builder = hdfsNewCoolBuilder();
        EXPECT_TRUE(hdfs_builder != nullptr);
        hdfs_file_system = hdfsBuilderConnect(hdfs_builder);
        EXPECT_TRUE(hdfs_file_system != nullptr);

        std::vector<uint32_t> test_file_lengths = {7654321};
        for (const auto& test_file_length : test_file_lengths) {
            GenUserFile(test_file_length);
        }
    }

    void TearDown() override {
        if (hdfs_file_system != nullptr) {
            hdfsDisconnect(hdfs_file_system);
            hdfs_file_system = nullptr;
        }
        if (hdfs_builder != nullptr) {
            hdfsFreeBuilder(hdfs_builder);
            hdfs_builder = nullptr;
        }
    }

    void GenUserFile(uint64_t file_length) {
        std::random_device rd;
        std::string file_path =
            fmt::format("/user/bytecool/test/user_file/test_file_{}",
                        BYTECOOL::UTILS::UuidUtils::ToString(
                            BYTECOOL::UTILS::UuidUtils::Generate()));
        BYTECOOL::STORE::BackendFileSystem backend_fs(
            BYTECOOL::COMMON::OperatePermission::kWriteOnly, hdfs_file_system);
        std::string file_data(file_length, 0);
        for (int i = 0; i < file_length; ++i) {
            file_data[i] = (char)(rd() % 26 + 'a');
        }
        uint32_t file_crc = BYTECOOL::UTILS::CRCUtils::Value(file_data);
        BYTECOOL::COMMON::Result ret = backend_fs.Open(file_path);
        ASSERT_TRUE(ret);
        ret = backend_fs.Write(file_data);
        ASSERT_TRUE(ret);
        ret = backend_fs.Flush();
        ASSERT_TRUE(ret);
        backend_fs.Close();
        hdfs_file_path.emplace_back(file_path, file_length);
        hdfs_file_data.insert({file_path, std::move(file_data)});
        std::printf(
            "[UserReadHandlerTest]Get test file info success. path=%s length=%lu "
            "crc=%d\n",
            file_path.c_str(), file_length, file_crc);
    }

    hdfsBuilder* hdfs_builder{nullptr};
    hdfsFS hdfs_file_system{nullptr};
    std::vector<std::pair<std::string, uint64_t>> hdfs_file_path{};
    std::map<std::string, std::string> hdfs_file_data{};
};

TEST_F(UserPReadHandlerTest, PRead) {
    BYTECOOL::COMMON::Config config = {
        .backend_fs = hdfs_file_system,
        .archive_path_prefix = "/user/bytecool/test/archive",
    };
    BYTECOOL::HANDLER::ByteCoolHandler bytecool_handler(config);
    ASSERT_TRUE(bytecool_handler.Init());

    BYTECOOL::COMMON::ImportRequest import_request = {
        .task_id = 0,
        .paths = hdfs_file_path,
        .cluster = "test_cluster",
        .tenant = "test_tenant",
        .partition = "20240101",
        .coding_policy = BYTECOOL::COMMON::CodingPolicy({
            .data_num = 6,
            .parity_num = 3,
            .chunk_size = 128 * 1024,
        }),
        .import_type = BYTECOOL::COMMON::ImportType::kImport,
    };
    BYTECOOL::COMMON::ImportResponse import_response;
    ASSERT_TRUE(bytecool_handler.Import(import_request, &import_response));
    ASSERT_EQ(import_response.success_paths.size(), hdfs_file_path.size());

    std::string file_path = hdfs_file_path[0].first;
    uint64_t file_length = hdfs_file_path[0].second;
    std::string data;

    // Test range read
    BYTECOOL::STORE::BackendFileSystem backend_fs1(
        BYTECOOL::COMMON::OperatePermission::kReadOnly, hdfs_file_system);
    ASSERT_EQ(backend_fs1.Open(file_path).GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);

    uint64_t read_offset = import_request.coding_policy.chunk_size + 123;
    uint64_t read_length = 3 * import_request.coding_policy.chunk_size;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, true, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 0;
    read_length = import_request.coding_policy.chunk_size / 2;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 2;
    read_length = import_request.coding_policy.chunk_size / 2;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, true, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 4;
    read_length = import_request.coding_policy.chunk_size;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    // Test normal read
    read_offset = 0;
    read_length = file_length;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, true, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 100;
    read_length = file_length - 100;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 200;
    read_length = import_request.coding_policy.data_num *
                      import_request.coding_policy.chunk_size * 2 +
                  200;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 300;
    read_length = import_request.coding_policy.data_num *
                      import_request.coding_policy.chunk_size * 2 +
                  300;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 400;
    read_length = import_request.coding_policy.data_num *
                      import_request.coding_policy.chunk_size * 3 +
                  400;
    ASSERT_EQ(backend_fs1.Read(read_offset, read_length, true, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    // Test another backend
    BYTECOOL::STORE::BackendFileSystem backend_fs2(
        BYTECOOL::COMMON::OperatePermission::kReadOnly, hdfs_file_system);
    ASSERT_EQ(backend_fs2.Open(file_path).GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);

    read_offset = 400;
    read_length = import_request.coding_policy.data_num *
                      import_request.coding_policy.chunk_size * 3 +
                  400;
    ASSERT_EQ(backend_fs2.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = import_request.coding_policy.chunk_size + 123;
    read_length = 3 * import_request.coding_policy.chunk_size;
    ASSERT_EQ(backend_fs2.Read(read_offset, read_length, true, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    read_offset = 100;
    read_length = file_length - 100;
    ASSERT_EQ(backend_fs2.Read(read_offset, read_length, false, false, &data)
                  .GetErrorCode(),
              BYTECOOL::COMMON::ErrorCode::kSuccess);
    ASSERT_EQ(BYTECOOL::UTILS::CRCUtils::Value(data),
              BYTECOOL::UTILS::CRCUtils::Value(
                  hdfs_file_data[file_path].substr(read_offset, read_length)));

    backend_fs1.Close();
    backend_fs2.Close();
    std::printf("[UserPReadHandlerTest]Test pread done. file_num=%zu\n",
                hdfs_file_path.size());
}