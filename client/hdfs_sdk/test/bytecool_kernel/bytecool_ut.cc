#include <gtest/gtest.h>

#include "./archive/test_archivepool.h"
#include "./common/test_common.h"
#include "./common/test_quorum.h"
#include "./handler/test_import_handler.h"
#include "./handler/test_read_handler.h"
#include "./handler/test_reimport_handler.h"
#include "./handler/test_repair_handler.h"
#include "./handler/test_user_pread_handler.h"
#include "./handler/test_user_read_handler.h"
#include "./handler/test_validate_handler.h"
#include "./store/test_backend_file_system.h"
#include "./store/test_backend_meta_system.h"
#include "./utils/test_ec_worker.h"
#include "./utils/test_lru_cache.h"
#include "./utils/test_misc.h"
#include "./volume/test_volume_layout.h"
#include "./volume/test_volume_worker.h"

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}