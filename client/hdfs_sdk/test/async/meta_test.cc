#include <client/hdfs.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

class TestMeta : public ::testing::Test {
   public:
    TestMeta() {
        hdfsBuilder* builder = hdfsNewBuilder();
        hdfsBuilderSetNameNode(builder, "10.227.159.28");
        hdfsBuilderSetNameNodePort(builder, 5060);
        hdfsBuilderConfSetStr(builder, "dfs.socket.read.ms.timeout", "1000");
        hdfsBuilderConfSetStr(builder, "dfs.client.hedged.read.enable", "true");
        hdfsBuilderConfSetStr(
            builder, "dfs.client.hedged.read.within.dc.threshold.millis", "10");
        hdfsBuilderConfSetStr(builder, "dfs.client.max.block.acquire.failures",
                              "2");
        hdfsBuilderConfSetStr(builder, "client.async_read_request.max", "1");
        fs = hdfsBuilderConnect(builder);

        if (fs == NULL) {
            throw std::runtime_error("cannot connect hdfs");
        }
    }

    ~TestMeta() { hdfsDisconnect(fs); }

   protected:
    hdfsFS fs;
};

TEST_F(TestMeta, TestChownName) {
    std::string path = "/usr/jinrj/testChxxxFile.txt";
    std::string chown_before = "Jinrj";
    std::string chown_after = "Xiongmu";

    hdfsChown(fs, path.c_str(), chown_before.c_str(), nullptr);
    hdfsFileInfo* verify_once = hdfsGetPathInfo(fs, path.c_str());
    std::string expected_own_before(verify_once->mOwner);
    ASSERT_EQ(expected_own_before, chown_before);

    hdfsChown(fs, path.c_str(), chown_after.c_str(), nullptr);
    hdfsFileInfo* verify_twice = hdfsGetPathInfo(fs, path.c_str());
    std::string expected_own_after(verify_twice->mOwner);
    ASSERT_EQ(expected_own_after, chown_after);

    hdfsFreeFileInfo(verify_once, 1);
    hdfsFreeFileInfo(verify_twice, 1);
}

TEST_F(TestMeta, TestChownGroup) {
    // We can chown twice so that we need not to ensure the initial state of
    // file.
    std::string path = "/usr/jinrj/testChxxxFile.txt";
    std::string chown_before = "JinGroup";
    std::string chown_after = "XiongGroup";

    hdfsChown(fs, path.c_str(), nullptr, chown_before.c_str());
    hdfsFileInfo* verify_once = hdfsGetPathInfo(fs, path.c_str());
    std::string expected_own_before(verify_once->mGroup);
    ASSERT_EQ(expected_own_before, chown_before);

    hdfsChown(fs, path.c_str(), nullptr, chown_after.c_str());
    hdfsFileInfo* verify_twice = hdfsGetPathInfo(fs, path.c_str());
    std::string expected_own_after(verify_twice->mGroup);
    ASSERT_EQ(expected_own_after, chown_after);

    hdfsFreeFileInfo(verify_once, 1);
    hdfsFreeFileInfo(verify_twice, 1);
}

TEST_F(TestMeta, TestChmod) {
    // We can chmod twice so that we need not to ensure the initial state of
    // file.
    std::string path = "/usr/jinrj/testChxxxFile.txt";

    hdfsChmod(fs, path.c_str(), 0777);
    hdfsFileInfo* verify_once = hdfsGetPathInfo(fs, path.c_str());
    ASSERT_EQ(verify_once->mPermissions, 0777);

    hdfsChmod(fs, path.c_str(), 0755);
    hdfsFileInfo* verify_twice = hdfsGetPathInfo(fs, path.c_str());
    ASSERT_EQ(verify_twice->mPermissions, 0755);

    hdfsFreeFileInfo(verify_once, 1);
    hdfsFreeFileInfo(verify_twice, 1);
}

TEST_F(TestMeta, TestListDir) {
    std::string list_path = "/usr/jinrj/testListDir/";
    int entry_number;
    hdfsFileInfo* result =
        hdfsListDirectory(fs, list_path.c_str(), &entry_number, NULL, NULL);

    ASSERT_EQ(entry_number, 2);
    std::string file_name, dir_name;
    for (int i = 0; i < entry_number; i++) {
        hdfsFileInfo tmp = result[i];
        if (tmp.mKind == kObjectKindFile) {
            file_name = std::string(tmp.mName);
        } else if (tmp.mKind == kObjectKindDirectory) {
            dir_name = std::string(tmp.mName);
        }
    }
    // Should we verify all the field here? Since we have verify all the fields
    // in TestFileStatus.
    ASSERT_EQ(file_name,
              "hdfs://10.227.159.28:5060/usr/jinrj/testListDir/listInnerFile");
    ASSERT_EQ(dir_name,
              "hdfs://10.227.159.28:5060/usr/jinrj/testListDir/listInnerDir");

    hdfsFreeFileInfo(result, entry_number);
}

TEST_F(TestMeta, TestListFile) {
    std::string list_path = "/usr/jinrj/testListDir/listInnerFile";
    int entry_number;
    hdfsFileInfo* result =
        hdfsListDirectory(fs, list_path.c_str(), &entry_number, NULL, NULL);

    ASSERT_EQ(entry_number, 1);
    std::string file_name;
    for (int i = 0; i < entry_number; i++) {
        hdfsFileInfo tmp = result[i];
        file_name = std::string(tmp.mName);
    }
    // Since we have verify all the fields in TestFileStatus.
    ASSERT_EQ(file_name,
              "hdfs://10.227.159.28:5060/usr/jinrj/testListDir/listInnerFile");

    hdfsFreeFileInfo(result, entry_number);
}

TEST_F(TestMeta, TestListEmptyDir) {
    std::string list_path = "/usr/jinrj/testListDir/listInnerDir";
    int entry_number = -1;
    hdfsFileInfo* result =
        hdfsListDirectory(fs, list_path.c_str(), &entry_number, NULL, NULL);

    ASSERT_EQ(entry_number, 0);

    hdfsFreeFileInfo(result, entry_number);
}

TEST_F(TestMeta, TestWideDir) {
    std::string list_path = "/usr/jinrj/wideDir";
    int entry_number = -1;
    hdfsFileInfo* result =
        hdfsListDirectory(fs, list_path.c_str(), &entry_number, NULL, NULL);

    ASSERT_EQ(entry_number, 2000);

    std::set<std::string> files;
    for (int i = 0; i < 2000; i++) {
        files.insert("hdfs://10.227.159.28:5060/usr/jinrj/wideDir/file" +
                     std::to_string(i));
    }

    int i = 0;
    for (auto file : files) {
        ASSERT_EQ(file, std::string(result[i].mName));
        i++;
    }

    hdfsFreeFileInfo(result, entry_number);
}

TEST_F(TestMeta, TestMkdir) {
    std::string path = "/usr/jinrj/testMkdir";
    hdfsCreateDirectory(fs, path.c_str());
    hdfsFileInfo* verify_mkdir = hdfsGetPathInfo(fs, path.c_str());

    std::string expected_name(verify_mkdir->mName);
    ASSERT_EQ(expected_name, "hdfs://10.227.159.28:5060/usr/jinrj/testMkdir");
    ASSERT_EQ(kObjectKindDirectory, verify_mkdir->mKind);
    hdfsFreeFileInfo(verify_mkdir, 1);

    hdfsDelete(fs, path.c_str(), true);
}

TEST_F(TestMeta, TestRenameFile) {
    std::string path_before = "/usr/jinrj/renameBefore.txt";
    std::string path_after = "/usr/jinrj/renameAfter.txt";

    // Create the file
    hdfsFile hdfs_file_create = hdfsOpenFile(fs, path_before.c_str(), O_WRONLY,
                                             64 * 1024, 3, 512 * 1024 * 1024);
    hdfsCloseFile(fs, hdfs_file_create);

    // According to API, 0 means success, -1 means failure
    int create_result = hdfsExists(fs, path_before.c_str());
    ASSERT_EQ(create_result, 0);

    hdfsRename(fs, path_before.c_str(), path_after.c_str());
    int rename_result = hdfsExists(fs, path_after.c_str());
    ASSERT_EQ(rename_result, 0);

    int old_file = hdfsExists(fs, path_before.c_str());
    ASSERT_EQ(old_file, -1);

    hdfsDelete(fs, path_after.c_str(), true);
}

TEST_F(TestMeta, TestRenameDir) {
    std::string path_before = "/usr/jinrj/renameBeforeDir/";
    std::string path_after = "/usr/jinrj/renameAfterDir/";

    // Create the file
    hdfsCreateDirectory(fs, path_before.c_str());
    // According to API, 0 means success, -1 means failure
    int create_result = hdfsExists(fs, path_before.c_str());
    ASSERT_EQ(create_result, 0);

    hdfsRename(fs, path_before.c_str(), path_after.c_str());
    int rename_result = hdfsExists(fs, path_after.c_str());
    ASSERT_EQ(rename_result, 0);

    int old_file = hdfsExists(fs, path_before.c_str());
    ASSERT_EQ(old_file, -1);

    hdfsDelete(fs, path_after.c_str(), true);
}

TEST_F(TestMeta, TestSetrep) {
    std::string path = "/usr/jinrj/testChxxxFile.txt";

    hdfsSetReplication(fs, path.c_str(), 3);
    hdfsFileInfo* verify_once = hdfsGetPathInfo(fs, path.c_str());
    ASSERT_EQ(verify_once->mReplication, 3);

    hdfsSetReplication(fs, path.c_str(), 2);
    hdfsFileInfo* verify_twice = hdfsGetPathInfo(fs, path.c_str());
    ASSERT_EQ(verify_twice->mReplication, 2);

    hdfsFreeFileInfo(verify_once, 1);
    hdfsFreeFileInfo(verify_twice, 1);
}

TEST_F(TestMeta, TestFileStatus) {
    std::string path = "/usr/jinrj/testStatusFile.txt";
    hdfsFileInfo* result = hdfsGetPathInfo(fs, path.c_str());

    std::string expected_path =
        "hdfs://10.227.159.28:5060/usr/jinrj/testStatusFile.txt";

    std::string actual_path(result->mName);
    std::string actual_owner(result->mOwner);
    std::string actual_group(result->mGroup);

    ASSERT_EQ(actual_path, expected_path);
    ASSERT_EQ(actual_owner, "root");
    ASSERT_EQ(actual_group, "supergroup");
    ASSERT_EQ(result->mReplication, 3);
    ASSERT_EQ(result->mBlockSize, 536870912);
    ASSERT_EQ(result->mLastAccess, 1623296142302);
    ASSERT_EQ(result->mLastMod, 1623296147325);
    ASSERT_EQ(result->mPermissions, 420);
    ASSERT_EQ(result->mKind, kObjectKindFile);
    ASSERT_EQ(result->mSize, 112576971);

    hdfsFreeFileInfo(result, 1);
}

TEST_F(TestMeta, TestDirStatus) {
    std::string path = "/usr/jinrj/testStatusDir";
    hdfsFileInfo* result = hdfsGetPathInfo(fs, path.c_str());

    std::string expected_path =
        "hdfs://10.227.159.28:5060/usr/jinrj/testStatusDir";

    std::string actual_path(result->mName);
    std::string actual_owner(result->mOwner);
    std::string actual_group(result->mGroup);

    ASSERT_EQ(actual_path, expected_path);
    ASSERT_EQ(actual_owner, "root");
    ASSERT_EQ(actual_group, "supergroup");
    ASSERT_EQ(result->mReplication, 0);
    ASSERT_EQ(result->mBlockSize, 0);
    ASSERT_EQ(result->mLastAccess, 1623296142281);
    ASSERT_EQ(result->mLastMod, 1623296142281);
    ASSERT_EQ(result->mPermissions, 493);
    ASSERT_EQ(result->mKind, kObjectKindDirectory);
    ASSERT_EQ(result->mSize, 0);

    hdfsFreeFileInfo(result, 1);
}

TEST_F(TestMeta, TestDeleteFile) {
    std::string path = "/usr/jinrj/deleteFile.txt";
    // Create the file
    hdfsFile hdfs_file_create = hdfsOpenFile(fs, path.c_str(), O_CREAT,
                                             64 * 1024, 3, 512 * 1024 * 1024);
    hdfsCloseFile(fs, hdfs_file_create);

    // According to API, 0 means success, -1 means failure
    int create_result = hdfsExists(fs, path.c_str());
    ASSERT_EQ(create_result, 0);

    hdfsDelete(fs, path.c_str(), true);
    int delete_result = hdfsExists(fs, path.c_str());
    ASSERT_EQ(delete_result, -1);
}

TEST_F(TestMeta, TestDeleteDir) {
    std::string path = "/usr/jinrj/deleteDirs";
    // Create the file
    int create_result = hdfsCreateDirectory(fs, path.c_str());
    ASSERT_EQ(create_result, 0);

    hdfsDelete(fs, path.c_str(), true);
    int delete_result = hdfsExists(fs, path.c_str());
    ASSERT_EQ(delete_result, -1);
}

TEST_F(TestMeta, TestGetBlkLocation) {
    std::string path = "/usr/jinrj/bigData100M.txt";
    int32_t number_of_block = 0;
    BlockLocation* locs = hdfsGetFileBlockLocations(
        fs, path.c_str(), 0, 512 * 1024 * 1024, &number_of_block);
    ASSERT_EQ(number_of_block, 1);
    ASSERT_EQ(locs[0].numOfNodes, 3);
    std::set<std::string> dns;
    dns.insert(std::string(locs[0].hosts[0]));
    dns.insert(std::string(locs[0].hosts[1]));
    dns.insert(std::string(locs[0].hosts[2]));

    ASSERT_NE(dns.find("*************"), dns.end());
    ASSERT_NE(dns.find("**************"), dns.end());
    ASSERT_NE(dns.find("*************"), dns.end());

    ASSERT_EQ(locs[0].offset, 0);
    ASSERT_EQ(locs[0].length, 112576971);
}

TEST_F(TestMeta, DeleteNotEmpty) {
    std::string path = "/usr/jinrj/faultTest/inner1";
    int result = hdfsDelete(fs, path.c_str(), false);

    ASSERT_EQ(hdfsExists(fs, path.c_str()), 0);
    ASSERT_EQ(result, -1);
    ASSERT_EQ(errno, ENOTEMPTY);
}

TEST_F(TestMeta, DeleteNoneExisting) {
    std::string path = "/usr/jinrj/faultTest/unExist";
    int result = hdfsDelete(fs, path.c_str(), false);

    ASSERT_EQ(result, -1);
    ASSERT_EQ(errno, EIO);
}

TEST_F(TestMeta, AppendExistingDir) {
    std::string path = "/usr/jinrj/faultTest/inner1";
    hdfsFile file =
        hdfsOpenFile(fs, path.c_str(), O_APPEND | O_WRONLY, 64 * 1024, 0, 0);
    ASSERT_EQ(file, nullptr);
    ASSERT_EQ(errno, EBUSY);
}

TEST_F(TestMeta, OpenExistingDir) {
    std::string path = "/usr/jinrj/faultTest/inner1";
    hdfsFile file = hdfsOpenFile(fs, path.c_str(), O_RDONLY, 64 * 1024, 0, 0);
    ASSERT_EQ(file, nullptr);
    ASSERT_EQ(ENOENT, errno);
}

TEST_F(TestMeta, OpenNoneExistingFile) {
    std::string path = "/usr/jinrj/faultTest/inner1";
    hdfsFile file = hdfsOpenFile(fs, path.c_str(), O_RDONLY, 64 * 1024, 0, 0);
    ASSERT_EQ(file, nullptr);
    ASSERT_EQ(ENOENT, errno);
}

TEST_F(TestMeta, RenameBothNoneExisting) {
    std::string src_path = "/usr/jinrj/faultTest/innerNoneExistingSRC";
    std::string dst_path = "/usr/jinrj/faultTest/innerNoneExistingDST";
    int ret = hdfsRename(fs, src_path.c_str(), dst_path.c_str());

    ASSERT_EQ(-1, ret);
    ASSERT_EQ(errno, ENOENT);
}

TEST_F(TestMeta, RenameBothExisting) {
    std::string src_path = "/usr/jinrj/faultTest/inner1/inner2File";
    std::string dst_path = "/usr/jinrj/faultTest/inner1/inner3File";
    int ret = hdfsRename(fs, src_path.c_str(), dst_path.c_str());

    ASSERT_EQ(-1, ret);
    ASSERT_EQ(errno, EIO);
}

TEST_F(TestMeta, MkdirExistingDir) {
    std::string path = "/usr/jinrj/faultTest/inner1/";
    int result = hdfsCreateDirectory(fs, path.c_str());
    ASSERT_EQ(0, result);
}

TEST_F(TestMeta, MkdirExistingFile) {
    std::string path = "/usr/jinrj/faultTest/inner1/inner3File";
    int ret = hdfsCreateDirectory(fs, path.c_str());

    ASSERT_EQ(-1, ret);
    ASSERT_EQ(EEXIST, errno);
}

TEST_F(TestMeta, MkdirCreateParent) {
    // hdfsCreateDirectory Equal to java code mkdirs.
    // Mkdirs will createParentDirectory by default, while mkdir will not.
    std::string path = "/usr/jinrj/faultTest/abcd/efgh";
    std::string parent_path = "/usr/jinrj/faultTest/abcd";
    ASSERT_EQ(hdfsDelete(fs, parent_path.c_str(), true), 0);
    ASSERT_EQ(hdfsExists(fs, path.c_str()), -1);

    int result = hdfsCreateDirectory(fs, path.c_str());
    ASSERT_EQ(0, result);
    ASSERT_EQ(hdfsExists(fs, path.c_str()), 0);
}

TEST_F(TestMeta, ListNoneExistingFile) {
    int entry_number = -1;
    std::string path = "/usr/jinrj/faultTest/abcd/hijk";
    hdfsFileInfo* result =
        hdfsListDirectory(fs, path.c_str(), &entry_number, NULL, NULL);
    ASSERT_EQ(result, nullptr);
    ASSERT_EQ(errno, ENOENT);
}

TEST_F(TestMeta, CreateExistingDir) {
    std::string path = "/usr/jinrj/faultTest/inner1";
    hdfsFile result = hdfsOpenFile(fs, path.c_str(), O_CREAT, 64 * 1024, 3,
                                   512 * 1024 * 1024);
    ASSERT_EQ(result, nullptr);
    ASSERT_EQ(errno, EEXIST);
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}