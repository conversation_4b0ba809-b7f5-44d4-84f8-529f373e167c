# This "brpc" only contains "bvar" and "butil" module. Other modules (like "rpc" module)
# are removed (see ${CMAKE_SOURCE_DIR}/patches/brpc.patch) because they are not required.
set(name brpc-1.1.0)
ExternalProject_Add(
  ${name}
  GIT_REPOSITORY ******************:storage/thirdparty.git
  # commit id for branch release-v1.1.0
  GIT_TAG 85f49abc01821ce5e52f8fdcfb8e05de3fd245cd
  GIT_SUBMODULES ""
  SOURCE_SUBDIR brpc
  # GIT_SHALLOW TRUE
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  PATCH_COMMAND
    git checkout -- . && git clean -f && patch -p1 < ${CMAKE_SOURCE_DIR}/patches/brpc.patch
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DbRPC_GFLAGS_PROVIDER=package
    -DbRPC_PROTOBUF_PROVIDER=package
    -DbRPC_OPENSSL_PROVIDER=package
    -DBUILD_SHARED_LIBS=OFF
  BUILD_COMMAND make -s -j${BUILDING_JOBS_NUM}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )
