#include "bytecoolsdk/common/common_util.h"

namespace bytecool_sdk {

// If object size <= 200M, use compact chunk size
size_t kCompactChunkSizeThreshold = 209715200;

size_t RoundUp(size_t what, size_t step_size) {
    const size_t fully_fitting = what / step_size;
    size_t ret = fully_fitting * step_size;

    const size_t remainder = what % step_size;
    if (remainder > 0) {
        ret += step_size;
    }

    return ret;
}

std::ostream& operator<<(std::ostream& os, const ArchiveInfo& arch) {
    os << arch.ToString();
    return os;
}

std::ostream& operator<<(std::ostream& os, const ObjectInfo& obj) {
    os << obj.ToString();
    return os;
}

std::ostream& operator<<(std::ostream& os, const ObjectId& object_id) {
    os << object_id.ToString();
    return os;
}

Result CheckUUIDFormat(const std::string& uuid_str) {
    boost::uuids::uuid uuid;
    try {
        uuid = boost::uuids::string_generator()(uuid_str);
    } catch (...) {
        return Result::err("Wrong UUID format: " + uuid_str);
    }
    if (uuid.version() == boost::uuids::uuid::version_unknown &&
        uuid != boost::uuids::nil_generator()()) {
        return Result::err("Unknown UUID version: " + uuid_str);
    }
    return Result::ok();
}

Result CheckDateFormat(const std::string& date_str) {
    bool d;
    try {
        auto str = date_str;
        str.erase(std::remove(str.begin(), str.end(), ' '), str.end());
        d = (str.length() >= 4);
    } catch (...) {
        return Result::err("Wrong dir format: " + date_str);
    }
    if (!d) {
        return Result::err("Wrong dir format: " + date_str);
    }
    return Result::ok();
}

size_t CalculateSuggestedChunkSize(const ssize_t chunk_size_hint,
                                   const size_t max_ec_chunk_size,
                                   const int data_units,
                                   const size_t object_size) {
    if (chunk_size_hint >= 0) {
        return chunk_size_hint;
    }

    // handle the case when object_size == 0. Actually we can return any number.
    if (object_size == 0) {
        return 1;
    }

    /*
    size_t first_proposal = object_size / data_units;
    size_t first_remainder = object_size % data_units;
    if (first_remainder > 0) {
      first_proposal += 1;
    }

    size_t suggested_chunk_size = first_proposal;

    int multiple = 1;
    if (first_proposal > max_ec_chunk_size) {
      multiple = first_proposal / max_ec_chunk_size;
      size_t multiple_reminder = first_proposal % max_ec_chunk_size;
      if (multiple_reminder > 0) {
        multiple += 1;
      }

      size_t final_proposal = first_proposal / multiple;
      size_t final_remainder = first_proposal % multiple;
      if (final_remainder > 0) {
        final_proposal += 1;
      }

      suggested_chunk_size = final_proposal;
    }
    */

    // We use max_ec_chunk_size (128k) as uniform chunk size.
    return max_ec_chunk_size;
}

void AppendAsMetricsTags(std::string& tags, const std::string& key,
                         const std::string& val) {
    if (!tags.empty()) {
        tags += "|";
    }
    tags += key + "=" + val;
}

}  // namespace bytecool_sdk
