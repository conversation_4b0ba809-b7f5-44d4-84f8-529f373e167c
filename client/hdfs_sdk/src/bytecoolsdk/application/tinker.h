//
// Created by renming on 2019-06-02.
//

#pragma once
#include <glog/logging.h>
#include <glog/stl_logging.h>
#include <sys/types.h>

#include <algorithm>
#include <fstream>

#pragma GCC diagnostic ignored "-Wnon-virtual-dtor"
#pragma GCC diagnostic ignored "-Wmissing-field-initializers"
#include <boost/asio/buffers_iterator.hpp>
#include <boost/asio/thread_pool.hpp>
#include <boost/filesystem.hpp>
#pragma GCC diagnostic warning "-Wmissing-field-initializers"
#pragma GCC diagnostic warning "-Wnon-virtual-dtor"

#include <functional>
#include <ios>
#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <streambuf>
#include <string>
#include <utility>
#include <vector>

#include "bytecoolsdk/archive/archive_reader.h"
#include "bytecoolsdk/common/result.h"
#include "bytecoolsdk/ec/dump.h"
#include "bytecoolsdk/ec/erasure_coder.h"
#include "bytecoolsdk/fs/backend_fs.h"
#include "common/assert.h"

namespace bytecool_sdk {

typedef std::vector<boost::asio::mutable_buffer> BoostBuffers;

class BufferSequence {
   public:
    using value_type = unsigned char;
    BufferSequence(uint32_t num_buf, int64_t size);
    BufferSequence(void* linear_data, uint32_t num_buf, int64_t size);
    ~BufferSequence();
    value_type** GetRawPtr();

   private:
    void InitMem();
    uint32_t num_buf_;
    std::size_t size_;
    value_type** buf_;
    value_type* linear_data_;
    bool delete_linear_data_;
    // If we want to verify this checksum,
    // use python package crc32c: https://pypi.org/project/crc32c/
    Hdfs::Internal::HWCrc32c crc_;
};

class Tinker {
   public:
    Tinker(ErasureCodeParameters parameters);
    ~Tinker() {}

    void SetErasuredByAvaliableIndex(const std::vector<int>& indexes);
    int64_t Decode(BufferSequence& buf_seq_dst, BufferSequence& buf_seq_src,
                   const std::vector<int> indexes);
    BoostBuffers MakeBoostBuffers(BufferSequence& buf_seq_decoded,
                                  BufferSequence& buf_seq_src,
                                  const std::vector<int> indexes);
    uint32_t GetObjChecksum() { return backend_file_checksum_; }

   private:
    ErasureCodeParameters ec_parameters_;
    IsalEncoder encoder_;
    IsalDecoder decoder_;
    BufferSequence data_units_;
    BufferSequence parity_units_;
    std::vector<int> erased_;
    std::vector<unsigned char*> available_data_;
    uint32_t backend_file_checksum_;

    friend class ECReadStreambuf;
};
typedef std::shared_ptr<Tinker> TinkerPtr;

}  // namespace bytecool_sdk
