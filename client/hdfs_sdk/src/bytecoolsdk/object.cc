#include "bytecoolsdk/object.h"

#include <fstream>
#include <iostream>

#include "bytecoolsdk/application/ec_read_streambuf.h"
#include "bytecoolsdk/application/hdfs_io_service.h"
#include "bytecoolsdk/common/log.h"
#include "bytecoolsdk/fs/backend_fs.h"
#include "bytecoolsdk/meta/dfs_meta_store.h"
#include "bytecoolsdk/meta/meta_manager.h"
#include "monitor/macro.h"

namespace bytecool_sdk {

Result ByteCoolObject::Open(const std::string& hdfs_path) {
    hdfs_path_ = hdfs_path;
    if (meta_manager_ == nullptr) {
        // Init meta_manager (DFSClient).
        auto meta_store = std::make_shared<DFSMetaStore>(backend_fs_);
        if (meta_store->Update(hdfs_path_)) {
            meta_manager_ =
                std::make_shared<MetaManager>(meta_store, backend_fs_);
        } else {
            MonitorEmitBytecoolMeta("update_meta_error", 1);
            return Result::err(
                "Failed update bytecool meta from backend filesystem");
        }
    }

    ObjectInfo object_info;
    auto ok = meta_manager_->GetObject(ctx_, ObjectId(), object_info);
    if (!ok) {
        return ok;
    }

    // get the parent path of the object
    std::string path = object_info.object_name;
    std::string parent_path = path.substr(0, path.find_last_of('/'));

    ErasureCodeParameters suggested_ec_params;
    ok = meta_manager_->GetECParamsFromObjectInfo(ctx_, object_info,
                                                  &suggested_ec_params);
    if (!ok) {
        return ok;
    }

    hdfs_io_svc_ = std::make_shared<HDFSIOService>(
        suggested_ec_params, backend_fs_, meta_manager_, session_config_);

    ec_read_stream_ =
        hdfs_io_svc_->MakeECReadStreamWithOffsetPtr(ctx_, object_info, 0);
    if (!ec_read_stream_) {
        return Result::err("Cannot init ec read stream");
    }
    rdbuf((ec_read_stream_->buf_).get());

    return Result::ok();
}

Result ByteCoolObject::TryRangeRead(const int64_t& read_pos,
                                    const int32_t& read_len,
                                    const int32_t& read_timeout_ms,
                                    char* buffer) {
    return ec_read_stream_->TryRangeRead(read_pos, read_len, read_timeout_ms,
                                         buffer);
}

Result ByteCoolObject::Close() {
    if (ec_read_stream_ && !ec_read_stream_->Close()) {
        return Result::err("Failed to close stream");
    }
    return Result::ok();
}

Result ByteCoolObject::Status() const {
    if (ec_read_stream_->bad()) {
        return Result::err("Failed to read from stream");
    } else {
        return Result::ok();
    }
}

void ByteCoolObject::SetPreadMode(bool flag) {
    if (ec_read_stream_) {
        ec_read_stream_->SetPreadMode(flag);
    }
}

std::string ByteCoolObject::GetLastErrorMsg() const {
    if (ec_read_stream_) {
        return ec_read_stream_->GetLastErrorMsg();
    }
    return "";
}

std::string ByteCoolObject::GetHdfsPath() const { return hdfs_path_; }

}  // namespace bytecool_sdk
