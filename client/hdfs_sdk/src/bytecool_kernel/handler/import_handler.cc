#include "bytecool_kernel/handler/bytecool_handler.h"

// Open this comment when you want test import_modify_files.h
// #define TEST_IMPORT_MODIFY

namespace BYTECOOL::HANDLER {

Result ByteCoolHandler::WriteOneFile_(
    const std::shared_ptr<ARCHIVE::Archive>& archive,
    const std::string& file_path, uint64_t file_length, uint32_t* file_crc,
    uint64_t* shard_offset, uint64_t* shard_length) const {
    // 1. Init backend fs
    STORE::BackendFileSystem backend_file_system(OperatePermission::kReadOnly,
                                                 config_.backend_fs);
    Result ret = backend_file_system.Open(file_path);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Import handler open user file failed. path={} error={}",
                 file_path, ret));
        return Result::Error(ErrorCode::kHandlerOpenUserFileFailed,
                             ret.GetErrorMsg());
    }
    DEFER([&]() { backend_file_system.Close(); });

    // 2. Calculate the maximum length that a user file can read at one time
    ARCHIVE::CodingPolicy coding_policy = archive->Schema();
    uint64_t aligned_max_length_per_read =
        config_.handler_max_length_per_user_read /
        (coding_policy.data_num * coding_policy.chunk_size) *
        (coding_policy.data_num * coding_policy.chunk_size);
    if (aligned_max_length_per_read == 0) {
        aligned_max_length_per_read =
            coding_policy.data_num * coding_policy.chunk_size;
    }

    // 3. Write shard header to archive
    ret = archive->WriteHeader(file_length, config_.handler_write_timeout_ms);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Import handler write stripe header failed. path={} error={}",
                 archive->Path(), ret));
        return Result::Error(ErrorCode::kHandlerWriteStripeHeaderFailed,
                             ret.GetErrorMsg());
    }

    // cannot get shard offset before writing header because we may also write
    // volume header if this is the first file to be written
    *shard_offset = archive->Length() - sizeof(VOLUME::ShardHeader);

    // 4. Write shard data to archive
    *file_crc = 0;
    uint64_t read_offset = 0;
    while (read_offset < file_length) {
        // Read
        std::string read_data;
        uint64_t read_length =
            std::min(aligned_max_length_per_read, file_length - read_offset);
        ret = backend_file_system.Read(read_offset, read_length, false, false,
                                       &read_data);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format("Import handler read user file failed. path={} "
                             "offset={} length={} error={}",
                             file_path, read_offset, read_length, ret));
            return Result::Error(ErrorCode::kHandlerReadUserFileFailed,
                                 ret.GetErrorMsg());
        }
        *file_crc = UTILS::CRCUtils::Extend(*file_crc, read_data, read_length);

        // Encode
        UTILS::ECWorker ec_worker(coding_policy.data_num,
                                  coding_policy.parity_num,
                                  coding_policy.chunk_size);
        std::unordered_map<uint32_t, std::string> encode_data;
        int ec_ret = ec_worker.Encode(read_data, &encode_data);
        if (ec_ret != 0) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler encode user file failed. path={} error={}",
                     file_path, ec_ret));
            return Result::Error(ErrorCode::kHandlerEncodeFailed);
        }

        // Write stripe data
        ret = archive->WriteStripe(std::move(encode_data),
                                   config_.handler_write_timeout_ms);
        if (!ret) {
            BLOG(
                ERROR,
                fmt::format(
                    "Import handler write stripe data failed. path={} error={}",
                    archive->Path(), ret));
            return Result::Error(ErrorCode::kHandlerWriteStripeDataFailed,
                                 ret.GetErrorMsg());
        }

        // Update read_offset
        read_offset += read_length;
    }

    // 5. Write shard footer to archive
    ret = archive->WriteFooter(config_.handler_write_timeout_ms);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Import handler write stripe footer failed. path={} error={}",
                 archive->Path(), ret));
        return Result::Error(ErrorCode::kHandlerWriteStripeFooterFailed,
                             ret.GetErrorMsg());
    }

    // 6. Update shard length
    *shard_length = archive->Length() - *shard_offset;
    return Result::Ok();
}

Result ByteCoolHandler::Import(const ImportRequest& request,
                               ImportResponse* response) const {
    // 1. Check and gen thread context
    CHECK_HANDLER_IS_INITED;
    UTILS::GenerateThreadContext();

    // 2. Clear response and init backend meta system
    response->success_paths.clear();
    response->failed_paths.clear();
    STORE::BackendMetaSystem backend_meta_system(config_.backend_fs);
    // 3. Acquire archive from pool and alloc threads
    std::shared_ptr<ARCHIVE::Archive> write_archive;
    ARCHIVE::ArchiveWriteSelector write_archive_selector;
    write_archive_selector.cluster = request.cluster;
    write_archive_selector.tenant = request.tenant;
    write_archive_selector.partition = request.partition;
    write_archive_selector.coding_policy = request.coding_policy;
    write_archive_selector.total_length = 0;
    for (const auto& [_, length] : request.paths) {
        write_archive_selector.total_length += length;
    }
    Result ret = resource_scheduler_->AcquireArchive(
        RequestType::kImport, write_archive_selector, &write_archive);
    if (!ret) {
        for (const auto& [path, _] : request.paths) {
            response->failed_paths.emplace_back(
                path, Result::Error(ErrorCode::kHandlerImportNotExecuted));
        }
        BLOG(
            ERROR,
            fmt::format(
                "Import handler acquire write archive failed. selector={} error={}",
                write_archive_selector.ToString(), ret));
        return Result::Error(ErrorCode::kHandlerImportFailed,
                             ret.GetErrorMsg());
    }
    DEFER([&]() {
        if (write_archive != nullptr) {
            ret = resource_scheduler_->ReclaimArchive(RequestType::kImport,
                                                      write_archive);
            if (!ret) {
                BLOG(
                    WARN,
                    fmt::format(
                        "Import handler reclaim write archive failed. path={} error={}",
                        write_archive->Path(), ret));
            }
        }
    });
    response->archive_path = write_archive->Path();

    std::shared_ptr<ARCHIVE::Archive> validate_archive;
    ARCHIVE::ArchiveReadSelector validate_archive_selector;
    validate_archive_selector.path = write_archive->Path();
    validate_archive_selector.coding_policy = write_archive->Schema();
    validate_archive_selector.backend_handlers.clear();
    validate_archive_selector.backend_paths.clear();
    ret = resource_scheduler_->AcquireArchive(
        RequestType::kValidate, validate_archive_selector, &validate_archive);
    if (!ret) {
        for (const auto& [path, _] : request.paths) {
            response->failed_paths.emplace_back(
                path, Result::Error(ErrorCode::kHandlerImportNotExecuted));
        }
        BLOG(ERROR,
             fmt::format("Import handler acquire validate archive failed. "
                         "selector={} error={}",
                         validate_archive_selector.ToString(), ret));
        return Result::Error(ErrorCode::kHandlerImportFailed,
                             ret.GetErrorMsg());
    }
    DEFER([&]() {
        if (validate_archive != nullptr) {
            ret = resource_scheduler_->ReclaimArchive(RequestType::kValidate,
                                                      validate_archive);
            if (!ret) {
                BLOG(WARN,
                     fmt::format("Import handler reclaim validate archive "
                                 "failed. path={} error={}",
                                 validate_archive->Path(), ret));
            }
        }
    });

    // 4. Import and validate
    Result import_one_file_ret = Result::Ok();
    uint32_t import_one_file_index;
    for (import_one_file_index = 0;
         import_one_file_index < request.paths.size();
         ++import_one_file_index) {
        // Get file path and length
        std::string file_path = request.paths[import_one_file_index].first;
        uint64_t file_length = request.paths[import_one_file_index].second;

        // Check import type
        bool is_shadow_file = false;
        STORE::ShadowFileInfo shadow_file_info;
        ret = backend_meta_system.IsShadowFile(file_path, &is_shadow_file,
                                               &shadow_file_info);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler get file type failed. path={} error={}",
                     file_path, ret));
            import_one_file_ret = Result::Error(
                ErrorCode::kHandlerImportGetFileTypeFailed, ret.GetErrorMsg());
            response->failed_paths.emplace_back(file_path, import_one_file_ret);
            break;
        }

        if (request.import_type == ImportType::kImport) {  // Import
            if (is_shadow_file) {
                BLOG(
                    ERROR,
                    fmt::format(
                        "Import handler file type is shadow when import. path={}",
                        file_path));
                response->failed_paths.emplace_back(
                    file_path,
                    Result::Error(
                        ErrorCode::kHandlerImportCheckFileIsNormalFailed));
                continue;
            }
        } else {  // ReImport
            if (!is_shadow_file) {
                BLOG(
                    ERROR,
                    fmt::format(
                        "Import handler file type is normal when reimport. path={}",
                        file_path));
                import_one_file_ret = Result::Error(
                    ErrorCode::kHandlerImportCheckFileIsShadowFailed);
                response->failed_paths.emplace_back(file_path,
                                                    import_one_file_ret);
                break;
            }
            if (shadow_file_info.payload.task_id == request.task_id) {
                response->success_paths.emplace_back(file_path);
                BLOG(INFO, fmt::format(
                               "Import handler reimport one file success with "
                               "reason of same id. path={} task_id={}",
                               file_path, request.task_id));
                continue;
            }
        }

        // Prepare
        ret = STORE::BackendMetaSystem::PrepareCommitSlice(
            write_archive->HyperFile(), file_path);
        if (!ret) {
            BLOG(
                ERROR,
                fmt::format(
                    "Import handler prepare commit slice failed. path={} error={}",
                    file_path, ret));
            import_one_file_ret =
                Result::Error(ErrorCode::kHandlerImportPrepareCommitSliceFailed,
                              ret.GetErrorMsg());
            response->failed_paths.emplace_back(file_path, import_one_file_ret);
            break;
        }

        // Write
        uint32_t file_crc = 0;
        uint64_t shard_offset = 0, shard_length = 0;
        ret = WriteOneFile_(write_archive, file_path, file_length, &file_crc,
                            &shard_offset, &shard_length);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler write internal failed. path={} error={}",
                     file_path, ret));
            import_one_file_ret = Result::Error(
                ErrorCode::kHandlerImportWriteOneFileFailed, ret.GetErrorMsg());
            response->failed_paths.emplace_back(file_path, import_one_file_ret);
            break;
        }
#ifdef TEST_IMPORT_MODIFY
        BLOG(INFO,
             fmt::format(
                 "Import handler write one file success and sleep 5s. path={}",
                 file_path));
        std::this_thread::sleep_for(std::chrono::seconds(5));
#endif

        // Validate
        uint32_t calculate_crc = 0;
        ret =
            ValidateOneFile_(validate_archive, shard_offset, shard_length,
                             file_length, file_crc, true, true, &calculate_crc);
        if (!ret) {
            BLOG(
                ERROR,
                fmt::format(
                    "Import handler validate internal failed. path={} error={}",
                    file_path, ret));
            import_one_file_ret =
                Result::Error(ErrorCode::kHandlerImportValidateOneFileFailed,
                              ret.GetErrorMsg());
            response->failed_paths.emplace_back(file_path, import_one_file_ret);
            break;
        }

        // Commit
        CodingPolicy write_archive_coding_policy = write_archive->Schema();
        uint32_t write_archive_volume_num =
            write_archive_coding_policy.data_num +
            write_archive_coding_policy.parity_num;
        ret = backend_meta_system.CommitSlice(
            write_archive->HyperFile(), shard_offset,
            shard_length * write_archive_volume_num, file_crc, request.task_id);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler commit slice failed. path={} error={}",
                     file_path, ret));
            import_one_file_ret = Result::Error(
                ErrorCode::kHandlerImportCommitSliceFailed, ret.GetErrorMsg());
            response->failed_paths.emplace_back(file_path, import_one_file_ret);
            break;
        }

        // Record
        import_one_file_ret = Result::Ok();
        response->success_paths.emplace_back(file_path);
    }

    // 5. Record files not imported if necessary
    if (!import_one_file_ret) {
        for (uint32_t i = (import_one_file_index + 1); i < request.paths.size();
             ++i) {
            response->failed_paths.emplace_back(
                request.paths[i].first,
                Result::Error(ErrorCode::kHandlerImportNotExecuted));
        }
        return Result::Ok();
    }

    return Result::Ok();
}

Result ByteCoolHandler::XReimport(const XReimportRequest& request,
                                  ImportResponse* response) const {
    // 1. Check and gen thread context
    CHECK_HANDLER_IS_INITED;
    UTILS::GenerateThreadContext();
    // 2. Clear response and init backend meta system
    response->success_paths.clear();
    response->failed_paths.clear();
    STORE::BackendMetaSystem backend_meta_system(config_.backend_fs);
    // 3. Acquire archive from pool and alloc threads
    std::shared_ptr<ARCHIVE::Archive> write_archive;
    ARCHIVE::ArchiveWriteSelector write_archive_selector;
    write_archive_selector.cluster =
        request.dst_cluster;  // should be dstbackend's cluster
    write_archive_selector.tenant = request.tenant;
    write_archive_selector.partition = request.partition;
    write_archive_selector.coding_policy = request.coding_policy;
    write_archive_selector.total_length = 0;
    for (const auto& [_, length] : request.src_paths) {
        write_archive_selector.total_length += length;
    }
    Result ret = resource_scheduler_->AcquireArchive(
        RequestType::kImport, write_archive_selector, &write_archive);
    if (!ret) {
        for (const auto& [path, _] : request.src_paths) {
            response->failed_paths.emplace_back(
                path, Result::Error(ErrorCode::kHandlerImportNotExecuted));
        }
        BLOG(
            ERROR,
            fmt::format(
                "Import handler acquire write archive failed. selector={} error={}",
                write_archive_selector.ToString(), ret));
        return Result::Error(ErrorCode::kHandlerImportFailed,
                             ret.GetErrorMsg());
    }
    DEFER([&]() {
        if (write_archive != nullptr) {
            ret = resource_scheduler_->ReclaimArchive(RequestType::kImport,
                                                      write_archive);
            if (!ret) {
                BLOG(
                    WARN,
                    fmt::format(
                        "Import handler reclaim write archive failed. path={} error={}",
                        write_archive->Path(), ret));
            }
        }
    });
    response->archive_path = write_archive->Path();

    std::shared_ptr<ARCHIVE::Archive> validate_archive;
    ARCHIVE::ArchiveReadSelector validate_archive_selector;
    validate_archive_selector.path = write_archive->Path();
    validate_archive_selector.coding_policy = write_archive->Schema();
    validate_archive_selector.backend_handlers.clear();
    validate_archive_selector.backend_paths.clear();
    ret = resource_scheduler_->AcquireArchive(
        RequestType::kValidate, validate_archive_selector, &validate_archive);
    if (!ret) {
        for (const auto& [path, _] : request.src_paths) {
            response->failed_paths.emplace_back(
                path, Result::Error(ErrorCode::kHandlerImportNotExecuted));
        }
        BLOG(ERROR,
             fmt::format("Import handler acquire validate archive failed. "
                         "selector={} error={}",
                         validate_archive_selector.ToString(), ret));
        return Result::Error(ErrorCode::kHandlerImportFailed,
                             ret.GetErrorMsg());
    }
    DEFER([&]() {
        if (validate_archive != nullptr) {
            ret = resource_scheduler_->ReclaimArchive(RequestType::kValidate,
                                                      validate_archive);
            if (!ret) {
                BLOG(WARN,
                     fmt::format("Import handler reclaim validate archive "
                                 "failed. path={} error={}",
                                 validate_archive->Path(), ret));
            }
        }
    });

    // 4. Import and validate
    Result import_one_file_ret = Result::Ok();
    uint32_t import_one_file_index;
    for (import_one_file_index = 0;
         import_one_file_index < request.src_paths.size();
         ++import_one_file_index) {
        // Get file path and length
        std::string src_file_path =
            request.src_paths[import_one_file_index].first;
        std::string dst_file_path = request.dst_paths[import_one_file_index];

        uint64_t file_length = request.src_paths[import_one_file_index].second;

        // Check import type
        bool is_shadow_file = false;
        STORE::ShadowFileInfo shadow_file_info;
        ret = backend_meta_system.IsShadowFile(
            src_file_path, &is_shadow_file,
            &shadow_file_info);  // check src path is a shadowfile
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler get file type failed. path={} error={}",
                     src_file_path, ret));
            import_one_file_ret = Result::Error(
                ErrorCode::kHandlerImportGetFileTypeFailed, ret.GetErrorMsg());
            response->failed_paths.emplace_back(src_file_path,
                                                import_one_file_ret);
            break;
        }

        if (!is_shadow_file) {
            BLOG(
                ERROR,
                fmt::format(
                    "Import handler file type is normal when reimport. path={}",
                    src_file_path));
            import_one_file_ret =
                Result::Error(ErrorCode::kHandlerImportCheckFileIsShadowFailed);
            response->failed_paths.emplace_back(src_file_path,
                                                import_one_file_ret);
            break;
        }
        if (shadow_file_info.payload.task_id == request.task_id) {
            response->success_paths.emplace_back(src_file_path);
            BLOG(INFO,
                 fmt::format("Import handler reimport one file success with "
                             "reason of same id. path={} task_id={}",
                             src_file_path, request.task_id));
            continue;
        }

        // Prepare
        // should set dst path as we need to link at dstbackend (dst_file_path =
        // /BACKEND_HINT/dstbackend/<dst_path>)
        ret = STORE::BackendMetaSystem::PrepareCommitSlice(
            write_archive->HyperFile(), dst_file_path, true);
        if (!ret) {
            BLOG(
                ERROR,
                fmt::format(
                    "Import handler prepare commit slice failed. path={} error={}",
                    src_file_path, ret));
            import_one_file_ret =
                Result::Error(ErrorCode::kHandlerImportPrepareCommitSliceFailed,
                              ret.GetErrorMsg());
            response->failed_paths.emplace_back(src_file_path,
                                                import_one_file_ret);
            break;
        }

        // Write
        uint32_t file_crc = 0;
        uint64_t shard_offset = 0, shard_length = 0;
        // write_archive is at dstbackend (no need for backend hint)
        // file_path is src_file_path to read from (src_file_path =
        // /BACKEND_HINT/srcbackend/<src_path>)
        ret = WriteOneFile_(write_archive, src_file_path, file_length,
                            &file_crc, &shard_offset, &shard_length);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler write internal failed. path={} error={}",
                     src_file_path, ret));
            import_one_file_ret = Result::Error(
                ErrorCode::kHandlerImportWriteOneFileFailed, ret.GetErrorMsg());
            response->failed_paths.emplace_back(src_file_path,
                                                import_one_file_ret);
            break;
        }
#ifdef TEST_IMPORT_MODIFY
        BLOG(INFO,
             fmt::format(
                 "Import handler write one file success and sleep 5s. path={}",
                 file_path));
        std::this_thread::sleep_for(std::chrono::seconds(5));
#endif

        // Validate
        uint32_t calculate_crc = 0;
        ret =
            ValidateOneFile_(validate_archive, shard_offset, shard_length,
                             file_length, file_crc, true, true, &calculate_crc);
        if (!ret) {
            BLOG(
                ERROR,
                fmt::format(
                    "Import handler validate internal failed. path={} error={}",
                    src_file_path, ret));
            import_one_file_ret =
                Result::Error(ErrorCode::kHandlerImportValidateOneFileFailed,
                              ret.GetErrorMsg());
            response->failed_paths.emplace_back(src_file_path,
                                                import_one_file_ret);
            break;
        }

        // Commit
        CodingPolicy write_archive_coding_policy = write_archive->Schema();
        uint32_t write_archive_volume_num =
            write_archive_coding_policy.data_num +
            write_archive_coding_policy.parity_num;
        ret = backend_meta_system.CommitSlice(
            write_archive->HyperFile(), shard_offset,
            shard_length * write_archive_volume_num, file_crc, request.task_id);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Import handler commit slice failed. path={} error={}",
                     src_file_path, ret));
            import_one_file_ret = Result::Error(
                ErrorCode::kHandlerImportCommitSliceFailed, ret.GetErrorMsg());
            response->failed_paths.emplace_back(src_file_path,
                                                import_one_file_ret);
            break;
        }

        // Record
        import_one_file_ret = Result::Ok();
        response->success_paths.emplace_back(src_file_path);
    }

    // 5. Record files not imported if necessary
    if (!import_one_file_ret) {
        for (uint32_t i = (import_one_file_index + 1);
             i < request.src_paths.size(); ++i) {
            response->failed_paths.emplace_back(
                request.src_paths[i].first,
                Result::Error(ErrorCode::kHandlerImportNotExecuted));
        }
        return Result::Ok();
    }

    return Result::Ok();
}

}  // namespace BYTECOOL::HANDLER