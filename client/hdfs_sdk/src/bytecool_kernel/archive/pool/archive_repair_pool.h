#pragma once

#include "bytecool_kernel/archive/pool/archive_pool.h"

namespace BYTECOOL::ARCHIVE {

class ArchiveRepairPool : public ArchivePool {
   public:
    ArchiveRepairPool() = delete;
    explicit ArchiveRepairPool(const Config& config)
        : ArchivePool(config.archive_repair_pool_capacity), config_(config) {}
    ~ArchiveRepairPool() override = default;

    Result Init() override { return Result::Ok(); }

    Result Close() override { return Result::Ok(); }

    // TODO(mkx, P1): improve repair?
    Result Acquire(const ArchiveSelector& archive_selector,
                   std::shared_ptr<ARCHIVE::Archive>* archive) override {
        auto archive_repair_selector =
            dynamic_cast<const ArchiveRepairSelector&>(archive_selector);
        std::string archive_path = archive_repair_selector.path;
        CodingPolicy archive_coding_policy =
            archive_repair_selector.coding_policy;

        *archive = ARCHIVE::Archive::make_archive(
            config_, archive_path, archive_coding_policy,
            OperatePermission::kRepairOnly);
        Result ret =
            (*archive)->OpenForWrite(archive_repair_selector.specific_indexes);
        if (!ret) {
            BLOG(
                ERROR,
                fmt::format(
                    "Acquire archive from repair pool failed. selector={} error={}",
                    archive_selector.ToString(), ret));
            return Result::Error(ErrorCode::kArchiveRepairPoolOpenArchiveFailed,
                                 ret.GetErrorMsg());
        }

        return Result::Ok();
    }

    Result Reclaim(const std::shared_ptr<ARCHIVE::Archive>& archive) override {
        Result ret = archive->Close();
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Reclaim archive to repair pool failed. path={} error={}",
                     archive->Path(), ret));
            return Result::Error(
                ErrorCode::kArchiveRepairPoolCloseArchiveFailed,
                ret.GetErrorMsg());
        }

        return Result::Ok();
    }

   private:
    Config config_{};
};

}  // namespace BYTECOOL::ARCHIVE