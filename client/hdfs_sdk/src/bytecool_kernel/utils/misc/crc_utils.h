#pragma once

#include <isa-l/crc.h>
#include <zlib.h>

#include <string>

namespace BYTECOOL::UTILS {

class CRCUtils {
   public:
    static uint32_t Combine(uint32_t crc1, uint32_t crc2, size_t len2) {
        return crc32_combine(crc1, crc2, (uint32_t)len2);
    }

    static inline uint32_t Extend(const uint32_t& crc, const std::string& data,
                                  size_t len) {
        return crc32_gzip_refl(crc, (const unsigned char*)data.data(), len);
    }

    static inline uint32_t Extend(const uint32_t& crc,
                                  const std::string_view& data, size_t len) {
        return crc32_gzip_refl(crc, (const unsigned char*)data.data(), len);
    }

    static inline uint32_t Value(const std::string& data, size_t n) {
        return Extend(0, data, n);
    }

    static inline uint32_t Value(const std::string& data) {
        return Extend(0, data, data.length());
    }

    static inline uint32_t Value(const std::string_view& data, size_t n) {
        return Extend(0, data, n);
    }

    static inline uint32_t Value(const std::string_view& data) {
        return Extend(0, data, data.length());
    }
};

}  // namespace BYTECOOL::UTILS