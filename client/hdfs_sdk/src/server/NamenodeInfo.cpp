/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "NamenodeInfo.h"

#include <sstream>
#include <string>
#include <vector>

#include "common/Exception.h"
#include "common/ExceptionInternal.h"
#include "common/StringUtil.h"
#include "common/XmlConfig.h"
#include "server/ServiceDiscovery.h"

using namespace Hdfs::Internal;

namespace Hdfs {

const char* NamenodeInfo::DFS_NAMESERVICES = "dfs.nameservices";
const char* NamenodeInfo::DFS_NAMENODE_HA = "dfs.ha.namenodes";
const char* NamenodeInfo::DFS_NAMENODE_RPC_ADDRESS_KEY =
    "dfs.namenode.rpc-address";
const char* NamenodeInfo::DFS_NAMENODE_HTTP_ADDRESS_KEY =
    "dfs.namenode.http-address";
const char* NamenodeInfo::DFS_NAMENODE_AUTO_FAILOVER_KEY =
    "dfs.ha.automatic-failover.enabled";
/*
Override priority: Env var > hdfsBuilderConfSetStr > hdfs-site.xml

"off": The configuration address of hdfs-site.xml is used by default.

"global": Try to use the global consul name, and an error is
        returned if it fails.

"match": tries to find a specific service consul if
        there is an ns consul The name must be used. If ns does not exist, try
        to roll back the local configuration address.
*/
const char* NamenodeInfo::DFS_NNPROXY_CONSUL_ENABLE_KEY =
    "dfs.client.nnproxy.consul.enablemode";
const char* NamenodeInfo::DFS_NNPROXY_CONSUL_NAME_KEY =
    "dfs.client.nnproxy.consul.name";

NamenodeInfo::NamenodeInfo() {}

std::string tryGetConsulName(const std::string& mode,
                             const std::string& global_name,
                             const std::string& service,
                             const std::string& name_key, const Config& conf) {
    std::string consul_name;

    // This means that a global consul is forcibly specified.
    if (mode == "global") {
        return global_name;
    }

    // This means trying to find a specific HDFS cluster.
    if (mode == "match") {
        try {
            consul_name = std::string(conf.getString(name_key + "." + service));
        } catch (const HdfsConfigNotFound& e) {
            HDFSLOG(INFO,
                    "[WARN] Consul mode: {}, but can't find {} consul name",
                    mode, service);
        }
    }

    return consul_name;
}

std::pair<std::string, std::vector<std::string>> splitConsulName(
    const std::string& fullName) {
    auto colonPos = fullName.rfind(':');
    if (colonPos == std::string::npos) {
        return {fullName, {}};
    }

    // parse consul name
    std::string firstPart = fullName.substr(0, colonPos);

    // parse vdc list
    std::string secondPart = fullName.substr(colonPos + 1);
    std::vector<std::string> secondPartVec;
    std::istringstream ss(secondPart);
    std::string token;

    while (std::getline(ss, token, ',')) {
        secondPartVec.push_back(token);
    }

    return {firstPart, secondPartVec};
}

std::vector<NamenodeInfo> NamenodeInfo::TryGetNNInfoFromConsul(
    std::shared_ptr<Internal::SessionConfig> sconf, const std::string& service,
    const std::string& name_key, const Config& conf) {
    auto consul_mode = sconf->getNNConsulMode();

    std::vector<NamenodeInfo> retval;
    if (consul_mode == "match" || consul_mode == "global") {
        auto consul_name = tryGetConsulName(
            consul_mode, sconf->getNNConsulName(), service, name_key, conf);
        HDFSLOG(DEBUG, "[consul] service: {}, find consul name: {}, mode: {}",
                service, consul_name, consul_mode);
        if (!consul_name.empty()) {
            auto expected_find_num = sconf->getNNConsulExpectedNum() < 1
                                         ? 1
                                         : sconf->getNNConsulExpectedNum();
            auto [firstPart, secondPartVec] = splitConsulName(consul_name);
            HDFSLOG(DEBUG, "[consul] service: {}, connect consul: {}, vdc: {}",
                    service, firstPart, fmt::join(secondPartVec, " "));
            auto addrs = HdfsEnv::Get()->GetConsulService()->GetConsulAddrs(
                firstPart, expected_find_num, secondPartVec);
            if (!addrs.empty()) {
                retval.resize(addrs.size());
                std::stringstream ss;
                for (size_t i = 0; i < addrs.size(); i++) {
                    retval[i].setRpcAddr(addrs[i]);
                    retval[i].setEnableForceFailover(true);
                    ss << addrs[i] << " ";
                }
                HDFSLOG(
                    INFO,
                    "Consul mode: {}, name: {}, vdc size: {}, got NN addrs: {}",
                    consul_mode, firstPart, secondPartVec.size(), ss.str());
                return retval;
            }
        }

        // if `global` return error.
        if (!sconf->nnproxy_consul_allow_failover_config_ ||
            consul_mode == "global") {
            THROW(
                HdfsConfigInvalid,
                "Try to connect cluster: %s, %s is set to %s, but failed to find "
                "addrs from consul: %s",
                service.data(), DFS_NNPROXY_CONSUL_ENABLE_KEY,
                consul_mode.data(), consul_name.data());
        }
    }
    // Consul not enabled
    HDFSLOG(INFO, "Skip Consul lookup, mode: {}, use local configs",
            consul_mode);
    return retval;
}

std::vector<NamenodeInfo> NamenodeInfo::GetHANamenodeInfo(
    const std::string& service, const Config& conf,
    std::shared_ptr<Internal::SessionConfig> sconf) {
    std::vector<NamenodeInfo> retval;

    retval = TryGetNNInfoFromConsul(sconf, service, DFS_NNPROXY_CONSUL_NAME_KEY,
                                    conf);
    if (!retval.empty()) {
        return retval;
    }

    std::string strNameNodes = StringTrim(
        conf.getString(std::string(DFS_NAMENODE_HA) + "." + service));
    std::vector<std::string> nns = StringSplit(strNameNodes, ",");
    retval.resize(nns.size());

    for (size_t i = 0; i < nns.size(); ++i) {
        std::string dfsRpcAddress =
            StringTrim(std::string(DFS_NAMENODE_RPC_ADDRESS_KEY) + "." +
                       service + "." + StringTrim(nns[i]));
        std::string dfsHttpAddress =
            StringTrim(std::string(DFS_NAMENODE_HTTP_ADDRESS_KEY) + "." +
                       service + "." + StringTrim(nns[i]));
        retval[i].setRpcAddr(StringTrim(conf.getString(dfsRpcAddress, "")));
        retval[i].setHttpAddr(StringTrim(conf.getString(dfsHttpAddress, "")));
    }

    // When namenode size equal 1, check if auto failover config enabled(eg.
    // nnrpoxy vip). If auto failover config not set or set false, HA will not
    // be triggered.
    if (retval.size() == 1) {
        try {
            std::string key =
                std::string(DFS_NAMENODE_AUTO_FAILOVER_KEY) + "." + service;
            retval[0].setEnableForceFailover(conf.getBool(key.c_str()));
        } catch (const HdfsConfigNotFound& e) {
            retval[0].setEnableForceFailover(false);
        }
    }

    return retval;
}
}  // namespace Hdfs
