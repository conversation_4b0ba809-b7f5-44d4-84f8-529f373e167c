#include "rpc/ByteTrace.h"

#include <chrono>
#include <string>

#include "ByteTrace.h"
#include "HdfsVersion.h"
#include "btrace.pb.h"
#include "common/StringUtil.h"

namespace Hdfs {
namespace Internal {

static const char* kByteTraceDefaultYarnAppId = "YARN_APP_ID";
static const char* kByteTraceDefaultArnoldJobId = "ARNOLD_JOB_ID";
static const char* kByteTraceDefaultPlatform =
    "CPP_HDFS_BTRACE_DEFAULT_PLATFORM";
static const char* kByteTraceDefaultUserAgent = "CPP_HDFS_DEFAULT_USER_AGENT";
static const char* kByteTraceContainerIDEnvName = "CONTAINER_ID";

void ByteTraceHelper::setValFromEnv(std::string& val, const char* envKey) {
    auto envVal = getenv(envKey);
    if (envVal != nullptr) {
        val.assign(envVal);
    }
    val = StringTrim(val);
}

template <typename HeaderTyp>
void ByteTraceHelper::SetHeaderBySessionConfigBTrace(
    const SessionConfigBTrace& confBTrace, HeaderTyp& header) {
    ::Hdfs::Internal::TraceBaggageProto* baggage;

    auto custom_appid = StringTrim(confBTrace.custom_appid_);
    if (custom_appid.empty()) {
        custom_appid = defaultYarnAppId;
    }
    if (custom_appid.empty()) {
        custom_appid = defaultArnoldJobId;
    }

    if (!custom_appid.empty()) {
        baggage = header.add_baggages();
        baggage->set_name("appid");
        baggage->set_value(custom_appid);
    }

    auto custom_jobid = StringTrim(confBTrace.custom_job_);
    if (custom_jobid.empty()) {
        custom_jobid = custom_appid;
    }

    if (!custom_jobid.empty()) {
        baggage = header.add_baggages();
        baggage->set_name("job");
        baggage->set_value(custom_jobid);
    }

    if (!confBTrace.custom_apptype_.empty()) {
        baggage = header.add_baggages();
        baggage->set_name("app_type");
        baggage->set_value(confBTrace.custom_apptype_);
    }

    if (!confBTrace.custom_grade_.empty()) {
        baggage = header.add_baggages();
        baggage->set_name("grade");
        baggage->set_value(confBTrace.custom_grade_);
    }

    std::string platform;
    if (!confBTrace.custom_platform_.empty()) {
        platform = confBTrace.custom_platform_;
    }

    if (platform.empty() && !defaultPlatform.empty()) {
        platform = defaultPlatform;
    }

    if (platform.empty()) {
        platform = std::string("hdfs_cpp_sdk") + HDFS_CPP_CLIENT_VERSION;
    }

    baggage = header.add_baggages();
    baggage->set_name("platform");
    baggage->set_value(platform);

    std::string version = HDFS_CPP_CLIENT_VERSION;
    baggage = header.add_baggages();
    baggage->set_name("sdk_version");
    baggage->set_value(version);

    if (!confBTrace.identify_user_.empty()) {
        baggage = header.add_baggages();
        baggage->set_name("user");
        baggage->set_value(confBTrace.identify_user_);
    }

    if (!confBTrace.identify_psm_.empty()) {
        baggage = header.add_baggages();
        baggage->set_name("psm");
        baggage->set_value(confBTrace.identify_psm_);
    }
}

ByteTraceHelper::ByteTraceHelper() {
    setValFromEnv(defaultPlatform, kByteTraceDefaultPlatform);
    if (defaultPlatform.empty()) {
        setValFromEnv(defaultPlatform, "CPP_HDFS_TASK_FAMILY");
    }
    setValFromEnv(defaultYarnAppId, kByteTraceDefaultYarnAppId);
    setValFromEnv(defaultArnoldJobId, kByteTraceDefaultArnoldJobId);
    setValFromEnv(defaultUserAgent, kByteTraceDefaultUserAgent);
    setValFromEnv(containerId, kByteTraceContainerIDEnvName);
    return;
}

ByteTraceHelper* ByteTraceHelper::Instance() {
    static ByteTraceHelper instance;
    return &instance;
}

void ByteTraceHelper::SetRpcHeaderBySessionConfigBTrace(
    RpcRemoteCallPtr& remoteCall, const SessionConfigBTrace& confBTrace,
    RpcRequestHeaderProto& header) {
    if (!defaultUserAgent.empty()) {
        auto traceinfo = header.mutable_traceinfo();
        btrace::ByteTraceId btid;
        btid.set_user_agent(defaultUserAgent);
        auto btidStr = traceinfo->mutable_btid();
        btidStr->assign(btid.SerializeAsString());
    }

    auto baggage = header.add_baggages();
    baggage->set_name("callId");
    baggage->set_value(std::to_string(remoteCall->getIdentity()));

    // refer to hadoop-dev  org.apache.hadoop.ipc.Client.java
    // baggage.put(ProtoUtil.VERSION_KEY, Client.CLIENT_VERSION))
    baggage = header.add_baggages();
    baggage->set_name("version");
    baggage->set_value("2.0.0");

    std::chrono::milliseconds ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch());
    baggage = header.add_baggages();
    baggage->set_name("emitTime");
    baggage->set_value(std::to_string(ms.count()));

    SetHeaderBySessionConfigBTrace<RpcRequestHeaderProto>(confBTrace, header);
}

void ByteTraceHelper::SetBaseHeaderProtoBySessionConfigBTrace(
    const std::string& path, const SessionConfigBTrace& confBTrace,
    BaseHeaderProto& header) {
    SetHeaderBySessionConfigBTrace<BaseHeaderProto>(confBTrace, header);
    if (!path.empty()) {
        auto baggage = header.add_baggages();
        baggage->set_name("path");
        baggage->set_value(path);
    }
}

}  // namespace Internal
}  // namespace Hdfs
