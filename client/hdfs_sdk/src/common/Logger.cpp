/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "common/Logger.h"

#include <fmt/format.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <stdlib.h>
#include <sys/time.h>
#include <unistd.h>

#include <cstdlib>
#include <exception>
#include <filesystem>
#include <iostream>
#include <vector>

#include "LogTtl.h"
#include "common/assert.h"
#include "monitor/byted/common.h"

namespace Hdfs {
namespace Internal {

using LogTypeBitmap = uint32_t;
static constexpr LogTypeBitmap kFileLogMask = 0x1;
static constexpr LogTypeBitmap kStdoutLogMask = 0x2;
static constexpr LogTypeBitmap kStderrLogMask = 0x4;

// The enviroment variable name for log dir.
const char* kLogDirEnvKey = "CPP_HDFS_LOG_DIR";
const char* kLogTtlSecEnvKey = "CPP_HDFS_LOG_TTL_SEC";
const char* kLogMaxFileSize = "CPP_HDFS_LOG_MAX_SIZE";
const char* kLogMaxFileNum = "CPP_HDFS_LOG_MAX_NUM";
// The enviroment variable name for log level, supported levels are:
// debug, info, warn, error
static const char* kLogLevelEnvKey = "CPP_HDFS_LOG_LEVEL";
// The enviroment variable name for log type, supported types are:
// file, stdout, stderr, none.
// Users can also set multiple log types split by `,`. For example,
// `file,stdout` means the logs will print to both file and stdout.
static const char* kLogTypeEnvKey = "CPP_HDFS_LOG_TYPE";

// Default log directory, used when the above env is not set.
const std::string kDefaultLogDir = "/var/log/tiger/native_hdfs_client";
const static std::string kLogFileName = "native_hdfs_client.log";
const static std::string kDefaultLogLevel = "info";
const static std::string kDefaultLogType = "file";
const static char kLogTypeDelimiter = ',';

// Each log file is at most 256MB.
constexpr static size_t kDefaultLogMaxFileSize = 256 * 1024 * 1024;
// There are at most 20 log files.
constexpr static size_t kDefaultLogMaxFileNum = 20;

Logger::Logger() { InitLogger(); }
Logger::~Logger() { logttl_ = nullptr; }

spdlog::logger* Logger::GetRaw() const { return logger_.get(); }

bool Logger::ShouldLog(LogLevel::level_enum level) {
    return spdlog::should_log(level);
}

static spdlog::level::level_enum ParseLogLevel(const std::string& level) {
    if (level == "debug") {
        return spdlog::level::debug;
    } else if (level == "info") {
        return spdlog::level::info;
    } else if (level == "warn") {
        return spdlog::level::warn;
    } else if (level == "error") {
        return spdlog::level::err;
    } else {
        std::cerr << "Unknown log level: " << level
                  << ". Use default log level: info";
        // default return info
        return spdlog::level::info;
    }
}

static LogTypeBitmap ParseLogType(const std::string& type,
                                  LogTypeBitmap bitmap) {
    if (type == "file") {
        bitmap |= kFileLogMask;
    } else if (type == "stdout") {
        bitmap |= kStdoutLogMask;
    } else if (type == "stderr") {
        bitmap |= kStderrLogMask;
    } else if (type == "none") {
        bitmap = 0;
    } else {
        // Unknow log type
        std::cerr << "Ignore unknown log type: " << type << std::endl;
    }
    return bitmap;
}

static LogTypeBitmap ParseLogTypes(const std::string& types) {
    size_t start = 0;
    size_t end = 0;
    LogTypeBitmap res = 0;
    for (; end < types.size(); end++) {
        if (types[end] != kLogTypeDelimiter) {
            continue;
        }
        if (start < end) {
            std::string type_str = types.substr(start, end - start);
            res = ParseLogType(type_str, res);
        }
        start = end + 1;
    }
    if (start < end) {
        std::string type_str = types.substr(start, end - start);
        res = ParseLogType(type_str, res);
    }
    return res;
}

std::vector<spdlog::sink_ptr> Logger::InitFileLogSinks(
    spdlog::level::level_enum level) {
    std::vector<spdlog::sink_ptr> sinks;
    std::error_code err_code;
    const char* logdir_env = getenv(kLogDirEnvKey);
    if (logdir_env == nullptr || (*logdir_env) == 0) {
        logdir_ = kDefaultLogDir;
    } else {
        logdir_ = std::string(logdir_env);
    }

    const int64_t log_file_num =
        getNumericFromConfKey(kLogMaxFileNum, kDefaultLogMaxFileNum);

    const int64_t log_max_file_size =
        getNumericFromConfKey(kLogMaxFileSize, kDefaultLogMaxFileSize);

    // std::filesystem::create_directories will return;
    //   1. true when the dir did not exist and be created successfully
    //   2. false when fail to create this dir:
    //     2.1. err_code.value() will be 0 if the dir has existed
    //     2.2. err_code.value() will NOT be 0 if the dir not exist but
    //     can not be created.
    std::filesystem::create_directories(logdir_, err_code);
    err_code.clear();
    auto fst = std::filesystem::status(logdir_, err_code);
    if ((fst.type() != std::filesystem::file_type::directory) ||
        (access(logdir_.c_str(), R_OK | W_OK | X_OK) != 0)) {
        // The target dir can not be used, report error.
        std::string err_msg =
            "HDFS client can not create log_dir at path: " + logdir_;
        std::cerr << err_msg << std::endl;
        std::abort();
    }
    if (logdir_.back() != '/') {
        logdir_.push_back('/');
    }

    std::string log_path =
        fmt::format("{}{}.{}", logdir_, kLogFileName, getpid());

    if (level <= spdlog::level::debug) {
        auto debug_sink =
            std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                log_path + ".DEBUG", log_max_file_size, log_file_num);
        debug_sink->set_level(spdlog::level::debug);
        sinks.push_back(std::move(debug_sink));
    }
    if (level <= spdlog::level::info) {
        auto info_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            log_path + ".INFO", log_max_file_size, log_file_num);
        info_sink->set_level(spdlog::level::info);
        sinks.push_back(std::move(info_sink));
    }
    if (level <= spdlog::level::warn) {
        auto warn_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            log_path + ".WARN", log_max_file_size, log_file_num);
        warn_sink->set_level(spdlog::level::warn);
        sinks.push_back(std::move(warn_sink));
    }
    if (level <= spdlog::level::err) {
        auto error_sink =
            std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                log_path + ".ERROR", log_max_file_size, log_file_num);
        error_sink->set_level(spdlog::level::err);
        sinks.push_back(std::move(error_sink));
    }
    return sinks;
}

void Logger::InitLogger() {
    std::string log_type_str;
    std::string log_level_str;
    std::vector<spdlog::sink_ptr> sinks;

    // Parse log level
    const char* log_level_env = getenv(kLogLevelEnvKey);
    if (log_level_env == nullptr || (*log_level_env) == 0) {
        log_level_str = kDefaultLogLevel;
    } else {
        log_level_str.assign(log_level_env);
    }
    auto log_level = ParseLogLevel(log_level_str);

    // Parse log type
    const char* log_type_env = getenv(kLogTypeEnvKey);
    if (log_type_env == nullptr || (*log_type_env) == 0) {
        log_type_str = kDefaultLogType;
    } else {
        log_type_str.assign(log_type_env);
    }
    LogTypeBitmap log_types = ParseLogTypes(log_type_str);

    // Init log sinks
    if (log_types & kFileLogMask) {
        sinks = InitFileLogSinks(log_level);
    }
    if (log_types & kStdoutLogMask) {
        auto stdout_sink =
            std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        stdout_sink->set_level(log_level);
        sinks.push_back(std::move(stdout_sink));
    }
    if (log_types & kStderrLogMask) {
        auto stderr_sink =
            std::make_shared<spdlog::sinks::stderr_color_sink_mt>();
        stderr_sink->set_level(log_level);
        sinks.push_back(std::move(stderr_sink));
    }

    logger_ = std::make_shared<spdlog::logger>("multi_sink", sinks.begin(),
                                               sinks.end());
    logger_->set_level(log_level);

    logger_->set_pattern("%L%Y-%m-%d %T.%f %s:%# p%P-t%t] %v");
    // Flush whenever warnings or more severe messages are logged.
    logger_->flush_on(spdlog::level::warn);

    spdlog::register_logger(logger_);
    // Flush every 3 seconds for all-level logs.
    spdlog::flush_every(std::chrono::seconds(3));

    {
        int64_t logttlSec = -1;
        const char* logttlSecEnv = getenv(kLogTtlSecEnvKey);
        try {
            logttlSec = std::stoi(std::string(logttlSecEnv));
        } catch (const std::exception& e) {
        }
        if (logttlSec > 0) {
            logttl_.reset(new LogTtl(logdir_, kLogFileName, logttlSec));
        }
    }
}

}  // namespace Internal
}  // namespace Hdfs
