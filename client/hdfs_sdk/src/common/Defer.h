#pragma once

#include <functional>
#include <memory>

namespace Hdfs {
namespace Internal {

#define _MACRO_CONTACT(x, y) x##y
#define _GEN_DEFER_OBJ_NAME(x, y) _MACRO_CONTACT(x, y)

/*
 * Example:
 *
 * DEFER([&](){
 *   doDeferJob();
 * });
 */
#define DEFER(CLEANUP_FUNC)                                        \
    auto _GEN_DEFER_OBJ_NAME(_defer_obj_, __LINE__) =              \
        std::unique_ptr<void, std::function<void(void*)>> {        \
        reinterpret_cast<void*>(1), [&](void*) { CLEANUP_FUNC(); } \
    }

}  // namespace Internal
}  // namespace Hdfs