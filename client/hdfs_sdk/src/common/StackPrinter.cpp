#include "StackPrinter.h"

#include <cxxabi.h>
#include <libunwind.h>

#include <sstream>
#include <string>

namespace Hdfs {
namespace Internal {

extern const std::string GetStackTrace() {
    char buffer[16 * 2048];
    int buffer_idx = 0;

    unw_cursor_t cursor;
    unw_context_t context;

    // Initialize cursor to current frame for local unwinding.
    unw_getcontext(&context);
    unw_init_local(&cursor, &context);
    int maxLoop = 16;

    // Unwind frames one by one, going up the frame stack.
    while (unw_step(&cursor) > 0 && maxLoop-- > 0) {
        unw_word_t offset, pc;
        unw_get_reg(&cursor, UNW_REG_IP, &pc);
        if (pc == 0) {
            break;
        }
        buffer_idx += sprintf(buffer + buffer_idx, "\tat 0x%lx:", pc);

        char sym[2048];
        if (unw_get_proc_name(&cursor, sym, sizeof(sym), &offset) == 0) {
            char* nameptr = sym;
            int status;
            char* demangled =
                abi::__cxa_demangle(sym, nullptr, nullptr, &status);
            if (status == 0) {
                nameptr = demangled;
            }
            buffer_idx +=
                sprintf(buffer + buffer_idx, " (%s+0x%lx)\n", nameptr, offset);
            free(demangled);
        } else {
            buffer_idx += sprintf(
                buffer + buffer_idx,
                " -- error: unable to obtain symbol name for this frame\n");
        }
    }

    return std::string(buffer);
}

}  // namespace Internal
}  // namespace Hdfs
