//
// Created by <PERSON><PERSON><PERSON><PERSON>.673 on 2023/5/25.
//

#ifndef NATIVE_DFS_CLIENT_IPV6UTIL_H
#define NATIVE_DFS_CLIENT_IPV6UTIL_H
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>

#include <iostream>
#include <string>

namespace Hdfs {
namespace Internal {

// The enviroment variable name for replace IPV6 DIR
static const char* kIPv6ReplaceEnableKey = "CPP_HDFS_IPV6_REPLACE";

// given ipv6 returns ipv4 (convert by hostname)
// if convery failed, returns ipv6
static inline std::string getIPv4FromIPv6(const std::string& ipv6_address) {
    struct sockaddr_in6 addr;
    char hostname[NI_MAXHOST];

    addr.sin6_family = AF_INET6;
    inet_pton(AF_INET6, ipv6_address.c_str(), &(addr.sin6_addr));

    int result = getnameinfo((struct sockaddr*)&addr, sizeof(addr), hostname,
                             sizeof(hostname), NULL, 0, NI_NAMEREQD);

    if (result != 0) {
        return ipv6_address;
    }

    char ipv4_address[16];
    struct hostent* host_info = gethostbyname(hostname);
    if (host_info == NULL) {
        return ipv6_address;
    }
    inet_ntop(AF_INET, host_info->h_addr_list[0], ipv4_address,
              sizeof(ipv4_address));
    return std::string(ipv4_address);
}

// return -1 means error
static inline int getAddrFamily(const std::string& address) {
    struct addrinfo hint, *res = NULL;
    int ret;

    memset(&hint, '\0', sizeof hint);

    hint.ai_family = PF_UNSPEC;
    hint.ai_flags = AI_NUMERICHOST;

    ret = getaddrinfo(address.c_str(), NULL, &hint, &res);
    if (ret) {
        freeaddrinfo(res);
        return -1;
    }

    int family = res->ai_family;
    freeaddrinfo(res);
    return family;
}

}  // namespace Internal
}  // namespace Hdfs

#endif  // NATIVE_DFS_CLIENT_IPV6UTIL_H
