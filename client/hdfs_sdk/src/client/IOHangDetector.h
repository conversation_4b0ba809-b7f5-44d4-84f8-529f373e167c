#include <folly/concurrency/ConcurrentHashMap.h>
#include <folly/executors/CPUThreadPoolExecutor.h>

#include "common/Logger.h"
#include "common/common.h"
#include "monitor/byted/io_trace.h"
#include "monitor/byted/iomonitor.h"
#include "monitor/byted/paths_trace.h"

namespace Hdfs {
namespace Internal {
const int64_t kIOHangDetectorIntervelSec = 5;

class IOHangDetector {
   public:
    IOHangDetector() {
        enabled_.store(getBoolFromConfKey(kEnableIOHangDetectorEnvKey));
        if (!enabled_.load() || stop_.load()) {
            return;
        }
        // sec->us
        threshold_us =
            getNumericFromConfKey(kIOHangThresholdSec, 60) * 1000 * 1000;
        executor_ = std::make_unique<folly::CPUThreadPoolExecutor>(
            1, std::make_shared<folly::NamedThreadFactory>("IOHangDetector"));
        Start();
    };
    ~IOHangDetector() { Stop(); }
    void AddTraceTag(std::shared_ptr<IOTraceTag> tag) {
        if (!enabled_.load() || stop_.load()) {
            return;
        }
        // no lock here
        auto cur_map = tags_.GetCurSet();
        cur_map->emplace(tag, true);
    }
    void Start() {
        if (!enabled_.load() || stop_.load()) {
            return;
        }
        HDFS_LOG(INFO, "IO Hang Detector Starts");
        executor_->add([this]() {
            while (true) {
                std::this_thread::sleep_for(
                    std::chrono::seconds(kIOHangDetectorIntervelSec));
                int64_t now = GetNowTimeUs();
                tags_.SwitchSet();
                auto cur_map = tags_.GetCurSet();
                auto it = cur_map->begin();
                while (it != cur_map->end()) {
                    if (stop_.load()) {
                        return;
                    }
                    if (it->first->IsEnd()) {
                        it = cur_map->erase(it);
                    } else if (it->first->IsExpired(now, threshold_us)) {
                        HDFSLOG(
                            WARN,
                            "[Metrics Alarm][Hang] path: {}, type: {}, task_id: {}, "
                            "task_family: {}, storage_type: {}, err: {}, err_msg: {}, "
                            "duration(us): {}",
                            it->first->GetPath(),
                            hdfsEvTypToStr(it->first->GetType()),
                            Hdfs::Internal::HdfsEnv::Get()
                                ->GetIOMonitor()
                                ->GetTaskID(),
                            Hdfs::Internal::HdfsEnv::Get()
                                ->GetIOMonitor()
                                ->GetTaskFamily(),
                            it->first->GetStorageType(),
                            it->first->GetLastException(),
                            it->first->GetExceptionDetail(),
                            now - it->first->GetStartTimeUs());
                        HdfsEnv::Get()->GetIOMonitor()->IOHangAlarm(
                            it->first->GetType());
                        ++it;
                    } else {
                        ++it;
                    }
                }
            }
        });
    }
    void Stop() {
        if (!enabled_.load() || stop_.load()) {
            return;
        }
        stop_.store(true);
        if (executor_) {
            executor_->stop();
            executor_->join();
            executor_.reset();
        }
    }

   private:
    std::unique_ptr<folly::CPUThreadPoolExecutor> executor_;
    DoubleBuffer<folly::ConcurrentHashMap<std::shared_ptr<IOTraceTag>, bool>>
        tags_;
    std::atomic<bool> stop_{false};
    std::atomic<bool> enabled_{false};
    int64_t threshold_us{0};
};
}  // namespace Internal
}  // namespace Hdfs
