/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "InputStream.h"

#include <memory>

#include "FileSystemImpl.h"
#include "FileSystemInter.h"
#include "InputStreamImpl.h"
#include "InputStreamInter.h"
#include "StreamFactory.h"
#include "common/HdfsEnv.h"

using namespace Hdfs::Internal;

namespace Hdfs {

InputStream::InputStream(const InputStreamOpenOption& option) {
    option_ = option;
    closed_.store(false);
    impl_.reset();
}

InputStream::~InputStream() { close(); }

void InputStream::open(FileSystem& fs) {
    Hdfs::MaybeThrowException();
    if (option_.platform_ == FileStoragePlatform::IFT_THIRD_PARTY_STORAGE ||
        option_.platform_ == FileStoragePlatform::IFT_AZURE_BLOB_V2 ||
        option_.platform_ ==
            FileStoragePlatform::IFT_GOOGLE_CLOUDE_STORAGE_V2) {
        impl_ = StreamFactory::NewThirdPartyCloudInputStream(
            fs.impl->filesystem, option_);
        return;
    }

    auto input = std::make_shared<InputStreamImpl>();
    impl_ = input;
    input->SetBlockLocations(option_.lbs_);
    if (option_.hyper_file_meta_ && option_.hyper_file_meta_->isInited()) {
        input->SetCoolFileState(option_.hyper_file_meta_);
    } else {
        input->SetCoolFileState(nullptr);
    }

    input->init(fs.impl->filesystem, option_.conf_, option_.verify_checksum_,
                option_.path_, option_.is_bytecool_);

    if (!input->isBytecoolFile()) {
        bool res = input->UpdateLastBlockLength();
        if (!res) {
            THROW(
                HdfsIOException,
                "InputStreamImpl: failed to get last block length for file=%s "
                "from all datanodes.",
                option_.path_.c_str());
        }
    }

    if (option_.readahead_switch_) {
        executor_ = std::make_unique<folly::CPUThreadPoolExecutor>(
            1, std::make_shared<folly::NamedThreadFactory>("InputStream"));
        max_readahead_iobuf_num_ = option_.conf_->input_max_readahead_buf_num();
        readahead_bufs_ = std::make_unique<ReadaheadQueue>();
        last_error_ = exception_ptr();

        HDFSLOG(DEBUG, "file {} open readahead, max buf num {}", option_.path_,
                max_readahead_iobuf_num_);
    }
}

/**
 * To read data from hdfs.
 * @param buf the buffer used to filled.
 * @param size buffer size.
 * @return return the number of bytes filled in the buffer, it may less than
 * size.
 */
int32_t InputStream::read(char* buf, int32_t size,
                          const inner_hdfs_io_context& context) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    if (executor_) {
        if (lazy_trigger_readahead_) {
            try_readahead();
            lazy_trigger_readahead_ = false;
        }

        return read_data_from_buffers(buf, size);
    } else {
        return impl_->read(buf, size);
    }
}

int32_t InputStream::pread(char* buf, int64_t offset, int32_t size) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    return impl_->pread(buf, offset, size);
}

hdfsStatus InputStream::asyncPRead(int64_t offset, int32_t length,
                                   inner_hdfs_io_context& context) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    return impl_->asyncPRead(offset, length, context);
}

/**
 * To read data from hdfs, block until get the given size of bytes.
 * @param buf the buffer used to filled.
 * @param size the number of bytes to be read.
 */
void InputStream::readFully(char* buf, int64_t size) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    if (executor_) {
        int64_t remain_size = size;
        int64_t read_size = 0;
        while (remain_size > 0) {
            int64_t once_read_size =
                read_data_from_buffers(buf + read_size, remain_size);
            remain_size -= once_read_size;
            read_size += once_read_size;
        }
    } else {
        impl_->readFully(buf, size);
    }
}

int64_t InputStream::available() {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    if (executor_) {
        return 0;
    }
    return impl_->available();
}

/**
 * To move the file point to the given position.
 * @param pos the given position.
 */
void InputStream::seek(int64_t pos, const inner_hdfs_io_context& ctx) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    if (executor_) {
        readahead_seek(pos);
    } else {
        impl_->seek(pos);
    }
}

void InputStream::readahead_seek(int64_t pos) {
    auto seek_from_impl = [&]() {
        remain_buffer_ = nullptr;
        auto baton = std::make_shared<folly::Baton<>>();
        executor_->add([this, pos, baton]() {
            clear_buffers();
            try {
                impl_->seek(pos);
                readahead_done_.store(false);
                last_error_ = exception_ptr();
            } catch (...) {
                last_error_ = current_exception();
            }
            readahead_produce_offset_.store(pos);
            baton->post();
            try_readahead();
        });

        baton->wait();

        if (last_error_ != exception_ptr()) {
            auto error = last_error_;
            rethrow_exception(error);
        }

        readahead_consume_offset_ = pos;
    };

    if (readahead_consume_offset_ == pos) {
        return;
    }

    if (readahead_consume_offset_ < pos &&
        pos <= readahead_produce_offset_.load()) {
        int32_t consume_size = pos - readahead_consume_offset_;
        bool need_seek_from_impl = false;
        while (consume_size > 0) {
            if (nullptr == remain_buffer_) {
                remain_buffer_ = readahead_bufs_->pop();
            }
            // in case of impl not see the last length.
            if (nullptr == remain_buffer_) {
                need_seek_from_impl = true;
                break;
            }

            auto once_consume_size =
                remain_buffer_->read(nullptr, consume_size);
            consume_size -= once_consume_size;
            readahead_consume_offset_ += once_consume_size;
            if (!remain_buffer_->remain()) {
                remain_buffer_ = nullptr;
            }
        }

        if (need_seek_from_impl) {
            assert(remain_buffer_ == nullptr);
            seek_from_impl();
        }

        assert(readahead_consume_offset_ == pos);
    } else {
        if (pos < readahead_consume_offset_) {
            HDFSLOG(
                DEBUG,
                "InputStream {} seek before current consume offset:{}, target:{}",
                option_.path_, readahead_consume_offset_, pos);
        } else {
            HDFSLOG(
                DEBUG,
                "InputStream {} seek after current produce offset:{}, target:{}",
                option_.path_, readahead_produce_offset_.load(), pos);
        }
        seek_from_impl();
    }
}

/**
 * To get the current file point position.
 * @return the position of current file point.
 */
int64_t InputStream::tell(const inner_hdfs_io_context& ctx) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    if (executor_) {
        return readahead_consume_offset_;
    }
    return impl_->tell();
}

/**
 * Close the sthream.
 */
void InputStream::close() {
    if (isClosed()) {
        return;
    }

    if (executor_) {
        readahead_done_.store(true);
        executor_->stop();
        executor_->join();
        executor_.reset();
    }

    if (impl_) {
        impl_->close();
    }
    closed_.store(true);
}

/**
 * Get the path of InputStream.
 */
const std::string& InputStream::getFilePath() const {
    try {
        if (isClosed()) {
            THROW(HdfsFileClosed, "InputStream has been closed.");
        }
        return impl_->GetFilePath();
    } catch (const std::exception& e) {
        HDFSLOG(INFO, "[WARN][InputStream] getFilePath exception: %s",
                e.what());
    }

    static std::string empty_string;
    return empty_string;
}

/**
 * Get the visible length of the file. It will include the length of the last
 * block even if that is in UnderConstruction state.
 * @return int64_t the File Length
 */
int64_t InputStream::getVisibleLength() {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    return impl_->getVisibleLength();
}

/**
 * Refresh DFSInputStream's info - file length.
 * @return Returns 0 on success, -1 on error and sets errno.
 *         errno = 0 means file length not changed.
 */
bool InputStream::refreshInfo() {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    return impl_->refreshInfo();
}

/**
 * Check if impl has been closed.
 */
bool InputStream::isClosed() const { return closed_.load(); }

/**
 * TODO: We don't need to pass fs pointer later because we already have the file
 * system object.
 */
void InputStream::openBytecoolHandler(HdfsFileSystemInternalWrapper* fs) {
    if (!isBytecoolFile()) {
        THROW(UnsupportedOperationException,
              "InputStream try to open bytecool object on normal file");
    }
    impl_->openBytecoolHandler(fs);
}

bool InputStream::isBytecoolFile() {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    return impl_->isBytecoolFile();
}

bool InputStream::isThirdPartyCloudeFile() {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    return impl_->isThirdPartyCloudeFile();
}

void InputStream::tryAsyncRefreshLocation(bool force_update_refresh) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }
    impl_->tryAsyncRefreshLocation(force_update_refresh);
}

void InputStream::readahead() {
    try {
        auto iobuf = std::make_shared<io::IOBuf>();
        assert(iobuf->data() != nullptr);
        auto read_size = impl_->read(iobuf->data(), iobuf->capacity());
        iobuf->set_size(read_size);
        readahead_bufs_->push(iobuf);
        readahead_produce_offset_ += read_size;
    } catch (const HdfsEndOfStream& e) {
        readahead_done_.store(true);
        last_error_ = current_exception();
    } catch (...) {
        readahead_done_.store(true);
        last_error_ = current_exception();
    }

    readahead_cv_.notify_one();
    try_readahead();
}

void InputStream::try_readahead() {
    if (readahead_done_.load()) {
        return;
    }

    if (readahead_bufs_->size() >= max_readahead_iobuf_num_) {
        return;
    }

    if (executor_->getTaskQueueSize() == 0) {
        FdTrace fd_trace = tl_fd_trace;
        executor_->add([this, fd_trace]() {
            tl_fd_trace = fd_trace;
            readahead();
        });
    }
}

int32_t InputStream::read_data_from_buffers(char* buf, int32_t size) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "InputStream has been closed.");
    }

    // end or exception
    if (remain_buffer_ == nullptr && readahead_done_.load() &&
        readahead_bufs_->empty()) {
        assert(last_error_ != exception_ptr());
        auto error = last_error_;
        rethrow_exception(error);
    }

    if (remain_buffer_ == nullptr) {
        if (!readahead_bufs_->empty()) {
            remain_buffer_ = readahead_bufs_->pop();
        } else {
            if (!readahead_done_) {
                try_readahead();
                std::unique_lock<std::mutex> lock(readahead_mutex_);
                readahead_cv_.wait(lock, [this] {
                    return readahead_bufs_->size() > 0 ||
                           readahead_done_.load();
                });
                if (!readahead_bufs_->empty()) {
                    remain_buffer_ = readahead_bufs_->pop();
                }
            } else {
                assert(false);
            }
        }
    }

    if (remain_buffer_ == nullptr) {
        assert(readahead_bufs_->empty() && readahead_done_.load());
        auto error = last_error_;
        rethrow_exception(error);
    }

    auto read_size = remain_buffer_->read(buf, size);
    readahead_consume_offset_ += read_size;

    if (!remain_buffer_->remain()) {
        remain_buffer_ = nullptr;
    }

    try_readahead();

    return read_size;
}

void InputStream::clear_buffers() {
    while (!readahead_bufs_->empty()) {
        readahead_bufs_->pop();
    }
}

}  // namespace Hdfs
