//
// Created by <PERSON><PERSON><PERSON> on 2021-05-08.
//

#ifndef HDFS_CLIENT_OUTPUTDATASTREAMER_H
#define HDFS_CLIENT_OUTPUTDATASTREAMER_H

#include <inttypes.h>

#include <chrono>
#include <cstdint>

#include "OutputStreamAsyncContext.h"
#include "client/DataReader.h"
#include "client/Pipeline.h"
#include "client/ResponseProcessor.h"
#include "client/failover_strategy/FastFailoverStrategy.h"

namespace Hdfs {
namespace Internal {

// The DataStreamer class is responsible for sending data packets to the
// datanodes in the pipeline. It retrieves a new blockid and block locations
// from the namenode, and starts streaming packets to the pipeline of
// Datanodes. Every packet has a sequence number associated with
// it. When all the packets for a block are sent out and acks for each
// if them are received, the DataStreamer closes the current block.
class OutputDataStreamer {
   public:
    OutputDataStreamer(const std::string& src, uint64_t file_id, bool is_append,
                       std::shared_ptr<SessionConfig> conf,
                       int32_t checksum_type, int32_t chunk_size,
                       int32_t replication, int64_t block_size,
                       std::shared_ptr<FileSystemInter> filesystem,
                       std::shared_ptr<OutputStreamAsyncContext> context,
                       std::shared_ptr<FastFailoverStrategy> strategy,
                       std::shared_ptr<BlockStoragePolicyProto> storagePolicy);

    ~OutputDataStreamer() {
        streamer_closed_ = true;
        if (data_streamer_loop_.joinable()) {
            data_streamer_loop_.join();
        }
    }

    void start();

    // Caused by user-thread.
    // OutputDataStreamer.run() will stop response_processor and close socket;
    void close();

    void join();

    std::shared_ptr<PathTrace> getPathTrace();

   private:
    // streamer thread is the only thread that opens streams to datanode,
    // and closes them. Any error recovery is also done by this thread.
    void run();

    // This method is used when no explicit error report was received,
    // but something failed. When the primary node is a suspect or
    // unsure about the cause, the primary node is marked as failed.
    void tryMarkPrimaryDatanodeFailed();

    std::shared_ptr<Packet> createHeartBeatPacket();

    void endBlock();

    void closeInternal();

    void setClosed();

    // Init response processort.
    void initDataStreaming();

    // Open a DataOutputStream to a DataNode so that it can be written to.
    // This happens when a file is created and each time a new block is
    // allocated. Must get block ID and the IDs of the destinations from the
    // namenode. Returns the list of target datanodes.
    std::shared_ptr<LocatedBlock> nextBlockOutputStream();

    std::shared_ptr<LocatedBlock> locateFollowingBlock(
        const std::vector<DatanodeInfo>& excluded_nodes);

    // Equal to java code: closeResponsor() + closeStream();
    void closeStreamAndResponsor();

    // Open a DataOutputStream to a DataNode pipeline so that
    // it can be written to.
    // This happens when a file is appended or data streaming fails
    // It keeps on trying until a pipeline is setup
    void setupPipelineForAppendOrRecovery();

    void addDatanode2ExistingPipeline();

    void transfer(const ExtendedBlock& blk, const DatanodeInfo& src,
                  const std::vector<DatanodeInfo>& targets, const Token& token,
                  const int storage_type);

    void transferImpl(const ExtendedBlock& blk, const DatanodeInfo& src,
                      const std::vector<DatanodeInfo>& targets,
                      const Token& token, const int storage_type);

    int findNewDatanode(const std::vector<DatanodeInfo>& new_nodes,
                        const std::vector<DatanodeInfo>& original_nodes);

    // connects to the first datanode in the pipeline
    // Returns true if success, otherwise return failure.
    bool createBlockOutputStream(const Token& token, int64_t gs, bool recovery);

    // If this stream has encountered any errors so far, shutdown
    // threads and mark stream as closed. Returns true if we should
    // sleep for a while after returning from this call.
    void processDatanodeError();

    void resetErrorContext();

   private:
    bool streamer_closed_{false};

    std::string src_;
    uint64_t file_id_;

    bool is_append_{false};
    std::shared_ptr<SessionConfig> config_;
    int32_t checksum_type_{CHECKSUM_TYPE_CRC32C};
    int32_t chunk_size_{4086};
    int32_t replication_{3};

    BlockConstructionStage stage_{PIPELINE_CLOSE};

    bool can_add_datanode_best_{false};
    int32_t block_write_retry_{3};
    int32_t connect_timeout_ms_{60000};
    int32_t read_timeout_ms_{60000};
    int32_t write_timeout_ms_{60000};
    std::chrono::milliseconds socket_timeout_ms_{60000};
    std::chrono::milliseconds cv_timeout_ms_{1000};
    std::chrono::milliseconds add_block_retry_interval_ms_{400};
    int32_t get_additonal_node_max_time_{8};
    int64_t block_size_{512 * 1024 * 1024};
    std::shared_ptr<FileSystemInter> filesystem_;
    std::shared_ptr<OutputStreamAsyncContext> ctx_;
    std::shared_ptr<FastFailoverStrategy> strategy_;
    std::shared_ptr<BlockStoragePolicyProto> storagePolicy_;

    std::string client_name_{"Default"};

    std::vector<DatanodeInfo> excluded_nodes_;

    int64_t bytes_sent_{0};
    std::shared_ptr<Socket> sock_{nullptr};
    std::shared_ptr<BufferedSocketReader> reader_{nullptr};
    std::shared_ptr<DataTransferProtocolSender> sender_{nullptr};
    std::shared_ptr<ResponseProcessor> responsor_{nullptr};

    std::thread data_streamer_loop_;

    int64_t last_acked_seqno_before_failure_{-1};
    int32_t pipeline_recovery_count_{0};
    int32_t max_pipeline_recovery_count_{0};

    std::atomic<int64_t> packet_send_count_in_block_{0};

    MediaType media_type_ = MediaType::MT_UNKNOWN;
    CrossDC cross_dc_ = CrossDC::CrossDC_UNKNOWN;
};

}  // namespace Internal
}  // namespace Hdfs
#endif  // HDFS_CLIENT_OUTPUTDATASTREAMER_H
