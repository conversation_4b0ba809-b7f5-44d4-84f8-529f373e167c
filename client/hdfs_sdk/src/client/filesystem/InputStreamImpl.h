/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_CLIENT_INPUTSTREAMIMPL_H_
#define _HDFS_LIBHDFS3_CLIENT_INPUTSTREAMIMPL_H_

#include <folly/Optional.h>

#include <atomic>
#include <cstdint>
#include <memory>
#include <mutex>

#include "async/RequestFactory.h"
#include "bytecool/bytecool_handler.h"
#include "cache/MetaCache.h"
#include "client/BlockReader.h"
#include "client/failover_strategy/FastSwitchReadStrategy.h"
#include "client/filesystem/FileSystem.h"
#include "client/filesystem/InputStreamInter.h"
#include "client/hdfsAsyncContext.h"
#include "common/ExceptionInternal.h"
#include "common/Hash.h"
#include "common/Logger.h"
#include "common/SessionConfig.h"
#include "common/json.h"
#include "platform.h"
#include "rpc/RpcAuth.h"
#include "server/Datanode.h"
#include "server/HyperFileMeta.h"
#include "server/LocatedBlock.h"
#include "server/LocatedBlocks.h"

#ifdef MOCK
#include "mock/TestDatanodeStub.h"
#endif

namespace Hdfs {
namespace Internal {

extern std::shared_ptr<HyperFileMeta> PrepareCoolMeta(
    std::shared_ptr<HyperFileMeta> meta);

/**
 * A input stream used read data from hdfs.
 */
class InputStreamImpl : public InputStreamInter,
                        public std::enable_shared_from_this<InputStreamImpl> {
   public:
    InputStreamImpl();
    virtual ~InputStreamImpl();

    // /**
    //  * Open a file to read
    //  * @param fs hdfs file system.
    //  * @param path the file to be read.
    //  * @param verifyChecksum verify the checksum.
    //  */
    // void open(std::shared_ptr<FileSystemInter> fs, const char* path,
    //           bool verifyChecksum, std::shared_ptr<LocatedBlocks> lb,
    //           bool is_bytecool) override;

    /**
     * Open a file to sync positional read.
     * @param buf the buffer used to filled.
     * @param offset provides position where read begin.
     * @param size provides size to read.
     */
    int32_t pread(char* buf, int64_t offset, int32_t size) override;

    /**
     * Open a file to async positional read.
     * @param offset provides position where read begin.
     * @param length provides length to read.
     * @param context provides callback and io options.
     * @return return the status of read operation.
     */
    hdfsStatus asyncPRead(int64_t offset, int32_t length,
                          inner_hdfs_io_context& context) override;

    /**
     * To read data from hdfs.
     * @param buf the buffer used to filled.
     * @param size buffer size.
     * @return return the number of bytes filled in the buffer, it may less than
     * size.
     */
    int32_t read(char* buf, int32_t size) override;

    /**
     * To read data from hdfs, block until get the given size of bytes.
     * @param buf the buffer used to filled.
     * @param size the number of bytes to be read.
     */
    void readFully(char* buf, int64_t size) override;

    int64_t available() override;

    /**
     * To move the file point to the given position.
     * @param pos the given position.
     */
    void seek(int64_t pos) override;

    /**
     * To get the current file point position.
     * @return the position of current file point.
     */
    int64_t tell() override;

    /**
     * Get the visible length of the file. It will include the length of the
     * last block even if that is in UnderConstruction state.
     * @return int64_t the File Length
     */
    int64_t getVisibleLength() override;

    /**
     * Refresh DFSInputStream's info - file length.
     * @return Return true means file length has updated.
     *         Return flase means file length has not updated.
     */
    bool refreshInfo() override;

    /**
     * Close the stream.
     */
    void close() override;

    /**
     * Convert to a printable string
     *
     * @return return a printable string
     */
    std::string toString() override;

    const std::string& GetFilePath() const override;

    void openBytecoolHandler(HdfsFileSystemInternalWrapper* fs) override;

    bool isBytecoolFile() override;

    void init(std::shared_ptr<Hdfs::Internal::FileSystemInter> fs,
              std::shared_ptr<Hdfs::Internal::SessionConfig> conf,
              bool verifyChecksum, std::string path, bool is_bytecool) override;

    // Get the last block length from datanode. The file length retured by
    // namenode does not contain the "length being written" of the last block
    // when this file is under construction (UC). This method will query
    // the datanodes holding the last block for its being-written-length and
    // set the result to lbs_->getLastBlock().
    // Return true if this method gets last block length from datanode
    // successfully. Return false otherwise.
    bool UpdateLastBlockLength() override;

    bool isThirdPartyCloudeFile() override { return false; }

    void SetBlockLocations(std::shared_ptr<LocatedBlocks> lbs) {
        std::unique_lock<std::shared_mutex> lock(lbs_mutex_);
        lbs_ = lbs;
    }

    void SetCoolFileState(std::shared_ptr<HyperFileMeta> meta) {
        setCoolFileState(meta);
    }

    int64_t GetLastUpdateTime() { return last_update_time.load(); }

    void SetLastUpdateTime(int64_t time) { last_update_time.store(time); }

    // Update block location infos. If lb is not nullptr, set lbs_ lb and
    // call UpdateLastBlockLength() to update the last block length.
    // If lb is nullptr, call UpdateBlockLocations() and then call
    // UpdateLastBlockLength().
    virtual std::shared_ptr<LocatedBlocks> updateBlockInfos(
        std::shared_ptr<LocatedBlocks> lb = nullptr,
        bool disable_fastvisit = false, bool force_update_block = false);
    void tryAsyncRefreshLocation(bool force_update_refresh) override;

   private:
    void assertReadUseState(bool expect_use, bool set_use);
    virtual hdfsStatus GetReadTaskPerBlock(
        std::vector<std::shared_ptr<AsyncReadContext>>& ctxs,
        inner_hdfs_io_context& user_context, int64_t offset, int32_t length,
        bool isByteRpc);

    bool choseBestNode();
    bool isLocalNode();
    int32_t readInternal(char* buf, int32_t size);
    int32_t readInternalImpl(char* buf, int32_t size);
    int32_t readOneBlock(char* buf, int32_t size, std::string* error_msg);
    int64_t getFileLength();
    int64_t readBlockLength(const LocatedBlock& b);
    void checkStatus();
    void checkClosed();
    void readFullyInternal(char* buf, int64_t size);
    void seekInternal(int64_t pos);
    void seekToBlock(const LocatedBlock& lb);
    void setupBlockReader(bool temporaryDisableLocalRead);
    virtual void createBlockReader(int64_t offset, int64_t len,
                                   const char* clientName);
    bool shouldCreateByteRpcBlockReader();

    virtual std::shared_ptr<LocatedBlocks> GetBlockLocations();

    // Get block locations from namenode and the result to lbs_. If block
    // location cache is enabled, the result is also upserted into
    // block location cache.
    // If fastvisit is true, then nnproxy will try to get block locations
    // for FAST datanodes.
    // Return true if this method gets block locations from namenode
    // successfully. Return false otherwise.
    std::shared_ptr<LocatedBlocks> UpdateBlockLocations(
        bool fastvisit, bool force_update_block);
    bool updateLastBlockLength(std::shared_ptr<LocatedBlocks> lbs);
    // After updating the file's blocks information, this function needs to be
    // called to mark whether the file is bytecool file. If an already open file
    // has a cool state conflict, throw an IO exception.
    void setCoolFileState(std::shared_ptr<HyperFileMeta> meta);

   protected:
    bool closed;
    bool localRead;
    std::atomic<bool> readInUse_{false};
    bool readFromUnderConstructedBlock;
    bool verify;
    bool enableLocationCache_{false};
    std::atomic<bool> is_inited_cool_state_{false};
    bool is_bytecool_file_{false};
    DatanodeInfo curNode;
    exception_ptr lastError;
    FileStatus fileInfo;
    int maxGetBlockInfoRetry;
    // The position of sequential read.
    int64_t cursor;
    // The location of block end in this file.
    int64_t endOfCurBlock;
    int64_t lastBlockBeingWrittenLength{0};
    int64_t prefetch_size_{0};
    RpcAuth auth;
    std::unique_ptr<BlockReader> block_reader_;
    std::shared_ptr<FileSystemInter> filesystem_;
    std::shared_ptr<LocatedBlock> curBlock;
    std::shared_mutex lbs_mutex_;
    std::shared_ptr<LocatedBlocks> lbs_;
    std::mutex cool_meta_mutex_;
    std::shared_ptr<HyperFileMeta> coolFileMeta_;
    std::shared_ptr<Hdfs::Bytecool::BytecoolHandler> cool_file_handler_{
        nullptr};
    std::shared_ptr<SessionConfig> sconf_;
    std::string path;
    std::vector<DatanodeInfo> failedNodes;
    std::vector<char> localReaderBuffer;

    std::shared_ptr<FastSwitchReadStrategy> switch_read_strategy_;

    uint32_t max_num_fly_async_read_;
    uint32_t async_worker_group_size_;

    MediaType media_type_ = MediaType::MT_UNKNOWN;
    CrossDC cross_dc_ = CrossDC::CrossDC_UNKNOWN;
    std::atomic<long> last_update_time{0};

    // For ByteRPC
    enum class BlockReaderType { NONE, LOCAL, XFER, BYTE_RPC };
    BlockReaderType block_reader_type_{BlockReaderType::NONE};
    bool force_block_reader_xfer_{false};

#ifdef MOCK
   private:
    Hdfs::Mock::TestDatanodeStub* stub;
#endif

    friend class StreamFactory;
};

}  // namespace Internal
}  // namespace Hdfs

#endif /* _HDFS_LIBHDFS3_CLIENT_INPUTSTREAMIMPL_H_ */
