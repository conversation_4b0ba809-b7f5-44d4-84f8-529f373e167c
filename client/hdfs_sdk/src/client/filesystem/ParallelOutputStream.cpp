#include "ParallelOutputStream.h"

#include <chrono>

#include "OutputStreamAsyncImpl.h"

namespace Hdfs {

ParallelOutputStream::ParallelOutputStream(const OutPutStreamOpenOption& option)
    : OutputStream(option) {
    for (uint32_t i = 0; i < option.parallel_io_num_; i++) {
        impls_.push_back(new Internal::OutputStreamAsyncImpl());
    }
    closed_ = false;
}

ParallelOutputStream::~ParallelOutputStream() {
    for (auto iter : impls_) {
        delete iter;
        iter = nullptr;
    }

    impls_.clear();
}

void ParallelOutputStream::open(FileSystem& fs) {
    if (!fs.impl) {
        THROW(HdfsIOException, "FileSystem: not connected.");
    }

    if (isClosed()) {
        THROW(HdfsFileClosed, "ParallelOutputStream has been closed.");
    }

    if (option_.flag_ & Append) {
        THROW(UnsupportedOperationException,
              "Append not suppored for ParallelOutputStream");
    }

    // create temp part file
    for (uint32_t i = 0; i < option_.parallel_io_num_; i++) {
        std::string part_file = option_.path_ + ".part" + std::to_string(i);
        part_file_paths_.push_back(part_file);
        impls_[i]->open(fs.impl->filesystem, part_file.c_str(), option_.flag_,
                        option_.permission_, option_.create_parent_,
                        option_.replication_, option_.block_size_,
                        option_.xattrs_, nullptr, option_.stripe_unit_count_,
                        option_.stripe_unit_size_);
    }

    impl->open(fs.impl->filesystem, option_.path_.c_str(), option_.flag_,
               option_.permission_, option_.create_parent_,
               option_.replication_, option_.block_size_, option_.xattrs_,
               nullptr, option_.stripe_unit_count_, option_.stripe_unit_size_);

    // befor concat, should close src path first;
    impl->close();

    fs_ = fs.impl->filesystem;
    path_ = option_.path_;
}

void ParallelOutputStream::appendv(const struct iovec* iov, int iov_cnt,
                                   const inner_hdfs_io_context& context) {
    for (int i = 0; i < iov_cnt; ++i) {
        append(static_cast<const char*>(iov[i].iov_base), iov[i].iov_len,
               context);
    }
}

/**
 * To append data to file.
 * @param buf the data used to append.
 * @param size the data size.
 */
void ParallelOutputStream::append(const char* buf, int64_t size,
                                  const inner_hdfs_io_context& context) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "ParallelOutputStream has been closed.");
    }

    if (finish_concat_) {
        THROW(HdfsIOException, "file already finish concat");
    }

    if (context.channel_id > option_.parallel_io_num_ ||
        context.channel_id == 0) {
        THROW(InvalidParameter, "invalid channel_id: %u", context.channel_id);
    }

    if (size < 0 || size > option_.partial_file_size_) {
        THROW(InvalidParameter, "invalid append buf size: %lu", size);
    }

    if (size > 0) {
        // auto start = std::chrono::high_resolution_clock::now();
        impls_[context.channel_id - 1]->append(buf, size);
        // auto end = std::chrono::high_resolution_clock::now();
        // std::chrono::duration<double, std::milli> elapsed = end - start;
        // HDFSLOG(INFO, "parallel output stream append, size {}, channel_id {},
        // path {}, cost {} ms", size,
        //    context.channel_id, path_.c_str(), elapsed.count());
        HDFSLOG(
            INFO,
            "parallel output stream append, size {}, channel_id {}, path {}",
            size, context.channel_id, path_.c_str());
        return;
    }

    // channel write finished
    assert(size == 0);
    // auto start = std::chrono::high_resolution_clock::now();
    impls_[context.channel_id - 1]->close();
    // auto end = std::chrono::high_resolution_clock::now();
    // std::chrono::duration<double, std::milli> elapsed = end - start;
    int32_t current_complete_num = part_file_write_complete_num_.fetch_add(1);
    // HDFSLOG(INFO,
    //        "finish write for channel: {}, current complete num: {}, path: {},
    //        cost {} ms", context.channel_id, current_complete_num + 1,
    //        path_.c_str(), elapsed.count());

    HDFSLOG(INFO,
            "finish write for channel: {}, current complete num: {}, path: {}",
            context.channel_id, current_complete_num + 1, path_.c_str());

    // concat part file to final file
    if ((current_complete_num + 1) == option_.parallel_io_num_) {
        const char** src_paths =
            (const char**)new const char*[option_.parallel_io_num_ + 1];
        for (uint32_t i = 0; i < option_.parallel_io_num_; ++i) {
            src_paths[i] = part_file_paths_[i].c_str();
        }
        src_paths[option_.parallel_io_num_] = nullptr;

        try {
            fs_->concat(path_.c_str(), src_paths);
        } catch (...) {
            delete[] src_paths;
            src_paths = nullptr;
            throw;
        }

        delete[] src_paths;
        src_paths = nullptr;

        finish_concat_ = true;
    }
}

/**
 * Flush all data in buffer and waiting for ack.
 * Will block until get all acks.
 */
void ParallelOutputStream::flush() { return; }

/**
 * return the current file length.
 * @return current file length.
 */
int64_t ParallelOutputStream::tell(const inner_hdfs_io_context& context) {
    if (isClosed()) {
        THROW(HdfsFileClosed, "ParallelOutputStream has been closed.");
    }

    if (finish_concat_) {
        THROW(HdfsIOException, "file already finish concat");
    }

    if (context.channel_id > option_.parallel_io_num_ ||
        context.channel_id == 0) {
        THROW(InvalidParameter, "invalid channel_id:%u", context.channel_id);
    }

    int64_t pos = impls_[context.channel_id - 1]->tell();
    return option_.partial_file_size_ * (context.channel_id - 1) + pos;
}

/**
 * the same as flush right now.
 */
void ParallelOutputStream::sync(bool updateLength) {
    THROW(HdfsIOException, "method not support yet.");
}

/**
 * close the stream.
 */
void ParallelOutputStream::close() {
    if (isClosed()) {
        return;
    }

    for (auto impl : impls_) {
        impl->close();
    }

    impl->close();
    closed_ = true;
}

hdfsStatus ParallelOutputStream::asyncWrite(const char* buf, int64_t size,
                                            inner_hdfs_io_context& context) {
    return hdfsStatus::STATUS_UNSUPPORTED_OP;
}

hdfsStatus ParallelOutputStream::asyncFlush(inner_hdfs_io_context& context) {
    return hdfsStatus::STATUS_UNSUPPORTED_OP;
}

hdfsStatus ParallelOutputStream::asyncWriteAndFlush(
    const char* buf, int64_t size, inner_hdfs_io_context& context) {
    return hdfsStatus::STATUS_UNSUPPORTED_OP;
}

const std::string& ParallelOutputStream::getFilePath() const { return path_; }

}  // namespace Hdfs