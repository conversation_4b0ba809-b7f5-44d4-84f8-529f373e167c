#pragma once

#include <metric_sdk.h>

#include <atomic>
#include <string>

namespace Hdfs {
namespace Internal {

struct HdfsMetricClient {
   public:
    ~HdfsMetricClient() { Exit(); }
    HdfsMetricClient();
    bool IsInit();
    void Exit();
    METRICS::Metric* CreateMetric(const std::string& metric_name,
                                  const std::vector<std::string>& tags);

   private:
    std::atomic<bool> inited_{false};
    METRICS::MetricClient* client_{nullptr};
    METRICS::MetricRegistryCfg registry_;
};

}  // namespace Internal
}  // namespace Hdfs
