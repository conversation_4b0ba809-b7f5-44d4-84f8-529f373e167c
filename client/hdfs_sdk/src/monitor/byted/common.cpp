#include "monitor/byted/common.h"

#include "monitor/byted/iomonitor.h"

namespace Hdfs {
namespace Internal {

const char* kEnableIOMonitorEnvKey = "CPP_HDFS_ENABLE_IO_MONITOR";
const char* kEnableIOHangDetectorEnvKey = "CPP_HDFS_ENABLE_IO_HANG_DETECTOR";
const char* kIOHangThresholdSec = "CPP_HDFS_IO_HANG_DETECT_THRESHOLD_SEC";
const char* kEnableIOMonitorDatabusEmitterChannelEnvKey =
    "CPP_HDFS_IO_MONITOR_DATABUS_CHANNEL";
const char* kEnableIOMonitorDatabusStatusChannelEnvKey =
    "CPP_HDFS_IO_MONITOR_STATUS_DATABUS_CHANNEL";
const char* kEnableMetricEnvKey = "CPP_HDFS_ENABLE_METRIC";
const char* kEnableTraceLogEnvKey = "CPP_HDFS_ENABLE_TRACE_LOG";
const char* kDisableStatusMonitorEnvKey = "CPP_HDFS_DISABLE_STATUS_MONITOR";
const char* kDisableMonitorLockEnvKey = "CPP_HDFS_IOMONITOR_WITHOUT_LOCK";

const char* kIOMonitorCustomPrefix = "CPP_HDFS_IO_MONITOR_CUSTOM_PREFIX";
const char* kMetricPrefix = "inf.hdfs.cpp_cli";

const char* kTaskFamilyEnvKey = "CPP_HDFS_TASK_FAMILY";
const char* kDefaultTaskFamily = "default";

const char* kTaskIdEnvKey = "CPP_HDFS_TASK_ID";
const char* kDefaultTaskId = "default";

const char* kMerlinJobEnvKey = "MERLIN_JOB_ID";
const char* kDefaultMerlinJobId = "default";

const char* kContainerIdEnvKey = "CONTAINER_ID";
const char* kDefaultContainerId = "default";

const char* kAppIdEnvKey = "YARN_APP_ID";
const char* kDefaultAppId = "default";

const char* kTenantNameKey = "CPP_HDFS_METRIC_TENANT";
const char* kDefaultTenantName = "default";

const char* kWritePathTraceType = "write";
const char* kReadPathTraceType = "read";
const char* kPreadPathTraceType = "pread";

const char* kEnablePathMonitorDatabusChannelEnvKey =
    "CPP_HDFS_PATH_MONITOR_DATABUS_CHANNEL";
const char* kEnablePathTraceDatabusChannelEnvKey =
    "CPP_HDFS_PATH_TRACE_DATABUS_CHANNEL";
const char* kEnablePathsTraceEnvKey = "CPP_HDFS_ENABLE_PATH_TRACE";
const char* kEnableDebugPathsTraceEnvKey = "CPP_HDFS_DEBUG_PATH_TRACE";
const char* kPathsTraceTraceDetailSlowMS =
    "CPP_HDFS_PATH_TRACE_TRACE_DETAIL_SLOW_MS";  // threshold of slow
                                                 // trace detail (ms)
const char* kPathsTraceWriteSpeedAlertMB =
    "CPP_HDFS_PATH_TRACE_WRITE_WARNING_SPEED_MB";  // speed threshold of
                                                   // metric alert (bytes
                                                   // per s)
const char* kPathsTraceWriteFatalSpeedMB =
    "CPP_HDFS_PATH_TRACE_WRITE_FATAL_SPEED_MB";  // speed threshold of
                                                 // fatal path trace
                                                 // (bytes per s)
const char* kPathsTraceReadSpeedAlertMB =
    "CPP_HDFS_PATH_TRACE_READ_WARNING_SPEED_MB";  // speed threshold of
                                                  // metric alert (bytes
                                                  // per s)
const char* kPathsTraceReadFatalSpeedMB =
    "CPP_HDFS_PATH_TRACE_READ_FATAL_SPEED_MB";  // speed threshold of
                                                // fatal path trace
                                                // (bytes per s)
const char* kPathsTracePreadSpeedAlertMB =
    "CPP_HDFS_PATH_TRACE_PREAD_WARNING_SPEED_MB";  // speed threshold of
                                                   // metric alert (bytes
                                                   // per s)
const char* kPathsTracePreadFatalSpeedMB =
    "CPP_HDFS_PATH_TRACE_PREAD_FATAL_SPEED_MB";  // speed threshold of
                                                 // fatal path trace
                                                 // (bytes per s)
const char* kInputCloseAsyncEnvKey = "CPP_HDFS_INPUT_CLOSE_ASYNC";
const char* kMetricsSelfMonitor = "CPP_HDFS_METRICS_SELF_MONITOR";

bool NeedTraceDataSize(hdfsEvTyp type) { return type <= hdfsEvTypHdfsWrite; }

int64_t getNumericFromConfKey(const char* key, int64_t default_value) {
    const char* envValue = getenv(key);
    int64_t intValue = default_value;
    if (envValue != nullptr) {
        try {
            int64_t intValue = std::stoi(envValue);
            return intValue;
        } catch (...) {
        }
    }
    return intValue;
}

bool getBoolFromConfKey(const char* key) {
    bool res = false;
    const char* env_val = getenv(key);
    if (env_val != nullptr) {
        std::string env_str = std::string(env_val);
        res = (env_str == "true" || env_str == "1");
    }
    return res;
}

std::string hdfsEvTypToStr(hdfsEvTyp type) {
    switch (type) {
#define FUNC(name, lat, speed, namestr) \
    case name:                          \
        return #namestr;
        HDFSIO_MAPPER(FUNC)
#undef FUNC
        default:
            return "unknown";
    }
}

IOTraceStatistic::IOTraceStatistic() {
    for (uint32_t i = 0; i < Enum2Int(hdfsEvTypEnd); i++) {
        io_cnt[i] = 0;
        io_done_cnt[i] = 0;
        io_lat_us[i] = 0;
        io_size[i] = 0;
        io_fail_cnt[i] = 0;
        io_fail_lat_us[i] = 0;
        io_fail_size[i] = 0;
        slowio_cnt[i] = 0;
        slowio_lat_us[i] = 0;
        slowio_size[i] = 0;
    }
}

std::string IOTraceStatistic::ToJsonStr() const {
    hdfsjson::json ret;
    SetJson(ret);
    return ret.dump();
}

void IOTraceStatistic::SetJson(hdfsjson::json& ret) const {
    std::string key;
    uint64_t value;

    for (uint32_t i = 0; i < uint32_t(hdfsEvTypEnd); i++) {
        key = hdfsEvTypToStr(static_cast<hdfsEvTyp>(i));

        value = io_cnt[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiCount"][key] = value;

        value = io_done_cnt[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiDoneCount"][key] = value;

        value = io_lat_us[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiLatMs"][key] = value / 1000;

        value = io_size[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiDataSize"][key] = value;

        value = io_fail_cnt[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiFailedCount"][key] = value;

        value = io_fail_lat_us[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiFailedLatMs"][key] = value / 1000;

        value = io_fail_size[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiFailedDataSize"][key] = value;

        value = slowio_cnt[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiSlowIOCount"][key] = value;

        value = slowio_lat_us[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiSlowIOLatMs"][key] = value / 1000;

        value = slowio_size[i].load(std::memory_order_acquire);
        if (value > 0) ret["apiSlowIODataSize"][key] = value;
    }

}  // namespace Internal

}  // namespace Internal
}  // namespace Hdfs
