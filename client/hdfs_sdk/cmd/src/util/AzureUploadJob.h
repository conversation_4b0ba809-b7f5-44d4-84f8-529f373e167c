// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#ifndef DISABLE_THIRDPARTY_STORAGE
#include <folly/dynamic.h>

#include <azure/core/io/body_stream.hpp>
#include <azure/storage/blobs.hpp>
#include <azure/storage/common/crypt.hpp>
#include <cerrno>
#include <chrono>
#include <map>
#include <memory>
#include <string>

#include "client/hdfs.h"
#include "util/ObjectUploader.h"
#include "util/Path.h"

namespace Hdfs::Internal {

class AzureStreamWrappedHdfsFile;
using AzureStreamWrappedHdfsFilePtr =
    std::unique_ptr<AzureStreamWrappedHdfsFile>;

/**
 * @brief AzureBlobUploadJobImpl
 * AzureBlobUploadJobImpl derived from BaseObjectUploadJob. Implements both
 * simple and multipart upload interfaces for Azure Block Blob storage.
 *
 * Features:
 * - Simple upload for files < 256MiB using PutBlob
 * - Multipart upload for large files using staged blocks
 * - Thread-safe block list management
 * - CRC64 integrity verification
 *
 * @note Maximum blob size is approximately 190.7 TiB with 50,000 blocks
 *       and 4000MiB block size.
 */
class AzureBlobUploadJobImpl : public BaseObjectUploadJob {
   public:
    AzureBlobUploadJobImpl(
        hdfsFS srcFS, const HdfsPath& srcUri,
        const Azure::Storage::Blobs::BlockBlobClient& blobClient,
        const folly::Uri& dstUri, bool overwrite);

    virtual ~AzureBlobUploadJobImpl();

    DISABLE_COPY_AND_MOVE(AzureBlobUploadJobImpl);

    /**
     * @brief Sets timeout for the upload job.
     * @param timeout Timeout in seconds.
     */
    void SetTimeout(std::chrono::seconds timeout) override;

   protected:
    /**
     * @brief Uploads small files (<256MiB) using PutBlob
     * @throws std::runtimer_error if timedout or fatal error occurred.
     */
    void uploadImpl() override;

    /**
     * @brief Initializes multipart upload
     * @param partSize Block size in bytes
     * @param parallel Maximum parallel upload threads
     * @throws std::runtimer_error if fatal error occurred.
     */
    void startMultipartUploadImpl(size_t partSize, int parallel) override;

    /**
     * @brief Finalizes multipart upload
     * @throws std::runtimer_error if fatal error occurred or canceled.
     */
    void completeMultipartUploadImpl() override;

    /**
     * @brief Aborts the multipart upload and cleans up resources.
     * Note: This function has no effect on the Azure Blob implementation.
     * @throws std::runtime_error if a fatal error occurs.
     */
    void abortMultipartUploadImpl() override;

    /**
     * @brief Uploads next part in multipart upload
     * @param parallelIdx Thread index in range of [0, parallel)
     * @return true if there are more parts to upload, false otherwise.
     * @throws std::runtimer_error if fatal error occurred or canceled.
     */
    bool uploadNextPartImpl(int parallelIdx) override;

   private:
    // Verifies and generate block list.
    std::vector<std::string> generateBlockList(bool verifyContinuous);

    // Generates the block ID by left-padding it with zeros and then
    // base64-encoding it. Uses the same block ID format algorithm as
    // Azure::Storage::Blobs::BlockBlobClient::UploadFrom.
    std::string generateBlockId(int64_t partId);

    static size_t calculateOptimalPartSize(size_t fileSize, size_t startFrom);

    constexpr static int kPartSizeStepBits = 22;  // 4MiB
    constexpr static size_t kPartSizeStep = 1UL << kPartSizeStepBits;
    constexpr static size_t kMaxPartSize = 4000UL * 1024 * 1024;  // 4000 MiB
    constexpr static size_t kOptimalPartNum = 10000UL;
    constexpr static size_t kMaxPartNum = 49990UL;

   private:
    Azure::Storage::Blobs::BlockBlobClient blobClient_;
    // {partId -> base64 encoded blockId}
    std::map<int64_t, std::string> uploadedBlocks_;
    std::mutex mutex_;
    Azure::Core::Context context_;
};

class AzureStreamWrappedHdfsFile final : public Azure::Core::IO::BodyStream,
                                         public BaseHdfsInputStream {
   public:
    explicit AzureStreamWrappedHdfsFile(hdfsFS fs, const std::string& path,
                                        int64_t offset = 0,
                                        int64_t length = -1);
    ~AzureStreamWrappedHdfsFile();
    DISABLE_COPY_AND_MOVE(AzureStreamWrappedHdfsFile);

    size_t OnRead(uint8_t* buffer, size_t count,
                  Azure::Core::Context const& context) override;

    void Rewind() override { Seek(startOffset_); }

    int64_t Length() const override { return GetLength(); }
    int64_t GetCrc64() const { return crc64Value_; }

    // FinalizeCrc64Hash() must be called exactly once before calling
    // GetCrc64().
    std::unique_ptr<Azure::Storage::Crc64Hash> FinalizeCrc64Hash();

    // Finalize staging hash and concatenate it to streamCrc64_.
    // Return staging hash value.
    int64_t StageCrc64Hash();

   protected:
    void updateChecksum(const uint8_t* buffer, size_t count) override;

   private:
    std::unique_ptr<Azure::Storage::Crc64Hash> stagingCrc64_;
    std::unique_ptr<Azure::Storage::Crc64Hash> streamCrc64_;
    uint64_t crc64Value_ = 0;
    friend class PartialAzureStream;
};

class PartialAzureStream final : public Azure::Core::IO::BodyStream {
   public:
    PartialAzureStream(AzureStreamWrappedHdfsFile* underlayStream,
                       int64_t length);
    ~PartialAzureStream();
    DISABLE_COPY_AND_MOVE(PartialAzureStream);

    size_t OnRead(uint8_t* buffer, size_t count,
                  Azure::Core::Context const& context) override;

    void Rewind() override;

    // FinalizeCrc64Hash() must be called exactly once before calling
    // GetCrc64().
    void FinalizeCrc64Hash();

    int64_t GetCrc64() const { return crc64Value_; }

    int64_t Length() const override { return endOffset_ - startOffset_; }
    int64_t GetCurrentOffset() const { return curOffset_; }

   private:
    AzureStreamWrappedHdfsFile* underlayStream_;
    int64_t startOffset_;
    int64_t endOffset_;
    int64_t curOffset_;
    int64_t length_;
    uint64_t crc64Value_ = 0;
};

// Turn int64_t into little endian bytes.
std::vector<uint8_t> uint64_to_le_bytes(uint64_t value);

// Turn little endian bytes to int64_t.
uint64_t le_bytes_to_uint64(const std::vector<uint8_t>& vec);

void verifyAzureCrc64(Azure::Nullable<Azure::Storage::ContentHash>& verifyCrc64,
                      uint64_t expectCrc64, bool ignoreIfNotCrc64);
}  // namespace Hdfs::Internal

#endif
