// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#include "util/AzureUploadJob.h"

#ifndef DISABLE_THIRDPARTY_STORAGE
#include <fmt/chrono.h>
#include <fmt/format.h>
#include <folly/Random.h>
#include <folly/String.h>
#include <folly/dynamic.h>
#include <folly/json.h>

#include <azure/storage/blobs.hpp>
#include <azure/storage/common/crypt.hpp>
#include <cerrno>
#include <chrono>
#include <cstdint>
#include <memory>
#include <string>

#include "util/util.h"

namespace Hdfs::Internal {
using Azure::Storage::Blobs::BlockBlobClient;

AzureBlobUploadJobImpl::AzureBlobUploadJobImpl(
    hdfsFS srcFS, const HdfsPath& srcUri,
    const Azure::Storage::Blobs::BlockBlobClient& blobClient,
    const folly::Uri& dstUri, bool overwrite)
    : BaseObjectUploadJob(srcFS, srcUri, dstUri, overwrite),
      blobClient_(blobClient) {}

AzureBlobUploadJobImpl::~AzureBlobUploadJobImpl() = default;

void AzureBlobUploadJobImpl::SetTimeout(std::chrono::seconds timeout) {
    context_ = Azure::Core::Context().WithDeadline(
        std::chrono::system_clock::now() + timeout);
    report_["timeout"] = fmt::format("{}s", timeout.count());
}

void AzureBlobUploadJobImpl::uploadImpl() {
    auto hdfsFileStream =
        std::make_unique<AzureStreamWrappedHdfsFile>(srcFS_, srcUri_.path());
    fileSize_ = hdfsFileStream->Length();
    // Perform upload.
    Azure::Storage::Blobs::UploadBlockBlobOptions options;
    if (!overwrite_) {
        // If the blob already exists, the upload will fail.
        options.AccessConditions.IfNoneMatch = Azure::ETag::Any();
    }
    try {
        auto response = blobClient_.Upload(*hdfsFileStream, options, context_);
        // Verify crc64 hash.
        hdfsFileStream->FinalizeCrc64Hash();
        fileCrc64_ = hdfsFileStream->GetCrc64();
        if (forceCrc64Verify_) {
            verifyAzureCrc64(response.Value.TransactionalContentHash,
                             fileCrc64_, false);
        }
        auto& etag = response.Value.ETag;
        report_["etag"] = etag.HasValue() ? etag.ToString() : "null";
    } catch (...) {
        throw std::runtime_error(
            fmt::format("Failed to upload blob due to: {}",
                        folly::exceptionStr(std::current_exception())));
    }
    report_["crc64"] = fmt::format("{:016x}", fileCrc64_);
}

void AzureBlobUploadJobImpl::startMultipartUploadImpl(size_t partSize,
                                                      int parallel) {
    // Calculate optimal part size and part number.
    partSize_ = calculateOptimalPartSize(fileSize_, partSize);
    partNum_ = (fileSize_ + partSize_ - 1) / partSize_;
    parallel_ = std::min<int>(parallel, partNum_);

    // Nothing to upload.
    inputStreams_.resize(parallel_);
    if (partNum_ <= 0) {
        return;
    }

    // Open hdfs streams for reading, all stream would align to partSize_.
    try {
        size_t startOffset = 0;
        const int extraParts = partNum_ % parallel_;
        for (int i = 0; i < parallel_; ++i) {
            const int parts = (partNum_ / parallel_) + (i < extraParts ? 1 : 0);
            const size_t byteToRead = parts * partSize_;
            inputStreams_[i] = std::make_unique<AzureStreamWrappedHdfsFile>(
                srcFS_, srcUri_.path(), startOffset, byteToRead);
            startOffset += byteToRead;
        }
    } catch (...) {
        // Failed to open file, return error.
        inputStreams_.clear();
        throw;
    }
}

bool AzureBlobUploadJobImpl::uploadNextPartImpl(int parallelIdx) {
    // Make partial azure stream from the underlay input stream.
    auto& readStream = inputStreams_[parallelIdx];
    if (!readStream || readStream->IsCorrupted()) {
        throw std::runtime_error(
            fmt::format("readStream[{}] is corrupt.", parallelIdx));
    }
    if (readStream->IsEOF()) {
        return false;
    }

    auto partitailReadStream = std::make_unique<PartialAzureStream>(
        static_cast<AzureStreamWrappedHdfsFile*>(readStream.get()), partSize_);

    // Calculate start offset and part id.
    int64_t startOff = partitailReadStream->GetCurrentOffset();
    if (startOff % partSize_ != 0) {
        // read stream not aligned, return error.
        throw std::runtime_error(fmt::format(
            "Stream Corrupted."
            " readStream[{}] not aligned with partSize({}), current offset is {}.",
            parallelIdx, partSize_, startOff));
    }

    // Check duplication.
    int64_t partId = startOff / partSize_;
    std::string blockId = generateBlockId(partId);
    {
        std::lock_guard<std::mutex> lock(mutex_);
        auto iter = uploadedBlocks_.find(partId);
        if (iter != uploadedBlocks_.end()) {
            throw std::runtime_error(
                fmt::format("part-{} already uploaded with block id:'{}'.",
                            partId, blockId));
        }
        uploadedBlocks_[partId] = blockId;
    }

    // Perform stage block.
    try {
        Azure::Storage::Blobs::StageBlockOptions options;
        auto response = blobClient_.StageBlock(blockId, *partitailReadStream,
                                               options, context_);
        // Verify crc64 hash.
        partitailReadStream->FinalizeCrc64Hash();
        if (forceCrc64Verify_) {
            verifyAzureCrc64(response.Value.TransactionalContentHash,
                             partitailReadStream->GetCrc64(), false);
        }
    } catch (...) {
        // Remove block id from uploadedBlocks_ when upload failed.
        std::lock_guard<std::mutex> lock(mutex_);
        uploadedBlocks_.erase(partId);
        throw std::runtime_error(
            fmt::format("Failed to stage block due to: {}",
                        folly::exceptionStr(std::current_exception())));
    }
    return true;
}

void AzureBlobUploadJobImpl::completeMultipartUploadImpl() {
    // Generate block id list and calculate file crc64.
    std::vector<std::string> blockList;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        blockList = generateBlockList(true);
    }

    // Update file crc64.
    if (fileCrc64_ == 0) {
        Azure::Storage::Crc64Hash crc64Hash;
        for (auto& stream : inputStreams_) {
            auto azureStream =
                std::dynamic_pointer_cast<AzureStreamWrappedHdfsFile>(stream);
            if (!stream) {
                throw std::runtime_error("Read stream is corrupt.");
            }
            if (!stream->IsEOF()) {
                throw std::runtime_error(
                    "Stream not EOF, data itegrity is at risk.");
            }
            auto streamCrc64 = azureStream->FinalizeCrc64Hash();
            if (!streamCrc64) {
                throw std::runtime_error("Try to finalize read stream twice.");
            }
            crc64Hash.Concatenate(*streamCrc64);
        }
        fileCrc64_ = le_bytes_to_uint64(crc64Hash.Final());
    }

    // Commit block list.
    Azure::Storage::Blobs::CommitBlockListOptions options;
    if (!overwrite_) {
        // Commit would failed if any other client has committed.
        options.AccessConditions.IfNoneMatch = Azure::ETag::Any();
    }
    try {
        auto response =
            blobClient_.CommitBlockList(blockList, options, context_);
        auto& etag = response.Value.ETag;
        report_["etag"] = etag.HasValue() ? etag.ToString() : "null";
    } catch (...) {
        throw std::runtime_error(
            fmt::format("Failed to commit block list due to: {}",
                        folly::exceptionStr(std::current_exception())));
    }
    report_["crc64"] = fmt::format("{:016x}", fileCrc64_);
}

void AzureBlobUploadJobImpl::abortMultipartUploadImpl() {
    // There is no transactional abort operation in Azure Blob Storage SDK.
    // And it's not safe to delete the blob which maybe uploaded by other
    // client.
}

std::vector<std::string> AzureBlobUploadJobImpl::generateBlockList(
    bool verifyContinuous) {
    // Check total part number.
    if (verifyContinuous && uploadedBlocks_.size() != partNum_) {
        throw std::runtime_error(fmt::format(
            "Expect total part number {} equal to expect part number {}.",
            uploadedBlocks_.size(), partNum_));
    }

    // Check continuity and generate block list.
    std::vector<std::string> blockList;
    blockList.reserve(uploadedBlocks_.size());
    int64_t prevSeenBlockId = -1;
    for (const auto& blockPair : uploadedBlocks_) {
        int64_t blockIdInt = blockPair.first;
        if (verifyContinuous && blockIdInt != prevSeenBlockId + 1) {
            throw std::runtime_error(
                fmt::format("Expect block id {} but got {}.",
                            prevSeenBlockId + 1, blockIdInt));
        }
        blockList.push_back(blockPair.second);
        prevSeenBlockId = blockIdInt;
    }
    return blockList;
}

std::string AzureBlobUploadJobImpl::generateBlockId(int64_t partId) {
    auto padding = fmt::format("{:064d}", partId);
    return Azure::Core::Convert::Base64Encode(
        std::vector<uint8_t>(padding.begin(), padding.end()));
}

size_t AzureBlobUploadJobImpl::calculateOptimalPartSize(size_t fileSize,
                                                        size_t startFrom) {
    if (startFrom > kMaxPartSize) {
        throw std::invalid_argument(
            fmt::format("Start from {} is too large.", startFrom));
    }
    startFrom = std::max(round_up(startFrom, kPartSizeStepBits), kPartSizeStep);

    if (fileSize <= kOptimalPartNum * startFrom) {
        // align to 4MiB
        return startFrom;
    } else if (fileSize <= kOptimalPartNum * kMaxPartSize) {
        // round up to the nearest multiple of kPartSizeStep.
        return round_up(fileSize / kOptimalPartNum, kPartSizeStepBits);
    } else if (fileSize <= kMaxPartNum * kMaxPartSize) {
        return kMaxPartSize;
    } else {
        throw std::invalid_argument(
            fmt::format("File size {} is too large.", fileSize));
        // never reached here.
        return kMaxPartSize;
    }
}

void verifyAzureCrc64(Azure::Nullable<Azure::Storage::ContentHash>& verifyCrc64,
                      uint64_t expectCrc64, bool ignoreIfNotCrc64) {
    if (!verifyCrc64.HasValue() ||
        verifyCrc64.Value().Algorithm != Azure::Storage::HashAlgorithm::Crc64) {
        if (ignoreIfNotCrc64) {
            return;
        }
        throw std::runtime_error(
            "Expect crc64 hash value contained in response.");
    }

    uint64_t crc64FromAzure = le_bytes_to_uint64(verifyCrc64.Value().Value);
    if (crc64FromAzure != expectCrc64) {
        throw std::runtime_error(
            fmt::format("Crc64 hash mismatch, crc64Expect: {:016x},"
                        " crc64FromAzure: {:016x}",
                        expectCrc64, crc64FromAzure));
    }
}

AzureStreamWrappedHdfsFile::AzureStreamWrappedHdfsFile(hdfsFS fs,
                                                       const std::string& path,
                                                       int64_t offset,
                                                       int64_t length)
    : BaseHdfsInputStream(fs, path, offset, length),
      stagingCrc64_(std::make_unique<Azure::Storage::Crc64Hash>()),
      streamCrc64_(std::make_unique<Azure::Storage::Crc64Hash>()) {
    OpenHdfsFile();
}

AzureStreamWrappedHdfsFile::~AzureStreamWrappedHdfsFile() { CloseHdfsFile(); }

size_t AzureStreamWrappedHdfsFile::OnRead(uint8_t* buffer, size_t count,
                                          Azure::Core::Context const& context) {
    int64_t ret = BaseHdfsInputStream::Read(buffer, count);
    if (ret < 0) {
        throw std::runtime_error(
            fmt::format("Failed to read from hdfs file. ret: {}", ret));
    }
    return ret;
}

void AzureStreamWrappedHdfsFile::updateChecksum(const uint8_t* buffer,
                                                size_t count) {
    stagingCrc64_->Append(buffer, count);
}

int64_t AzureStreamWrappedHdfsFile::StageCrc64Hash() {
    if (stagingCrc64_ && streamCrc64_) {
        streamCrc64_->Concatenate(*stagingCrc64_);
    }
    uint64_t stagingChecksum = 0;
    if (stagingCrc64_) {
        stagingChecksum = le_bytes_to_uint64(stagingCrc64_->Final());
    }
    stagingCrc64_ = std::make_unique<Azure::Storage::Crc64Hash>();
    return stagingChecksum;
}

std::unique_ptr<Azure::Storage::Crc64Hash>
AzureStreamWrappedHdfsFile::FinalizeCrc64Hash() {
    StageCrc64Hash();
    if (streamCrc64_) {
        crc64Value_ = le_bytes_to_uint64(streamCrc64_->Final());
        return std::move(streamCrc64_);
    }
    return nullptr;
}

PartialAzureStream::PartialAzureStream(
    AzureStreamWrappedHdfsFile* underlayStream, int64_t length)
    : underlayStream_(underlayStream),
      startOffset_(underlayStream_->GetCurrentOffset()),
      length_(length) {
    // length should not be larger than the remaining data.
    int64_t remainingData =
        underlayStream_->endOffset_ - underlayStream_->GetCurrentOffset();
    if (length_ == -1 || length_ > remainingData) {
        length_ = remainingData;
    }
    curOffset_ = startOffset_;
    endOffset_ = startOffset_ + length_;
}

PartialAzureStream::~PartialAzureStream() = default;

size_t PartialAzureStream::OnRead(uint8_t* buffer, size_t count,
                                  Azure::Core::Context const& context) {
    if (!underlayStream_) {
        throw std::runtime_error("Underlay stream is already released.");
    }
    size_t copyLength = std::min<size_t>(count, length_);
    int64_t readBytes = 0;
    if (copyLength > 0) {
        readBytes = underlayStream_->OnRead(buffer, count, context);
    }
    if (readBytes > 0) {
        curOffset_ += readBytes;
        length_ -= readBytes;
    }
    return readBytes;
}

void PartialAzureStream::Rewind() {
    if (!underlayStream_) {
        throw std::runtime_error("Underlay stream is already released.");
    }
    underlayStream_->Seek(startOffset_);
    curOffset_ = startOffset_;
    length_ = endOffset_ - curOffset_;
}

void PartialAzureStream::FinalizeCrc64Hash() {
    crc64Value_ = underlayStream_->StageCrc64Hash();
}

// Turn int64_t into little endian bytes.
std::vector<uint8_t> uint64_to_le_bytes(uint64_t value) {
    uint64_t leValue = htole64(value);
    std::vector<uint8_t> result(8);
    *reinterpret_cast<uint64_t*>(result.data()) = leValue;
    return result;
}

// Turn little endian bytes to int64_t.
uint64_t le_bytes_to_uint64(const std::vector<uint8_t>& vec) {
    uint64_t leValue = 0;
    // If vec is too short, pad it with zero.
    if (vec.size() < 8) {
        std::vector<uint8_t> cloneVec = vec;
        cloneVec.resize(8, 0);
        leValue = *reinterpret_cast<const uint64_t*>(cloneVec.data());
    } else {
        leValue = *reinterpret_cast<const uint64_t*>(vec.data());
    }
    return le64toh(leValue);
}
}  // namespace Hdfs::Internal

#endif
