
#pragma once

#include <string>

namespace Hdfs::Internal {
constexpr const char* kAzureConnectionStringKey = "CPP_HDFS_AZURE_TOKEN_STRING";
constexpr const char* kGcsTokenStringKey = "CPP_HDFS_GCP_TOKEN_STRING";

// Get Azure connection string, if not set, will set it from env.
std::string GetAzureConnectionString();
// Set Azure connection string.
void SetAzureConnectionString(const std::string& connection_string);

// Get GCS token string, if not set, will set it from env.
std::string GetGcsToken();
// Set GCS token string.
void SetGcsToken(const std::string& token);
}  // namespace Hdfs::Internal
