// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#include "util/GcsUploadJob.h"

#ifndef DISABLE_THIRDPARTY_STORAGE
#include <fmt/chrono.h>
#include <fmt/format.h>
#include <folly/Exception.h>
#include <folly/Random.h>
#include <folly/String.h>
#include <folly/Uri.h>
#include <folly/dynamic.h>
#include <folly/hash/Checksum.h>
#include <folly/io/IOBuf.h>
#include <folly/lang/Bits.h>
#include <google/cloud/storage/client.h>
#include <google/cloud/storage/hashing_options.h>
#include <stdint.h>

#include <azure/core/base64.hpp>
#include <chrono>
#include <memory>
#include <string>
#include <vector>

#include "util/Path.h"
#include "util/util.h"

namespace gcs = google::cloud::storage;
using ::google::cloud::StatusOr;

namespace Hdfs::Internal {
GcsHdfsInputStream::GcsHdfsInputStream(hdfsFS fs, const std::string& path,
                                       int64_t offset, int64_t length)
    : BaseHdfsInputStream(fs, path, offset, length) {
    OpenHdfsFile();
}

GcsHdfsInputStream::~GcsHdfsInputStream() { CloseHdfsFile(); }

std::string GcsHdfsInputStream::EncodeGcsChecksum(uint32_t checksum) {
    // Folly calculate reverse crc32c, so we need to invert it to fix gcs.
    checksum = ~checksum;
    std::vector<uint8_t> bytes(sizeof(checksum));
    *reinterpret_cast<uint32_t*>(bytes.data()) =
        folly::Endian::big<uint32_t>(checksum);
    return Azure::Core::Convert::Base64Encode(bytes);
}

uint32_t GcsHdfsInputStream::CombineChecksum(uint32_t crc1, uint32_t crc2,
                                             size_t crc2_length) {
    return folly::crc32c_combine(~crc1, crc2, crc2_length);
}

void GcsHdfsInputStream::updateChecksum(const uint8_t* buffer, size_t count) {
    crc32c = folly::crc32c(buffer, count, crc32c);
}

GcsObjectUploadJobImpl::GcsObjectUploadJobImpl(
    hdfsFS srcFS, const HdfsPath& srcUri,
    std::shared_ptr<google::cloud::storage::Client> client,
    const folly::Uri& dstUri, bool overwrite)
    : BaseObjectUploadJob(srcFS, srcUri, dstUri, overwrite),
      gcsClient_(std::move(client)),
      deadline_(std::chrono::steady_clock::time_point::max()) {}

GcsObjectUploadJobImpl::~GcsObjectUploadJobImpl() {}

void GcsObjectUploadJobImpl::parseDstUri() {
    // Gcs use host part in uri as its bucket name.
    bucket_ = dstUri_.host();
    // Remove leading '/' in object name.
    folly::StringPiece objectUri(dstUri_.path());
    objectUri.removePrefix("/");
    object_ = objectUri;
    // Recheck bucket and object name.
    if (bucket_.empty() || object_.empty()) {
        throw std::invalid_argument(fmt::format(
            "Failed to parse dst uri: {}.", dstUri_.toString<std::string>()));
    }
}

void GcsObjectUploadJobImpl::uploadImpl() {
    parseDstUri();
    auto inputStream =
        std::make_unique<GcsHdfsInputStream>(srcFS_, srcUri_.path());
    fileSize_ = inputStream->GetLength();
    if (fileSize_ <= kSimpleUploadObjectLimit) {
        // Use insert upload object API if object size smaller than 20MiB.
        simpleUploadImpl(inputStream.get());
    } else {
        streamingUploadImpl(inputStream.get());
    }
}

void GcsObjectUploadJobImpl::simpleUploadImpl(GcsHdfsInputStream* inputStream) {
    // fully read content from stream.
    std::string content;
    content.resize(fileSize_);
    int64_t readBytes =
        inputStream->ReadFully(reinterpret_cast<uint8_t*>(content.data()),
                               fileSize_, kStreamingUploadPacketSize);
    // Expect to read all data.
    if (readBytes < 0) {
        throw std::runtime_error(
            fmt::format("Failed to read data from stream."));
    } else if (!inputStream->IsEOF() || readBytes != fileSize_) {
        throw std::runtime_error(fmt::format(
            "Failed to read all data from stream. curoff: {}, length: {}",
            inputStream->GetCurrentOffset(), inputStream->GetLength()));
    }
    // Compute checksum.
    std::string encodedChecksum = inputStream->EncodeGcsChecksum();
    // Upload small object with InsertObject() API.
    StatusOr<gcs::ObjectMetadata> metadata;
    if (overwrite_) {
        metadata =
            gcsClient_->InsertObject(bucket_, object_, content,
                                     gcs::Crc32cChecksumValue(encodedChecksum));
    } else {
        metadata =
            gcsClient_->InsertObject(bucket_, object_, content,
                                     gcs::Crc32cChecksumValue(encodedChecksum),
                                     gcs::IfGenerationMatch(0));
    }
    if (!metadata) {
        throw std::runtime_error(fmt::format("Failed to upload object. {}",
                                             metadata.status().message()));
    }
    report_["etag"] = metadata->etag();
    report_["generation"] = metadata->generation();
    report_["crc32c"] = fmt::format("{:08x}", inputStream->GetCrc32c());
    report_["crc32c_encoded"] = fmt::format("{}", encodedChecksum);
}

void GcsObjectUploadJobImpl::streamingUploadImpl(
    GcsHdfsInputStream* inputStream) {
    // Open gcs write stream on target object.
    std::shared_ptr<gcs::ObjectWriteStream> gcsStream;
    if (overwrite_) {
        gcsStream =
            std::make_shared<gcs::ObjectWriteStream>(gcsClient_->WriteObject(
                bucket_, object_, gcs::NewResumableUploadSession(),
                gcs::AutoFinalizeDisabled()));
    } else {
        gcsStream =
            std::make_shared<gcs::ObjectWriteStream>(gcsClient_->WriteObject(
                bucket_, object_, gcs::NewResumableUploadSession(),
                gcs::AutoFinalizeDisabled(), gcs::IfGenerationMatch(0)));
    }
    // Check gcs stream open success.
    if (!gcsStream->IsOpen()) {
        throw std::runtime_error(
            fmt::format("Failed to open gcs write stream. {}",
                        gcsStream->metadata().status().message()));
    }
    auto iobuf = folly::IOBuf::create(kStreamingUploadPacketSize);
    iobuf->append(kStreamingUploadPacketSize);
    try {
        // Streaming upload.
        int kMaxLoopLimit = INT32_MAX;  // (2^31-1) * 2MiB = 4PB
        do {
            forwardStreaming(inputStream, gcsStream.get(), iobuf.get(),
                             deadline_);
        } while (!inputStream->IsEOF() && --kMaxLoopLimit > 0);
        // Close write stream and verify checksum.
        gcsStream->Close();
        if (!gcsStream->metadata()) {
            throw std::runtime_error(
                fmt::format("Failed to close gcs write stream. {}",
                            gcsStream->metadata().status().message()));
        }
        auto derivedInputStream =
            reinterpret_cast<GcsHdfsInputStream*>(inputStream);
        std::string localCrc32c = derivedInputStream->EncodeGcsChecksum();
        std::string remoteCrc32c = gcsStream->metadata()->crc32c();
        if (remoteCrc32c != localCrc32c) {
            throw std::runtime_error(
                fmt::format("Checksum mismatch. local: {}, remote: {}",
                            localCrc32c, remoteCrc32c));
        }
        fileCrc32c_ = derivedInputStream->GetCrc32c();
        report_["etag"] = gcsStream->metadata()->etag();
        report_["generation"] = gcsStream->metadata()->generation();
        report_["crc32c"] = fmt::format("{:08x}", fileCrc32c_);
        report_["crc32c_encoded"] = fmt::format("{}", localCrc32c);
    } catch (...) {
        // Abort resumable upload when failed.
        if (gcsStream->IsOpen()) {
            gcsClient_->DeleteResumableUpload(
                gcsStream->resumable_session_id());
        }
        // It's safe to delete object when generation is not 0.
        if (gcsStream->metadata()->generation() > 0) {
            safeDeleteObject(gcsClient_.get(), bucket_, object_,
                             gcsStream->metadata()->generation());
        }
        throw;
    }
}

void GcsObjectUploadJobImpl::startMultipartUploadImpl(size_t partSize,
                                                      int parallel) {
    // Calculate optimal part size and part number.
    partSize_ = round_up(partSize, 20);
    partNum_ = (fileSize_ + partSize - 1) / partSize;
    parallel_ = std::min<int>(parallel, partNum_);
    iobufs_.clear();
    iobufs_.resize(parallel_);

    inputStreams_.resize(parallel_);
    parseDstUri();
    if (partNum_ <= 0) {
        return;
    }

    // Open hdfs input streams.
    try {
        size_t startOffset = 0;
        const int extraParts = partNum_ % parallel_;
        for (int i = 0; i < parallel_; ++i) {
            const int parts = (partNum_ / parallel_) + (i < extraParts ? 1 : 0);
            const size_t byteToRead = parts * partSize_;
            inputStreams_[i] = std::make_unique<GcsHdfsInputStream>(
                srcFS_, srcUri_.path(), startOffset, byteToRead);
            startOffset += byteToRead;
        }
    } catch (...) {
        // Failed to open file, return error.
        inputStreams_.clear();
        fileSize_ = 0;
        partNum_ = 0;
        throw;
    }

    // Open gcs write stream.
    tmpObjectsContainer_ = makeTmpObjectContainerName(object_);
    report_["tmpobj_prefix"] = tmpObjectsContainer_;
    for (int i = 0; i < parallel_; ++i) {
        auto tmpObject = fmt::format("{}{:08d}", tmpObjectsContainer_, i);
        auto gcsStream =
            std::make_shared<gcs::ObjectWriteStream>(gcsClient_->WriteObject(
                bucket_, tmpObject, gcs::NewResumableUploadSession(),
                gcs::AutoFinalizeDisabled()));
        if (!gcsStream->IsOpen()) {
            throw std::runtime_error(fmt::format(
                "Failed to open gcs write stream on {}, {}", tmpObject,
                gcsStream->metadata().status().message()));
        }
        gcsStreams_.push_back(gcsStream);
        tmpObjects_.push_back(tmpObject);
    }

    // Preallocate io bufs.
    for (int i = 0; i < parallel_; ++i) {
        iobufs_[i] = folly::IOBuf::create(kStreamingUploadPacketSize);
        iobufs_[i]->append(kStreamingUploadPacketSize);
    }
}

void GcsObjectUploadJobImpl::completeMultipartUploadImpl() {
    // Close all gcs write stream with checksum.
    std::vector<int64_t> generations(gcsStreams_.size());
    for (int i = 0; i < gcsStreams_.size(); ++i) {
        auto& inputStream = inputStreams_[i];
        auto& gcsStream = gcsStreams_[i];
        // Double check input stream is EOF.
        if (!inputStream->IsEOF()) {
            throw std::runtime_error(fmt::format(
                "Stream not EOF, data itegrity is at risk. offset: {}, length: {}",
                inputStream->GetCurrentOffset(), inputStream->GetLength()));
        }
        gcsStream->Close();
        if (!gcsStream->metadata()) {
            throw std::runtime_error(
                fmt::format("Failed to close gcs write stream. {}",
                            gcsStream->metadata().status().message()));
        }
        auto derivedInputStream =
            std::dynamic_pointer_cast<GcsHdfsInputStream>(inputStream);
        std::string localCrc32c = derivedInputStream->EncodeGcsChecksum();
        std::string remoteCrc32c = gcsStream->metadata()->crc32c();
        if (remoteCrc32c != localCrc32c) {
            throw std::runtime_error(
                fmt::format("Checksum mismatch. local: {}, remote: {}",
                            localCrc32c, remoteCrc32c));
        }
        generations[i] = gcsStream->metadata()->generation();
        fileCrc32c_ = GcsHdfsInputStream::CombineChecksum(
            fileCrc32c_, derivedInputStream->GetCrc32c(),
            inputStream->GetLength());
    }

    // Compose tmp object to final object.
    std::vector<google::cloud::storage::ComposeSourceObject> compose_objects;
    for (int i = 0; i < tmpObjects_.size(); ++i) {
        compose_objects.push_back(
            {tmpObjects_[i], generations[i], generations[i]});
    }
    StatusOr<google::cloud::storage::ObjectMetadata> metadata;
    if (compose_objects.empty()) {
        if (overwrite_) {
            metadata = gcsClient_->InsertObject(bucket_, object_, "");
        } else {
            metadata = gcsClient_->InsertObject(bucket_, object_, "",
                                                gcs::IfGenerationMatch(0));
        }
    } else if (compose_objects.size() <= kComposeObjectNumLimit) {
        if (overwrite_) {
            metadata = gcsClient_->ComposeObject(
                bucket_, std::move(compose_objects), object_);
        } else {
            metadata =
                gcsClient_->ComposeObject(bucket_, std::move(compose_objects),
                                          object_, gcs::IfGenerationMatch(0));
        }
    } else {
        if (overwrite_) {
            metadata = gcs::ComposeMany(*gcsClient_, bucket_,
                                        std::move(compose_objects),
                                        tmpObjectsContainer_, object_, true);
        } else {
            metadata = gcs::ComposeMany(
                *gcsClient_, bucket_, std::move(compose_objects),
                tmpObjectsContainer_, object_, true, gcs::IfGenerationMatch(0));
        }
    }

    // Verify checksum in response.
    if (!metadata) {
        throw std::runtime_error(fmt::format("Failed to compose object. {}",
                                             metadata.status().message()));
    }
    std::string localCrc32c =
        GcsHdfsInputStream::EncodeGcsChecksum(fileCrc32c_);
    if (metadata->crc32c() != localCrc32c) {
        // Clean up if checksum mismatch.
        safeDeleteObject(gcsClient_.get(), bucket_, object_,
                         metadata->generation());
        throw std::runtime_error(
            fmt::format("Checksum mismatch. local: {}, remote: {}", localCrc32c,
                        metadata->crc32c()));
    }

    report_["etag"] = metadata->etag();
    report_["generation"] = metadata->generation();
    report_["crc32c"] = fmt::format("{:08x}", fileCrc32c_);
    report_["crc32c_encoded"] = fmt::format("{}", localCrc32c);

    // Clean up tmp object container.
    cleanUpTmpObjectContainer();
}

void GcsObjectUploadJobImpl::abortMultipartUploadImpl() {
    // Cancel all resumable uploads.
    for (auto& gcsStream : gcsStreams_) {
        if (!gcsStream->IsOpen()) {
            // Maybe already closed in completeMultipartUploadImpl.
            continue;
        }
        auto status = gcsClient_->DeleteResumableUpload(
            gcsStream->resumable_session_id());
        if (!status.ok() &&
            status.code() != google::cloud::StatusCode::kNotFound) {
            // Ignore, just log it.
            report_["abort_fail_reason"] = status.message();
            fmt::print(stderr,
                       "Failed to abort resumable upload: {}, error: {}\n",
                       gcsStream->resumable_session_id(), status.message());
        }
    }
    gcsStreams_.clear();
    // Clean up tmp object container.
    cleanUpTmpObjectContainer();
}

void GcsObjectUploadJobImpl::cleanUpTmpObjectContainer() {
    // Check if it's safe to clean up tmp objects recursively.
    if (tmpObjectsContainer_.empty()) {
        return;
    }
    // Double check that tmp container is under TMP directory.
    if (tmpObjectsContainer_.find(kUploadTmpObjectPrefix) != 0) {
        throw std::runtime_error(fmt::format(
            "Tmp object container {} not under tmp directory, not safe to delete.",
            tmpObjectsContainer_));
    }
    // Send delete request to gcs.
    auto status =
        gcs::DeleteByPrefix(*gcsClient_, bucket_, tmpObjectsContainer_);
    if (!status.ok() && status.code() != google::cloud::StatusCode::kNotFound) {
        report_["abort_fail_reason"] = status.message();
        auto errormsg =
            fmt::format("Failed to delete tmp object container: {}, error: {}",
                        tmpObjectsContainer_, status.message());
        fmt::print(stderr, "{}", errormsg);
        throw std::runtime_error(errormsg);
    }
}

bool GcsObjectUploadJobImpl::uploadNextPartImpl(int parallelIdx) {
    // Fully read part from input stream.
    forwardStreaming(inputStreams_[parallelIdx].get(),
                     gcsStreams_[parallelIdx].get(), iobufs_[parallelIdx].get(),
                     deadline_);
    return !inputStreams_[parallelIdx]->IsEOF();
}

std::string GcsObjectUploadJobImpl::makeTmpObjectContainerName(
    const std::string& finalObjectName) const {
    uint64_t hexDigest = std::hash<std::string>{}(finalObjectName);
    return fmt::format("{}{:016x}_{:016x}/", kUploadTmpObjectPrefix,
                       folly::Random::rand64(), hexDigest);
}

void GcsObjectUploadJobImpl::forwardStreaming(
    BaseHdfsInputStream* inputStream,
    google::cloud::storage::ObjectWriteStream* outputStream,
    folly::IOBuf* iobuf, std::chrono::steady_clock::time_point deadline) {
    // Check timedout.
    if (std::chrono::steady_clock::now() > deadline) {
        throw std::runtime_error("Timeout on streaming upload");
    }
    // Read data from HDFS input stream.
    int64_t readBytes = inputStream->ReadFully(
        iobuf->writableData(), iobuf->length(), kStreamingUploadPacketSize);
    if (readBytes < 0) {
        throw std::runtime_error(
            fmt::format("Failed to read data from stream."));
    }
    if (!inputStream->IsEOF()) {
        // Gcs restricts that write data must align to 256KiB, except the last
        // chunk.
        if (readBytes != iobuf->length()) {
            throw std::runtime_error(fmt::format(
                "Failed to fully read data from stream. curoff: {}, length: {}",
                inputStream->GetCurrentOffset(), inputStream->GetLength()));
        }
    } else if (readBytes == 0) {
        // Input stream reach EOF and no more data to write, return.
        return;
    }

    // Write to gcs object.
    outputStream->write((const char*)iobuf->data(), readBytes);
    if (outputStream->fail()) {
        throw std::runtime_error(
            fmt::format("Write stream write failed. error: {}",
                        outputStream->metadata().status().message()));
    }
}

bool GcsObjectUploadJobImpl::safeDeleteObject(
    google::cloud::storage::Client* client, const std::string& bucket_,
    const std::string& object, int64_t generation) {
    if (generation == 0) {
        throw std::invalid_argument(
            "safeDeleteObject() does not accept 0 as generation.");
    }
    auto status = client->DeleteObject(bucket_, object,
                                       gcs::IfGenerationMatch(generation));
    if (status.ok() || status.code() != google::cloud::StatusCode::kNotFound) {
        return true;
    } else {
        return false;
    }
}
}  // namespace Hdfs::Internal

#endif
