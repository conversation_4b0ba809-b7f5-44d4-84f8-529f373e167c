#!/bin/bash

# This shell script is only used when users get this hdfs binary from SCM (
# i.e. via `bvc clone xxx`).
# This script cd to "native_hdfs_client_home/bin" and then run hdfs.bin because
# the rpath of hdfs.bin is set to "../lib:../lib/glibc-bundle-minimal"
# so as to use the bundled glibc-2.28.
#
# Note that the rpath is only set when compiled with "build.sh --bundle-glibc",
# which is used as the SCM build commond.

# The directory structure of binaries and libraries from SCM should be:
#
# native_hdfs_client_home
# |-- include
# |   |-- hdfsAsyncContext.h
# |   `-- hdfs.h
# |-- bin
# |   |-- hdfs.bin
# |   |-- hdfs.sh
# |   `-- hdfs --> hdfs.sh
# `-- lib
#     |-- glibc-bundle-minimal
#     |   `--  libc.so, libm.so, libresolv.so, ld-2.28.so, etc
#     `-- lib_hdfs_client.so

# set -e -o pipefail

# export INFSEC_HADOOP_ENABLED=1

CUR_DIR=`dirname "$0"`
HOME_DIR=`cd ${CUR_DIR}/..;pwd`

# Run hdfs.bin with the interpreter from the bundled glibc-2.28 and specify
# the load library path to the bundled glibc-2.28 path.
export LD_PRELOAD_BACKUP=${LD_PRELOAD}
unset LD_PRELOAD
export LD_PRELOAD=${HOME_DIR}/lib/libhdfs_client.so
export CPP_HDFS_ENABLE_PARALLEL_INPUT_PERFORMANCE=true
${HOME_DIR}/bin/hdfs.bin "$@"
HDFS_EXIT_CODE=$?
export LD_PRELOAD=${LD_PRELOAD_BACKUP}
export CPP_HDFS_ENABLE_PARALLEL_INPUT_PERFORMANCE=""
exit ${HDFS_EXIT_CODE}
