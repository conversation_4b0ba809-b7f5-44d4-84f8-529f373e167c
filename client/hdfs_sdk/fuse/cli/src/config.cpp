#include "config.h"

#include <regex>

#include "fstream"
#include "logger.h"
#include "sstream"
#include "string"

config root_config;

void config::set_config(hdfsBuilder* bld, const std::string& key,
                        const std::string& value) {
    static config_default<int> int_values[] = {
        {&max_retry_on_connection_, "dfs.fuse.max-retry-on-connection", 5,
         nullptr},
        {&pread_adaptive_threshold, "dfs.fuse.pread-adaptive-threshold",
         1048576, nullptr},
        {&stripe_unit_size_, "dfs.fuse.stripe.unit.size", 0, nullptr},
        {&stripe_unit_count_, "dfs.fuse.stripe.unit.count", 0, nullptr},
        {&fuse_attr_gid_, "dfs.fuse.attr.gid", 0, nullptr},
        {&fuse_attr_uid_, "dfs.fuse.attr.uid", 0, nullptr},
        {&open_wait_closed_sec, "dfs.fuse.open.wait.closed.sec", 30, nullptr}};

    static config_default<std::string> str_values[] = {
        {&acc_write_mode_str_, "dfs.fuse.acc.write-mode", "async", nullptr},
        {&acc_append_file_pattern_, "dfs.fuse.acc.append-file-pattern", "",
         nullptr},
        {&log_severity_, "dfs.fuse.log.severity", "INFO", nullptr},
        {&fuse_hdfs_prefix_, "dfs.fuse.mount.prefix", "", nullptr},
        {&fuse_hdfs_namespace_, "dfs.fuse.mount.namespace", "", nullptr},
    };

    static config_default<bool> bool_values[] = {
        {&is_pread_, "dfs.fuse.use.pread", false, nullptr},
        {&is_adaptive_pread_, "dfs.fuse.is-adaptive-pread", true, nullptr},
        {&is_fio_test, "dfs.fuse.is-fio-test", false, nullptr},
        {&fuse_enable_delete_, "dfs.fuse.delete.enable", false, nullptr},
    };

    std::call_once(init_config_flag_, [&]() {
        for (auto& c : int_values) {
            *(c.variable) = c.value;
        }
        for (auto& c : str_values) {
            *(c.variable) = c.value;
        }
        for (auto& c : bool_values) {
            *(c.variable) = c.value;
        }
    });

    for (auto& c : int_values) {
        if (c.key == key) {
            *(c.variable) = std::stoi(value);
            LOG(INFO, "FUSE config key=%s, value=%d", key.c_str(),
                *(c.variable));
            return;
        }
    }

    for (auto& c : str_values) {
        if (c.key == key) {
            *(c.variable) = value;
            LOG(INFO, "FUSE config key=%s, value=%s", key.c_str(),
                c.variable->c_str());
            return;
        }
    }

    for (auto& c : bool_values) {
        if (c.key == key) {
            *(c.variable) = value == "true" ? true : false;
            LOG(INFO, "FUSE config key=%s, value=%d", key.c_str(),
                *(c.variable));
            return;
        }
    }

    LOG(INFO, "FUSE set libhdfs config key=%s, value=%s", key.c_str(),
        value.c_str());
    hdfsBuilderConfSetStr(bld, key.c_str(), value.c_str());
}

bool config::parse_config(hdfsBuilder* bld) {
    std::vector<std::pair<std::string, std::string>> defualt_libhdfs_configs = {
        {"cfs.client.metadata-cache.magicSleepUs", "3000"},
    };
    for (auto& config : defualt_libhdfs_configs) {
        config::set_config(bld, config.first, config.second);
    }

    std::string conf_file;
    if (const char* conf_file_ = getenv("HDFS_FUSE_CONF")) {
        conf_file = conf_file_;
        LOG(INFO, "HDFS_FUSE_CONF is %s", conf_file.c_str());
    } else {
        LOG(ERROR, "HDFS_FUSE_CONF not set");
        return false;
    }

    std::ifstream file_stream(conf_file);
    if (file_stream.bad()) {
        LOG(ERROR, "Failed to load config file %s", conf_file.c_str());
        return false;
    }

    std::string line, key, value;
    while (std::getline(file_stream >> std::ws, line)) {
        if (line.front() == '#') {
            continue;
        }
        std::istringstream line_stream(line);
        if (std::getline(line_stream, key, '=')) {
            key.resize(key.find_last_not_of(' ') + 1);
            if (key.empty()) {
                continue;
                ;
            }
            if (std::getline(line_stream >> std::ws, value)) {
                value.resize(value.find_last_not_of(' ') + 1);
                config::set_config(bld, key, value);
            }
        }
    }
    try {
        const std::regex regex(acc_append_file_pattern_);
    } catch (std::regex_error& e) {
        LOG(ERROR,
            "Failed to init fuse because of regex_error, please check "
            "'dfs.fuse.acc.append-file-pattern'");
        return false;
    }
    return true;
}
