syntax="proto2";
import "google/protobuf/descriptor.proto";

package byterpc.proto;

option cc_generic_services = true;

// Internal test usage
message IndexRequest {}
message IndexResponse {} 
message PingRequest {}
message PingResponse {}
message StatusRequest {}
message StatusResponse {}

// Flag service, show and modify current progress gflags
message FlagsRequest {}
message FlagsResponse {} 

// Trigger dump stacktrace by user
message DumpStackTraceRequest {}
message DumpStackTraceResponse {}

// Show connection status of each byterpc thread
message ConnectionsRequest {}
message ConnectionsResponse {}

// Internal usage, get some frontend js scripts for web page
message GetJsRequest {}
message GetJsResponse {}

// For Fallback channel
message HealthCheckRequest {}
message HealthCheckResponse {}
message SwitchTransportRequest {}
message SwitchTransportResponse {}

// ServiceStatus service
message ServiceStatusRequest {}
message ServiceStatusResponse {} 

service index {
    rpc default_method(IndexRequest) returns (IndexResponse);
}

service ping {
    rpc default_method(PingRequest) returns (PingResponse);
}

service status {
    rpc default_method(StatusRequest) returns (StatusResponse);
}

service flags {
    rpc default_method(FlagsRequest) returns (FlagsResponse);
}

service dump_stack_trace {
    rpc default_method(DumpStackTraceRequest) returns (DumpStackTraceResponse);
}

service connections {
    rpc default_method(ConnectionsRequest) returns (ConnectionsResponse);
}

service js {
    rpc jquery_min(GetJsRequest) returns (GetJsResponse);
    rpc flot_min(GetJsRequest) returns (GetJsResponse);
}

service transport {
    rpc check_health(HealthCheckRequest) returns (HealthCheckResponse);
    rpc switch_backup(SwitchTransportRequest) returns (SwitchTransportResponse);
    rpc switch_prefer(SwitchTransportRequest) returns (SwitchTransportResponse);
}

service service_status {
    rpc default_method(ServiceStatusRequest) returns (ServiceStatusResponse);
}
