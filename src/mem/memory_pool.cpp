// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <iterator>
#include <mutex>  // NOLINT(build/c++11)
#include <string>
#include <utility>

#include <byte/base/closure.h>
#include <byte/string/algorithm.h>

#include "background_task/watcher_thread_helper.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"
#include "mem/bemalloc_memory_pool.h"
#include "mem/default_region_alloc_hook.h"
#include "mem/hugepage_region_alloc_hook.h"
#include "mem/memory_pool.h"
#include "util/block_pool.h"

namespace byterpc {

MemoryPool* MemoryPool::g_memory_pool = nullptr;

bool MemoryPool::Init(RegionAllocHook hugepage_region_alloc_hook,
                      RegionAllocCallBack region_alloc_call_back) {
    if (BYTERPC_UNLIKELY(g_memory_pool)) {
        BYTERPC_LOG(ERROR) << "Should not double init MemoryPool";
        return false;
    }

    if (!hugepage_region_alloc_hook.alloc || !hugepage_region_alloc_hook.dealloc) {
        if (BYTERPC_UNLIKELY(hugepage_region_alloc_hook.alloc ||
                             hugepage_region_alloc_hook.dealloc)) {
            BYTERPC_LOG(ERROR) << "alloc and dealloc must be set together";
            return false;
        }
        if (FLAGS_byterpc_byte_express_memory_pool_use_hugepage) {
            hugepage_region_alloc_hook.alloc = &HugepageRegionAllocHook::AllocRegion;
            hugepage_region_alloc_hook.dealloc = &HugepageRegionAllocHook::DeallocRegion;
        }
    }

    RegionAllocator region_allocator(
        hugepage_region_alloc_hook,
        {&DefaultRegionAllocHook::AllocRegion, &DefaultRegionAllocHook::DeallocRegion},
        region_alloc_call_back);

    // TODO(dpw): support other memory allocators besides bemalloc
    g_memory_pool = new BemallocMemoryPool(region_allocator);

    if (!g_memory_pool->InitRegions()) {
        BYTERPC_LOG(ERROR) << "MemoryPool fails to init regions";
        delete g_memory_pool;
        return false;
    }

    if (FLAGS_byterpc_enable_block_pool) {
        if (!util::InitBlockPools()) {
            delete g_memory_pool;
            return false;
        }
    }

    return true;
}

void MemoryPool::Reset() {
    if (FLAGS_byterpc_enable_block_pool) {
        util::DestroyBlockPools();
    }
    delete g_memory_pool;
    g_memory_pool = nullptr;
}

MemoryPool::MemoryPool(const RegionAllocator& region_allocator)
    : _region_size(FLAGS_byterpc_memory_pool_region_size),
#ifdef BYTERPC_WITH_NUMA
      _enable_numa(FLAGS_byterpc_enable_numa_aware),
#else
      _enable_numa(false),
#endif
      _region_map(),
      _region_map_rwlock(byte::RwLock::kKindPreferWriter),
      _total_region_num(0),
      _region_allocator(region_allocator),
      _region_managers() {
    BYTERPC_CHECK(FLAGS_byterpc_min_resident_capacity_in_MB_to_expand_memory * 1024 * 1024UL <
                  _region_size);
    int numa_num = _enable_numa ? util::get_numa_num() : 0;
    if (numa_num > 0) {
        BYTERPC_CHECK_GE(numa_num, 2);

        std::vector<std::string> max_hugepage_regions_str;
        byte::SplitString(
            FLAGS_byterpc_memory_pool_numa_max_hugepage_region_num, ",", &max_hugepage_regions_str);

        std::vector<uint32_t> max_hugepage_regions(max_hugepage_regions_str.size());
        for (size_t i = 0; i < max_hugepage_regions_str.size(); ++i) {
            try {
                max_hugepage_regions[i] = std::stoul(max_hugepage_regions_str[i]);
            } catch (const std::exception& e) {
                BYTERPC_LOG(FATAL) << "Invalid byterpc_memory_pool_numa_max_hugepage_region_num: "
                                   << FLAGS_byterpc_memory_pool_numa_max_hugepage_region_num;
            }
        }

        for (uint32_t i = 0; i < static_cast<uint32_t>(numa_num); ++i) {
            uint32_t max_hugepage_region_num =
                (max_hugepage_regions.size() > i) ? max_hugepage_regions[i] : UINT32_MAX;
            _region_managers.emplace_back(new RegionManager(i, max_hugepage_region_num));
        }
    } else {
        BYTERPC_CHECK(!_enable_numa)
            << "Numa-aware is enabled, but no numa device is found. Should "
               "use numa-supported environment or disable numa-aware.";

        _region_managers.emplace_back(
            new RegionManager(-1, FLAGS_byterpc_byte_express_memory_pool_max_hugepage_regions));
    }
}

void* MemoryPool::GetUserDataQuickly(const void* addr) {
    return GetUserData(addr);
}

void* MemoryPool::GetUserData(const void* addr) {
    byte::RwLock::ReaderLocker lock(&_region_map_rwlock);
    auto iter = _region_map.upper_bound(addr);
    if (BYTERPC_LIKELY(iter != _region_map.end()) &&
        BYTERPC_LIKELY(static_cast<const Region*>(iter->second)->RegionInfo().ContainAddr(addr))) {
        return static_cast<const Region*>(iter->second)->Data();
    }
    return nullptr;
}

MemoryPool::~MemoryPool() {
    // wait for all background alloc tasks to finish
    for (size_t i = 0; i < _region_managers.size(); ++i) {
        RegionManager* manager = _region_managers[i].get();
        std::unique_lock<std::mutex> region_lock(manager->region_mutex);
        if (manager->is_allocating) {
            manager->cond.wait(region_lock);
        }
    }
    DeallocFreeRegions();
    BYTERPC_DCHECK(_total_region_num.load() == 0)
        << "All memory is not freed when memory pool is destructed";
}

void MemoryPool::DeallocFreeRegions() {
    for (size_t i = 0; i < _region_managers.size(); ++i) {
        RegionManager* manager = _region_managers[i].get();
        while (!manager->uninit_list.empty()) {
            manager->region_mutex.lock();
            Region* region = manager->uninit_list.front();
            manager->uninit_list.pop_front();
            manager->avail_mem_size -= region->RegionInfo().len;
            manager->region_mutex.unlock();
            DeallocRegion(i, region);
        }
    }
}

size_t MemoryPool::NumaIdToIndex(int numa_id) const {
    if (_enable_numa) {
        if (BYTERPC_UNLIKELY(numa_id < 0)) {
            thread_local int current_thread_numa_id = util::get_numa_id_of_this_thread();
            if (BYTERPC_UNLIKELY(current_thread_numa_id < 0)) {
                return 0;
            } else {
                return current_thread_numa_id;
            }
        } else {
            return numa_id;
        }
    }
    return 0;
}

MemoryPoolStatistics MemoryPool::GetMemoryPoolStatistics() {
    MemoryPoolStatistics stat;
    stat.avail_mem.resize(_region_managers.size());
    stat.region_num.resize(_region_managers.size());
    for (size_t i = 0; i < _region_managers.size(); ++i) {
        RegionManager* manager = _region_managers[i].get();
        manager->region_mutex.lock();
        stat.avail_mem[i] = manager->avail_mem_size;
        manager->region_mutex.unlock();
        manager->alloc_mutex.lock();
        stat.region_num[i] = manager->total_region_num;
        manager->alloc_mutex.unlock();
    }
    return stat;
}

bool MemoryPool::InitRegions() {
    if (_enable_numa) {
        std::vector<std::string> init_regions_str;
        byte::SplitString(FLAGS_byterpc_memory_pool_numa_init_region_num, ",", &init_regions_str);

        std::vector<uint32_t> init_regions(init_regions_str.size());
        bool init_str_all_zero = true;
        for (size_t i = 0; i < init_regions_str.size(); ++i) {
            try {
                init_regions[i] = std::stoul(init_regions_str[i]);
            } catch (const std::exception& e) {
                BYTERPC_LOG(ERROR) << "Invalid byterpc_memory_pool_numa_init_region_num: "
                                   << FLAGS_byterpc_memory_pool_numa_init_region_num;
                return false;
            }
            if (init_regions[i]) {
                init_str_all_zero = false;
            }
        }

        // For compatibility, if all zeros in byterpc_memory_pool_numa_init_region_num, we use
        // byterpc_numa0_memory_pool_init_regions and byterpc_numa1_memory_pool_init_regions
        if (init_str_all_zero) {
            init_regions.resize(2);
            init_regions[0] = FLAGS_byterpc_numa0_memory_pool_init_regions;
            init_regions[1] = FLAGS_byterpc_numa1_memory_pool_init_regions;
        }

        if (_region_managers.size() != init_regions.size()) {
            BYTERPC_LOG(WARNING) << "There are " << _region_managers.size()
                                 << " numa nodes on the machine, but " << init_regions.size()
                                 << " nodes' init region nums are specified";
        }

        size_t n = init_regions.size() < _region_managers.size() ? init_regions.size()
                                                                 : _region_managers.size();
        for (size_t i = 0; i < n; ++i) {
            for (uint32_t k = 0; k < init_regions[i]; ++k) {
                Region* region = AllocRegion(i);
                if (!region) {
                    BYTERPC_LOG(ERROR) << "Fail to allocate region on numa "
                                       << _region_managers[i]->numa_id << " when init";
                    return false;
                }
                _region_managers[i]->uninit_list.emplace_back(region);
                _region_managers[i]->avail_mem_size += region->RegionInfo().len;
            }
        }
    } else {
        for (uint32_t k = 0; k < FLAGS_byterpc_byte_express_memory_pool_init_regions; ++k) {
            Region* region = AllocRegion(0);
            if (!region) {
                BYTERPC_LOG(ERROR) << "Fail to allocate region when init";
                return false;
            }
            _region_managers[0]->uninit_list.emplace_back(region);
            _region_managers[0]->avail_mem_size += region->RegionInfo().len;
        }
    }
    return true;
}

bool MemoryPool::InsertIntoRegionMap(const Region* region) {
    byte::RwLock::WriterLocker lock(&_region_map_rwlock);
    return _region_map
        .emplace(std::make_pair(
            reinterpret_cast<const void*>(reinterpret_cast<uint64_t>(region->RegionInfo().vaddr) +
                                          region->RegionInfo().len),
            region))
        .second;
}

bool MemoryPool::RemoveFromRegionMap(const Region* region) {
    byte::RwLock::WriterLocker lock(&_region_map_rwlock);
    return _region_map.erase(reinterpret_cast<const void*>(
               reinterpret_cast<uint64_t>(region->RegionInfo().vaddr) + region->RegionInfo().len)) >
           0;
}

void MemoryPool::PrintRegionMap() {
    byte::RwLock::ReaderLocker lock(&_region_map_rwlock);

    std::ostringstream os;
    os << _region_map.size() << " regions have been allocated: " << std::endl;
    for (const auto& iter : _region_map) {
        const Region* region = iter.second;
        os << "[" << region->RegionInfo().vaddr << ", "
           << reinterpret_cast<void*>(reinterpret_cast<uintptr_t>(region->RegionInfo().vaddr) +
                                      region->RegionInfo().len)
           << "), is_hugepage: " << region->IsHugePage()
           << ", page_size: " << region->RegionInfo().page_sz << "B"
           << ", preferred_numa_id: " << region->NumaId() << std::endl;
    }
    BYTERPC_LOG(INFO) << os.str();
}

void MemoryPool::DeallocRegion(size_t idx, Region* region) {
    RegionManager* manager = _region_managers[idx].get();

    bool success = RemoveFromRegionMap(region);
    BYTERPC_DCHECK(success);

    bool is_huge = region->IsHugePage();
    _region_allocator.DeallocRegion(region);

    std::unique_lock<std::mutex> alloc_lock(manager->alloc_mutex);
    if (is_huge) {
        --manager->hugepage_region_num;
    }
    --manager->total_region_num;

    _total_region_num.fetch_sub(1, std::memory_order_relaxed);
}

Region* MemoryPool::AllocRegion(size_t idx) {
    RegionManager* manager = _region_managers[idx].get();

    uint32_t expected = _total_region_num.load(std::memory_order_relaxed);
    while (true) {
        if (expected >= FLAGS_byterpc_byte_express_memory_pool_max_regions) {
            BYTERPC_LOG_EVERY(ERROR, 10000)
                << "Fail to alloc a new region on numa: " << manager->numa_id
                << ", because it has reached the max region num: "
                << FLAGS_byterpc_byte_express_memory_pool_max_regions;
            return nullptr;
        }

        if (BYTERPC_LIKELY(_total_region_num.compare_exchange_weak(
                expected, expected + 1, std::memory_order_release, std::memory_order_relaxed))) {
            break;
        }
    }

    std::unique_lock<std::mutex> alloc_lock(manager->alloc_mutex);

    bool try_hugepage = (manager->hugepage_region_num < manager->hugepage_region_max_num);
    Region* new_region =
        _region_allocator.AllocRegion(_region_size, manager->numa_id, try_hugepage);

    if (new_region) {
        ++manager->total_region_num;
        if (new_region->IsHugePage()) {
            ++manager->hugepage_region_num;
        }

        bool success = InsertIntoRegionMap(new_region);
        BYTERPC_DCHECK(success);

        BYTERPC_LOG(INFO) << "Successfully allocate region [" << new_region->RegionInfo().vaddr
                          << ", "
                          << reinterpret_cast<void*>(
                                 reinterpret_cast<uintptr_t>(new_region->RegionInfo().vaddr) +
                                 new_region->RegionInfo().len)
                          << "), is_hugepage: " << new_region->IsHugePage()
                          << ", page_size: " << new_region->RegionInfo().page_sz << "B"
                          << ", preferred_numa_id: " << new_region->NumaId();
        PrintRegionMap();
    } else {
        _total_region_num.fetch_sub(1, std::memory_order_relaxed);
    }

    return new_region;
}

void MemoryPool::PreAllocRegion(size_t idx) {
    RegionManager* manager = _region_managers[idx].get();

    Region* new_region = AllocRegion(idx);

    std::unique_lock<std::mutex> region_lock(manager->region_mutex);
    if (new_region) {
        manager->uninit_list.emplace_back(new_region);
        manager->avail_mem_size += new_region->RegionInfo().len;
    }

    manager->is_allocating = false;
    manager->cond.notify_all();
}

void MemoryPool::InvokePreAllocRegion(size_t idx) {
    // InvokePreAllocRegion should be called in locked region_mutex
    RegionManager* manager = _region_managers[idx].get();

    // check if it has reached the max region num
    if (BYTERPC_UNLIKELY(_total_region_num.load(std::memory_order_relaxed) >=
                         FLAGS_byterpc_byte_express_memory_pool_max_regions)) {
        return;
    }

    if (auto bg_thread = WatcherThread::GetInstance().lock()) {
        manager->is_allocating = true;
        bg_thread->Invoke(NewClosure<void>(this, &MemoryPool::PreAllocRegion, idx));
        BYTERPC_LOG(INFO) << "Invoke PreAllocRegion for numa " << manager->numa_id;
    }
}

Segment* MemoryPool::GetSegment(size_t idx, size_t size, size_t alignment) {
    // Ensure that a region contains at least one segment
    if (BYTERPC_UNLIKELY(_region_size < size + alignment - 1)) {
        BYTERPC_LOG(ERROR) << "Region size may be not enough, try to increase "
                              "byterpc_memory_pool_region_size. Region size: "
                           << _region_size << "B, required size: " << size
                           << "B, required segment alignment: " << alignment << "B.";
        return nullptr;
    }

    // size must be a multiple of alignment
    if (BYTERPC_UNLIKELY(size % alignment != 0)) {
        BYTERPC_LOG(ERROR) << "Invalid segment size and alignment, size = " << size
                           << "B, alignment = " << alignment << "B.";
        return nullptr;
    }

    RegionManager* manager = _region_managers[idx].get();

    std::unique_lock<std::mutex> region_lock(manager->region_mutex);

    if (BYTERPC_LIKELY(manager->segment_size != 0)) {
        if (BYTERPC_UNLIKELY(size != manager->segment_size ||
                             alignment != manager->segment_alignment)) {
            BYTERPC_LOG(ERROR) << "Not support different (segment_size, segment_alignment). "
                                  "There are already regions of ("
                               << manager->segment_size << "B, " << manager->segment_alignment
                               << "B), but (" << size << "B, " << alignment << "B) is asked for.";
            return nullptr;
        }
    }

    while (true) {
        // Try to get a segment from regions in inuse_list
        if (BYTERPC_LIKELY(!manager->inuse_list.empty())) {
            Region* region = &*(manager->inuse_list.begin());
            BYTERPC_DCHECK(region->Status() == RegionStatus::READY ||
                           region->Status() == RegionStatus::INUSE);
            Segment* segment = region->GetSegment();
            BYTERPC_DCHECK(segment);
            if (BYTERPC_UNLIKELY(region->Status() == RegionStatus::FULL)) {
                manager->inuse_list.pop_front();
            }
            manager->avail_mem_size -= segment->size;
            if (BYTERPC_UNLIKELY(manager->avail_mem_size <
                                     FLAGS_byterpc_min_resident_capacity_in_MB_to_expand_memory *
                                         1024 * 1024UL &&
                                 !manager->is_allocating)) {
                InvokePreAllocRegion(idx);
            }
            return segment;
        }

        // no available segment, check if it is in the process of allocating a new region
        if (BYTERPC_UNLIKELY(manager->is_allocating)) {
            manager->cond.wait(region_lock);
        } else {
            // break when there is no available segment and ask for a new region in the next part
            break;
        }
    }

    Region* new_region = nullptr;
    // Get an uninit region from uninit_stack. If not, alloc a new region.
    if (!manager->uninit_list.empty()) {
        new_region = manager->uninit_list.front();
        manager->uninit_list.pop_front();
        manager->avail_mem_size -= new_region->RegionInfo().len;
    } else {
        new_region = AllocRegion(idx);
    }

    if (BYTERPC_UNLIKELY(!new_region)) {
        return nullptr;
    }
    BYTERPC_DCHECK(new_region->Status() == RegionStatus::UNINIT);

    if (BYTERPC_UNLIKELY(!new_region->Init(size, alignment))) {
        // if fail to init, push the region into uninit_list
        manager->uninit_list.emplace_back(new_region);
        manager->avail_mem_size += new_region->RegionInfo().len;
        return nullptr;
    }

    Segment* segment = new_region->GetSegment();
    if (BYTERPC_LIKELY(new_region->Status() == RegionStatus::INUSE)) {
        manager->inuse_list.push_back(*new_region);
        manager->avail_mem_size += new_region->SegmentSize() * new_region->FreeSegmentNum();
    }

    if (BYTERPC_UNLIKELY(manager->avail_mem_size <
                             FLAGS_byterpc_min_resident_capacity_in_MB_to_expand_memory * 1024 *
                                 1024UL &&
                         !manager->is_allocating)) {
        InvokePreAllocRegion(idx);
    }

    if (BYTERPC_UNLIKELY(manager->segment_size == 0)) {
        manager->segment_size = size;
        manager->segment_alignment = alignment;
    }

    return segment;
}

void MemoryPool::ReturnSegment(Segment* segment) {
    size_t idx = NumaIdToIndex(segment->belong_to_region->NumaId());
    RegionManager* manager = _region_managers[idx].get();

    std::unique_lock<std::mutex> lock(manager->region_mutex);
    Region* region = segment->belong_to_region;
    RegionStatus old_status = region->Status();
    BYTERPC_DCHECK(old_status == RegionStatus::INUSE || old_status == RegionStatus::FULL);
    region->ReturnSegment(segment);
    manager->avail_mem_size += segment->size;
    BYTERPC_DCHECK(region->Status() == RegionStatus::INUSE ||
                   region->Status() == RegionStatus::READY);
    if (BYTERPC_UNLIKELY(region->Status() == RegionStatus::READY)) {
        // no segments are in use, push region into ready_list
        if (BYTERPC_LIKELY(old_status == RegionStatus::INUSE)) {
            BYTERPC_DCHECK(region->is_linked());
            // remove from inuse_list
            region->unlink();
        }
        manager->avail_mem_size -= region->SegmentSize() * region->FreeSegmentNum();
        region->Reset();
        // TODO(dpw): if exceeding max_region_num/max_huge_region_num, dealloc it instead of push
        // into stack
        manager->uninit_list.emplace_back(region);
        manager->avail_mem_size += region->RegionInfo().len;
    } else {  // region->Status() == RegionStatus::INUSE
        // some segments are in use, region should be put into inuse_list if not yet
        if (BYTERPC_UNLIKELY(old_status == RegionStatus::FULL)) {
            BYTERPC_DCHECK(!region->is_linked());
            manager->inuse_list.push_back(*region);
        }
    }
}

}  // namespace byterpc
