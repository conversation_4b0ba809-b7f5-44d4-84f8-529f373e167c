// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <cstddef>
#include <memory>

namespace byterpc {

static constexpr size_t DEFAULT_TLS_OBJECT_CACHE_SIZE = 1024UL;

template <typename T,
          class Deleter = std::default_delete<T>,
          size_t CACHE_SIZE = DEFAULT_TLS_OBJECT_CACHE_SIZE>
class LocalObjectCache {
public:
// Disable GCC optimization to make sure `_object_num' is reset after destructor.
#pragma GCC push_options
#pragma GCC optimize("O0")
    virtual ~LocalObjectCache() {
        while (!IsEmpty()) {
            T* obj = GetOne();
            Deleter()(obj);
        }

        // NOTE: DO NOT CHANGE it!!!
        // Make sure object will NOT returnd to cache after destruct.
        _object_num = CACHE_SIZE + 1;
    }
#pragma GCC pop_options

    bool IsNotFull() {
        return _object_num < CACHE_SIZE;
    }

    bool IsEmpty() {
        return _object_num == 0;
    }

    void Return(T* t) {
        _objects[_object_num++] = t;
    }

    T* GetOne() {
        if (_object_num > 0) {
            return _objects[--_object_num];
        }
        return nullptr;
    }

private:
    size_t _object_num = 0;
    T* _objects[CACHE_SIZE] = {nullptr};
};

}  // namespace byterpc
