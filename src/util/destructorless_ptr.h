// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>


namespace byterpc {

// Like a std::unique_ptr, but free the piece of memory without
// calling corresponding destructor when the object is released;
template <class T>
class destructorless_ptr {
public:
    explicit destructorless_ptr(T* v = nullptr) : _v(v) {}
    explicit destructorless_ptr(destructorless_ptr<T>&& b) : _v(nullptr) {
        std::swap(_v, b._v);
    }

    ~destructorless_ptr() {
        if (_v) {
            operator delete(_v);
        }
    }

    inline T* move() {
        auto r = _v;
        _v = nullptr;
        return r;
    }

private:
    T* _v;
};

}  // namespace byterpc
