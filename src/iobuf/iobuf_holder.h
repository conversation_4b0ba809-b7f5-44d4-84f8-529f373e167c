// Copyright (c) 2023, ByteDance Inc. All rights reserved.
#pragma once

#include <algorithm>
#include <cstddef>
#include <deque>
#include <memory>
#include <sstream>
#include <utility>
#include <vector>

#include "byterpc/io_buf.h"
#include "byterpc/io_buf_accessor.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/logging.h"

namespace byterpc {

// Iterator to access data inside ring buffer.
template <typename T, uint64_t Mask> struct Iter {
    uint64_t cur_tail;
    uint8_t* data_buf;

    Iter(uint64_t start_tail, uint8_t* buf) : cur_tail(start_tail), data_buf(buf) {}

    T* operator*() {
        return reinterpret_cast<T*>(&data_buf[(cur_tail & Mask) * sizeof(T)]);
    }

    bool operator!=(const Iter& rhs) const {
        return cur_tail != rhs.cur_tail;
    }

    bool operator==(const Iter& rhs) const {
        return cur_tail == rhs.cur_tail;
    }

    ptrdiff_t operator-(const Iter& rhs) {
        return cur_tail - rhs.cur_tail;
    }

    Iter& operator++(int) {
        cur_tail++;
        return *this;
    }
};

// Simple ring buffer (with omit element's destructor)
// use Emplace to take T as input, update head,
// (produce T into ring buffer)
// user use iterator to access buffered T elements,
// user can consume from ring buf by call Update(new_it),
// this operation will pop all elements from [Begin(), new_it)
// all popped elements **will not** call destructor
template <typename T, uint32_t CAP> struct NoDestructRingBuf {
    static constexpr uint64_t MASK = CAP - 1;
    using InnerIter = Iter<T, MASK>;

    uint8_t data_bytes[sizeof(T) * CAP];
    // we use u64 to avoid wrapping around(unsigned integer overflow).
    uint64_t head, tail;

    NoDestructRingBuf() : head(0), tail(0) {}

    size_t FreeCnt() const {
        return CAP - (head - tail);
    }

    size_t Size() const {
        return (head - tail);
    }

    void Emplace(T&& ele) {
        uint8_t* ptr = &data_bytes[(head & MASK) * sizeof(T)];
        head++;
        new (ptr) T(std::move(ele));
        // make sure move happened.
    }

    InnerIter Begin() const {
        return Iter<T, MASK>(tail, const_cast<uint8_t*>(data_bytes));
    }

    InnerIter End() const {
        return Iter<T, MASK>(head, const_cast<uint8_t*>(data_bytes));
    }

    void Update(InnerIter first_it) {
        tail = first_it.cur_tail;
    }

    void Clear() {
        head = 0;
        tail = 0;
    }
};

// When iobuf can not fully write into ring buf immediately,
// we need a struct to save the filling progress.
struct UnfinishedIOBuf {
    std::unique_ptr<IOBuf> io_buf;

    // Mark the start index of ioblockref inside this iobuf.
    // if s_ref_idx =  0, means next filling() will try filling the first ioblock ref
    // into ring buf.
    size_t s_ref_idx;

    // saved io_buf->block_num();
    // because io_buf in a partial state
    // we can not call block_num() anymore.
    size_t origin_block_num;
};

// Take msg as input(by calling PutMsg), after putting msg inside holder,
// you will get a iterator to get ioblockref to these msgs(in order),
// its transport's task to send these ioblockref.
// once ioblockref has been sent, call Update() to tell msg holder
// that some ioblockref has been drained into transport,
// actually it means transport have took the ownership of these ioblockref,
// so msg holder will release these ioblockref (**without calling destructor**),
// in the same time, PendingMsgCnt also get updated.
// PendingMsgCnt will tell you how many msg(maybe partial sent) still pending inside.
//
// Note:
//       Msg
//       | . PutMsg(IOBuf)
//       V
//     \***/ MsgHolder
//      |*|
//       |
//       | extract by calling Update()
//       V
//       ioblockref using by transport.
//
//   easy way to understand msg holder is to think holder as a funnel,
//   user put msg from top, and transport extract ioblockref from lower part by calling Update(),
//   after Update() called, ownership of these ioblockref actually have been transferred into
//   transport, so its transport's duty to call ioblock->dec_ref() to release underlay memory.
//   for those ioblockref that do not extract by transport,
//   msg holder will call their destructor as a normal container.
//
template <uint32_t RING_BUF_SZ> struct MsgHolder {
    static_assert((RING_BUF_SZ > 1) & !(RING_BUF_SZ & (RING_BUF_SZ - 1)),
                  "ring buffer sz must 2**N");
    using IORIter = typename NoDestructRingBuf<IOBlockRef, RING_BUF_SZ>::InnerIter;

    ~MsgHolder() {
        Clear();
    }

    // Input:
    // put msg into holder, msg means one or more ioblockref.
    void PutMsg(IOBlockRef&& first,
                std::unique_ptr<IOBuf>&& second = nullptr,
                std::unique_ptr<IOBuf>&& third = nullptr);

    // Tell how many msg inside holder.
    // once you call Update(), this number maybe be changed also.
    size_t PendingMsgCnt() const;

    // Returns total number of blocks for some pending messages.
    // Tries to include as many messages as possible:
    // 1) starts from front of the pending queue
    // 2) only complete messages will be counted
    // 3) total number of blocks shall not exceed limit.
    size_t GetFrontBlockCounts(size_t limit) const;

    // Output:
    // get iterator to read ioblockref.
    IORIter Begin() const {
        return ring_buf.Begin();
    }
    IORIter End() const {
        return ring_buf.End();
    }

    size_t RingBufNum() const {
        return ring_buf.Size();
    }

    // When ioblockref sent success, move cursor,
    // all ioblockref [Begin(), it) will be release()
    // Note: ioblockref's destructor **will not** called!
    void Update(IORIter it);

    // Release all pending data, destructor will be called.
    // NOTE:
    //   msg holder will take ownership of a msg(one or more blockref),
    //   so its our duty to release all pending ioblockref.
    void Clear() {
        for (auto it = Begin(); it != End(); it++) {
            (*it)->reset();
        }
        ring_buf.Clear();
        msg_ref_cnt.clear();

        // the destructor of smallview iobuf can not handle the situation
        // that only second ioblockref remain.
        // (they only check first ref, that will leak second one).
        for (auto& iter : pending_iobuf) {
            auto& iob = iter.io_buf;
            for (auto idx = iter.s_ref_idx; idx < iter.origin_block_num; idx++) {
                iob->block_ref_at(idx).block()->dec_ref();
                // NOTE: MUST call clear here, or ioblock will be double-freed
                // when clear `pending_iobuf'.
                iob->block_ref_at(idx).clear();
            }
        }
        pending_iobuf.clear();
    }

protected:
    NoDestructRingBuf<IOBlockRef, RING_BUF_SZ> ring_buf;
    std::deque<size_t> msg_ref_cnt;
    std::deque<UnfinishedIOBuf> pending_iobuf;

    // return the number how many ioblockref filled into ring buf.
    size_t filling(IOBuf* buf, size_t buf_ref_cnt, size_t s_idx);
};

template <uint32_t RING_BUF_SZ>
inline size_t MsgHolder<RING_BUF_SZ>::filling(IOBuf* buf, size_t buf_ref_cnt, size_t s_ref_idx) {
    const size_t avail_cnt = ring_buf.FreeCnt();
    const size_t fill_cnt = std::min(buf_ref_cnt - s_ref_idx, avail_cnt);

    for (size_t i = s_ref_idx; i < s_ref_idx + fill_cnt; i++) {
        ring_buf.Emplace(std::move(buf->block_ref_at(i)));
    }

    return fill_cnt;
}

template <uint32_t RING_BUF_SZ>
inline void MsgHolder<RING_BUF_SZ>::PutMsg(IOBlockRef&& first,
                                           std::unique_ptr<IOBuf>&& second,
                                           std::unique_ptr<IOBuf>&& third) {
    const size_t second_ref_cnt = second ? second->block_num() : 0;
    const size_t third_ref_cnt = third ? third->block_num() : 0;
    const size_t total_ref_cnt =
        (BYTERPC_LIKELY(!first.empty()) ? 1 : 0) + second_ref_cnt + third_ref_cnt;
    BYTERPC_DCHECK_GT(total_ref_cnt, 0UL);
    BYTERPC_DCHECK(!(second_ref_cnt == 0 && third_ref_cnt > 0));

    msg_ref_cnt.push_back(total_ref_cnt);

    // rare case
    if (BYTERPC_UNLIKELY(ring_buf.FreeCnt() == 0)) {
        if (second) {
            if (BYTERPC_LIKELY(!first.empty())) {
                second->prepend(std::move(first));
            }
            if (third) {
                second->append(*third);
            }
            pending_iobuf.push_back({std::move(second), 0, total_ref_cnt});
        } else {
            auto buf = std::make_unique<IOBuf>();
            buf->append(std::move(first));
            pending_iobuf.push_back({std::move(buf), 0, total_ref_cnt});
        }
        return;
    }

    // atleast we can fill meta ref into ring buf.
    if (BYTERPC_LIKELY(!first.empty())) {
        ring_buf.Emplace(std::move(first));
    }

    // try fill second into ring buf.
    if (second) {
        size_t cnt = filling(second.get(), second_ref_cnt, 0);
        if (BYTERPC_UNLIKELY(cnt < second_ref_cnt)) {
            pending_iobuf.push_back({std::move(second), cnt, second_ref_cnt});
        }
    }

    // try fill third into ring buf.
    if (third) {
        size_t cnt = filling(third.get(), third_ref_cnt, 0);
        if (BYTERPC_UNLIKELY(cnt < third_ref_cnt)) {
            pending_iobuf.push_back({std::move(third), cnt, third_ref_cnt});
        }
    }
}

template <uint32_t RING_BUF_SZ> inline size_t MsgHolder<RING_BUF_SZ>::PendingMsgCnt() const {
    return msg_ref_cnt.size();
}

template <uint32_t RING_BUF_SZ>
inline size_t MsgHolder<RING_BUF_SZ>::GetFrontBlockCounts(size_t limit) const {
    size_t total_ref_cnt = 0;
    for (size_t cnt : msg_ref_cnt) {
        if (total_ref_cnt + cnt > limit) {
            break;
        }
        total_ref_cnt += cnt;
    }
    return total_ref_cnt;
}

template <uint32_t RING_BUF_SZ> inline void MsgHolder<RING_BUF_SZ>::Update(MsgHolder::IORIter it) {
    // fast path: no reloading needed.
    if (BYTERPC_LIKELY(pending_iobuf.empty() && it == ring_buf.End())) {
        ring_buf.Update(it);
        msg_ref_cnt.clear();
        return;
    }

    // TODO(crq): add metrics for entering slow path.
    size_t cnt = it - ring_buf.Begin();
    BYTERPC_CHECK(cnt <= static_cast<size_t>(ring_buf.End() - ring_buf.Begin())) << "out range";
    ring_buf.Update(it);

    while (cnt >= msg_ref_cnt.front()) {
        cnt -= msg_ref_cnt.front();
        msg_ref_cnt.pop_front();
    }
    if (cnt > 0) {
        msg_ref_cnt.front() -= cnt;
    }

    // reload ring_buf.
    while (!pending_iobuf.empty()) {
        auto& front = pending_iobuf.front();

        const size_t fill_cnt =
            filling(front.io_buf.get(), front.origin_block_num, front.s_ref_idx);
        const size_t new_s_ref_idx = front.s_ref_idx + fill_cnt;
        if (new_s_ref_idx != front.origin_block_num) {
            front.s_ref_idx = new_s_ref_idx;
            break;  // ring buffer fulled again.
        } else {
            pending_iobuf.pop_front();
            // release iobuf, all ref inside this iobuf already move to ring_buf.
            // NOTE: all refs inside this iobuf have been moved.
            //       so any block->dec_ref() will never happened.
        }
    }
}

}  // namespace byterpc
