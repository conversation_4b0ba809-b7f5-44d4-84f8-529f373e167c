// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>
#include <unordered_map>
#include <utility>
#include <vector>

#include "background_task/task_base.h"
#include "metrics/byterpc_metrics.h"

namespace byterpc {

class TaskInternalRoutine : public TaskBasic {
    friend class BackgroundTaskManager;

public:
    TaskInternalRoutine(BackgroundTaskManager* mgr, const TaskOption& option);

    bool DoReg() override;
    bool DoUnReg() override;
    TaskID GetTaskID() override {
        return TaskID::INTERNAL_ROUTINE;
    }

private:
    static std::atomic_bool g_task_internal_routine_regged;
    static constexpr TaskOption kDefaultOption{30 * 1000 * 1000UL /*30s*/, 1, 0};
    static constexpr int kDummyMetricsValue = 1;

    void RunInWatcherThread(void* user_ctx) override;
    void ReportVersionInfo();

    byte::embedded_metrics::Guage* _biz_running_metrics;
};

}  // namespace byterpc
