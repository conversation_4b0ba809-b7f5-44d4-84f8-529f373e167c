// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>

#include "byterpc/util/endpoint.h"
#include "rpc/rpc_service_registry.h"
#include "transport/transport.h"
#include "util/fd_guard.h"

namespace byterpc {

class EventRegistry;
class MessageHandlerManager;
class Server;

/*
 This class abstract the behavior of a TCP server socket.

 To run a single thread server, create an instance of this class
 in a epoll loop;

 To run multiple thread server, first set command line flag: "--byterpc_reuse_port=true",
 and then create an instance in each of worker threads that run epoll loop;
 */
class TcpAcceptor : protected IOHandler, public BasicAcceptor {
public:
    TcpAcceptor(EventRegistry* ev_reg,
                MessageHandlerManager* msg_handler_mgr,
                Server* server);
    // return 0 on success, -1 otherwise
    int StartAccept(const util::EndPoint& listen_addr) override;

    // return 0 on success, -1 otherwise
    int StopAccept() override;

    const util::EndPoint& GetListenAddress() const override;

    bool IsValidForMultiThreading() const;

    void OwnerRef() override;

    void OwnerUnref() override;

protected:
    void HandleReadEvent() override;

    int GetFD() const override;

    void Reset(int error_code) override;

    uint32_t NumOfResets() const override;

private:
    util::fd_guard _listen_fd;
    util::EndPoint _listen_ep;
    int _owner_ref;
    uint32_t _reset_count;
    EventRegistry* _ev_reg;
    MessageHandlerManager* _msg_handler_mgr;
    Server* _related_server;

    void HandleWriteEvent() override {}
};

}  // namespace byterpc
