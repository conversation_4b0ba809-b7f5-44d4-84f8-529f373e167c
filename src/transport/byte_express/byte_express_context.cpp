// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "transport/byte_express/byte_express_context.h"

#include <memory>
#include <mutex>  // NOLINT(build/c++11)
#include <sstream>
#include <string>

#include <be/net/socket_address.hpp>

#include "byterpc/byterpc_flags.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"
#include "byterpc/util/owner_ptr.h"
#include "mem/bemalloc_memory_pool.h"
#include "transport/byte_express/byte_express_event_dispatcher.h"
#include "transport/byte_express/byte_express_memory_pool.h"

namespace byterpc {

namespace {
std::once_flag g_init_flag;
ByteExpressContext* g_ctx = nullptr;
}  // namespace

class ByteExpressContext::RdmaEventDispatcherThread {
public:
    explicit RdmaEventDispatcherThread(const std::string& thread_name) {
        _dispatcher = std::make_shared<::be::RdmaEventDispatcher>(&_evb);
        _th = std::thread([this] {
            _dispatcher->Start();
            _evb.LoopForever();
            _dispatcher->Stop();
        });
        // set pthread name
        auto rc = pthread_setname_np(_th.native_handle(), thread_name.c_str());
        BYTERPC_LOG_IF(WARNING, rc != 0) << "pthread_setname_np failed: " << strerror(rc);
    }

    ~RdmaEventDispatcherThread() {
        _evb.TerminateLoopSoon();
        _th.join();
    }

    ::be::RdmaEventDispatcher::SPtr GetDispatcher() const {
        return _dispatcher;
    }

    ::be::EventRegistry* GetEventRegistry() const {
        return &_evb;
    }

    void StartAdoptingDeferred(std::chrono::milliseconds interval) {
        BYTERPC_CHECK(interval.count());
        _evb.RunInEventRegistryThreadAndWait([this, interval] {
            _adopt_deferred_timer = std::make_unique<AdoptDeferredTimer>(&_evb, interval);
            _adopt_deferred_timer->Run();
        });
    }

private:
    class AdoptDeferredTimer : public ::be::AsyncTimeout {
    public:
        AdoptDeferredTimer(::be::EventRegistry* ev_reg, std::chrono::milliseconds interval)
            : AsyncTimeout(ev_reg), _interval(interval) {}
        // refcount to memory pool will be dropped automatically in dtor
        ~AdoptDeferredTimer() = default;
        void Run() {
            // interval == 0 means that GC is disabled
            BYTERPC_CHECK(_interval.count());
            ScheduleTimeout(_interval);
        }

    private:
        void TimeoutExpired() noexcept override {
            static_cast<BemallocMemoryPool*>(MemoryPool::Get())->AdoptDeferred();
            ScheduleTimeout(_interval);
        }
        std::chrono::milliseconds _interval;
    };
    std::thread _th;
    mutable ::be::EventBase _evb;
    ::be::RdmaEventDispatcher::SPtr _dispatcher;
    std::unique_ptr<AdoptDeferredTimer> _adopt_deferred_timer;
};

ByteExpressContext& ByteExpressContext::Get() {
    return *g_ctx;
}

ByteExpressContext::ByteExpressContext() : _inited(false), _rdma_dispatcher_idx(0U) {}

int ByteExpressContext::Init() {
    std::call_once(g_init_flag, []() {
        // ibv_fork_init() should be called before calling any other function in libibverbs.
        // Reference: https://www.rdmamojo.com/2012/05/24/ibv_fork_init/
        int ret = ibv_fork_init();
        if (ret != 0) {
            BYTERPC_PLOG(WARNING) << "Fail to call ibv_fork_init, errno=" << ret;
        }

        g_ctx = new (std::nothrow) ByteExpressContext;
        if (!g_ctx) {
            BYTERPC_LOG(ERROR) << "Fail to allocate ByteExpressContext";
            return;
        }

        g_ctx->InitInternal();
        ByteExpressMemoryPool::Init();
    });

    return (g_ctx && g_ctx->_inited) ? 0 : -1;
}

void ByteExpressContext::Destroy() {
    if (g_ctx) {
        ByteExpressMemoryPool::Reset();
        delete g_ctx;
        g_ctx = nullptr;
    }
}

void ByteExpressContext::InitInternal() {
    _rdma_ctxs = ::be::RdmaContext::GetList();
    if (_rdma_ctxs.empty()) {
        BYTERPC_LOG(ERROR) << "NOT support RDMA, please check your environment";
        return;
    }

    // create and start dispatchers
    for (uint32_t i = 0; i != FLAGS_byterpc_byte_express_dispatcher_num; ++i) {
        // set different name for each thread
        std::ostringstream oss;
        oss << "rdma_conn_mgr" << i;
        _rdma_dispatchers.emplace_back(new RdmaEventDispatcherThread(oss.str()));
    }

    // create RdmaResourceAllocator if flag is set
    if (FLAGS_byterpc_byte_express_use_res_allocator) {
        _rdma_res_allocator = std::make_shared<::be::RdmaCommonResourceAllocator>();
    }

    _inited = true;
}

::be::Worker::SPtr ByteExpressContext::CreateWorker(ByteExpressEventDispatcher* ev_reg,
                                                    bool polling,
                                                    bool enable_napi) const {
    ::be::Worker::Options options;
    options.node_id = util::get_numa_id_of_this_thread();
    // create a new worker
    auto worker = std::make_shared<::be::Worker>(ev_reg, options);
    // add contexts to worker
    if (polling) {
        ::be::Worker::PollingContextOptions ctx_options;
        ctx_options.allocator = _rdma_res_allocator;
        for (auto& ctx : _rdma_ctxs) {
            worker->AddPollingContext(ctx, ctx_options);
        }
    } else if (!enable_napi) {
        ::be::Worker::EventContextOptions ctx_options;
        ctx_options.allocator = _rdma_res_allocator;
        for (auto& ctx : _rdma_ctxs) {
            worker->AddEventContext(ctx, ctx_options);
        }
    } else {
        ::be::Worker::HybridContextOptions ctx_options;
        ctx_options.allocator = _rdma_res_allocator;
        for (auto& ctx : _rdma_ctxs) {
            worker->AddHybridContext(ctx, ctx_options);
        }
    }

    return worker;
}

::be::RdmaEventDispatcher::SPtr ByteExpressContext::GetEventDispatcher() const {
    // get an available dispatcher in a round-robin fashion
    auto idx = _rdma_dispatcher_idx.fetch_add(1, std::memory_order_relaxed);
    return _rdma_dispatchers[idx % _rdma_dispatchers.size()]->GetDispatcher();
}

void ByteExpressContext::SyncWithDispatcherThread() {
    // sync with ALL the dispatcher threads
    for (auto&& dispatcher : _rdma_dispatchers) {
        dispatcher->GetEventRegistry()->RunInEventRegistryThreadAndWait([] {});
    }
}

bool ByteExpressContext::IsInited() {
    return g_ctx ? g_ctx->_inited : false;
}

void ByteExpressContext::StartAdoptingDeferredInDispatcherThread() {
    std::chrono::milliseconds interval(
        FLAGS_byterpc_byte_express_memory_pool_garbage_collection_in_conn_mgr_interval_ms);
    // GC is disabled when interval == 0
    if (!interval.count()) {
        return;
    }
    for (auto&& dispatcher : _rdma_dispatchers) {
        dispatcher->StartAdoptingDeferred(interval);
    }
}

void endpoint2SocketAddress(const util::EndPoint& point, be::SocketAddress* sock_addr) {
    if (FLAGS_byterpc_byte_express_use_infiniband) {
        sock_addr->SetFromIbPort(util::ip2str(point.ip).c_str(), point.port);
    } else {
        sock_addr->SetFromIpPort(util::ip2str(point.ip).c_str(), point.port);
    }
}

std::vector<util::RdmaDevAddress> ByteExpressContext::GetRdmaDevAddresses() {
    std::vector<::be::RdmaContext::SPtr> rdma_ctxs = ::be::RdmaContext::GetList();
    if (rdma_ctxs.empty()) {
        BYTERPC_LOG(ERROR) << "NOT support RDMA, please check your environment";
        return {};
    }

    std::vector<util::RdmaDevAddress> result;
    if (!FLAGS_byterpc_byte_express_use_infiniband) {
        for (auto& ctx : rdma_ctxs) {
            util::RdmaDevAddress rdma_dev_info;
            std::vector<::be::IPAddress> ips = ctx->GetIPAddresses();
            bool set_ipv4 = false;
            bool set_ipv6 = false;
            for (auto& ip : ips) {
                if (ip.IsLinkLocal() || (!ip.IsV4() && !ip.IsV6())) {
                    continue;
                }
                util::EndPoint* ep = nullptr;
                if (ip.IsV4()) {
                    if (BYTERPC_UNLIKELY(set_ipv4)) {
                        BYTERPC_LOG(FATAL) << "Read two ipv4 for one rdma device, first_ip: "
                                           << util::ip2str(rdma_dev_info.ipv4_ep.ip).c_str()
                                           << " second_ip: " << ip.Str();
                        return {};
                    }
                    set_ipv4 = true;
                    ep = &rdma_dev_info.ipv4_ep;
                } else {
                    if (BYTERPC_UNLIKELY(set_ipv6)) {
                        BYTERPC_LOG(FATAL) << "Read two ipv6 for one rdma device, first_ip: "
                                           << util::ip2str(rdma_dev_info.ipv6_or_ib_ep.ip).c_str()
                                           << " second_ip: " << ip.Str();
                        return {};
                    }
                    set_ipv6 = true;
                    ep = &rdma_dev_info.ipv6_or_ib_ep;
                }

                sockaddr_storage sock_storage;
                int len = ip.ToSockaddrStorage(&sock_storage);
                if (util::sockaddr2endpoint(ep, &sock_storage, len) != 0) {
                    BYTERPC_LOG(ERROR) << "Invalid rdma ip address: " << ip.Str();
                    return {};
                }
            }
            if (set_ipv4 || set_ipv6) {
                result.push_back(rdma_dev_info);
            }
        }
    } else {
        LOG(ERROR) << "NOT support get infiniband device addresses";
    }
    return result;
}

ibv_pd* ByteExpressContext::GetPD(const util::EndPoint& endpoint) const {
    for (const ::be::RdmaContext::SPtr& ctx : _rdma_ctxs) {
        std::vector<::be::IPAddress> ips = ctx->GetIPAddresses();
        for (const ::be::IPAddress& ip : ips) {
            util::EndPoint ep;
            sockaddr_storage sock_storage;
            int len = ip.ToSockaddrStorage(&sock_storage);
            if (util::sockaddr2endpoint(&ep, &sock_storage, len) != 0) {
                BYTERPC_LOG(FATAL) << "Invalid rdma ip address: " << ip.Str();
            }
            if (endpoint.ip == ep.ip) {
                return ctx->GetPD();
            }
        }
    }
    return nullptr;
}

}  // namespace byterpc
