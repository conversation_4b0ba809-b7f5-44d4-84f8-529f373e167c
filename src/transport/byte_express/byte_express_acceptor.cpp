// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "transport/byte_express/byte_express_acceptor.h"

#include <byte/thread/this_thread.h>

#include <chrono>  // NOLINT(build/c++11)
#include <mutex>   // NOLINT(build/c++11)
#include <string>
#include <utility>
#include <vector>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/server.h"
#include "byterpc/util/container_wrapper.h"
#include "byterpc/util/logging.h"
#include "metrics/byterpc_metrics.h"
#include "rpc/server_socket.h"
#include "transport/byte_express/byte_express_context.h"
#include "transport/byte_express/byte_express_event_dispatcher.h"
#include "transport/byte_express/byte_express_transport.h"

namespace byterpc {

using DispatcherMap = util::HashMap<util::EndPoint,
                                    std::pair<std::weak_ptr<::be::EngineAcceptor>, uint32_t>,
                                    util::EndPointHash>;

// global dispatcher map
std::unique_ptr<DispatcherMap> g_dispatcher_map;
std::mutex g_dispatcher_map_mutex;

std::shared_ptr<::be::EngineAcceptor> GetDispatcher(const util::EndPoint& listen_addr) {
    std::lock_guard<std::mutex> lg(g_dispatcher_map_mutex);
    if (!g_dispatcher_map) {
        g_dispatcher_map = std::make_unique<DispatcherMap>();
    }

    auto it = g_dispatcher_map->find(listen_addr);
    std::shared_ptr<::be::EngineAcceptor> acceptor;
    ::be::EngineAcceptor::Options acceptor_opt;
    acceptor_opt.schedule_policy = ByteExpressAcceptor::GetAcceptDispatchPolicy();
    acceptor_opt.engine_options.flow_control_timeout =
        std::chrono::milliseconds(FLAGS_byterpc_byte_express_flow_control_timeout_ms);
    acceptor_opt.engine_options.keepalive_interval =
        std::chrono::milliseconds(FLAGS_byterpc_byte_express_keepalive_interval_ms);
    acceptor_opt.engine_options.qp_max_send_wr = FLAGS_byterpc_byte_express_qp_max_send_wr;
    acceptor_opt.engine_options.qp_max_recv_wr = FLAGS_byterpc_byte_express_qp_max_recv_wr;
    acceptor_opt.engine_options.recv_buffer_size = FLAGS_byterpc_byte_express_recv_buffer_size;
    acceptor_opt.engine_options.qp_retry_cnt = FLAGS_byterpc_byte_express_qp_retry_cnt;
    acceptor_opt.engine_options.qp_timeout = FLAGS_byterpc_byte_express_qp_timeout;
    acceptor_opt.engine_options.qp_mtu =
        static_cast<be::Engine::Mtu>(FLAGS_byterpc_byte_express_qp_mtu);
    int rdma_prio = ExecCtx::GetTrafficPriority(TYPE_RDMA);
    if (rdma_prio > 0) {
        acceptor_opt.engine_options.qp_traffic_class = static_cast<uint8_t>(rdma_prio);
    }
    if (it == g_dispatcher_map->end() || !(acceptor = it->second.first.lock())) {
        // create a new dispatcher and insert into map
        auto dispatcher = ByteExpressContext::Get().GetEventDispatcher();
        acceptor = ::be::EngineAcceptor::Create(dispatcher, acceptor_opt);
        be::SocketAddress addr;
        endpoint2SocketAddress(listen_addr, &addr);
        try {
            acceptor->Bind(addr);
            acceptor->Listen(FLAGS_byterpc_byte_express_listen_backlog);
        } catch (const std::exception& ex) {
            BYTERPC_LOG(ERROR) << "Failed to init EngineAcceptor " << ex.what()
                               << " for listen_addr=" << listen_addr;
            return nullptr;
        }
        dispatcher->GetEventRegistry()->RunInEventRegistryThread(
            [acceptor] { acceptor->StartAccepting(); });
        (*g_dispatcher_map)[listen_addr].first = acceptor;
        (*g_dispatcher_map)[listen_addr].second = 1;
        // Record ref_count of `acceptor' manually. When this ref_count reaches zero,
        // it means it's the last one who references `acceptor', `StopAccepting' is
        // need to make sure all new-arriving rdma connects will be rejected.
    } else {
        it->second.second++;
    }
    return acceptor;
}

void UnRegisterDispatcher(ByteExpressAcceptor* acceptor, ::be::Worker* worker) {
    std::lock_guard<std::mutex> lg(g_dispatcher_map_mutex);
    if (BYTERPC_UNLIKELY(!g_dispatcher_map)) {
        return;
    }

    auto it = g_dispatcher_map->find(acceptor->GetListenAddress());
    std::shared_ptr<::be::EngineAcceptor> dispatcher;
    if (it != g_dispatcher_map->end() && (dispatcher = it->second.first.lock())) {
        if (--it->second.second == 0) {
            // The last user of acceptor need StopAccepting immediately
            // to make sure all new-arriving rdma connects will be rejected.
            dispatcher->GetEventRegistry()->RunInEventRegistryThreadAndWait(
                [&dispatcher]() { dispatcher->StopAccepting(); });
            g_dispatcher_map->erase(it);
        } else {
            // Call BE engine RemoveAcceptCallback, then wait for AcceptStopped callback
            // use AndWait() version to decrease the number of polling attempt. This is mainly an
            // optimization and will not affect the correctness if we use the non-blocking version.
            dispatcher->GetEventRegistry()->RunInEventRegistryThreadAndWait(
                [&dispatcher, acceptor, worker]() {
                    dispatcher->RemoveAcceptCallback(acceptor, worker);
                });
        }
    }
}

ByteExpressAcceptor::ByteExpressAcceptor(EventRegistry* ev_reg,
                                         MessageHandlerManager* msg_handler_mgr,
                                         Server* server)
    : _owner_ref(0),
      _ev_reg(dynamic_cast<ByteExpressEventDispatcher*>(ev_reg)),
      _msg_handler_mgr(msg_handler_mgr),
      _related_server(server),
      _state(State::NORMAL) {}

int ByteExpressAcceptor::StartAccept(const util::EndPoint& listen_addr) {
    _listen_addr = listen_addr;
    // get a dispatcher by listen_addr
    _dispatcher = GetDispatcher(listen_addr);
    // _dispatcher == nullptr if EngineAcceptor init failed
    if (!_dispatcher)
        return -1;
    // get worker on this thread (using ev_reg)
    auto worker = _ev_reg->GetWorker();
    auto* ev_reg = _dispatcher->GetEventRegistry();
    // RunInEventRegistryThreadAndWait() is not needed here since the destructor of
    // ByteExpressEventDispatcher will sync with the dispatcher thread.
    /* register acceptor to AcceptorDispatcher */
    ev_reg->RunInEventRegistryThread(
        [this, worker]() mutable { _dispatcher->AddAcceptCallback(this, std::move(worker)); });
    return 0;
}

void ByteExpressAcceptor::ConnectionAccepted(::be::Engine::UPtr&& engine) noexcept {
    // client may connect succ, but will soon disconnect
    if (BYTERPC_UNLIKELY(_state != State::NORMAL)) {
        return;
    }
    // transport is created in worker thread
    auto* socket = new ServerSocket(_msg_handler_mgr, _related_server->GetSvcReg(), this);
    auto trans = std::make_unique<ByteExpressTransport>(std::move(engine), socket);
    socket->ReadyToServe(trans.release());

    if (!_socket_map.Insert(socket)) {
        BYTERPC_LOG(WARNING) << "Insert socket to socketmap fail";
    }
}

void ByteExpressAcceptor::AcceptStopped() noexcept {
    _state = State::STOPPED;
}

void ByteExpressAcceptor::AcceptError(const std::exception& ex) noexcept {
    BYTERPC_LOG(ERROR) << "AcceptError: " << this << " " << ex.what()
                       << " listen_addr=" << _related_server->listen_address();
    std::string error_text = ex.what();
    if (error_text.find("RDMA_CM_EVENT_DEVICE_REMOVAL") != std::string::npos) {
        METRICS_byterpc_rdma_device_removal_error()->Set(true);
    }
}

int ByteExpressAcceptor::StopAccept() {
    _state = State::STOPPING;
    UnRegisterDispatcher(this, _ev_reg->GetWorker().get());

    // Need temporary socketmap, ss->SetFailed may modify iterator
    SocketMap sm(_socket_map);
    for (const auto& it : sm.GetMap()) {
        ServerSocket* ss = reinterpret_cast<ServerSocket*>(it);
        ss->ResetTransport();
    }
    // Polling for AcceptStopped callback
    int wait_times = 1000;
    while (wait_times--) {
        ExecCtx::ConsumeRemoteTasks();
        if (State::STOPPED == _state) {
            BYTERPC_LOG(WARNING) << "Already stop accept, start recycle resource";
            break;
        }
        byte::ThisThread::SleepInMs(10);
    }
    if (BYTERPC_UNLIKELY(State::STOPPED != _state)) {
        BYTERPC_LOG(ERROR) << "Wait for BE removing accept callback timeout";
        return -1;
    }

    // Release rdma-related resource in be::EngineAcceptor to reject further connections. Otherwise
    // the acceptor will receive further connections without handling them, leading to timeout in
    // client side.
    _dispatcher.reset();

    return 0;
}

::be::EngineAcceptor::SchedulePolicy ByteExpressAcceptor::GetAcceptDispatchPolicy() {
    ::be::EngineAcceptor::SchedulePolicy chosen_policy =
        ::be::EngineAcceptor::SchedulePolicy::kRoundRobin;
    switch (FLAGS_byterpc_byte_express_accept_schedule_policy) {
    case 1:
        chosen_policy = ::be::EngineAcceptor::SchedulePolicy::kRoundRobin;
        break;
    case 2:
        chosen_policy = ::be::EngineAcceptor::SchedulePolicy::kRandom;
        break;
    case 3:
        chosen_policy = ::be::EngineAcceptor::SchedulePolicy::kLeastUtil;
        break;
    default:
        BYTERPC_LOG(WARNING) << "config invalid be EngineAcceptor::SchedulePolicy="
                             << FLAGS_byterpc_byte_express_accept_schedule_policy
                             << ", use default kRoundRobin";
    }
    return chosen_policy;
}

void ByteExpressAcceptor::OwnerRef() {
    ++_owner_ref;
}

// TODO(fuziang): will recycle byteexpress resource
void ByteExpressAcceptor::OwnerUnref() {
    if (--_owner_ref == 0) {
        delete this;
    } else if (_owner_ref < 0) {
        BYTERPC_LOG(FATAL) << "reference should not be negative!";
    }
}

}  // namespace byterpc
