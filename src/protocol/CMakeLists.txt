# byte must on
set(protocol_SRCS message_handler_manager.cpp extend_header.cpp)

if(BYTERPC_ENABLE_BYTE_STD)
  list(APPEND protocol_SRCS 
    byte_std/byte_client_message_handler.cpp
    byte_std/byte_server_message_handler.cpp
    byte_std/byte_message_serdes.cpp)
endif()

if(BYTERPC_ENABLE_ALIGNED_BYTE_STD)
  list(APPEND protocol_SRCS 
    aligned_byte_std/aligned_byte_client_message_handler.cpp
    aligned_byte_std/aligned_byte_server_message_handler.cpp
    aligned_byte_std/aligned_byte_message_serdes.cpp)
endif()

if(BYTERPC_ENABLE_BAIDU_STD)
  list(APPEND protocol_SRCS 
    baidu_std/baidu_message_serdes.cpp
    baidu_std/baidu_server_message_handler.cpp
    baidu_std/baidu_client_message_handler.cpp)
endif()

if(BYTERPC_ENABLE_PROTOBUF_BYTE_STD)
  list(APPEND protocol_SRCS
    protobuf_byte_std/protobuf_byte_message_serdes.cpp
    protobuf_byte_std/protobuf_byte_server_message_handler.cpp
    protobuf_byte_std/protobuf_byte_client_message_handler.cpp)
endif()

if(BYTERPC_ENABLE_HTTP)
  list(APPEND protocol_SRCS
    http/http_client_message_handler.cpp
    http/http_server_message_handler.cpp
    http/http_plugin.cpp
    http/details/http_message.cpp
    http/details/http_parser.c
    http/details/http_protocol.cpp
    http/details/http_status_code.cpp
    http/details/restful.cpp
    http/details/uri.cpp)
endif()

if(BYTERPC_ENABLE_PROXY_PROTOCOL)
  list(APPEND protocol_SRCS 
    proxy_protocol/proxy_server_message_handler.cpp
    proxy_protocol/pp_message_serdes.cpp)
endif()

if(BYTERPC_ENABLE_THRIFT)
  list(APPEND protocol_SRCS 
    thrift/thrift_client_message_handler.cpp
    thrift/thrift_server_message_handler.cpp
    thrift/thrift_serdes.cpp
    thrift/thrift_message.cpp)
endif()

add_library(byterpc_protocol OBJECT ${protocol_SRCS})
target_link_libraries(byterpc_protocol PRIVATE byterpc_internal_include)
