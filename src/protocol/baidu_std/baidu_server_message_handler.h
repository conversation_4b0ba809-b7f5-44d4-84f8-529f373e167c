// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <google/protobuf/message.h>
#include <google/protobuf/service.h>
#include <google/protobuf/stubs/callback.h>

#include <deque>
#include <memory>
#include <string>
#include <utility>

#include "byterpc/controller.h"
#include "byterpc/util/object_pool.h"
#include "proto/baidu_rpc_meta.pb.h"
#include "protocol/message_base.h"
#include "rpc/rpc_service_registry.h"
#include "rpc/server_socket.h"

namespace byterpc {

class ThreadContext;

class BaiduServerMessageHandler : public ServerMessageHandler {
public:
    BaiduServerMessageHandler();

    std::string GetMessageHeader() const override;
    ProtocolType GetProtocolType() const override;

    ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) override;
};

// The place to invoke svc->CallMethod(), and where
// the callback to send response back.
class BaiduServerController final : public Controller {
    BYTERPC_OBJECT_POOL_FRIENDS;
    friend class BaiduServerMessageHandler;

public:
    BaiduServerController();

    ~BaiduServerController() {}

    void OnRecycle() override;

    void DoRecycle() override;

    void ProcessRequest(IOBuf* payload_buf);

    util::EndPoint local_side() override {
        return _parent->local_side();
    }

    util::EndPoint remote_side() override {
        return _parent->remote_side();
    }

#ifdef BYTERPC_ENABLE_PROXY_PROTOCOL
    const ProxyProtocolInfo& GetProxyProtocolInfo() const override {
        return _parent->GetProxyProtocolInfo();
    }
#endif

    void Reset() override;

    ProtocolType request_protocol() const override {
        return PROTOCOL_BAIDU_STD;
    }

private:
    util::owner_ptr<ServerSocket> _parent;
    std::unique_ptr<proto::RpcMeta> _meta;
    std::unique_ptr<google::protobuf::Message> _req;
    std::unique_ptr<google::protobuf::Message> _resp;
    MethodStatus* _method_status;
    uint64_t _received_timestamp;
    CrcMode _crc_mode;

    bool NeedCalCrc() const {
#ifdef BYTERPC_ENABLE_EXTEND_HEADER
        return _crc_mode != TYPE_NONE;
#else
        return false;
#endif
    }

    void SendResponse(ThreadContext* thread_context);
    void SendResponseImpl();
};

}  // namespace byterpc
