// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <queue>
#include <vector>

#include "byterpc/util/container_wrapper.h"

#include <boost/intrusive/list.hpp>

namespace byterpc {

struct TimeEventHandler;

struct TimeoutContext : public boost::intrusive::list_base_hook<> {
    TimeEventHandler* handler;
    // the duration of timeout passed in by user
    uint64_t timeout_duration;
    // absolute timeout timestamp = timestamp (when user setting _timeout_duration) +
    // _timeout_duration
    uint64_t deal_event_ts;

    TimeoutContext(TimeEventHandler* handler, uint64_t timeout_duration, uint64_t deal_event_ts)
        : handler(handler), timeout_duration(timeout_duration), deal_event_ts(deal_event_ts) {}

    TimeoutContext(const TimeoutContext& rhs)
        : handler(rhs.handler),
          timeout_duration(rhs.timeout_duration),
          deal_event_ts(rhs.deal_event_ts) {}

    bool operator>(const TimeoutContext& rhs) const {
        return deal_event_ts > rhs.deal_event_ts;
    }
};

typedef boost::intrusive::list<TimeoutContext> TimeoutList;
typedef TimeoutList::iterator TimeoutIterator;

class TimeoutManager {
public:
    TimeoutManager();

    ~TimeoutManager();

    // After `microseconds_since_now`, a time event happen, call corresponding
    // callback (i.e. HandleTimeEvent())
    // Returns Timeout Iterator
    TimeoutIterator AddTimeConsumer(TimeEventHandler* h, uint64_t microseconds_since_now);

    // Remove time event on TimeoutIterator;
    // Return 0 on success, -1 otherwise
    int RemoveTimeConsumer(TimeoutIterator timeout_iter);

    // Process with timeout event, call HandleTimeEvent() when a time event happen.
    // Return processed timeout events number
    size_t ProcessTimeout();

    // Return microsecond from now to the latest timeout event;
    // If no timeout event exist, return UINT64_MAX
    uint64_t GetLatestEventWaitUs();

    // Return the latest timeout event's timestamp;
    // If no timeout event exist, return UINT64_MAX
    uint64_t GetLatestEventTimeStamp();

private:
    TimeEventHandler* _current_timer_handler;
    // We use TimeoutList* as value instead of TimeoutList, because _timers may be rehash
    // when process event with AddTimeConsumer operation and make iterator invalid.
    // Use TimeoutList* can ensure it will always can be accessed.
    util::HashMap<uint64_t, TimeoutList*> _timers;
    std::priority_queue<TimeoutContext, std::vector<TimeoutContext>, std::greater<TimeoutContext>>
        _timeouts;

    // only used ut
    size_t GetTimerSize() {
        return _timers.size();
    }

    size_t GetTimeoutSize() {
        return _timeouts.size();
    }
};

}  // namespace byterpc
