// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "rpc/protobuf_channel.h"

#include "byterpc/util/logging.h"
#include "protocol/message_handler_manager.h"
#include "rpc/stateful_controller.h"
#include "rpc/time_profiler.h"

namespace byterpc {

ProtobufChannel::ProtobufChannel(EventRegistry* ev_reg,
                                 MessageHandlerManager* msg_handler_mgr)
    : _ev_reg(ev_reg),
      _msg_handler_mgr(msg_handler_mgr),
      _client_socket(nullptr),
      _num_invoked(0),
      _client_address() {}


int ProtobufChannel::Init(const util::EndPoint& remote_side,
                          const Builder::ChannelOptions& options,
                          const util::EndPoint& local_side) {
    _options = options;
    _server_address = remote_side;
    _client_address = local_side;
    _client_socket.reset(new ClientSocket(remote_side, _ev_reg,
                                          _msg_handler_mgr, options._trans_type,
                                          options._connect_timeout_ms, local_side));
    if (!_client_socket) {
        return -1;
    }
    if (options._build_connect_option == Builder::ConnectOptions::ASYNC_CONNECT &&
        _client_socket->PreConnect() == -1) {
        BYTERPC_LOG(WARNING) << "Submit connect request failed when build channel";
    }
    return 0;
}

void ProtobufChannel::CallMethod(const google::protobuf::MethodDescriptor* method,
                                 google::protobuf::RpcController* controller,
                                 const google::protobuf::Message* request,
                                 google::protobuf::Message* response,
                                 google::protobuf::Closure* done) {
    _num_invoked += 1;

    auto cntl = static_cast<StatefulController*>(controller);
    BYTERPC_CHECK(cntl);
    CTRL_INIT_PROFILER(cntl, util::TimeStamp::Now());

    // Register RPC timeout timer
    cntl->SetupTimeoutEventHandler(_ev_reg, _options._rpc_timeout_ms * 1000);
    cntl->IssueRPC(_client_socket.get(), method, request, response, done);
}

void ProtobufChannel::ResetTransport(int error_code) {
    _client_socket->ResetTransport(error_code);
}

void ProtobufChannel::AllocCorrelationId(Controller* cntl) {
    BYTERPC_DCHECK(_client_socket);
    static_cast<StatefulController*>(cntl)->InitCorrelationId(_client_socket.get());
}

}  // namespace byterpc
