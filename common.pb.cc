// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: common.proto
// Protobuf C++ Version: 5.29.3

#include "common.pb.h"

#include <algorithm>
#include <type_traits>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/generated_message_tctable_impl.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/reflection_ops.h"
#include "google/protobuf/wire_format.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace bds {
namespace proto {

inline constexpr TraceBaggageProto::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        key_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        value_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()) {}

template <typename>
PROTOBUF_CONSTEXPR TraceBaggageProto::TraceBaggageProto(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct TraceBaggageProtoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TraceBaggageProtoDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~TraceBaggageProtoDefaultTypeInternal() {}
  union {
    TraceBaggageProto _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TraceBaggageProtoDefaultTypeInternal _TraceBaggageProto_default_instance_;

inline constexpr CommonResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        baggages_{},
        msg_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        trace_id_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        server_id_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        leader_addr_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        request_time_{::uint64_t{0u}},
        response_time_{::uint64_t{0u}},
        status_{static_cast< ::bds::proto::Status >(0)},
        total_count_{0u},
        server_epoch_{::uint64_t{0u}},
        page_number_{0u},
        page_size_{0u},
        processing_time_ms_{::uint64_t{0u}},
        rate_limit_reset_{::uint64_t{0u}},
        rate_limit_remaining_{0u} {}

template <typename>
PROTOBUF_CONSTEXPR CommonResponse::CommonResponse(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct CommonResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CommonResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~CommonResponseDefaultTypeInternal() {}
  union {
    CommonResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CommonResponseDefaultTypeInternal _CommonResponse_default_instance_;

inline constexpr CommonRequest::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        baggages_{},
        ip_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        trace_id_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        ts_{::uint64_t{0u}},
        port_{0u} {}

template <typename>
PROTOBUF_CONSTEXPR CommonRequest::CommonRequest(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct CommonRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CommonRequestDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~CommonRequestDefaultTypeInternal() {}
  union {
    CommonRequest _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CommonRequestDefaultTypeInternal _CommonRequest_default_instance_;
}  // namespace proto
}  // namespace bds
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_common_2eproto[2];
static constexpr const ::_pb::ServiceDescriptor**
    file_level_service_descriptors_common_2eproto = nullptr;
const ::uint32_t
    TableStruct_common_2eproto::offsets[] ABSL_ATTRIBUTE_SECTION_VARIABLE(
        protodesc_cold) = {
        PROTOBUF_FIELD_OFFSET(::bds::proto::TraceBaggageProto, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::TraceBaggageProto, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::bds::proto::TraceBaggageProto, _impl_.key_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::TraceBaggageProto, _impl_.value_),
        0,
        1,
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _impl_.ip_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _impl_.port_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _impl_.ts_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _impl_.trace_id_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonRequest, _impl_.baggages_),
        0,
        3,
        2,
        1,
        ~0u,
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.status_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.msg_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.trace_id_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.request_time_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.response_time_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.server_id_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.server_epoch_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.leader_addr_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.total_count_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.page_number_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.page_size_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.processing_time_ms_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.baggages_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.rate_limit_remaining_),
        PROTOBUF_FIELD_OFFSET(::bds::proto::CommonResponse, _impl_.rate_limit_reset_),
        6,
        0,
        1,
        4,
        5,
        2,
        8,
        3,
        7,
        9,
        10,
        11,
        ~0u,
        13,
        12,
};

static const ::_pbi::MigrationSchema
    schemas[] ABSL_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
        {0, 10, -1, sizeof(::bds::proto::TraceBaggageProto)},
        {12, 25, -1, sizeof(::bds::proto::CommonRequest)},
        {30, 53, -1, sizeof(::bds::proto::CommonResponse)},
};
static const ::_pb::Message* const file_default_instances[] = {
    &::bds::proto::_TraceBaggageProto_default_instance_._instance,
    &::bds::proto::_CommonRequest_default_instance_._instance,
    &::bds::proto::_CommonResponse_default_instance_._instance,
};
const char descriptor_table_protodef_common_2eproto[] ABSL_ATTRIBUTE_SECTION_VARIABLE(
    protodesc_cold) = {
    "\n\014common.proto\022\tbds.proto\"/\n\021TraceBaggag"
    "eProto\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t\"w\n\rCo"
    "mmonRequest\022\n\n\002ip\030\001 \001(\t\022\014\n\004port\030\002 \001(\r\022\n\n"
    "\002ts\030\003 \001(\004\022\020\n\010trace_id\030\004 \001(\t\022.\n\010baggages\030"
    "\005 \003(\0132\034.bds.proto.TraceBaggageProto\"\376\002\n\016"
    "CommonResponse\022!\n\006status\030\001 \001(\0162\021.bds.pro"
    "to.Status\022\013\n\003msg\030\002 \001(\t\022\020\n\010trace_id\030\003 \001(\t"
    "\022\024\n\014request_time\030\004 \001(\004\022\025\n\rresponse_time\030"
    "\005 \001(\004\022\021\n\tserver_id\030\006 \001(\t\022\024\n\014server_epoch"
    "\030\007 \001(\004\022\023\n\013leader_addr\030\010 \001(\t\022\023\n\013total_cou"
    "nt\030\t \001(\r\022\023\n\013page_number\030\n \001(\r\022\021\n\tpage_si"
    "ze\030\013 \001(\r\022\032\n\022processing_time_ms\030\014 \001(\004\022.\n\010"
    "baggages\030\r \003(\0132\034.bds.proto.TraceBaggageP"
    "roto\022\034\n\024rate_limit_remaining\030\016 \001(\r\022\030\n\020ra"
    "te_limit_reset\030\017 \001(\004*\200\001\n\020IPStackModeProt"
    "o\022\030\n\024IPStackMode_kUnknown\020\000\022\032\n\026IPStackMo"
    "de_kIPv4_Only\020\001\022\032\n\026IPStackMode_kIPv6_Onl"
    "y\020\002\022\032\n\026IPStackMode_kDualStack\020\003*\244\002\n\006Stat"
    "us\022\013\n\007SUCCESS\020\000\022\t\n\005ERROR\020\001\022\034\n\030ERROR_INVA"
    "LID_PARAMETERS\020\002\022\031\n\025ERROR_NOT_IMPLEMENTE"
    "D\020\003\022\033\n\027ERROR_OPERATION_TIMEOUT\020\004\022\034\n\030ERRO"
    "R_RESOURCE_NOT_FOUND\020\005\022\033\n\027ERROR_PERMISSI"
    "ON_DENIED\020\006\022\034\n\030ERROR_RESOURCE_EXHAUSTED\020"
    "\007\022\035\n\031ERROR_SERVICE_UNAVAILABLE\020\010\022\022\n\016ERRO"
    "R_INTERNAL\020\t\022 \n\033ERROR_SYSTEM_SPECIFIC_BE"
    "GIN\020\221NB\003\200\001\001"
};
static ::absl::once_flag descriptor_table_common_2eproto_once;
PROTOBUF_CONSTINIT const ::_pbi::DescriptorTable descriptor_table_common_2eproto = {
    false,
    false,
    1011,
    descriptor_table_protodef_common_2eproto,
    "common.proto",
    &descriptor_table_common_2eproto_once,
    nullptr,
    0,
    3,
    schemas,
    file_default_instances,
    TableStruct_common_2eproto::offsets,
    file_level_enum_descriptors_common_2eproto,
    file_level_service_descriptors_common_2eproto,
};
namespace bds {
namespace proto {
const ::google::protobuf::EnumDescriptor* IPStackModeProto_descriptor() {
  ::google::protobuf::internal::AssignDescriptors(&descriptor_table_common_2eproto);
  return file_level_enum_descriptors_common_2eproto[0];
}
PROTOBUF_CONSTINIT const uint32_t IPStackModeProto_internal_data_[] = {
    262144u, 0u, };
bool IPStackModeProto_IsValid(int value) {
  return 0 <= value && value <= 3;
}
const ::google::protobuf::EnumDescriptor* Status_descriptor() {
  ::google::protobuf::internal::AssignDescriptors(&descriptor_table_common_2eproto);
  return file_level_enum_descriptors_common_2eproto[1];
}
PROTOBUF_CONSTINIT const uint32_t Status_internal_data_[] = {
    655360u, 65536u, 10001u, };
bool Status_IsValid(int value) {
  return ::_pbi::ValidateEnum(value, Status_internal_data_);
}
// ===================================================================

class TraceBaggageProto::_Internal {
 public:
  using HasBits =
      decltype(std::declval<TraceBaggageProto>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_._has_bits_);
};

TraceBaggageProto::TraceBaggageProto(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:bds.proto.TraceBaggageProto)
}
inline PROTOBUF_NDEBUG_INLINE TraceBaggageProto::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::bds::proto::TraceBaggageProto& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        key_(arena, from.key_),
        value_(arena, from.value_) {}

TraceBaggageProto::TraceBaggageProto(
    ::google::protobuf::Arena* arena,
    const TraceBaggageProto& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  TraceBaggageProto* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:bds.proto.TraceBaggageProto)
}
inline PROTOBUF_NDEBUG_INLINE TraceBaggageProto::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        key_(arena),
        value_(arena) {}

inline void TraceBaggageProto::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
TraceBaggageProto::~TraceBaggageProto() {
  // @@protoc_insertion_point(destructor:bds.proto.TraceBaggageProto)
  SharedDtor(*this);
}
inline void TraceBaggageProto::SharedDtor(MessageLite& self) {
  TraceBaggageProto& this_ = static_cast<TraceBaggageProto&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.key_.Destroy();
  this_._impl_.value_.Destroy();
  this_._impl_.~Impl_();
}

inline void* TraceBaggageProto::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) TraceBaggageProto(arena);
}
constexpr auto TraceBaggageProto::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(TraceBaggageProto),
                                            alignof(TraceBaggageProto));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull TraceBaggageProto::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_TraceBaggageProto_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &TraceBaggageProto::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<TraceBaggageProto>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &TraceBaggageProto::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<TraceBaggageProto>(), &TraceBaggageProto::ByteSizeLong,
            &TraceBaggageProto::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_._cached_size_),
        false,
    },
    &TraceBaggageProto::kDescriptorMethods,
    &descriptor_table_common_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* TraceBaggageProto::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 44, 2> TraceBaggageProto::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::bds::proto::TraceBaggageProto>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // optional string value = 2;
    {::_pbi::TcParser::FastSS1,
     {18, 1, 0, PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_.value_)}},
    // optional string key = 1;
    {::_pbi::TcParser::FastSS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_.key_)}},
  }}, {{
    65535, 65535
  }}, {{
    // optional string key = 1;
    {PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_.key_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // optional string value = 2;
    {PROTOBUF_FIELD_OFFSET(TraceBaggageProto, _impl_.value_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\33\3\5\0\0\0\0\0"
    "bds.proto.TraceBaggageProto"
    "key"
    "value"
  }},
};

PROTOBUF_NOINLINE void TraceBaggageProto::Clear() {
// @@protoc_insertion_point(message_clear_start:bds.proto.TraceBaggageProto)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.value_.ClearNonDefaultToEmpty();
    }
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* TraceBaggageProto::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const TraceBaggageProto& this_ = static_cast<const TraceBaggageProto&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* TraceBaggageProto::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const TraceBaggageProto& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:bds.proto.TraceBaggageProto)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // optional string key = 1;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_key();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.TraceBaggageProto.key");
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // optional string value = 2;
          if (cached_has_bits & 0x00000002u) {
            const std::string& _s = this_._internal_value();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.TraceBaggageProto.value");
            target = stream->WriteStringMaybeAliased(2, _s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:bds.proto.TraceBaggageProto)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t TraceBaggageProto::ByteSizeLong(const MessageLite& base) {
          const TraceBaggageProto& this_ = static_cast<const TraceBaggageProto&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t TraceBaggageProto::ByteSizeLong() const {
          const TraceBaggageProto& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:bds.proto.TraceBaggageProto)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x00000003u) {
            // optional string key = 1;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_key());
            }
            // optional string value = 2;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_value());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void TraceBaggageProto::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<TraceBaggageProto*>(&to_msg);
  auto& from = static_cast<const TraceBaggageProto&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:bds.proto.TraceBaggageProto)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_key(from._internal_key());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_value(from._internal_value());
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void TraceBaggageProto::CopyFrom(const TraceBaggageProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:bds.proto.TraceBaggageProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void TraceBaggageProto::InternalSwap(TraceBaggageProto* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.key_, &other->_impl_.key_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.value_, &other->_impl_.value_, arena);
}

::google::protobuf::Metadata TraceBaggageProto::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class CommonRequest::_Internal {
 public:
  using HasBits =
      decltype(std::declval<CommonRequest>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_._has_bits_);
};

CommonRequest::CommonRequest(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:bds.proto.CommonRequest)
}
inline PROTOBUF_NDEBUG_INLINE CommonRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::bds::proto::CommonRequest& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        baggages_{visibility, arena, from.baggages_},
        ip_(arena, from.ip_),
        trace_id_(arena, from.trace_id_) {}

CommonRequest::CommonRequest(
    ::google::protobuf::Arena* arena,
    const CommonRequest& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  CommonRequest* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, ts_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, ts_),
           offsetof(Impl_, port_) -
               offsetof(Impl_, ts_) +
               sizeof(Impl_::port_));

  // @@protoc_insertion_point(copy_constructor:bds.proto.CommonRequest)
}
inline PROTOBUF_NDEBUG_INLINE CommonRequest::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        baggages_{visibility, arena},
        ip_(arena),
        trace_id_(arena) {}

inline void CommonRequest::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, ts_),
           0,
           offsetof(Impl_, port_) -
               offsetof(Impl_, ts_) +
               sizeof(Impl_::port_));
}
CommonRequest::~CommonRequest() {
  // @@protoc_insertion_point(destructor:bds.proto.CommonRequest)
  SharedDtor(*this);
}
inline void CommonRequest::SharedDtor(MessageLite& self) {
  CommonRequest& this_ = static_cast<CommonRequest&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.ip_.Destroy();
  this_._impl_.trace_id_.Destroy();
  this_._impl_.~Impl_();
}

inline void* CommonRequest::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) CommonRequest(arena);
}
constexpr auto CommonRequest::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.baggages_) +
          decltype(CommonRequest::_impl_.baggages_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(CommonRequest), alignof(CommonRequest), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&CommonRequest::PlacementNew_,
                                 sizeof(CommonRequest),
                                 alignof(CommonRequest));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull CommonRequest::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_CommonRequest_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &CommonRequest::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<CommonRequest>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &CommonRequest::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<CommonRequest>(), &CommonRequest::ByteSizeLong,
            &CommonRequest::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_._cached_size_),
        false,
    },
    &CommonRequest::kDescriptorMethods,
    &descriptor_table_common_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* CommonRequest::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 5, 1, 42, 2> CommonRequest::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_._has_bits_),
    0, // no _extensions_
    5, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967264,  // skipmap
    offsetof(decltype(_table_), field_entries),
    5,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::bds::proto::CommonRequest>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // optional string ip = 1;
    {::_pbi::TcParser::FastSS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.ip_)}},
    // optional uint32 port = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(CommonRequest, _impl_.port_), 3>(),
     {16, 3, 0, PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.port_)}},
    // optional uint64 ts = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(CommonRequest, _impl_.ts_), 2>(),
     {24, 2, 0, PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.ts_)}},
    // optional string trace_id = 4;
    {::_pbi::TcParser::FastSS1,
     {34, 1, 0, PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.trace_id_)}},
    // repeated .bds.proto.TraceBaggageProto baggages = 5;
    {::_pbi::TcParser::FastMtR1,
     {42, 63, 0, PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.baggages_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // optional string ip = 1;
    {PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.ip_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // optional uint32 port = 2;
    {PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.port_), _Internal::kHasBitsOffset + 3, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // optional uint64 ts = 3;
    {PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.ts_), _Internal::kHasBitsOffset + 2, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // optional string trace_id = 4;
    {PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.trace_id_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // repeated .bds.proto.TraceBaggageProto baggages = 5;
    {PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.baggages_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::bds::proto::TraceBaggageProto>()},
  }}, {{
    "\27\2\0\0\10\0\0\0"
    "bds.proto.CommonRequest"
    "ip"
    "trace_id"
  }},
};

PROTOBUF_NOINLINE void CommonRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:bds.proto.CommonRequest)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.baggages_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.ip_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.trace_id_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&_impl_.ts_, 0, static_cast<::size_t>(
        reinterpret_cast<char*>(&_impl_.port_) -
        reinterpret_cast<char*>(&_impl_.ts_)) + sizeof(_impl_.port_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* CommonRequest::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const CommonRequest& this_ = static_cast<const CommonRequest&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* CommonRequest::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const CommonRequest& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:bds.proto.CommonRequest)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // optional string ip = 1;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_ip();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.CommonRequest.ip");
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // optional uint32 port = 2;
          if (cached_has_bits & 0x00000008u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                2, this_._internal_port(), target);
          }

          // optional uint64 ts = 3;
          if (cached_has_bits & 0x00000004u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                3, this_._internal_ts(), target);
          }

          // optional string trace_id = 4;
          if (cached_has_bits & 0x00000002u) {
            const std::string& _s = this_._internal_trace_id();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.CommonRequest.trace_id");
            target = stream->WriteStringMaybeAliased(4, _s, target);
          }

          // repeated .bds.proto.TraceBaggageProto baggages = 5;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_baggages_size());
               i < n; i++) {
            const auto& repfield = this_._internal_baggages().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    5, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:bds.proto.CommonRequest)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t CommonRequest::ByteSizeLong(const MessageLite& base) {
          const CommonRequest& this_ = static_cast<const CommonRequest&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t CommonRequest::ByteSizeLong() const {
          const CommonRequest& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:bds.proto.CommonRequest)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .bds.proto.TraceBaggageProto baggages = 5;
            {
              total_size += 1UL * this_._internal_baggages_size();
              for (const auto& msg : this_._internal_baggages()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x0000000fu) {
            // optional string ip = 1;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_ip());
            }
            // optional string trace_id = 4;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_trace_id());
            }
            // optional uint64 ts = 3;
            if (cached_has_bits & 0x00000004u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_ts());
            }
            // optional uint32 port = 2;
            if (cached_has_bits & 0x00000008u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_port());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void CommonRequest::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<CommonRequest*>(&to_msg);
  auto& from = static_cast<const CommonRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:bds.proto.CommonRequest)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_baggages()->MergeFrom(
      from._internal_baggages());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_ip(from._internal_ip());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_trace_id(from._internal_trace_id());
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_impl_.ts_ = from._impl_.ts_;
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_impl_.port_ = from._impl_.port_;
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void CommonRequest::CopyFrom(const CommonRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:bds.proto.CommonRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void CommonRequest::InternalSwap(CommonRequest* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.baggages_.InternalSwap(&other->_impl_.baggages_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.ip_, &other->_impl_.ip_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.trace_id_, &other->_impl_.trace_id_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.port_)
      + sizeof(CommonRequest::_impl_.port_)
      - PROTOBUF_FIELD_OFFSET(CommonRequest, _impl_.ts_)>(
          reinterpret_cast<char*>(&_impl_.ts_),
          reinterpret_cast<char*>(&other->_impl_.ts_));
}

::google::protobuf::Metadata CommonRequest::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class CommonResponse::_Internal {
 public:
  using HasBits =
      decltype(std::declval<CommonResponse>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_._has_bits_);
};

CommonResponse::CommonResponse(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:bds.proto.CommonResponse)
}
inline PROTOBUF_NDEBUG_INLINE CommonResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::bds::proto::CommonResponse& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        baggages_{visibility, arena, from.baggages_},
        msg_(arena, from.msg_),
        trace_id_(arena, from.trace_id_),
        server_id_(arena, from.server_id_),
        leader_addr_(arena, from.leader_addr_) {}

CommonResponse::CommonResponse(
    ::google::protobuf::Arena* arena,
    const CommonResponse& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  CommonResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, request_time_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, request_time_),
           offsetof(Impl_, rate_limit_remaining_) -
               offsetof(Impl_, request_time_) +
               sizeof(Impl_::rate_limit_remaining_));

  // @@protoc_insertion_point(copy_constructor:bds.proto.CommonResponse)
}
inline PROTOBUF_NDEBUG_INLINE CommonResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        baggages_{visibility, arena},
        msg_(arena),
        trace_id_(arena),
        server_id_(arena),
        leader_addr_(arena) {}

inline void CommonResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, request_time_),
           0,
           offsetof(Impl_, rate_limit_remaining_) -
               offsetof(Impl_, request_time_) +
               sizeof(Impl_::rate_limit_remaining_));
}
CommonResponse::~CommonResponse() {
  // @@protoc_insertion_point(destructor:bds.proto.CommonResponse)
  SharedDtor(*this);
}
inline void CommonResponse::SharedDtor(MessageLite& self) {
  CommonResponse& this_ = static_cast<CommonResponse&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.msg_.Destroy();
  this_._impl_.trace_id_.Destroy();
  this_._impl_.server_id_.Destroy();
  this_._impl_.leader_addr_.Destroy();
  this_._impl_.~Impl_();
}

inline void* CommonResponse::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) CommonResponse(arena);
}
constexpr auto CommonResponse::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.baggages_) +
          decltype(CommonResponse::_impl_.baggages_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(CommonResponse), alignof(CommonResponse), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&CommonResponse::PlacementNew_,
                                 sizeof(CommonResponse),
                                 alignof(CommonResponse));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull CommonResponse::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_CommonResponse_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &CommonResponse::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<CommonResponse>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &CommonResponse::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<CommonResponse>(), &CommonResponse::ByteSizeLong,
            &CommonResponse::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_._cached_size_),
        false,
    },
    &CommonResponse::kDescriptorMethods,
    &descriptor_table_common_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* CommonResponse::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<4, 15, 2, 72, 2> CommonResponse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_._has_bits_),
    0, // no _extensions_
    15, 120,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294934528,  // skipmap
    offsetof(decltype(_table_), field_entries),
    15,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::bds::proto::CommonResponse>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // optional .bds.proto.Status status = 1;
    {::_pbi::TcParser::FastEvS1,
     {8, 6, 1, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.status_)}},
    // optional string msg = 2;
    {::_pbi::TcParser::FastSS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.msg_)}},
    // optional string trace_id = 3;
    {::_pbi::TcParser::FastSS1,
     {26, 1, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.trace_id_)}},
    // optional uint64 request_time = 4;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(CommonResponse, _impl_.request_time_), 4>(),
     {32, 4, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.request_time_)}},
    // optional uint64 response_time = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(CommonResponse, _impl_.response_time_), 5>(),
     {40, 5, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.response_time_)}},
    // optional string server_id = 6;
    {::_pbi::TcParser::FastSS1,
     {50, 2, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.server_id_)}},
    // optional uint64 server_epoch = 7;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(CommonResponse, _impl_.server_epoch_), 8>(),
     {56, 8, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.server_epoch_)}},
    // optional string leader_addr = 8;
    {::_pbi::TcParser::FastSS1,
     {66, 3, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.leader_addr_)}},
    // optional uint32 total_count = 9;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(CommonResponse, _impl_.total_count_), 7>(),
     {72, 7, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.total_count_)}},
    // optional uint32 page_number = 10;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(CommonResponse, _impl_.page_number_), 9>(),
     {80, 9, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.page_number_)}},
    // optional uint32 page_size = 11;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(CommonResponse, _impl_.page_size_), 10>(),
     {88, 10, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.page_size_)}},
    // optional uint64 processing_time_ms = 12;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(CommonResponse, _impl_.processing_time_ms_), 11>(),
     {96, 11, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.processing_time_ms_)}},
    // repeated .bds.proto.TraceBaggageProto baggages = 13;
    {::_pbi::TcParser::FastMtR1,
     {106, 63, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.baggages_)}},
    // optional uint32 rate_limit_remaining = 14;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(CommonResponse, _impl_.rate_limit_remaining_), 13>(),
     {112, 13, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.rate_limit_remaining_)}},
    // optional uint64 rate_limit_reset = 15;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(CommonResponse, _impl_.rate_limit_reset_), 12>(),
     {120, 12, 0, PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.rate_limit_reset_)}},
  }}, {{
    65535, 65535
  }}, {{
    // optional .bds.proto.Status status = 1;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.status_), _Internal::kHasBitsOffset + 6, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kEnum)},
    // optional string msg = 2;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.msg_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // optional string trace_id = 3;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.trace_id_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // optional uint64 request_time = 4;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.request_time_), _Internal::kHasBitsOffset + 4, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // optional uint64 response_time = 5;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.response_time_), _Internal::kHasBitsOffset + 5, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // optional string server_id = 6;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.server_id_), _Internal::kHasBitsOffset + 2, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // optional uint64 server_epoch = 7;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.server_epoch_), _Internal::kHasBitsOffset + 8, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // optional string leader_addr = 8;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.leader_addr_), _Internal::kHasBitsOffset + 3, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kRawString | ::_fl::kRepAString)},
    // optional uint32 total_count = 9;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.total_count_), _Internal::kHasBitsOffset + 7, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // optional uint32 page_number = 10;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.page_number_), _Internal::kHasBitsOffset + 9, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // optional uint32 page_size = 11;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.page_size_), _Internal::kHasBitsOffset + 10, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // optional uint64 processing_time_ms = 12;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.processing_time_ms_), _Internal::kHasBitsOffset + 11, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // repeated .bds.proto.TraceBaggageProto baggages = 13;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.baggages_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // optional uint32 rate_limit_remaining = 14;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.rate_limit_remaining_), _Internal::kHasBitsOffset + 13, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // optional uint64 rate_limit_reset = 15;
    {PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.rate_limit_reset_), _Internal::kHasBitsOffset + 12, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
  }}, {{
    {::_pbi::TcParser::GetTable<::bds::proto::TraceBaggageProto>()},
    {::_pbi::FieldAuxEnumData{}, ::bds::proto::Status_internal_data_},
  }}, {{
    "\30\0\3\10\0\0\11\0\13\0\0\0\0\0\0\0"
    "bds.proto.CommonResponse"
    "msg"
    "trace_id"
    "server_id"
    "leader_addr"
  }},
};

PROTOBUF_NOINLINE void CommonResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:bds.proto.CommonResponse)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.baggages_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.msg_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.trace_id_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      _impl_.server_id_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000008u) {
      _impl_.leader_addr_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x000000f0u) {
    ::memset(&_impl_.request_time_, 0, static_cast<::size_t>(
        reinterpret_cast<char*>(&_impl_.total_count_) -
        reinterpret_cast<char*>(&_impl_.request_time_)) + sizeof(_impl_.total_count_));
  }
  if (cached_has_bits & 0x00003f00u) {
    ::memset(&_impl_.server_epoch_, 0, static_cast<::size_t>(
        reinterpret_cast<char*>(&_impl_.rate_limit_remaining_) -
        reinterpret_cast<char*>(&_impl_.server_epoch_)) + sizeof(_impl_.rate_limit_remaining_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* CommonResponse::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const CommonResponse& this_ = static_cast<const CommonResponse&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* CommonResponse::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const CommonResponse& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:bds.proto.CommonResponse)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // optional .bds.proto.Status status = 1;
          if (cached_has_bits & 0x00000040u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteEnumToArray(
                1, this_._internal_status(), target);
          }

          // optional string msg = 2;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_msg();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.CommonResponse.msg");
            target = stream->WriteStringMaybeAliased(2, _s, target);
          }

          // optional string trace_id = 3;
          if (cached_has_bits & 0x00000002u) {
            const std::string& _s = this_._internal_trace_id();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.CommonResponse.trace_id");
            target = stream->WriteStringMaybeAliased(3, _s, target);
          }

          // optional uint64 request_time = 4;
          if (cached_has_bits & 0x00000010u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                4, this_._internal_request_time(), target);
          }

          // optional uint64 response_time = 5;
          if (cached_has_bits & 0x00000020u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                5, this_._internal_response_time(), target);
          }

          // optional string server_id = 6;
          if (cached_has_bits & 0x00000004u) {
            const std::string& _s = this_._internal_server_id();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.CommonResponse.server_id");
            target = stream->WriteStringMaybeAliased(6, _s, target);
          }

          // optional uint64 server_epoch = 7;
          if (cached_has_bits & 0x00000100u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                7, this_._internal_server_epoch(), target);
          }

          // optional string leader_addr = 8;
          if (cached_has_bits & 0x00000008u) {
            const std::string& _s = this_._internal_leader_addr();
            ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(_s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormat::SERIALIZE,
                                        "bds.proto.CommonResponse.leader_addr");
            target = stream->WriteStringMaybeAliased(8, _s, target);
          }

          // optional uint32 total_count = 9;
          if (cached_has_bits & 0x00000080u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                9, this_._internal_total_count(), target);
          }

          // optional uint32 page_number = 10;
          if (cached_has_bits & 0x00000200u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                10, this_._internal_page_number(), target);
          }

          // optional uint32 page_size = 11;
          if (cached_has_bits & 0x00000400u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                11, this_._internal_page_size(), target);
          }

          // optional uint64 processing_time_ms = 12;
          if (cached_has_bits & 0x00000800u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                12, this_._internal_processing_time_ms(), target);
          }

          // repeated .bds.proto.TraceBaggageProto baggages = 13;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_baggages_size());
               i < n; i++) {
            const auto& repfield = this_._internal_baggages().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    13, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          // optional uint32 rate_limit_remaining = 14;
          if (cached_has_bits & 0x00002000u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                14, this_._internal_rate_limit_remaining(), target);
          }

          // optional uint64 rate_limit_reset = 15;
          if (cached_has_bits & 0x00001000u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                15, this_._internal_rate_limit_reset(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:bds.proto.CommonResponse)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t CommonResponse::ByteSizeLong(const MessageLite& base) {
          const CommonResponse& this_ = static_cast<const CommonResponse&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t CommonResponse::ByteSizeLong() const {
          const CommonResponse& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:bds.proto.CommonResponse)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .bds.proto.TraceBaggageProto baggages = 13;
            {
              total_size += 1UL * this_._internal_baggages_size();
              for (const auto& msg : this_._internal_baggages()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x000000ffu) {
            // optional string msg = 2;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_msg());
            }
            // optional string trace_id = 3;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_trace_id());
            }
            // optional string server_id = 6;
            if (cached_has_bits & 0x00000004u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_server_id());
            }
            // optional string leader_addr = 8;
            if (cached_has_bits & 0x00000008u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_leader_addr());
            }
            // optional uint64 request_time = 4;
            if (cached_has_bits & 0x00000010u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_request_time());
            }
            // optional uint64 response_time = 5;
            if (cached_has_bits & 0x00000020u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_response_time());
            }
            // optional .bds.proto.Status status = 1;
            if (cached_has_bits & 0x00000040u) {
              total_size += 1 +
                            ::_pbi::WireFormatLite::EnumSize(this_._internal_status());
            }
            // optional uint32 total_count = 9;
            if (cached_has_bits & 0x00000080u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_total_count());
            }
          }
          if (cached_has_bits & 0x00003f00u) {
            // optional uint64 server_epoch = 7;
            if (cached_has_bits & 0x00000100u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_server_epoch());
            }
            // optional uint32 page_number = 10;
            if (cached_has_bits & 0x00000200u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_page_number());
            }
            // optional uint32 page_size = 11;
            if (cached_has_bits & 0x00000400u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_page_size());
            }
            // optional uint64 processing_time_ms = 12;
            if (cached_has_bits & 0x00000800u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_processing_time_ms());
            }
            // optional uint64 rate_limit_reset = 15;
            if (cached_has_bits & 0x00001000u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_rate_limit_reset());
            }
            // optional uint32 rate_limit_remaining = 14;
            if (cached_has_bits & 0x00002000u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_rate_limit_remaining());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void CommonResponse::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<CommonResponse*>(&to_msg);
  auto& from = static_cast<const CommonResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:bds.proto.CommonResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_baggages()->MergeFrom(
      from._internal_baggages());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_msg(from._internal_msg());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_trace_id(from._internal_trace_id());
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_internal_set_server_id(from._internal_server_id());
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_internal_set_leader_addr(from._internal_leader_addr());
    }
    if (cached_has_bits & 0x00000010u) {
      _this->_impl_.request_time_ = from._impl_.request_time_;
    }
    if (cached_has_bits & 0x00000020u) {
      _this->_impl_.response_time_ = from._impl_.response_time_;
    }
    if (cached_has_bits & 0x00000040u) {
      _this->_impl_.status_ = from._impl_.status_;
    }
    if (cached_has_bits & 0x00000080u) {
      _this->_impl_.total_count_ = from._impl_.total_count_;
    }
  }
  if (cached_has_bits & 0x00003f00u) {
    if (cached_has_bits & 0x00000100u) {
      _this->_impl_.server_epoch_ = from._impl_.server_epoch_;
    }
    if (cached_has_bits & 0x00000200u) {
      _this->_impl_.page_number_ = from._impl_.page_number_;
    }
    if (cached_has_bits & 0x00000400u) {
      _this->_impl_.page_size_ = from._impl_.page_size_;
    }
    if (cached_has_bits & 0x00000800u) {
      _this->_impl_.processing_time_ms_ = from._impl_.processing_time_ms_;
    }
    if (cached_has_bits & 0x00001000u) {
      _this->_impl_.rate_limit_reset_ = from._impl_.rate_limit_reset_;
    }
    if (cached_has_bits & 0x00002000u) {
      _this->_impl_.rate_limit_remaining_ = from._impl_.rate_limit_remaining_;
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void CommonResponse::CopyFrom(const CommonResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:bds.proto.CommonResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void CommonResponse::InternalSwap(CommonResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.baggages_.InternalSwap(&other->_impl_.baggages_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.msg_, &other->_impl_.msg_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.trace_id_, &other->_impl_.trace_id_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.server_id_, &other->_impl_.server_id_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.leader_addr_, &other->_impl_.leader_addr_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.rate_limit_remaining_)
      + sizeof(CommonResponse::_impl_.rate_limit_remaining_)
      - PROTOBUF_FIELD_OFFSET(CommonResponse, _impl_.request_time_)>(
          reinterpret_cast<char*>(&_impl_.request_time_),
          reinterpret_cast<char*>(&other->_impl_.request_time_));
}

::google::protobuf::Metadata CommonResponse::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// @@protoc_insertion_point(namespace_scope)
}  // namespace proto
}  // namespace bds
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::std::false_type
    _static_init2_ PROTOBUF_UNUSED =
        (::_pbi::AddDescriptors(&descriptor_table_common_2eproto),
         ::std::false_type{});
#include "google/protobuf/port_undef.inc"
