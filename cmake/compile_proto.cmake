function(compile_proto OUT_HDRS OUT_SRCS DESTDIR HDR_OUTPUT_DIR PROTO_DIR PROTO_FILES)
    foreach(P ${PROTO_FILES})
        string(REPLACE .proto .pb.h HDR_FILE ${P})
        string(REPLACE .proto .pb.cc CPP_FILE ${P})
        message(STATUS "add proto target: ${P} -> ${HDR_FILE},${CPP_FILE}")

        set(OUTPUT_HDR_FILE ${DESTDIR}/${HDR_FILE})
        set(OUTPUT_CPP_FILE ${DESTDIR}/${CPP_FILE})
        set(INPUT_PROTO_FILE ${PROTO_DIR}/${P})

        list(APPEND HDRS ${OUTPUT_HDR_FILE})
        list(APPEND SRCS ${OUTPUT_CPP_FILE})
        add_custom_command(
            OUTPUT ${OUTPUT_HDR_FILE} ${OUTPUT_CPP_FILE}
            COMMAND ${PROTOBUF_PROTOC_EXECUTABLE} ${PROTOC_FLAGS} -I${PROTO_DIR} --cpp_out=${DESTDIR} ${INPUT_PROTO_FILE}
            COMMAND ${CMAKE_COMMAND} -E copy ${OUTPUT_HDR_FILE} ${HDR_OUTPUT_DIR}/${HDR_FILE}
            DEPENDS ${INPUT_PROTO_FILE} ${PROTOBUF_PROTOC}
            COMMENT "generate proto file ${OUTPUT_HDR_FILE}, ${OUTPUT_CPP_FILE} at ${CMAKE_CURRENT_BINARY_DIR}"
        )
    endforeach()
    set(${OUT_HDRS} ${HDRS} PARENT_SCOPE)
    set(${OUT_SRCS} ${SRCS} PARENT_SCOPE)
endfunction()
