#include "async_manager.h"

#include <byte/thread/this_thread.h>
#include <gtest/gtest.h>

#include <atomic>
#include <vector>

#include "byterpc/exec_ctx.h"
#include "byterpc/util/logging.h"

namespace byterpc {

static int sThreadNum = 8;
static std::vector<int> sOutput(sThreadNum, 0);
static std::atomic<bool> sComplete(false);

class AsyncManagerTest : public ::testing::Test {
public:
    AsyncManagerTest() {}

    ~AsyncManagerTest() {}

    void SetUp() {
        for (int i = 0; i < sThreadNum; ++i) {
            sOutput[i] = 0;
        }
    }

    void TearDown() {}
};

void UserCb(int thread_idx) {
    sOutput[thread_idx] = thread_idx;
    BYTERPC_LOG(INFO) << "Call UserCb in thread: " << byte::ThisThread::GetThreadId();
    BYTERPC_LOG(INFO) << "sOutput[" << thread_idx << "] is: " << sOutput[thread_idx];
}

void userCbRedirect(void* param) {
    UserCb(*(static_cast<int*>(param)));
    sComplete = true;
}

TEST_F(AsyncManagerTest, CStyleRun) {
    sComplete = false;
    int64_t test_idx = 1;
    EXPECT_EQ(sOutput[test_idx], 0);
    AsyncManager::GetInstance()->Run(userCbRedirect, &test_idx);
    while (true) {
        if (sComplete) {
            break;
        }
    }
    EXPECT_EQ(sOutput[test_idx], test_idx);
}

void WaitFlagReady(std::atomic_bool* ready_flag) {
    while (!ready_flag->load(std::memory_order_acquire)) {
        usleep(1);
    }
    ready_flag->store(false, std::memory_order_release);
}

TEST_F(AsyncManagerTest, CppStlpeRun) {
    int test_idx = 6;
    EXPECT_EQ(sOutput[test_idx], 0);
    std::atomic_bool ready_flag(false);
    AsyncManager::GetInstance()->Run([&ready_flag, test_idx]() {
        UserCb(test_idx);
        ready_flag.store(true, std::memory_order_release);
    });
    WaitFlagReady(&ready_flag);

    EXPECT_EQ(sOutput[test_idx], test_idx);

    test_idx = 7;
    EXPECT_EQ(sOutput[test_idx], 0);
    AsyncManager::GetInstance()->Run([&ready_flag, test_idx]() {
        UserCb(test_idx);
        ready_flag.store(true, std::memory_order_release);
    });
    WaitFlagReady(&ready_flag);
    EXPECT_EQ(sOutput[test_idx], test_idx);
}

TEST_F(AsyncManagerTest, ExecSubmit) {
    for (int i = 0; i < 100; ++i) {
        int test_idx = i % sThreadNum;
        sOutput[test_idx] = 0;
        EXPECT_EQ(sOutput[test_idx], 0);
        std::atomic_bool ready_flag(false);
        AsyncManager::GetInstance()->Run([&ready_flag, test_idx]() {
            UserCb(test_idx);
            ready_flag.store(true, std::memory_order_release);
        });
        WaitFlagReady(&ready_flag);
        EXPECT_EQ(sOutput[test_idx], test_idx);
    }
}

}  // namespace byterpc
