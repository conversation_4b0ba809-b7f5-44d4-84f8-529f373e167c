// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "protocol/http/details/http_parser.h"
#include "byterpc/util/logging.h"

#include <byte/system/timestamp.h>
#include <gtest/gtest.h>

#include <iostream>

namespace {

class HttpParserTest : public testing::Test {
protected:
    void SetUp() {}
    void TearDown() {}
};

TEST_F(HttpParserTest, init_perf) {
    const size_t loops = 10000000;
    uint64_t start_time = byte::GetCurrentTimeInNs();
    for (size_t i = 0; i < loops; ++i) {
        http_parser parser;
        http_parser_init(&parser, HTTP_REQUEST);
    }
    uint64_t cost = byte::GetCurrentTimeInNs() - start_time;
    std::cout << "It takes " << cost / loops << "ns to init a http_parser" << std::endl;
}

int on_message_begin(http_parser*) {
    BYTERPC_LOG(INFO) << "Start parsing message";
    return 0;
}

int on_url(http_parser*, const char* at, const size_t length) {
    BYTERPC_LOG(INFO) << "Get url " << std::string(at, length);
    return 0;
}

int on_headers_complete(http_parser*) {
    BYTERPC_LOG(INFO) << "Header complete";
    return 0;
}

int on_message_complete(http_parser*) {
    BYTERPC_LOG(INFO) << "Message complete";
    return 0;
}

int on_header_field(http_parser*, const char* at, const size_t length) {
    BYTERPC_LOG(INFO) << "Get header field " << std::string(at, length);
    return 0;
}

int on_header_value(http_parser*, const char* at, const size_t length) {
    BYTERPC_LOG(INFO) << "Get header value " << std::string(at, length);
    return 0;
}

int on_body(http_parser*, const char* at, const size_t length) {
    BYTERPC_LOG(INFO) << "Get body " << std::string(at, length);
    return 0;
}

TEST_F(HttpParserTest, http_request_example) {
    const char* http_request = "GET /path/file.html?sdfsdf=sdfs HTTP/1.0\r\n"
                               "From: <EMAIL>\r\n"
                               "User-Agent: HTTPTool/1.0\r\n"
                               "Content-Type: json\r\n"
                               "Content-Length: 19\r\n"
                               "Host: sdlfjslfd\r\n"
                               "Accept: */*\r\n"
                               "\r\n"
                               "Message Body sdfsdf\r\n";
    std::cout << http_request << std::endl;

    http_parser parser;
    http_parser_init(&parser, HTTP_REQUEST);
    http_parser_settings settings;
    memset(&settings, 0, sizeof(settings));
    settings.on_message_begin = on_message_begin;
    settings.on_url = on_url;
    settings.on_headers_complete = on_headers_complete;
    settings.on_message_complete = on_message_complete;
    settings.on_header_field = on_header_field;
    settings.on_header_value = on_header_value;
    settings.on_body = on_body;
    BYTERPC_LOG(INFO) << "parse response len="
                      << http_parser_execute(
                             &parser, &settings, http_request, strlen(http_request));

    EXPECT_EQ(HPE_OK, HTTP_PARSER_ERRNO(&parser));
}

TEST_F(HttpParserTest, http_response_example) {
    const char* http_response = "HTTP/1.1 200 OK\r\n"
                                "Date: Mon, 20 Jan 2022 22:22:22 GMT\r\n"
                                "Content-Type: application/json\r\n"
                                "Content-Length: 8\r\n"
                                "\r\n"
                                "xxxxxxxx\r\n";
    std::cout << http_response << std::endl;

    http_parser parser;
    http_parser_init(&parser, HTTP_RESPONSE);
    http_parser_settings settings;
    memset(&settings, 0, sizeof(settings));
    settings.on_message_begin = on_message_begin;
    settings.on_url = on_url;
    settings.on_headers_complete = on_headers_complete;
    settings.on_message_complete = on_message_complete;
    settings.on_header_field = on_header_field;
    settings.on_header_value = on_header_value;
    settings.on_body = on_body;
    BYTERPC_LOG(INFO) << "parse response len="
                      << http_parser_execute(
                             &parser, &settings, http_response, strlen(http_response));

    EXPECT_EQ(HPE_OK, HTTP_PARSER_ERRNO(&parser));
}
}  // namespace
