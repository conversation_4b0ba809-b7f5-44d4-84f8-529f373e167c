// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "rpc/channel_manager.h"

#include <byte/concurrent/count_down_latch.h>
#include <byte/string/number.h>
#include <byte/util/defer.h>
#include <unistd.h>

#include <future>
#include <memory>
#include <thread>

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/rpc.h"
#include "byterpc/server.h"
#include "google/protobuf/stubs/callback.h"
#include "gtest/gtest.h"
#include "proto/builtin_service.pb.h"
#include "protocol/message_handler_manager.h"
#include "rpc/fallback_channel.h"
#include "rpc/protobuf_channel.h"
#include "rpc/stateful_controller.h"
#include "transport/tcp/event_dispatcher.h"

namespace byterpc {

class ChannelManagerTest : public ::testing::Test {
public:
    ChannelManagerTest() {}
    ~ChannelManagerTest() {}

    void SetUp() override {
        gflags::FlagSaver saver;
        FLAGS_byterpc_min_resident_capacity_in_MB_to_expand_memory = 0;
        ExecCtx ctx(LOOP_IF_POSSIBLE);
    }

    void TearDown() override {}
};

TEST_F(ChannelManagerTest, PairEndpointsHash) {
    PairEndpointsHash hashFunc;

    util::EndPoint remote_ep;
    util::EndPoint local_ep;
    util::str2endpoint("127.0.0.1:18888", &remote_ep);
    util::str2endpoint("127.0.0.1:12333", &local_ep);
    PairEndpoints pair_eps(remote_ep, local_ep, TYPE_KERNEL_TCP);
    PairEndpoints pair_eps1 = pair_eps;
    PairEndpoints pair_eps2(pair_eps);

    EXPECT_EQ(hashFunc(pair_eps), hashFunc(pair_eps1));
    EXPECT_EQ(hashFunc(pair_eps), hashFunc(pair_eps2));
}

TEST_F(ChannelManagerTest, GetChannel) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_channel_map_check_interval_ms = 1000;
    FLAGS_byterpc_channel_defer_close_second = 1;
    std::unique_ptr<ChannelManager> channel_manager = std::make_unique<ChannelManager>(nullptr);

    Builder::ChannelOptions options;
    options._rpc_timeout_ms = 1000;
    options._connect_timeout_ms = 1000;
    const std::string ip_str = "127.0.0.1";
    std::vector<std::shared_ptr<Builder::Channel>> channels;
    for (int i = 8000; i < 8100; ++i) {
        std::string addr = ip_str + ":" + byte::NumberToString(i);
        util::EndPoint remote_side;
        util::str2endpoint(addr.c_str(), &remote_side);
        for (int k = 0; k < 3; k += 2) {  // transport type
            options._trans_type = static_cast<TransportType>(k);
            std::shared_ptr<Builder::Channel> channel =
                channel_manager->GetChannel(remote_side, options);
            EXPECT_TRUE(channel != nullptr);
            channels.push_back(channel);
        }
    }
    EXPECT_EQ(100 * 2, channel_manager->GetChannelCount());

    // Get Channel again
    int index = 0;
    for (int i = 8000; i < 8100; ++i) {
        std::string addr = ip_str + ":" + byte::NumberToString(i);
        util::EndPoint remote_side;
        util::str2endpoint(addr.c_str(), &remote_side);
        for (int k = 0; k < 3; k += 2) {  // transport type
            options._trans_type = static_cast<TransportType>(k);
            std::shared_ptr<Builder::Channel> channel =
                channel_manager->GetChannel(remote_side, options);
            EXPECT_EQ(channel, channels[index++]);
        }
    }
    // No new transport was created
    EXPECT_EQ(100 * 2, channel_manager->GetChannelCount());

    // Add fallback channel
    std::vector<std::shared_ptr<Builder::Channel>> fb_channels;
    for (int i = 8000; i < 8100; ++i) {
        std::string addr = ip_str + ":" + byte::NumberToString(i);
        util::EndPoint remote_side;
        util::str2endpoint(addr.c_str(), &remote_side);
        options._trans_type = TYPE_RDMA;
        std::shared_ptr<Builder::Channel> channel1 =
            channel_manager->GetChannel(remote_side, options);
        options._trans_type = TYPE_KERNEL_TCP;
        std::shared_ptr<Builder::Channel> channel2 =
            channel_manager->GetChannel(remote_side, options);
        options._trans_type = TYPE_RDMA;
        std::shared_ptr<Builder::Channel> fchannel = std::make_shared<FallbackChannel>();
        FallbackChannel* chan = dynamic_cast<FallbackChannel*>(fchannel.get());
        chan->Init(channel1, channel2, channel_manager.get());
        channel_manager->PutFbChannel(remote_side, options, fchannel);
        fb_channels.push_back(fchannel);
    }
    // No new transport was created
    EXPECT_EQ(100 * 2, channel_manager->GetChannelCount());
    EXPECT_EQ(100, channel_manager->_fb_map.size());

    // Get fallback channel
    index = 0;
    for (int i = 8000; i < 8100; ++i) {
        std::string addr = ip_str + ":" + byte::NumberToString(i);
        util::EndPoint remote_side;
        util::str2endpoint(addr.c_str(), &remote_side);
        options._trans_type = TYPE_RDMA;
        auto fchannel = channel_manager->GetFbChannel(remote_side, options);
        EXPECT_TRUE(fchannel != nullptr);
        EXPECT_EQ(fchannel, fb_channels[index++]);
    }
    // try to get un-exist channel
    std::string addr = ip_str + ":8200";
    util::EndPoint remote_side;
    options._trans_type = TYPE_RDMA;
    ASSERT_EQ(0, util::str2endpoint(addr.c_str(), &remote_side));
    EXPECT_EQ(nullptr, channel_manager->GetFbChannel(remote_side, options));

    // release 100 channels
    for (int i = 0; i < 100; ++i) {
        channels.pop_back();
    }
    // no channel will be released, because fallback channels hold the ref count.
    int sleep_time = 0;
    while (channel_manager->GetChannelCount() == 200) {
        ExecCtx::LoopOnce();
        if (++sleep_time >= 3) {
            break;
        }
        ExecCtx::LoopOnce();
        sleep(1);
    }
    for (auto channel : channels) {
        ProtobufChannel* chan = dynamic_cast<ProtobufChannel*>(channel.get());
        EXPECT_FALSE(chan->IsConnected());
    }

    // clear fallback channel, and it will release the additional ref count.
    fb_channels.clear();
    while (channel_manager->GetChannelCount() != 100) {
        sleep(1);
        ExecCtx::LoopOnce();
    }
    EXPECT_EQ(0, channel_manager->_fb_map.size());

    // release last 100 channels
    for (int i = 0; i < 100; ++i) {
        channels.pop_back();
    }
    while (channel_manager->GetChannelCount() != 0) {
        sleep(1);
        ExecCtx::LoopOnce();
    }

    EXPECT_EQ(0, channels.size());
    EXPECT_EQ(0, channel_manager->GetChannelCount());

    channel_manager.reset();
    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, CustomizeChannelRecycleTime) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_channel_map_check_interval_ms = 1;  // Not 0 since 0 means never check
    FLAGS_byterpc_channel_defer_close_second = 0;
    std::unique_ptr<ChannelManager> channel_manager = std::make_unique<ChannelManager>(nullptr);

    auto create_channel = [&](const std::string& addr, int customized_close_second = -1) {
        util::EndPoint remote_side;
        util::str2endpoint(addr.c_str(), &remote_side);
        Builder::ChannelOptions options;
        if (customized_close_second != -1) {
            options._customized_defer_close_second = customized_close_second;
        }
        channel_manager->GetChannel(remote_side, options);
    };
    auto loop_once_with_delay = []() {
        usleep(FLAGS_byterpc_channel_map_check_interval_ms * 1'000L);
        ExecCtx::LoopOnce();
    };

    // Default: recycle the channel without delay
    // since we set FLAGS_byterpc_channel_defer_close_second = 0
    {
        create_channel("127.0.0.1:8000");
        EXPECT_EQ(1, channel_manager->GetChannelCount());
        loop_once_with_delay();  // kick off

        sleep(0);
        loop_once_with_delay();  // recyle channel 127.0.0.1:8000
        EXPECT_EQ(0, channel_manager->GetChannelCount());
    }

    // Customize one channel to be recycled after 1s
    {
        create_channel("127.0.0.1:8001", 1);
        EXPECT_EQ(1, channel_manager->GetChannelCount());
        loop_once_with_delay();  // kick off timer

        sleep(1);
        loop_once_with_delay();  // recyle channel 127.0.0.1:8001
        EXPECT_EQ(0, channel_manager->GetChannelCount());
    }

    // Customize two different channels with different recycle time
    // One after 1s and the other after 3s
    {
        create_channel("127.0.0.1:8002", 1);
        create_channel("127.0.0.1:8003", 3);
        EXPECT_EQ(2, channel_manager->GetChannelCount());
        loop_once_with_delay();  // kick off timer

        sleep(1);
        loop_once_with_delay();  // recyle channel 127.0.0.1:8002
        EXPECT_EQ(1, channel_manager->GetChannelCount());

        sleep(2);
        loop_once_with_delay();  // recyle channel 127.0.0.1:8003
        EXPECT_EQ(0, channel_manager->GetChannelCount());
    }

    channel_manager.reset();
    ExecCtx::ResetThreadNode();
}

static void HealthCheckCb(proto::HealthCheckResponse* response,
                          Controller* cntl,
                          byte::CountDownLatch* latch) {
    std::unique_ptr<proto::HealthCheckResponse> resp_guard(response);
    EXPECT_EQ(cntl->ErrorCode(), 0);
    latch->CountDown();
}

TEST_F(ChannelManagerTest, RecycleIdleConnectionsAhead) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_channel_map_check_interval_ms = 1000;
    FLAGS_byterpc_channel_defer_close_second = 60;
    std::unique_ptr<MessageHandlerManager> client_msg_handler_mgr(new MessageHandlerManager(true));
    client_msg_handler_mgr->RegisterMessageHandlers();
    std::unique_ptr<ChannelManager> channel_manager =
        std::make_unique<ChannelManager>(client_msg_handler_mgr.get());

    bool server_ready = false;
    bool stop_server = false;
    util::EndPoint local_ep;
    local_ep.ip = util::my_ip();
    int32_t start_port = 23444;
    const int32_t k_server_num = 20;
    std::vector<Server*> listen_servers;
    std::thread server_thread([&]() {
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        ServerOptions options(true, false, true);
        options._enable_builtin_service = true;
        int32_t server_num = 0;
        while (server_num < k_server_num) {
            Server* server = new Server();
            local_ep.port = start_port++;
            if (server->Start(local_ep, options) == 0) {
                server_num++;
                listen_servers.push_back(server);
            } else {
                delete server;
            }
        }
        server_ready = true;

        while (!stop_server) {
            ExecCtx::LoopOnce();
        }

        for (auto server : listen_servers) {
            delete server;
        }
        ExecCtx::ResetThreadNode();
    });

    // wait until server started
    while (!server_ready) {
        usleep(1);
    }

    Builder::ChannelOptions options;
    std::vector<std::shared_ptr<Builder::Channel>> channels;
    for (auto server : listen_servers) {
        for (int k = 0; k < 3; k += 2) {  // transport type
            options._trans_type = static_cast<TransportType>(k);
            options._rpc_timeout_ms = 10000;
            options._connect_timeout_ms = 10000;
            options._build_connect_option = Builder::ConnectOptions::ASYNC_CONNECT;
            std::shared_ptr<Builder::Channel> channel =
                channel_manager->GetChannel(server->listen_address(), options);
            EXPECT_TRUE(channel != nullptr);
            channels.push_back(channel);
        }
    }
    EXPECT_EQ(k_server_num * 2, channel_manager->GetChannelCount());
    EXPECT_EQ(k_server_num * 2, channels.size());
    // wait until all connections is setup
    for (size_t i = 0; i < channels.size(); ++i) {
        ProtobufChannel* chan = dynamic_cast<ProtobufChannel*>(channels[i].get());
        while (!chan->IsConnected()) {
            ExecCtx::LoopOnce();
        }
    }

    // Add fallback channel
    std::vector<std::shared_ptr<Builder::Channel>> fb_channels;
    for (auto server : listen_servers) {
        util::EndPoint remote_side = server->listen_address();
        options._trans_type = TYPE_RDMA;
        std::shared_ptr<Builder::Channel> channel1 =
            channel_manager->GetChannel(remote_side, options);
        options._trans_type = TYPE_KERNEL_TCP;
        std::shared_ptr<Builder::Channel> channel2 =
            channel_manager->GetChannel(remote_side, options);
        options._trans_type = TYPE_RDMA;
        std::shared_ptr<Builder::Channel> fchannel = std::make_shared<FallbackChannel>();
        FallbackChannel* chan = dynamic_cast<FallbackChannel*>(fchannel.get());
        chan->Init(channel1, channel2, channel_manager.get());
        channel_manager->PutFbChannel(remote_side, options, fchannel);
        fb_channels.push_back(fchannel);
    }
    // No new transport was created
    EXPECT_EQ(k_server_num * 2, channel_manager->GetChannelCount());
    EXPECT_EQ(k_server_num, channel_manager->_fb_map.size());

    // speedup channel check
    FLAGS_byterpc_channel_defer_close_second = 1;
    // all channels still exist, but the underlay connections are released.
    int sleep_time = 0;
    while (channel_manager->GetChannelCount() == k_server_num * 2) {
        ExecCtx::LoopOnce();
        if (++sleep_time >= 3) {
            break;
        }
        ExecCtx::LoopOnce();
        sleep(1);
    }
    for (auto channel : channels) {
        ProtobufChannel* chan = dynamic_cast<ProtobufChannel*>(channel.get());
        EXPECT_FALSE(chan->IsConnected());
    }
    EXPECT_EQ(k_server_num * 2, channel_manager->GetChannelCount());
    EXPECT_EQ(k_server_num, channel_manager->_fb_map.size());

    // try to send rpc, and rebuild connections.
    Builder builder;
    byte::CountDownLatch inflight_rpc(channels.size());
    // slow down channel check to prevent newly established connections
    // from being accidentally released
    FLAGS_byterpc_channel_defer_close_second = 60;
    for (auto channel : channels) {
        auto cntl = builder.CreateSessionController(PROTOCOL_BYTE_STD);
        proto::HealthCheckRequest request;
        proto::HealthCheckResponse* response = new proto::HealthCheckResponse();
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<proto::HealthCheckResponse*, Controller*, byte::CountDownLatch*>(
                HealthCheckCb, response, cntl, &inflight_rpc);
        proto::transport_Stub stub(channel.get());
        stub.check_health(cntl, &request, response, done);
    }
    // wait until all rpc finished.
    while (inflight_rpc.GetCount() != 0) {
        usleep(10);
        ExecCtx::LoopOnce();
    }
    for (auto channel : channels) {
        ProtobufChannel* chan = dynamic_cast<ProtobufChannel*>(channel.get());
        EXPECT_TRUE(chan->IsConnected());
    }

    // clear all channels, and it will release the additional ref count.
    channels.clear();
    fb_channels.clear();
    // speedup channel check
    FLAGS_byterpc_channel_defer_close_second = 1;
    while (channel_manager->GetChannelCount() != 0) {
        sleep(1);
        ExecCtx::LoopOnce();
    }

    stop_server = true;
    server_thread.join();

    channel_manager.reset();
    ExecCtx::ResetThreadNode();
}

class SimpleController : public StatefulController {
public:
    SimpleController() {}

    void OnRecycle() override {}

    void DoRecycle() override {}

    util::EndPoint local_side() override {
        return util::EndPoint();
    }

    util::EndPoint remote_side() override {
        return util::EndPoint();
    }

    void IssueRPC(ClientSocket* socket,
                  const google::protobuf::MethodDescriptor* method,
                  const google::protobuf::Message* request,
                  google::protobuf::Message* response,
                  google::protobuf::Closure* done) override {}

    ProtocolType request_protocol() const override {
        return PROTOCOL_UNKNOWN;
    }
};

TEST_F(ChannelManagerTest, InvokedWillResetIdleIntervals) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_channel_map_check_interval_ms = 1000;
    FLAGS_byterpc_channel_defer_close_second = 1;
    std::unique_ptr<ChannelManager> channel_manager = std::make_unique<ChannelManager>(nullptr);

    Builder::ChannelOptions options;
    options._rpc_timeout_ms = 1000;
    options._connect_timeout_ms = 1000;
    const std::string ip_str = "127.0.0.1";

    std::shared_ptr<Builder::Channel> last_channel;
    for (int i = 8000; i < 8100; ++i) {
        std::string addr = ip_str + ":" + byte::NumberToString(i);
        util::EndPoint remote_side;
        util::str2endpoint(addr.c_str(), &remote_side);
        options._trans_type = TYPE_RDMA;
        std::shared_ptr<Builder::Channel> channel1 =
            channel_manager->GetChannel(remote_side, options);
        options._trans_type = TYPE_KERNEL_TCP;
        std::shared_ptr<Builder::Channel> channel2 =
            channel_manager->GetChannel(remote_side, options);
        options._trans_type = TYPE_RDMA;
        last_channel = std::make_shared<FallbackChannel>();
        FallbackChannel* chan = dynamic_cast<FallbackChannel*>(last_channel.get());
        chan->Init(channel1, channel2, channel_manager.get());
        channel_manager->PutFbChannel(remote_side, options, last_channel);
    }
    EXPECT_EQ(100 * 2, channel_manager->GetChannelCount());
    EXPECT_EQ(100, channel_manager->_fb_map.size());

    struct Closure : public google::protobuf::Closure {
        void Run() override {}
    };

    Closure closure;
    std::weak_ptr<Builder::Channel> weak_channel = last_channel;
    last_channel.reset();
    // NOTE: MUST switch to backup channel
    FallbackChannel::ForceSwitchChannel(true);
    while (channel_manager->GetChannelCount() > 2) {
        if (auto channel = weak_channel.lock()) {
            std::unique_ptr<Controller> cntl(new SimpleController());
            channel->CallMethod(nullptr, cntl.get(), nullptr, nullptr, &closure);
        }
        usleep(100000);  // 100ms
        ExecCtx::LoopOnce();
    }
    EXPECT_EQ(2, channel_manager->GetChannelCount());
    EXPECT_EQ(1, channel_manager->_fb_map.size());

    while (channel_manager->GetChannelCount() != 0) {
        sleep(1);
        ExecCtx::LoopOnce();
    }
    EXPECT_EQ(0, channel_manager->GetChannelCount());
    EXPECT_EQ(0, channel_manager->_fb_map.size());

    channel_manager.reset();
    ExecCtx::ResetThreadNode();
}

// Health check related tests
constexpr int kRpcNum = 100;

static std::shared_ptr<Builder::Channel> CreateChannel(ChannelManager& channel_manager,
                                                       const util::EndPoint& server_ep = {}) {
    Builder::ChannelOptions opts;
    EventRegistry* ev_reg = ExecCtx::GetOrNewThreadEventRegistry();
    BYTERPC_CHECK(ev_reg);
    return channel_manager.GetChannel(server_ep, opts);
}

TEST_F(ChannelManagerTest, MarkChannelAsUnhealhty) {
    gflags::FlagSaver saver;
    // Forbid health check
    FLAGS_byterpc_fallback_health_check_interval_s = 0;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the previous one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;
    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        for (size_t fail_cnt = 1; fail_cnt <= kRpcNum; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
            if (fail_cnt < kRpcNum) {
                EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
            } else {
                // Channel becomes unhealthy only after kRpcNum failures, not before
                EXPECT_FALSE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
            }
        }
    }

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, IgnoreUnrelatedError) {
    gflags::FlagSaver saver;
    // Forbid health check
    FLAGS_byterpc_fallback_health_check_interval_s = 0;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the previous one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;
    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        for (size_t fail_cnt = 1; fail_cnt <= kRpcNum; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), EOVERCROWDED);
        }
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
    }

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, MarkChannelAsDamaged) {
    gflags::FlagSaver saver;
    // Forbid health check
    FLAGS_byterpc_fallback_health_check_interval_s = 0;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the last one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;

    // 1. Switch from RISKY
    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
        channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERDMAUNAVAIL);
        EXPECT_TRUE(channel_manager.TEST_IsChannelDamaged(PairEndpoints(channel.get())));
    }

    // 2. Switch from UNHEALTHY
    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
        for (size_t fail_cnt = 1; fail_cnt <= kRpcNum; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        EXPECT_TRUE(channel_manager.TEST_IsChannelUnhealthy(PairEndpoints(channel.get())));
        channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERDMAUNAVAIL);
        EXPECT_TRUE(channel_manager.TEST_IsChannelDamaged(PairEndpoints(channel.get())));
    }

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, IgnoreRepeatedFailure) {
    gflags::FlagSaver saver;
    // Forbid health check
    FLAGS_byterpc_fallback_health_check_interval_s = 0;
    // Do not count repeated rpc failures of the same error code
    // Assume 20s is long enough to finish this test 
    FLAGS_byterpc_fallback_dedup_failure_window_us = 20'000'000;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;

    // 1. Repeated failures will be discarded
    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
        for (size_t fail_cnt = 1; fail_cnt <= kRpcNum; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
    }

    // 2. Non-repeated failures will always be counted
    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
        for (size_t fail_cnt = 1; fail_cnt <= kRpcNum; ++fail_cnt) {
            if (fail_cnt % 2 == 0) {
                channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
            } else {
                channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ECONNECTFAILED);
            }
        }
        EXPECT_FALSE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
    }

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, PopExpiredConnectionHealthInfo) {
    gflags::FlagSaver saver;
    // Forbid health check
    FLAGS_byterpc_fallback_health_check_interval_s = 0;
    FLAGS_byterpc_fallback_transport_switch_window_ms = 1000;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the last one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;
    // Disable garbage collection
    FLAGS_byterpc_channel_map_check_interval_ms = 1000'000;

    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        for (size_t fail_cnt = 1; fail_cnt <= (kRpcNum + 1) / 2; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));

        // Wait to make sure all above failure info is expired
        uint64_t last_failure_ts = util::GetCurrentTimeInMs();
        while (util::GetCurrentTimeInMs() <=
               last_failure_ts + FLAGS_byterpc_fallback_transport_switch_window_ms) {
        }

        for (size_t fail_cnt = 1; fail_cnt <= (kRpcNum + 1) / 2; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        // channel is still healthy though we have >= kRpcNum failures
        // since the first (kRpcNum + 1)/2 failure info all expired
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
        for (size_t fail_cnt = 1; fail_cnt <= (kRpcNum + 1) / 2; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        // channel is unhealthy now since it has accumulated >= kRpcNum rpc failures
        EXPECT_FALSE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
    }

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, RemoveExpiredRiskyConnections) {
    gflags::FlagSaver saver;
    // Forbid health check
    FLAGS_byterpc_fallback_health_check_interval_s = 0;
    FLAGS_byterpc_fallback_transport_switch_window_ms = 1000;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the last one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;
    FLAGS_byterpc_channel_map_check_interval_ms = 1000;
    FLAGS_byterpc_fallback_risky_conn_gc_interval_s = 1;

    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel = CreateChannel(channel_manager);
        for (size_t fail_cnt = 1; fail_cnt <= (kRpcNum + 1) / 2; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        // Only one risky channel in _unhealthy_map
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));
        EXPECT_EQ(1, channel_manager._unhealthy_map.size());

        const int kLoopCnt = FLAGS_byterpc_fallback_risky_conn_gc_interval_s * 1000 /
                             FLAGS_byterpc_channel_map_check_interval_ms;
        for (int i = 0; i < kLoopCnt + 1; ++i) {
            ExecCtx::LoopOnce();
            sleep(1);  // > FLAGS_byterpc_channel_map_check_interval_ms;
        }
        EXPECT_EQ(0, channel_manager._unhealthy_map.size());
    }

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, HealthCheck) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_fallback_health_check_interval_s = 1;
    FLAGS_byterpc_fallback_transport_switch_window_ms = 30'000;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the last one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;
    FLAGS_byterpc_fallback_health_check_continuous_succ_time = 3;
    // Garbage collect redundance risky info every 1 second
    FLAGS_byterpc_fallback_risky_conn_gc_interval_s = 1;
    FLAGS_byterpc_channel_map_check_interval_ms = 1000;
    FLAGS_byterpc_connect_timeout_ms = 10'000;
    FLAGS_byterpc_rpc_timeout_ms = 10'000;

    std::promise<util::EndPoint> server_ep_promise;
    std::future<util::EndPoint> server_ep_future = server_ep_promise.get_future();
    bool finished = false;

    auto start_server = [&finished](std::promise<util::EndPoint> ep_promise) {
        { ExecCtx ctx(LOOP_IF_POSSIBLE); }
        Server server;
        ServerOptions options(true, false, false);
        options._enable_builtin_service = true;
        util::EndPoint server_ep;
        server_ep.ip = util::my_ip();
        server_ep.port = 15001;
        while (server.Start(server_ep, options) < 0) {
            BYTERPC_LOG(ERROR) << "Fail to start server at port=" << server_ep.port;
            server_ep.port++;
        }
        ep_promise.set_value(server_ep);
        while (!finished) {
            ExecCtx::LoopOnce();
        }
        ExecCtx::ResetThreadNode();
    };
    std::thread server_thread(start_server, std::move(server_ep_promise));

    {
        ChannelManager channel_manager(nullptr);
        std::shared_ptr<Builder::Channel> channel =
            CreateChannel(channel_manager, server_ep_future.get());
        // Switch to UNHEALTHY status
        for (int fail_cnt = 0; fail_cnt < kRpcNum; ++fail_cnt) {
            channel_manager.UpdateChannelStatusOnError(PairEndpoints(channel.get()), ERPCTIMEDOUT);
        }
        EXPECT_FALSE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));

        // Wait until health check success
        while (!channel_manager.IsChannelHealthy(PairEndpoints(channel.get()))) {
            ExecCtx::LoopOnce();
        }
        EXPECT_TRUE(channel_manager.IsChannelHealthy(PairEndpoints(channel.get())));

        // Watch channel and remove the entry
        const int kLoopCnt = FLAGS_byterpc_fallback_risky_conn_gc_interval_s * 1000 /
                             FLAGS_byterpc_channel_map_check_interval_ms;
        for (int i = 0; i < kLoopCnt + 1; ++i) {
            ExecCtx::LoopOnce();
            sleep(1);  // > FLAGS_byterpc_channel_map_check_interval_ms;
        }
        EXPECT_EQ(0, channel_manager._unhealthy_map.size());
    }

    finished = true;
    server_thread.join();

    ExecCtx::ResetThreadNode();
}

TEST_F(ChannelManagerTest, AllowManyUnhealthyChannels) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_fallback_health_check_interval_s = 1;
    FLAGS_byterpc_fallback_transport_switch_window_ms = 30'000;
    // Allow repeated failure, i.e. a rpc failure with the same error code as the last one
    // is not discarded and will increment the failure queue length
    FLAGS_byterpc_fallback_dedup_failure_window_us = 0;
    FLAGS_byterpc_fallback_risky_max_failed_count = kRpcNum;
    FLAGS_byterpc_fallback_health_check_continuous_succ_time = 3;
    // never trigger gc task
    FLAGS_byterpc_channel_map_check_interval_ms = 1000'000;
    FLAGS_byterpc_connect_timeout_ms = 10'000;
    FLAGS_byterpc_rpc_timeout_ms = 10'000;

    {
        ChannelManager channel_manager(nullptr);
        // For running UT with --gtest_repeat=N (N > 1), kChannelNum <= 32 is required
        // However, in production scenario, kChannelNum cound be much larger
        constexpr int kChannelNum = 32;
        std::vector<std::shared_ptr<Builder::Channel>> channels;
        for (int i = 0; i < kChannelNum; ++i) {
            util::EndPoint server_ep;
            server_ep.ip = util::my_ip();
            server_ep.port = 15001 + i;
            channels.push_back(CreateChannel(channel_manager, server_ep));
        }

        // Switch to UNHEALTHY status
        for (auto chann : channels) {
            for (int fail_cnt = 0; fail_cnt < kRpcNum; ++fail_cnt) {
                channel_manager.UpdateChannelStatusOnError(PairEndpoints(chann.get()),
                                                           ERPCTIMEDOUT);
            }
            EXPECT_FALSE(channel_manager.IsChannelHealthy(PairEndpoints(chann.get())));
        }

        // Send HealthCheck RPC
        sleep(FLAGS_byterpc_fallback_health_check_interval_s);
        ExecCtx::LoopOnce();
    }

    ExecCtx::ResetThreadNode();
}

}   // namespce byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    byterpc::InitOptions init_opt(false, true);
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
