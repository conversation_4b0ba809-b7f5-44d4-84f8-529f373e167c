// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "protocol/baidu_std/baidu_client_message_handler.h"
#include "protocol/baidu_std/baidu_message_serdes.h"
#include "protocol/baidu_std/baidu_server_message_handler.h"

#include <memory>

#include <byte/fiber/fiber.h>
#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/endpoint.h"
#include "byterpc/util/logging.h"
#include "iobuf/io_rbuf_pipe.h"
#include "rpc/exec_ctx_node.h"
#include "rpc/protobuf_serdes.h"
#include "rpc/rpc_service_registry.h"
#include "rpc/server_socket.h"
#include "status.pb.h"
#include "util/raw_pack.h"

namespace byterpc {

namespace {
    byte::fiber::TlsFiberPool* fiber_pool;
} // namespace

// test OnRecycle and DoRecycle
TEST(FiberBaiduMessageHandlerTest, ClientRecycle) {
    {ExecCtx ctx(LOOP_UNTIL_QUIT);}
    EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));

    BaiduClientMessageHandler client_msg_handler;
    Controller* cntl = client_msg_handler.CreateSessionController(1000);

    byte::fiber::CreateOpt opt;
    opt.joinable_ = true;
    byte::fiber::Fiber* rpc_fiber = byte::fiber::Create(opt, [=]() {
        EXPECT_FALSE(static_cast<BaiduRpcCall*>(cntl)->IsTerminating());
        EXPECT_NE(nullptr, static_cast<BaiduRpcCall*>(cntl)->_rpc_fiber);
        byte::fiber::Usleep(-1);
        // rpc done, but cntl has not been recycled
        EXPECT_TRUE(static_cast<BaiduRpcCall*>(cntl)->IsTerminating());
        EXPECT_NE(nullptr, static_cast<BaiduRpcCall*>(cntl)->_rpc_fiber);
        // call DoRecycle() to recycle the cntl
        cntl->DoRecycle();
    });

    // simulate sending rpc
    static_cast<BaiduRpcCall*>(cntl)->_rpc_fiber = rpc_fiber;
    byte::fiber::Yield();

    // rpc done (either success or failure) will call OnRecycle()
    cntl->OnRecycle();
    byte::fiber::Join(rpc_fiber);
    ExecCtx::ResetThreadNode();
}

// Belows are copied from baidu_message_handler_test.cpp and modified by fiber mode

class SimpleTransport : public Transport {
public:
    SimpleTransport()
        : _socket(nullptr),
          _rbuf_pipe(nullptr),
          _owner_ref(0),
          _upd_meta(false),
          _tamper_rpc_meta(false),
          _tamper_rpc_pb(false),
          _fail_send_msg(false) {}

    ~SimpleTransport() {}

    util::EndPoint local_side() const override {
        return util::EndPoint();
    }

    util::EndPoint remote_side() const override {
        return util::EndPoint();
    }

    TransportType GetTransportType() const override {
        return TYPE_KERNEL_TCP;
    }

    bool StartRead(size_t read_limit) override {
        return true;
    }

    void SetIORbufPipe(IORbufPipe* rbuf_pipe) {
        _rbuf_pipe = rbuf_pipe;
    }

    ssize_t StartWrite(std::unique_ptr<IOBuf>&& b) {
        if (_upd_meta) {
            // modify request or response meta to test meta info error
            UpdateRpcMeta(b.get());
        } else if (_tamper_rpc_meta) {
            // Tamper rpc serialized meta
            TamperRpcMeta(b.get());
        } else if (_tamper_rpc_pb) {
            // Tamper rpc serialized request/response
            TamperRpcPb(b.get());
        }
        size_t msg_size = b->size();
        b->cut_to(_rbuf_pipe, b->length());
        if (_fail_send_msg) {
            return msg_size;
        } else {
            // return 0 means send all msg successfully
            return 0;
        }
    }

    ssize_t StartWrite(
        IOBlockRef&& first,
        std::unique_ptr<IOBuf>&& second,
        std::unique_ptr<IOBuf>&& third,
        std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&& rdma_write_bufs) override {
        auto buf = std::make_unique<IOBuf>();
        if (first.size() > 0) {
            buf->append(std::move(first));
        }
        if (second) {
            buf->append(*second);
        }
        if (third) {
            buf->append(*third);
        }
        return StartWrite(std::move(buf));
    }

    void ResetSocket(Socket* replacement) override {}

    void HandleReadEvent() override {}

    void HandleWriteEvent() override {}

    int GetFD() const override {
        return 0;
    }

    uint32_t NumOfResets() const override {
        return 0;
    }

    void Reset(int error_code) override {}

    void OwnerRef() override {
        ++_owner_ref;
    }

    void OwnerUnref() override {
        if (--_owner_ref == 0) {
            delete this;
        }
    }

    int PreConnect() override {
        return 0;
    }

    bool IsConnected() override {
        return false;
    };

    void SetSocket(Socket* socket) {
        _socket = socket;
    }

    void SetUpdRpcMeta(bool upd_meta) {
        _upd_meta = upd_meta;
    }

    void SetTamperRpcMeta(bool tamper_rpc_meta) {
        _tamper_rpc_meta = tamper_rpc_meta;
    }

    void SetTamperRpcPb(bool tamper_rpc_pb) {
        _tamper_rpc_pb = tamper_rpc_pb;
    }

    void SetSendMsgFail(bool fail) {
        _fail_send_msg = fail;
    }

    void UpdateRpcMeta(IOBuf* buf) {
        static bool udp_att_size = true;
        size_t length = buf->length();
        proto::RpcMeta meta;
        IOBuf payload_buf;
        IORbufPipe rbuf_pipe;
        buf->cut_to(&rbuf_pipe, buf->length());
        ExtendHeader extend_header;
        ParseError pr =
            BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta, &payload_buf, &extend_header);
        EXPECT_EQ(PARSE_OK, pr);

        if (udp_att_size) {
            meta.set_attachment_size(2 * length);
        } else {
            meta.set_compress_type(proto::COMPRESS_TYPE_SNAPPY);
        }
        udp_att_size = !udp_att_size;
        IOBuf new_meta;
        BaiduStdSerDes::SerializeRpcMeta(&meta, &new_meta, payload_buf.length());
        buf->append(new_meta);
        buf->append(payload_buf);
    }

    void TamperRpcMeta(IOBuf* buf) {
        IOBuf head_buf;
        buf->cut_to(&head_buf, 12);
        // modify rpc meta
        buf->pop_front(2);
        buf->prepend("AA", 2);
        buf->prepend(head_buf);
    }

    void TamperRpcPb(IOBuf* buf) {
        proto::RpcMeta meta;
        IOBuf payload_buf;
        IORbufPipe rbuf_pipe;
        buf->cut_to(&rbuf_pipe, buf->length());
        ExtendHeader extend_header;
        ParseError pr =
            BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta, &payload_buf, &extend_header);
        EXPECT_EQ(PARSE_OK, pr);

        IOBuf new_meta;
        BaiduStdSerDes::SerializeRpcMeta(&meta, &new_meta, payload_buf.length());
        buf->append(new_meta);
        // modify rpc request/response
        payload_buf.pop_front(2);
        payload_buf.prepend("AA", 2);
        buf->append(payload_buf);
    }

private:
    Socket* _socket;
    IORbufPipe* _rbuf_pipe;
    int _owner_ref;
    bool _upd_meta;         // used to indicate to modify request or response meta info.
    bool _tamper_rpc_meta;  // used to indicate to tamper rpc meta.
    bool _tamper_rpc_pb;    // userd to indicate to tamper rpc request/response.
    bool _fail_send_msg;    // return msg size to indicate send msg fail
};

class MyStatusService : public status {
public:
    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) {
        util::ClosureGuard done_guard(done);
        if (request->has_has_attachment() && request->has_attachment()) {
            response->set_latency_us(100);
            auto cntl = static_cast<byterpc::Controller*>(controller);
            EXPECT_TRUE(cntl->HasIncomingAttachment());
            IOBuf* attachment = cntl->ReleaseIncomingAttachment();
            cntl->InstallOutgoingAttachment(*attachment);
            delete attachment;
        }
    }
};

class MyIndexService : public index {
public:
    void default_method(::google::protobuf::RpcController* controller,
                        const IndexRequest* request,
                        IndexResponse* response,
                        ::google::protobuf::Closure* done) {
        util::ClosureGuard done_guard(done);
    }
};

TEST(FiberBaiduMessageHandlerTest, Success) {
    ExecCtx ctx(LOOP_UNTIL_QUIT);
    EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client send request
    int kNum = 5;
    int client_send_rpc_num = 0;
    std::vector<StatusRequest> req(kNum);
    std::vector<StatusResponse> resp(kNum);
    MyStatusService* status = new MyStatusService;
    const google::protobuf::ServiceDescriptor* sd = status->GetDescriptor();
    const google::protobuf::MethodDescriptor* md = sd->method(0);
    BaiduClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            if (i % 2 == 1) {
                req[i].set_has_attachment(true);
                auto att = std::make_unique<IOBuf>();
                att->append(std::string(1024, 'x'));
                cntl->InstallOutgoingAttachment(std::move(att));
            }
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), 0);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    // server deal with request
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    RpcServiceRegistry svc_reg;
    ServiceOptions options;
    svc_reg.RegisterService(status, options);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    server_socket.ReadyToServe(server_trans);
    server_trans->SetSocket(&server_socket);

    BaiduServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    // client deal response
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();

    EXPECT_EQ(kNum, num);

    delete status;
    ExecCtx::ResetThreadNode();
}

TEST(FiberBaiduMessageHandlerTest, ServerFail) {
    ExecCtx ctx(LOOP_UNTIL_QUIT);
    EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // 1. test server not find method
    int kNum = 8;
    int client_send_rpc_num = 0;
    std::vector<StatusRequest> req(kNum);
    std::vector<StatusResponse> resp(kNum);
    MyStatusService* status = new MyStatusService;
    const google::protobuf::ServiceDescriptor* sd = status->GetDescriptor();
    const google::protobuf::MethodDescriptor* md = sd->method(0);
    int num = 0;
    BaiduClientMessageHandler client_msg_handler;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), ENOMETHOD);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    // server deal with request
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    RpcServiceRegistry svc_reg;
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    server_socket.ReadyToServe(server_trans);
    server_trans->SetSocket(&server_socket);

    BaiduServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();

    EXPECT_EQ(kNum, num);

    // server register status service to deal with client request
    ServiceOptions options;
    svc_reg.RegisterService(status, options);

    // 2. test request meta error
    client_trans->SetUpdRpcMeta(true);
    num = 0;
    client_send_rpc_num = 0;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), EREQUEST);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    client_trans->SetUpdRpcMeta(false);

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();

    EXPECT_EQ(kNum, num);

    // 3. test tamper rpc serialized pb request
    client_trans->SetTamperRpcPb(true);
    num = 0;
    client_send_rpc_num = 0;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            req[i].set_has_attachment(true);
            auto att = std::make_unique<IOBuf>();
            att->append(std::string(1024, 'x'));
            cntl->InstallOutgoingAttachment(std::move(att));
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), EREQUEST);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    client_trans->SetTamperRpcPb(false);

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();

    EXPECT_EQ(kNum, num);
    for (int i = 0; i < kNum; ++i) {
        req[i].Clear();
    }

    // 4. test response meta error
    num = 0;
    client_send_rpc_num = 0;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), ERESPONSE);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    server_trans->SetUpdRpcMeta(true);
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();
    server_trans->SetUpdRpcMeta(false);

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();
    
    EXPECT_EQ(kNum, num);

    // 5. test tamper rpc serialized pb response
    num = 0;
    client_send_rpc_num = 0;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            req[i].set_has_attachment(true);
            auto att = std::make_unique<IOBuf>();
            att->append(std::string(1024, 'x'));
            cntl->InstallOutgoingAttachment(std::move(att));
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), ERESPONSE);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    server_trans->SetTamperRpcPb(true);
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();
    server_trans->SetTamperRpcPb(false);

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();

    EXPECT_EQ(kNum, num);
    for (int i = 0; i < kNum; ++i) {
        req[i].Clear();
    }

    // 6. test server overcrowded
    num = 0;
    client_send_rpc_num = 0;
    // send one rpc, make server overcrowded
    for (int i = 0; i < 1; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), 0);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < 1) {
        byte::fiber::Yield();
    }

    FLAGS_byterpc_socket_max_unwritten_bytes = 0;
    server_trans->SetSendMsgFail(true);
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    // now server is overcrowded
    // client continuously send rpc
    for (int i = 1; i < kNum; ++i) {
        fiber_pool->AddTask([&,i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), EOVERCROWDED);
            ++num;
            cntl->DoRecycle();
        }, false);
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    FLAGS_byterpc_socket_max_unwritten_bytes = 64 * 1024 * 1024;
    server_trans->SetSendMsgFail(false);

    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();
    EXPECT_EQ(kNum, num);
    // recycle ServerController to avoid memory leak
    while (!server_socket._pending_writes.empty()) {
        server_socket._pending_writes.front().first->OnRecycle();
        server_socket._pending_writes.pop_front();
    }

    delete status;
    ExecCtx::ResetThreadNode();
}

TEST(FiberBaiduMessageHandlerTest, ControllerNotFound) {
    ExecCtx ctx(LOOP_UNTIL_QUIT);
    EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client send request
    int kNum = 5;
    int client_send_rpc_num = 0;
    std::vector<StatusRequest> req(kNum);
    std::vector<StatusResponse> resp(kNum);
    MyStatusService* status = new MyStatusService;
    const google::protobuf::ServiceDescriptor* sd = status->GetDescriptor();
    const google::protobuf::MethodDescriptor* md = sd->method(0);
    BaiduClientMessageHandler client_msg_handler;
    int num = 0;
    std::vector<Controller*> controllers;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&, i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            controllers.push_back(cntl);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), -1);
            ++num;
            cntl->DoRecycle();
        });
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    for (int i = 0; i < kNum; ++i) { 
        // delete cntl from client_socket to test controller not find in client_socket
        static_cast<BaiduRpcCall*>(controllers[i])->SetFailed(-1, "auto set failed");
    }
    fiber_pool->WaitForAllTasksDone();
    num = 0;

    // server deal request
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    RpcServiceRegistry svc_reg;
    ServiceOptions options;
    svc_reg.RegisterService(status, options);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    server_socket.ReadyToServe(server_trans);
    server_trans->SetSocket(&server_socket);

    BaiduServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    // client deal response
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(0, num);

    delete status;
    ExecCtx::ResetThreadNode();
}

TEST(FiberBaiduMessageHandlerTest, RespNotInitialized) {
    ExecCtx ctx(LOOP_UNTIL_QUIT);
    EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client send request
    int kNum = 5;
    int client_send_rpc_num = 0;
    std::vector<IndexRequest> req(kNum);
    std::vector<IndexResponse> resp(kNum);
    MyIndexService* index = new MyIndexService;
    const google::protobuf::ServiceDescriptor* sd = index->GetDescriptor();
    const google::protobuf::MethodDescriptor* md = sd->method(0);
    BaiduClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        fiber_pool->AddTask([&, i](){
            Controller* cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req[i], &resp[i], nullptr);
            EXPECT_EQ(cntl->ErrorCode(), ERESPONSE);
            ++num;
            cntl->DoRecycle();
        });
    }

    // wait for client to send rpc
    while (client_send_rpc_num < kNum) {
        byte::fiber::Yield();
    }

    // server deal with request
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    RpcServiceRegistry svc_reg;
    ServiceOptions options;
    svc_reg.RegisterService(index, options);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    server_socket.ReadyToServe(server_trans);
    server_trans->SetSocket(&server_socket);

    BaiduServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

    // client deal response
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
    fiber_pool->WaitForAllTasksDone();

    EXPECT_EQ(kNum, num);

    delete index;
    ExecCtx::ResetThreadNode();
}

TEST(FiberBaiduMessageHandlerTest, TamperRpcMeta) {
    ExecCtx ctx(LOOP_UNTIL_QUIT);
    EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client send request
    StatusRequest req;
    StatusResponse resp;
    MyStatusService* status = new MyStatusService;
    const google::protobuf::ServiceDescriptor* sd = status->GetDescriptor();
    const google::protobuf::MethodDescriptor* md = sd->method(0);
    BaiduClientMessageHandler client_msg_handler;

    // server deal with request
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    RpcServiceRegistry svc_reg;
    ServiceOptions options;
    svc_reg.RegisterService(status, options);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    server_socket.ReadyToServe(server_trans);
    server_trans->SetSocket(&server_socket);
    BaiduServerMessageHandler server_msg_handler;

    // test request meta is tampered
    {
        int num = 0;
        int client_send_rpc_num = 0;
        client_trans->SetTamperRpcMeta(true);

        Controller* cntl = nullptr;
        fiber_pool->AddTask([&](){
            cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req, &resp, nullptr);
            EXPECT_EQ(cntl->ErrorCode(), 0);
            ++num;
            cntl->DoRecycle();
        });

        // wait for client to send rpc
        while (client_send_rpc_num < 1) {
            byte::fiber::Yield();
        }
        
        client_trans->SetTamperRpcMeta(false);

        EXPECT_EQ(PARSE_ERROR_ABSOLUTELY_WRONG,
                  server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
        ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();

        // client deal with response
        EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
                  client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
        cntl->OnRecycle();
        fiber_pool->WaitForAllTasksDone();
    }

    {
        int num = 0;
        int client_send_rpc_num = 0;
        // test response meta is tampered
        Controller* cntl = nullptr;
        fiber_pool->AddTask([&](){
            cntl = client_msg_handler.CreateSessionController(1000);
            ++client_send_rpc_num;
            static_cast<BaiduRpcCall*>(cntl)->IssueRPC(&client_socket, md, &req, &resp, nullptr);
            EXPECT_EQ(cntl->ErrorCode(), 0);
            ++num;
            cntl->DoRecycle();
        });

        // wait for client to send rpc
        while (client_send_rpc_num < 1) {
            byte::fiber::Yield();
        }

        server_trans->SetTamperRpcMeta(true);
        EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
                  server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
        ExecCtxInternal::GetFiberPool()->WaitForAllTasksDone();
        server_trans->SetTamperRpcMeta(false);
        // client deal with response
        EXPECT_EQ(PARSE_ERROR_ABSOLUTELY_WRONG,
                  client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));
        cntl->OnRecycle();
        fiber_pool->WaitForAllTasksDone();
    }

    delete status;
    ExecCtx::ResetThreadNode();
}

TEST(FiberBaiduMessageHandlerTest, TestParseRpcMessage) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_max_body_size = 100;
    IORbufPipe rbuf_pipe;
    IOBuf buf;
    proto::RpcMeta meta_buf;
    IOBuf payload_buf;
    ExtendHeader extend_header;

    // construct a mock socket and controller
    SimpleTransport* client_trans = new SimpleTransport();
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();

    // 1. NOT bigger than rpc magic size
    buf.append("pra");
    buf.cut_to(&rbuf_pipe, buf.size());
#ifdef BYTERPC_ENABLE_EXTEND_HEADER
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_NOT_ENOUGH_DATA);
#else
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_TRY_OTHERS);
#endif
    rbuf_pipe.clear();
    buf.append("PRP");
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_NOT_ENOUGH_DATA);
    rbuf_pipe.clear();

    // 2. equal to magic size but magic is NOT correct
    buf.append("PRPA");
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_TRY_OTHERS);
    rbuf_pipe.clear();

    // 3. bigger than magic size but lower than meta size
    buf.append("PRPCa");
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_NOT_ENOUGH_DATA);
    rbuf_pipe.clear();

    // 4. meta is corrupt but length is not enough
    proto::RpcMeta meta;
    proto::RpcRequestMeta* request_meta = meta.mutable_request();
    request_meta->set_service_name("my_service_name");
    request_meta->set_method_name("my_method_name");
    request_meta->set_log_id(100);
    meta.set_compress_type(0 /*COMPRESS_TYPE_NONE*/);
    meta.set_correlation_id(100);
    StatusRequest req;
    BaiduStdSerDes::SerializeRpcMessage(&meta, &req, nullptr, &buf, CrcMode::TYPE_NONE);
    buf.pop_back(2);
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_NOT_ENOUGH_DATA);
    rbuf_pipe.clear();

    // 5. meta is corrupt and length is enough
    BaiduStdSerDes::SerializeRpcMessage(&meta, &req, nullptr, &buf, CrcMode::TYPE_NONE);
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_OK);
    rbuf_pipe.clear();

    // 6. body_size bigger than FLAGS_byterpc_max_body_size
    IOBuf attach_buf;
    attach_buf.append(std::string(FLAGS_byterpc_max_body_size, 'x'));
    meta.set_attachment_size(attach_buf.size());
    BaiduStdSerDes::SerializeRpcMessage(&meta, &req, &attach_buf, &buf, CrcMode::TYPE_NONE);
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_TOO_BIG_DATA);
    rbuf_pipe.clear();

    // 7. meta_size bigger than body_size
    char header[12];
    uint32_t* dummy = reinterpret_cast<uint32_t*>(header);
    *dummy = *reinterpret_cast<const uint32_t*>("PRPC");
    uint32_t req_meta_size = 20;
    util::RawPacker(header + 4)
        .pack32(req_meta_size /* body_size */)
        .pack32(req_meta_size + 10 /* meta_size */);
    buf.append(header, sizeof(header));
    buf.append(std::string(req_meta_size, 'x'));
    buf.cut_to(&rbuf_pipe, buf.size());
    EXPECT_EQ(BaiduStdSerDes::ParseRpcMessage(&rbuf_pipe, &meta_buf, &payload_buf, &extend_header),
              PARSE_ERROR_ABSOLUTELY_WRONG);
    rbuf_pipe.clear();
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));
    auto byterpc_fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });

    BYTERPC_CHECK(0 == byte::fiber::GlobalInit({}));
    auto fiber_global_fini_guard = byte::defer([]() { byte::fiber::GlobalFini(); });

    byte::fiber::ThreadInitOption opt;
    byte::fiber::ThreadInit(opt);
    byterpc::fiber_pool = new byte::fiber::TlsFiberPool();

    auto fiber_thread_fini_guard = byte::defer([]() {
        delete byterpc::fiber_pool;
        byte::fiber::ThreadFini();
    });

    return RUN_ALL_TESTS();
}
