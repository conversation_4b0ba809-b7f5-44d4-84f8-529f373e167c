// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "transport/tarzan/tarzan_acceptor.h"

#include <byte/base/atomic.h>
#include <gtest/gtest.h>

#include <memory>
#include <thread>

#include "byterpc/rpc.h"
#include "byterpc/server.h"
#include "byterpc/util/endpoint.h"
#include "byterpc/util/logging.h"
#include "tarzan.h"
#include "transport/tarzan/tarzan_context.h"
#include "transport/tarzan/tarzan_event_dispatcher.h"

namespace byterpc {

static bool need_init = true;
static byte::Atomic<int32_t> index = 0;
extern thread_local int32_t current_thread_tarzan_serial_number;

enum TestIOEventType {
    FailAddEpollOut = 1,
    FailAddConsumer = 2,
    None = 3,
};

class SimpleEventDispatcher : public EventRegistry {
public:
    SimpleEventDispatcher() {
        if (-1 == current_thread_tarzan_serial_number) {
            current_thread_tarzan_serial_number = 0;
        }
    }

    size_t NumRegisteredIOHandler() const override {
        return 0;
    }

    int AddConsumer(IOHandler* h) override {
        if (_type == TestIOEventType::FailAddConsumer) {
            return -1;
        }
        return 0;
    }

    int RemoveConsumer(IOHandler* h) override {
        return 0;
    }

    int AddEpollOut(IOHandler* h, bool pollin) override {
        if (_type == TestIOEventType::FailAddEpollOut) {
            return -1;
        }
        return 0;
    }

    int RemoveEpollOut(IOHandler* h, bool pollin) override {
        return 0;
    }

    TimeoutIterator AddTimeConsumer(TimeEventHandler* h, uint64_t microseconds_since_now) override {
        TimeoutIterator iter;
        return iter;
    }

    int RemoveTimeConsumer(TimeoutIterator iter) override {
        return 0;
    }

    void SetOutActive(IOHandler* h) override {}
    void ClearOutActive(IOHandler* h) override {}

    void SetTestFlag(TestIOEventType type) {
        _type = type;
    }

private:
    TestIOEventType _type;
};

static void StartAcceptor(byte::Atomic<int32_t>* fd) {
    // start tarzan acceptor
    Server server;
    SimpleEventDispatcher ev_reg;
    TarzanAcceptor* acceptor =
        new TarzanAcceptor(&ev_reg, nullptr, &server, UTCP_LISTEN_MULTIPLE_THREAD);
    acceptor->OwnerRef();
    util::EndPoint endpoint;
    EXPECT_EQ(0, util::str2endpoint("127.0.0.1:28888", &endpoint));
    while (acceptor->StartAccept(endpoint) != 0) {
        BYTERPC_LOG(WARNING) << "Fail to start accept on port=" << endpoint.port;
        endpoint.port++;
    }
    int32_t listen_fd = acceptor->GetFD();
    EXPECT_GE(listen_fd, 0);
    if (!fd->CompareExchange(-1, listen_fd)) {
        EXPECT_EQ(fd->Value(), listen_fd);
    }
    util::EndPoint listen_addr = acceptor->GetListenAddress();
    EXPECT_EQ(listen_addr, endpoint);
    ++index;
    while (index != 2) {
        // wait util two acceptors both listen on successfully
        usleep(1);
    }

    // stop tarzan acceptor
    acceptor->StopAccept();
    EXPECT_EQ(-2, acceptor->GetFD());
    EXPECT_EQ(0, (acceptor->GetSocketMap()->GetMap()).size());

    // recycle tarzan acceptor
    acceptor->OwnerUnref();
}

TEST(TarzanAcceptorTest, Basic) {
    if (need_init) {
        EXPECT_EQ(0, tarzan_init(1));
        need_init = false;
    }
    index = 0;
    byte::Atomic<int32_t> listen_fd = -1;
    std::unique_ptr<std::thread> acceptor1(new std::thread(StartAcceptor, &listen_fd));
    std::unique_ptr<std::thread> acceptor2(new std::thread(StartAcceptor, &listen_fd));
    acceptor1->join();
    acceptor2->join();
}

TEST(TarzanAcceptorTest, SingleListenMode) {
    if (need_init) {
        EXPECT_EQ(0, tarzan_init(1));
        need_init = false;
    }

    // 1. test start single listen mode
    Server server;
    SimpleEventDispatcher ev_reg;
    TarzanAcceptor* acceptor1 =
        new TarzanAcceptor(&ev_reg, nullptr, &server, UTCP_LISTEN_SINGLE_THREAD);
    acceptor1->OwnerRef();
    util::EndPoint addr;
    EXPECT_EQ(0, util::str2endpoint("127.0.0.1:28889", &addr));
    EXPECT_EQ(0, acceptor1->StartAccept(addr));
    EXPECT_GE(acceptor1->GetFD(), 0);

    // 2. test listen failed for sinle mode when already listen success before
    TarzanAcceptor* acceptor2 =
        new TarzanAcceptor(&ev_reg, nullptr, &server, UTCP_LISTEN_SINGLE_THREAD);
    acceptor2->OwnerRef();
    EXPECT_NE(0, acceptor2->StartAccept(addr));
    EXPECT_LT(acceptor2->GetFD(), 0);
    acceptor2->OwnerUnref();

    // 3. test listen failed after call StopAcceptor
    EXPECT_EQ(0, acceptor1->StopAccept());
    EXPECT_LT(acceptor1->GetFD(), 0);
    EXPECT_NE(0, acceptor1->StartAccept(addr));
    EXPECT_LT(acceptor1->GetFD(), 0);
    acceptor1->OwnerUnref();
}

TEST(TarzanAcceptorTest, HandleConnection) {
    if (need_init) {
        EXPECT_EQ(0, tarzan_init(1));
        need_init = false;
    }
    Server server;
    SimpleEventDispatcher ev_reg;
    TarzanAcceptor* acceptor =
        new TarzanAcceptor(&ev_reg, nullptr, &server, UTCP_LISTEN_SINGLE_THREAD);
    acceptor->OwnerRef();
    util::EndPoint listen_addr;
    EXPECT_EQ(0, util::str2endpoint("127.0.0.1:29000", &listen_addr));
    EXPECT_EQ(0, acceptor->StartAccept(listen_addr));

    // client establish connection
    struct tarzan_sock_opts opts;
    memset(&opts, 0, sizeof(opts));
    opts.has_worker_id = 1;
    opts.worker_id = current_thread_tarzan_serial_number;
    int connect_cd =
        tarzan_connect_to(util::ip2str(listen_addr.ip).c_str(), listen_addr.port, &opts);
    EXPECT_GE(connect_cd, 0);

    // server handle connection failed because StartRead failed
    tarzan_accept_sock socks[32];
    int nr = 0;
    while (nr == 0) {
        tarzan_worker_run(current_thread_tarzan_serial_number);
        nr = tarzan_accept_burst(current_thread_tarzan_serial_number, socks, 32);
    }
    int fd = *(static_cast<int32_t*>(socks[0].data));
    EXPECT_EQ(fd, acceptor->GetFD());
    ev_reg.SetTestFlag(TestIOEventType::FailAddConsumer);
    acceptor->HandleNewConnection(&socks[0]);
    EXPECT_EQ(0, acceptor->_socket_map._map.size());
    tarzan_close(connect_cd);
    connect_cd = -1;

    // server handle connection success
    ev_reg.SetTestFlag(TestIOEventType::None);
    memset(socks, 0, sizeof(socks));
    nr = 0;
    connect_cd = tarzan_connect_to(util::ip2str(listen_addr.ip).c_str(), listen_addr.port, &opts);
    EXPECT_GE(connect_cd, 0);
    while (nr == 0) {
        tarzan_worker_run(current_thread_tarzan_serial_number);
        nr = tarzan_accept_burst(current_thread_tarzan_serial_number, socks, 32);
    }
    fd = *(static_cast<int32_t*>(socks[0].data));
    EXPECT_EQ(fd, acceptor->GetFD());
    acceptor->HandleNewConnection(&socks[0]);
    EXPECT_EQ(1, acceptor->_socket_map._map.size());
    tarzan_close(connect_cd);
    connect_cd = -1;
    acceptor->StopAccept();
    acceptor->OwnerUnref();
    int index = 0;
    while (index < 20) {
        tarzan_worker_run(current_thread_tarzan_serial_number);
        ++index;
    }
}

}  // namespace byterpc
