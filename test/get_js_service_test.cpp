// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "builtin/get_js_service.h"

#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include <atomic>
#include <cstdio>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

#include "builtin/common.h"
#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/controller.h"
#include "byterpc/protocol/http/details/http_protocol.h"
#include "byterpc/protocol/http/details/uri.h"
#include "byterpc/rpc.h"
#include "byterpc/server.h"
#include "byterpc/util/endpoint.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/timestamp.h"
#include "protocol/http/http_client_message_handler.h"

namespace byterpc {
namespace {

static bool g_client_finish = false;

// Sends GET request to url using curl command and returns reponse string
std::string GetResponseFromUrl(const std::string& url) {
    const std::string cmd = "curl " + url;
    FILE* pipe = popen(cmd.c_str(), "r");
    if (!pipe) {
        return "";
    }
    constexpr int kMaxLineLength = 256;
    char buffer[kMaxLineLength];
    std::string result;
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        result += buffer;
    }
    pclose(pipe);
    return result;
}

static void WaitFlagReady(std::atomic_bool* ready_flag) {
    while (!ready_flag->load(std::memory_order_acquire))
        usleep(1);
    ready_flag->store(false, std::memory_order_release);
}

static void HTTPCallBack(HttpRpcCall* controller) {
    g_client_finish = true;  // stop server loop
    EXPECT_FALSE(controller->Failed());
    EXPECT_TRUE(controller->HasIncomingAttachment());
    std::string res;
    std::unique_ptr<IOBuf> attachment(controller->ReleaseIncomingAttachment());
    attachment->cut_to(&res, attachment->length());
    EXPECT_GT(res.size(), 0);
    ExecCtx::QuitLoop();  // stop client loop
}

static void SendHTTPGetRequest(const std::string& url) {
    Builder builder;
    Builder::ChannelOptions options;
    std::shared_ptr<Builder::Channel> channel = builder.BuildChannelByURL(url, options);
    HttpRpcCall* controller =
        static_cast<HttpRpcCall*>(builder.CreateSessionController(PROTOCOL_HTTP));
    controller->http_request().uri() = url;
    controller->http_request().set_method(byterpc::HTTP_METHOD_GET);
    // set USER_AGENT_STR so that server will respond with html format
    controller->http_request().SetHeader(USER_AGENT_STR, "aaa");
    google::protobuf::Closure* done =
        ::byterpc::NewCallback<HttpRpcCall*>(HTTPCallBack, controller);
    channel->CallMethod(nullptr, controller, nullptr, nullptr, done);
}

// Tests rpc requests using http protocol to http://server_address/suffix will be responded
void TestHTTPRequestWithUrlSuffix(const std::string& suffix) {
    std::atomic_bool ready_flag(false);
    g_client_finish = false;
    Server* svr = nullptr;
    std::thread server([&]() {
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        svr = new Server();
        ServerOptions options;
        options._enable_ktcp = true;
        options._enable_builtin_service = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        while (!g_client_finish) {
            ExecCtx::LoopOnce();
        }
    });
    WaitFlagReady(&ready_flag);

    std::thread client([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        const std::string server_addr = util::endpoint2str(svr->listen_address()).c_str();
        const std::string url = server_addr + suffix;
        SendHTTPGetRequest(url);
        ExecCtx::LoopUntilQuit();
    });

    server.join();
    client.join();
    delete svr;
}

class GetJSServiceTest : public testing::Test {
public:
    GetJSServiceTest() {}

    ~GetJSServiceTest() {}

    void SetUp() override {}

    void TearDown() {}
};

TEST_F(GetJSServiceTest, Curl) {
    std::atomic_bool ready_flag(false);
    g_client_finish = false;
    Server* svr = nullptr;
    std::thread server([&]() {
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        svr = new Server();
        ServerOptions options;
        options._enable_ktcp = true;
        options._enable_builtin_service = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        while (!g_client_finish) {
            ExecCtx::LoopOnce();
        }
    });
    WaitFlagReady(&ready_flag);

    const std::string server_addr = util::endpoint2str(svr->listen_address()).c_str();
    const std::string url = server_addr + "/js/jquery_min";
    std::string response = GetResponseFromUrl(url);
    EXPECT_GT(response.size(), 0);

    g_client_finish = true;
    server.join();
    delete svr;
}

TEST_F(GetJSServiceTest, JQueryMin) {
    TestHTTPRequestWithUrlSuffix("/js/jquery_min");
}

TEST_F(GetJSServiceTest, FlotMin) {
    TestHTTPRequestWithUrlSuffix("/js/flot_min");
}

}  // namespace
}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#else
    byterpc::FLAGS_byterpc_byte_express_memory_pool_init_regions = 1;
#endif
    byterpc::InitOptions opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
