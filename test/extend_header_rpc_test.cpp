// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <gtest/gtest.h>

#include <byte/util/defer.h>
#include <random>

#include <memory>
#include <string>
#include <thread>
#include <vector>

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/rpc.h"
#include "byterpc/server.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/logging.h"
#include "status.pb.h"

namespace byterpc {
int g_server_rpc = 0;

static int rand_num(int low, int high) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> distr(low, high);

    int random_number = distr(gen);
    return random_number;
}

class MyStatusService : public status {
public:
    MyStatusService(int rpc_num, bool set_fail = false) : _rpc_num(rpc_num), _set_fail(set_fail) {}

    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) override {
        Controller* cntl = static_cast<Controller*>(controller);

        if (!_set_fail) {
            if (cntl->HasIncomingAttachment()) {
                IOBuf* buf = cntl->ReleaseIncomingAttachment();
                std::unique_ptr<IOBuf> att = std::make_unique<IOBuf>();
                att->append(*buf);
                cntl->InstallOutgoingAttachment(std::move(att));
                delete buf;
            }

            // set request pb randomly
            if (0 == rand_num(0, 100) % 2) {
                response->set_latency_us(rand_num(100, 30000));
            }
        } else {
            cntl->SetFailed(byterpc::ENOMETHOD, "mocking no method");
        }

        { util::ClosureGuard done_guard(done); }

        ++g_server_rpc;
        if (g_server_rpc == _rpc_num) {
            ExecCtx::QuitLoop();
        }
    }

private:
    int _rpc_num;
    bool _set_fail;
};

static void StatusCallback(StatusRequest* req,
                           StatusResponse* resp,
                           Controller* cntl,
                           int* finished_rpc_num,
                           int expect_err,
                           int expect_rpc_num) {
    std::unique_ptr<StatusRequest> req_guard(req);
    std::unique_ptr<StatusResponse> resp_guard(resp);
    EXPECT_EQ(cntl->ErrorCode(), expect_err);
    if (cntl->Failed()) {
        BYTERPC_LOG(WARNING) << "Cntl fail:" << cntl->ErrorText();
    }
    if (++(*finished_rpc_num) == expect_rpc_num) {
        ExecCtx::QuitLoop();
    }
}

static std::atomic_bool g_check_valid_crc_mode(false);
static void SendStatusRequest(const std::string& server_addr,
                              ProtocolType protocol,
                              int expect_err,
                              int expect_rpc_num,
                              CrcMode crc_mode,
                              int* finished_rpc_num) {
    Builder builder;
    Builder::ChannelOptions options;
    options._connect_timeout_ms = 20000;
    options._rpc_timeout_ms = 10000;
    util::EndPoint server_ep;
    util::str2endpoint(server_addr.c_str(), &server_ep);
    std::shared_ptr<Builder::Channel> channel = builder.BuildChannel(server_ep, options);
    Builder::ControllerOptions opt;
    opt.timeout_us = 10000000;
    opt.protocol = protocol;
    opt.crc_mode = crc_mode;

    Controller* cntl = builder.CreateSessionController(opt);
    EXPECT_NE(nullptr, channel.get());
    EXPECT_NE(nullptr, cntl);

    // Test invalid crc_mode
    if (!g_check_valid_crc_mode.load()) {
        Builder::ControllerOptions fake_opt = opt;
        fake_opt.crc_mode = static_cast<CrcMode>(200);
        EXPECT_EQ(nullptr, builder.CreateSessionController(fake_opt));
        g_check_valid_crc_mode.store(true);
    }

    auto* req = new StatusRequest;
    auto* resp = new StatusResponse;
    // set attachment randomly
    if (0 == rand_num(0, 100) % 2) {
        int data_len = rand_num(0, 128);
        if (data_len != 0) {
            std::vector<char> ch(data_len, 'a');
            std::unique_ptr<IOBuf> buf = std::make_unique<IOBuf>();
            buf->append(ch.data(), data_len);
            cntl->InstallOutgoingAttachment(std::move(buf));
        }
    }
    // set request pb randomly
    if (0 == rand_num(0, 100) % 2) {
        req->set_str(std::string(rand_num(0, 10), 'x'));
    }

    auto done = byterpc::NewCallback<StatusRequest*, StatusResponse*, Controller*, int*, int, int>(
        StatusCallback, req, resp, cntl, finished_rpc_num, expect_err, expect_rpc_num);
    status_Stub stub(channel.get());
    stub.default_method(cntl, req, resp, done);
}

static void WaitFlagReady(std::atomic_bool* ready_flag) {
    while (!ready_flag->load(std::memory_order_acquire))
        usleep(1);
    ready_flag->store(false, std::memory_order_release);
}

static void TestAllSameChecksumMessage(ProtocolType protocol, CrcMode crc_mode) {
    BYTERPC_LOG(WARNING) << "TestAllSameChecksumMessage, protocol = " << protocol
                         << ", crc_mode = " << crc_mode;
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_server_rpc = 0;
    int num = 100;
    int thread_num = 5;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyStatusService(num * thread_num));
        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::vector<std::unique_ptr<std::thread>> ths;
    for (int i = 0; i < thread_num; i++) {
        ths.emplace_back(new std::thread([&]() {
            ExecCtx ctx(LOOP_UNTIL_QUIT);
            int finished_rpc_num = 0;
            std::string ip = util::endpoint2str(svr->listen_address()).c_str();
            for (int i = 0; i < num; ++i) {
                SendStatusRequest(ip, protocol, 0, num, crc_mode, &finished_rpc_num);
            }
            ExecCtx::LoopUntilQuit();
        }));
    }

    for (auto&& th : ths) {
        th->join();
    }

    server.join();

    delete svr;
}

static void TestVariousChecksumMessage(ProtocolType protocol) {
    BYTERPC_LOG(WARNING) << "TestVariousChecksumMessage, protocol = " << protocol;
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_server_rpc = 0;
    int num = 100;
    int thread_num = 5;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyStatusService(num * thread_num));
        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::vector<std::unique_ptr<std::thread>> ths;
    for (int i = 0; i < thread_num; i++) {
        ths.emplace_back(new std::thread([&]() {
            ExecCtx ctx(LOOP_UNTIL_QUIT);
            int finished_rpc_num = 0;
            std::string ip = util::endpoint2str(svr->listen_address()).c_str();
            for (int i = 0; i < num; ++i) {
                ProtocolType type = protocol;
                CrcMode crc_mode = CrcMode::TYPE_NONE;
                int remainder = rand_num(0, 100) % 3;
                switch (remainder) {
                case 0:
                    crc_mode = CrcMode::TYPE_NONE;
                    break;
                case 1:
                    crc_mode = CrcMode::TYPE_FULL;
                    break;
                case 2:
                    crc_mode = CrcMode::TYPE_META_PB;
                }
                SendStatusRequest(ip, type, 0, num, crc_mode, &finished_rpc_num);
            }
            ExecCtx::LoopUntilQuit();
        }));
    }

    for (auto&& th : ths) {
        th->join();
    }

    server.join();

    delete svr;
}

static void TestVariousChecksumMessageWithRandomProtocol() {
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_server_rpc = 0;
    int num = 100;
    int thread_num = 5;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyStatusService(num * thread_num));
        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::vector<std::unique_ptr<std::thread>> ths;
    for (int i = 0; i < thread_num; i++) {
        ths.emplace_back(new std::thread([&]() {
            ExecCtx ctx(LOOP_UNTIL_QUIT);
            int finished_rpc_num = 0;
            std::string ip = util::endpoint2str(svr->listen_address()).c_str();
            for (int i = 0; i < num; ++i) {
                ProtocolType type = ProtocolType::PROTOCOL_BYTE_STD;
                CrcMode crc_mode = CrcMode::TYPE_NONE;
                int remainder = rand_num(0, 100) % 3;
                switch (remainder) {
                case 0:
                    crc_mode = CrcMode::TYPE_NONE;
                    break;
                case 1:
                    crc_mode = CrcMode::TYPE_FULL;
                    break;
                case 2:
                    crc_mode = CrcMode::TYPE_META_PB;
                }

                remainder = rand_num(0, 888) % 3;
                switch (remainder) {
                case 0:
                    type = PROTOCOL_BYTE_STD;
                    break;
                case 1:
                    type = PROTOCOL_BAIDU_STD;
                    break;
                case 2:
                    type = PROTOCOL_ALIGNED_BYTE_STD;
                }
                SendStatusRequest(ip, type, 0, num, crc_mode, &finished_rpc_num);
            }
            ExecCtx::LoopUntilQuit();
        }));
    }

    for (auto&& th : ths) {
        th->join();
    }

    server.join();

    delete svr;
}

static void TestVariousChecksumWithServerFail(ProtocolType protocol) {
    BYTERPC_LOG(WARNING) << "TestVariousChecksumWithServerFail, protocol = " << protocol;
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_server_rpc = 0;
    int num = 100;
    int thread_num = 5;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyStatusService(num * thread_num, true));
        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::vector<std::unique_ptr<std::thread>> ths;
    for (int i = 0; i < thread_num; i++) {
        ths.emplace_back(new std::thread([&]() {
            ExecCtx ctx(LOOP_UNTIL_QUIT);
            int finished_rpc_num = 0;
            std::string ip = util::endpoint2str(svr->listen_address()).c_str();
            for (int i = 0; i < num; ++i) {
                ProtocolType type = protocol;
                CrcMode crc_mode = CrcMode::TYPE_NONE;
                int remainder = rand_num(0, 100) % 3;
                switch (remainder) {
                case 0:
                    crc_mode = CrcMode::TYPE_NONE;
                    break;
                case 1:
                    crc_mode = CrcMode::TYPE_FULL;
                    break;
                case 2:
                    crc_mode = CrcMode::TYPE_META_PB;
                }
                SendStatusRequest(ip, type, ENOMETHOD, num, crc_mode, &finished_rpc_num);
            }
            ExecCtx::LoopUntilQuit();
        }));
    }

    for (auto&& th : ths) {
        th->join();
    }

    server.join();

    delete svr;
}

TEST(ExtendHeaderRpcTest, AllSameChecksumMessage) {
#ifdef BYTERPC_ENABLE_BYTE_STD
    TestAllSameChecksumMessage(PROTOCOL_BYTE_STD, CrcMode::TYPE_NONE);
    TestAllSameChecksumMessage(PROTOCOL_BYTE_STD, CrcMode::TYPE_FULL);
    TestAllSameChecksumMessage(PROTOCOL_BYTE_STD, CrcMode::TYPE_META_PB);
#endif

#ifdef BYTERPC_ENABLE_ALIGNED_BYTE_STD
    TestAllSameChecksumMessage(PROTOCOL_ALIGNED_BYTE_STD, CrcMode::TYPE_NONE);
    TestAllSameChecksumMessage(PROTOCOL_ALIGNED_BYTE_STD, CrcMode::TYPE_FULL);
    TestAllSameChecksumMessage(PROTOCOL_ALIGNED_BYTE_STD, CrcMode::TYPE_META_PB);
#endif

#ifdef BYTERPC_ENABLE_BAIDU_STD
    TestAllSameChecksumMessage(PROTOCOL_BAIDU_STD, CrcMode::TYPE_NONE);
    TestAllSameChecksumMessage(PROTOCOL_BAIDU_STD, CrcMode::TYPE_FULL);
    TestAllSameChecksumMessage(PROTOCOL_BAIDU_STD, CrcMode::TYPE_META_PB);
#endif
}

TEST(ExtendHeaderRpcTest, VariousChecksumMessage) {
#ifdef BYTERPC_ENABLE_BYTE_STD
    TestVariousChecksumMessage(PROTOCOL_BYTE_STD);
#endif

#ifdef BYTERPC_ENABLE_ALIGNED_BYTE_STD
    TestVariousChecksumMessage(PROTOCOL_ALIGNED_BYTE_STD);
#endif

#ifdef BYTERPC_ENABLE_BAIDU_STD
    TestVariousChecksumMessage(PROTOCOL_BAIDU_STD);
#endif
}

TEST(ExtendHeaderRpcTest, VariousChecksumMessageWithRandomProtocol) {
#ifdef BYTERPC_ENABLE_BYTE_STD
#ifdef BYTERPC_ENABLE_ALIGNED_BYTE_STD
#ifdef BYTERPC_ENABLE_BAIDU_STD
    TestVariousChecksumMessageWithRandomProtocol();
#endif
#endif
#endif
}

TEST(ExtendHeaderRpcTest, VariousChecksumSetFail) {
#ifdef BYTERPC_ENABLE_BYTE_STD
    TestVariousChecksumWithServerFail(PROTOCOL_BYTE_STD);
#endif

#ifdef BYTERPC_ENABLE_ALIGNED_BYTE_STD
    TestVariousChecksumWithServerFail(PROTOCOL_ALIGNED_BYTE_STD);
#endif

#ifdef BYTERPC_ENABLE_BAIDU_STD
    TestVariousChecksumWithServerFail(PROTOCOL_BAIDU_STD);
#endif
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
