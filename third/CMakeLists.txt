if(BYTERPC_ENABLE_ASAN)
    message(STATUS "build byte & thirdparty with address sanitizer")
    set(BYTE_WITH_ASAN ON CACHE BOOL "Whether to enable byte fsanitize=address" FORCE)
    set(THIRDPARTY_WITH_ASAN ON CACHE BOOL "Whether to enable thirdparty fsanitize=address" FORCE)
endif()

if(BYTERPC_BUILD_SHARED)
    # make sure all dependency libs compile with -fPIC
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")
endif()

if(NOT TARGET byte)
    # disable byte unittest
    set(BYTE_BUILD_TESTS OFF CACHE BOOL "Build unit tests of byte")
    # disable dep-less lib in thirdparty
    set(BUILD_EV OFF CACHE BOOL "build libev or not")
    set(BUILD_ISAL OFF CACHE BOOL "build isa-l or not")
    set(BUILD_ZOOKEEPER OFF CACHE BOOL "build zookeeper or not")
    set(BUILD_ZSTD OFF CACHE BOOL "build zstd or not")
    set(BUILD_LZ4 OFF CACHE BOOL "build lz4 or not")
    set(BUILD_SNAPPY OFF CACHE BOOL "build snappy or not")
    set(BUILD_GF_COMPLETE OFF CACHE BOOL "build gf-complete or not")
    set(BUILD_JERASURE OFF CACHE BOOL "build jerasure or not")
    set(BUILD_CRYPTOPP OFF CACHE BOOL "build cryptopp or not")
    if (NOT BYTERPC_WITH_LIBUNWIND)
        set(ENABLE_PROFILER OFF CACHE BOOL "build gperftools with profiler")
        set(WITH_UNWIND OFF CACHE BOOL "build glog with unwind")
    endif()
    if(BYTERPC_ENABLE_FIBER)
        set(BUILD_FIBER ON CACHE BOOL "build fiber module or not")
    endif()
    add_subdirectory(byte)
endif()

UNSET(CMAKE_ASM_COMPILER)
UNSET(CMAKE_ASM_FLAGS)
UNSET(CMAKE_ASM_NASM_FLAGS)
UNSET(CMAKE_ASM_SOURCE_FILE_EXTENSIONS)
UNSET(CMAKE_ASM_NASM_COMPILER)

if (BYTERPC_ENABLE_MEMORY_DEBUG)
    set(BEMALLOC_ENABLE_COOKIE ON CACHE BOOL "enable bemalloc cookie check or not" FORCE)
endif()

if(BYTERPC_ENABLE_BYTE_EXPRESS)
    set(BYTE_EXPRESS_USE_GLOG OFF CACHE BOOL "Set to OFF to use a bundled logging library instead of glog" FORCE)
    # set BYTE_EXPRESS_USE_INTERNAL_BOOST_LIBRARY to OFF and let ByteRPC decide the BOOST_ROOT
    set(BYTE_EXPRESS_USE_INTERNAL_BOOST_LIBRARY OFF CACHE BOOL "build byte_express with system boost" FORCE)
    add_subdirectory(byte_express)
else()
    # if not enable BYTE_EXPRESS, try to add bemalloc, it will use in unify_memory_pool
    add_subdirectory(byte_express/external/bemalloc)
endif()

if(BYTERPC_ENABLE_THRIFT)
    if (NOT TARGET thrift)
        set(WITH_AS3 OFF CACHE INTERNAL "")
        set(WITH_C_GLIB OFF CACHE INTERNAL "")
        set(WITH_JAVA OFF CACHE INTERNAL "")
        set(WITH_JAVASCRIPT OFF CACHE INTERNAL "")
        set(WITH_NODEJS OFF CACHE INTERNAL "")
        set(WITH_PYTHON OFF CACHE INTERNAL "")
        set(BUILD_TESTING OFF CACHE INTERNAL "")
        # Adjust some options to avoid compilation errors
        add_compile_options(-Wno-error=sign-compare -Wno-deprecated-declarations)
        # Remove asan check in thrift, since its compiler would throw an asan error.
        # Temporarily there's no good solution and it doesn't affect normal cases.
        string(REPLACE "-fsanitize=address" "" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
        add_subdirectory(thrift)
        # target exported above doesn't include directory and expose boost 
        target_include_directories(thrift INTERFACE
        "$<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/third/thrift/lib/cpp/src>"
        "$<BUILD_INTERFACE:${CMAKE_BINARY_DIR}/third/thrift>")
        target_link_libraries(thrift INTERFACE 
            Boost::boost
        )
    endif()
endif()
