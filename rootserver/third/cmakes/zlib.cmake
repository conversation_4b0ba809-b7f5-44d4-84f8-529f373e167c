set(lib_name zlib)
ExternalProject_Add(
  ${lib_name}
  URL http://tosv.byted.org/obj/cloudfs/dancedn-thirdparty/zlib-1.2.11.tar.gz
  URL_HASH MD5=1c9f62f0778697a09d36121ead88e08e
  DOWNLOAD_NAME zlib-1.2.11.tar.gz
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE TRUE
  CONFIGURE_COMMAND
    ${common_configure_envs}
    ./configure --prefix=${CMAKE_INSTALL_PREFIX}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)
