#!/bin/bash

# 开启错误退出模式，当命令执行失败时脚本立即退出
set -e
# 开启调试模式，执行命令时会打印命令本身
set -x

# 获取 CPU 核心数，若无法获取则可能为空
NPROC=`grep -c ^processor /proc/cpuinfo 2>/dev/null`
# 获取当前脚本所在的源目录
SOURCE_DIR=`pwd`
# 定义构建目录
BUILD_DIR=$SOURCE_DIR/build

# 创建构建目录，若目录已存在则忽略
mkdir -p $BUILD_DIR
# 创建输出目录，若目录已存在则忽略
mkdir -p $SOURCE_DIR/output/

# 打印当前的构建类型
echo "SCM BUILD_TYPE=$BUILD_TYPE"

# scm 也包含 BUILD_TYPE 变量，可复用
# 检查 BUILD_TYPE 变量是否为空，若为空则赋值为空字符串
if [ -z "$BUILD_TYPE" ];then
    BUILD_TYPE=""
fi

# 初始化构建模式，默认值为 master
BUILD_MODE="master"
# 初始化是否启用 gcov，默认关闭
ENABLE_GCOV=OFF
# 初始化是否启用 fiu，默认关闭
ENABLE_FIU=OFF
# 初始化是否启用 asan，默认关闭
ENABLE_ASAN=OFF
# 初始化是否启用 fio，默认关闭
ENABLE_FIO=OFF
# 初始化是否仅编译 fio 工具，默认关闭
ENABLE_FIO_ONLY=OFF
# 初始化是否仅构建 Python 部分，默认关闭
BUILD_PYTHON_ONLY=OFF
# 初始化是否启用 lz4 的 ipp 优化，默认开启
ENABLE_LZ4_IPP=ON
# 初始化是否启用测试，默认关闭
ENABLE_TEST=OFF
# 初始化是否启用协程模式，默认关闭
ENABLE_FIBER=OFF
# 初始化是否启用 metrics2，默认开启
ENABLE_METRICS2=ON
# 初始化是否启用 driveclient，默认关闭
ENABLE_DRIVECLIENT=OFF
# 初始化是否启用模拟模式，默认关闭
ENABLE_MOCK=OFF
CXXSTD="17"

uname -r
cmake --version

# 检查系统架构信息的函数
checkArchInfo() {
    # 获取系统架构信息
    ARCH=$(uname -m)
    case $ARCH in
    # 64 位 x86 架构
    x86_64|amd64)
        ENABLE_LZ4_IPP=ON
        echo "64-bit architecture"
        ;;
    # 32 位 x86 架构
    i686|i386)
        ENABLE_LZ4_IPP=ON
        echo "32-bit architecture"
        ;;
    # ARM v6 架构
    armv6l)
        ENABLE_LZ4_IPP=OFF
        echo "ARM v6 architecture"
        ;;
    # ARM v7 架构
    armv7l)
        ENABLE_LZ4_IPP=OFF
        echo "ARM v7 architecture"
        ;;
    # 64 位 ARM 架构
    aarch64|arm64)
        ENABLE_LZ4_IPP=OFF
        echo "ARM 64-bit architecture"
        ;;
    # 未知架构
    *)
        echo "Unknown architecture: $ARCH"
        ;;
    esac
}
# 调用检查系统架构信息的函数
checkArchInfo

# 记录开始时间
start_time=$(date +%s)

# 获取当前脚本所在目录
CUR_DIR=$(dirname "$0")
# 获取根服务器目录
RS_DIR=$(cd "$CUR_DIR"; pwd)
# 定义第三方库安装目录
THIRDPARTY_INSTALL_DIR=${RS_DIR}/third/install
# 获取第三方库版本号
THIRDPARTY_VERSION=$(grep RootServer ${RS_DIR}/third/current_thirdparty_version | awk -F'=' '{print $NF}')

# 检查第三方库当前版本文件是否存在
# if [ -f "${THIRDPARTY_INSTALL_DIR}/current_revision" ]; then
#   # 若存在，则检查版本是否一致，不一致则更新
#   CURRENT_THIRDPARTY_VERSION=$(cat ${THIRDPARTY_INSTALL_DIR}/current_revision | grep version | awk -F':' '{print $2}')
#   if [ "$CURRENT_THIRDPARTY_VERSION" == "$THIRDPARTY_VERSION" ]; then
#     echo "The thirdparty version is the same as the current version, skip update."
#   else
#     pushd ${THIRDPARTY_INSTALL_DIR}
#     echo "update thirdparty version to ${THIRDPARTY_VERSION}"
#     bvc reset ${THIRDPARTY_VERSION}
#     popd
#   fi
# else 
#   # 若不存在，则下载第三方库
#   rm -rf ${THIRDPARTY_INSTALL_DIR}
#   pushd ${RS_DIR}/third
#   echo "download thirdparty version to ${THIRDPARTY_VERSION}"
#   bvc clone inf/bds/rootserver_thirdparty install --version ${THIRDPARTY_VERSION}
#   popd
# fi

# 创建构建目录，进入目录，清理测试目录和 CMake 缓存，运行 cmake 配置，编译项目
mkdir -p $BUILD_DIR \
    && cd $BUILD_DIR \
    && rm -rf test \
    && rm -rf CMakeCache.txt \
    && cmake .. \
        -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
        -DEIC_ENABLE_MOCK=$ENABLE_MOCK \
        -DEIC_BUILD_MODE=$BUILD_MODE \
        -DEIC_ENABLE_TEST=$ENABLE_TEST -DEIC_ENABLE_FIBER=$ENABLE_FIBER \
        -DEIC_ENABLE_GCOV=$ENABLE_GCOV -DEIC_ENABLE_FIU=${ENABLE_FIU} \
        -DEIC_ENABLE_ASAN=$ENABLE_ASAN -DBUILD_TESTING=ON -DENABLE_PROFILER=ON \
        -DEIC_ENABLE_RDMA=ON -DBYTERPC_ENABLE_IOBUF_MTHREADS=ON \
        -DEIC_ENABLE_IPP=$ENABLE_LZ4_IPP \
        -DBYTE_ENABLE_METRICS2=$ENABLE_METRICS2 \
        -DEIC_ENABLE_DRIVECLIENT=$ENABLE_DRIVECLIENT \
        -DCMAKE_CXX_STANDARD=${CXXSTD} \
        -DCMAKE_INSTALL_PREFIX=${THIRDPARTY_INSTALL_DIR}  \
        -DCMAKE_PREFIX_PATH=${THIRDPARTY_INSTALL_DIR}  \
        -DCMAKE_INCLUDE_PATH=${THIRDPARTY_INSTALL_DIR}/include \
        -DCMAKE_LIBRARY_PATH=${THIRDPARTY_INSTALL_DIR}/lib \
    && make -j${NPROC} 

cd $BUILD_DIR/src && make -j${NPROC} install

ret=$?
if [ $ret -ne 0 ]; then
    echo "rs compile $BUILD_MODE error"
    exit $ret
else
    echo "rs compile $BUILD_MODE success"
fi

end_time=$(date +%s)
echo "compile rs cost(s) $(($end_time - $start_time))"

ret=$?
if [ $ret -ne 0 ]; then
    echo "rs compile error"
    exit $ret
else
    echo "rs compile success"
fi

end_time=$(date +%s)
echo "build rs cost(s) $(($end_time - $start_time))"

# 以状态码 0 退出脚本，表示正常退出
exit 0