
#pragma once

#include "utils.h"
// #include "hash_router/multi_space_router.h"

namespace bds {
namespace rootserver {
namespace cfs {

// reserved 0-1000 IDs
static const uint32_t CFS_DISK_ID_START = 1000U;
static const uint64_t CFS_DN_ID_START = 1000U;
static const uint32_t CFS_SPACE_ID_START = 1000U;
static const uint32_t CFS_GROUP_ID_START = 1000U;

const std::string DOT_TOKEN = {'.'};
const std::string CFS_KEY_PREFIX = "cfs" + DOT_TOKEN;

const std::string CFS_KEY_CLUSTER_UUID = CFS_KEY_PREFIX + "cluster_uuid";

const std::string CFS_KEY_MAX_SPACE_ID = CFS_KEY_PREFIX + "max_space_id";
const std::string CFS_KEY_MAX_GROUP_ID = CFS_KEY_PREFIX + "max_group_id";
const std::string CFS_KEY_MAX_NS_ID = CFS_KEY_PREFIX + "max_namespace_id";
const std::string CFS_KEY_MAX_DN_ID = CFS_KEY_PREFIX + "max_dn_id";
// Auto-incrementing DISK_ID allocated within one datanode
// const std::string CFS_KEY_MAX_DISK_ID = CFS_KEY_PREFIX + "max_disk_id"; 
const std::string CFS_KEY_SPACE_PREFIX = CFS_KEY_PREFIX + "space" + DOT_TOKEN;
const std::string CFS_KEY_NS_PREFIX = CFS_KEY_PREFIX + "namespace" + DOT_TOKEN;
const std::string CFS_KEY_GROUP_PREFIX = CFS_KEY_PREFIX + "group" + DOT_TOKEN;
const std::string CFS_KEY_DN_PREFIX = CFS_KEY_PREFIX + "datanode" + DOT_TOKEN;
// const std::string CFS_KEY_DISK_PREFIX = CFS_KEY_PREFIX + "disk" + DOT_TOKEN;

inline std::string SpaceIdKey(uint32_t space_id) {
    std::string fix_space_id;
    bds::common::StringUtil::encode_fixed_uint32_nonsorting(space_id, &fix_space_id);
    return CFS_KEY_SPACE_PREFIX + fix_space_id;
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds