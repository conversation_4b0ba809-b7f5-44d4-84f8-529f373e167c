
//=============================================================================
// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.
// Author: <EMAIL>
// Date: 2025-05-24 14:18
// Description:
// This file implements the RSMetaWriter class for modifying metadata stored in RocksDB.
//=============================================================================

#pragma once

#include "eic/master/metadata/store/writer.h"
#include "rootserver.pb.h"
#include "common/type_def.h"

namespace bds {
namespace rootserver {
namespace cfs {
class RootServer;
/** 
 * This class provides an interface to write and update metadata information
 * such as spaces and groups in the underlying KVStore. It handles
 * batch operations and provides atomic write capabilities through the Flush method.
 */
class RSMetaWriter {
 public:
  explicit RSMetaWriter(RootServer* root_server);
  void AddSpace(const bds::proto::SpaceProto& space);
  void UpdateMaxSpaceID(uint32_t next_space_id);
  void Flush(const eic::master::MetaAsyncWriteCallback& callback);

 private:
  void AddPut(const std::string& key, const std::string& value);

 private:
  RootServer* root_server_;
  eic::master::MetaWriter* writer_;
};

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds