//=============================================================================
// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.
// Author: <EMAIL>
// Date: 2025-05-24 14:18
// Description:
// This file implements the RSMetaReader class for accessing metadata stored in RocksDB.
//=============================================================================

#pragma once

#include <string>
#include <vector>
#include "eic/master/metadata/store/kvstore.h"
#include "common/type_def.h"
#include "rootserver.pb.h"

namespace bds {
namespace rootserver {
namespace cfs {

class RootServer;
/**
 * This class provides an interface to read metadata information such as spaces
 * and clusters from the underlying KVStore. It encapsulates the details of key
 * construction and error handling when interacting with the storage layer.
 */
class RSMetaReader {
 public:
  explicit RSMetaReader(RootServer* root_server);
  
  bds::proto::Status ListSpaces(std::vector<bds::proto::SpaceProto>& space_list);
  bds::proto::Status GetSpace(uint32_t space_id, bds::proto::SpaceProto* space);
  bds::proto::Status GetCluster(const std::string& cluster_uuid, bds::proto::ClusterProto* cluster_proto);

 private:
  RootServer* root_server_;
  eic::master::KVStore* kv_store_;
};

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds