
#include "metadata/store/rs_meta_reader.h"
#include "root_server.h"

namespace bds {
namespace rootserver {
namespace cfs {

  RSMetaReader::RSMetaReader(RootServer* root_server) {
    root_server_ = root_server;
    kv_store_ = root_server_->replica_driver()->GetFSM()->GetKVStore();
  }

  bds::proto::Status RSMetaReader::ListSpaces(std::vector<bds::proto::SpaceProto>& space_list) {
      // TODO
      return bds::proto::Status::SUCCESS;
  }

  bds::proto::Status RSMetaReader::GetSpace(uint32_t space_id, bds::proto::SpaceProto* space) {
    std::string space_str;
    auto get_rc = kv_store_->Get(SpaceIdKey(space_id),
                                 &space_str, rocksdb::kDefaultColumnFamilyName);
    if (UNLIKELY(get_rc == eic::proto::EIC_MASTER_KV_STORE_KEY_NOT_FOUND)) {
      LOG(INFO) << "space not found in rocksdb, space_id: " << space_id;
      return bds::proto::Status::ERROR_RS_KV_STORE_KEY_NOT_FOUND;
    } else if (UNLIKELY(get_rc != eic::proto::EIC_SUCCESS)) {
      LOG(ERROR) << "load space data from rocksdb failed, space_id: "
                 << std::to_string(space_id)
                 << ", ret_msg: " << eic::proto::EicErrorCode_Name(get_rc);
      return bds::proto::Status::ERROR_RS_KV_STORE_ERROR;
    }
    
    if (!space->ParseFromString(space_str)) {
      LOG(ERROR) << "deserialize using space proto from rocksdb data failed";
      return bds::proto::Status::ERROR_RS_KV_STORE_ERROR;
    }
    return bds::proto::Status::SUCCESS;
  }

  bds::proto::Status RSMetaReader::GetCluster(
    const std::string& cluster_uuid,
    ClusterProto* cluster_proto) {
    std::string cluster_str;
    auto get_rc = kv_store_->Get(CFS_KEY_CLUSTER_UUID,
                                &cluster_str, rocksdb::kDefaultColumnFamilyName);
    if (UNLIKELY(get_rc == eic::proto::EIC_MASTER_KV_STORE_KEY_NOT_FOUND)) {
      LOG(INFO) << "cluster not found in rocksdb, cluster_uuid: " << cluster_uuid;
      return bds::proto::Status::ERROR_RS_KV_STORE_KEY_NOT_FOUND;
    } else if (UNLIKELY(get_rc != eic::proto::EIC_SUCCESS)) {
      LOG(ERROR) << "load cluster data from rocksdb failed, cluster_uuid: "
                << cluster_uuid
                << ", ret_msg: " << eic::proto::EicErrorCode_Name(get_rc);
      return bds::proto::Status::ERROR_RS_KV_STORE_ERROR;
    }
    if (!cluster_proto->ParseFromString(cluster_str)) {
      LOG(ERROR) << "deserialize using cluster proto from rocksdb data failed";
      return bds::proto::Status::ERROR_RS_KV_STORE_ERROR;
    }
    return bds::proto::Status::SUCCESS;
  }

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds