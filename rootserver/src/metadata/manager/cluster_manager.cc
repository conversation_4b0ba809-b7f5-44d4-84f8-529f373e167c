#include "metadata/manager/cluster_manager.h"
#include "metadata/meta/node.h"
#include "root_server.h"
#include "metadata/store/rs_meta_reader.h"
#include "metadata/store/rs_meta_writer.h"

#include <iostream>

namespace bds {
namespace rootserver {
namespace cfs {

// Rack implementations
void Rack::getNodeList(NodeVector* node_list) {
  if (!node_list) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& pair : node_map_) {
    node_list->push_back(pair.second);
  }
}

int Rack::addNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  return node_map_.insert(std::make_pair(EndpointToString(node_ptr->getAddr()), node_ptr)).second ? 0 : -1;
}

int Rack::dropNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  return node_map_.erase(EndpointToString(node_ptr->getAddr())) ? 0 : -1;
}

// Cell implementations
void Cell::getNodeList(NodeVector* node_list) {
  if (!node_list) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& rack_pair : rack_map_) {
    rack_pair.second->getNodeList(node_list);
  }
}

void Cell::getRackList(RackVector* rack_list) {
  if (!rack_list) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& rack_pair : rack_map_) {
    rack_list->push_back(rack_pair.second);
  }
}

int Cell::addNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }
  
  // First get the rack name
  std::string rack_name = node_ptr->getLocationRack();
  
  // Find the rack or create it if it doesn't exist
  std::shared_ptr<Rack> rack_ptr;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto rack_iter = rack_map_.find(rack_name);
    
    if (rack_iter == rack_map_.end()) {
      // Rack doesn't exist, create it
      rack_ptr = std::make_shared<Rack>(rack_name);
      rack_map_.insert(std::make_pair(rack_name, rack_ptr));
    } else {
      rack_ptr = rack_iter->second;
    }
  }
  
  // Add the node to the rack
  return rack_ptr->addNode(node_ptr);
}

int Cell::dropNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }
  
  std::string rack_name = node_ptr->getLocationRack();
  std::shared_ptr<Rack> rack_ptr;
  
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto rack_iter = rack_map_.find(rack_name);
    
    if (rack_iter == rack_map_.end()) {
      return -1; // Rack doesn't exist
    }
    
    rack_ptr = rack_iter->second;
  }
  
  return rack_ptr->dropNode(node_ptr);
}

int Cell::addRack(const std::shared_ptr<Rack>& rack_ptr) {
  if (!rack_ptr) {
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  return rack_map_.insert(std::make_pair(rack_ptr->rackName(), rack_ptr)).second ? 0 : -1;
}

int Cell::dropRack(const std::shared_ptr<Rack>& rack_ptr) {
  if (!rack_ptr) {
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  return rack_map_.erase(rack_ptr->rackName()) ? 0 : -1;
}

std::shared_ptr<Rack> Cell::getRack(const std::string& rack_name) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = rack_map_.find(rack_name);
  if (it == rack_map_.end()) {
    return nullptr;
  }
  return it->second;
}

// Dc implementations
void Dc::getNodeList(NodeVector* node_list) {
  if (!node_list) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& cell_pair : cell_map_) {
    cell_pair.second->getNodeList(node_list);
  }
}

void Dc::getCellList(CellVector* cell_list) {
  if (!cell_list) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& cell_pair : cell_map_) {
    cell_list->push_back(cell_pair.second);
  }
}

int Dc::addNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }
  
  // Use const method to get cell name
  std::string cell_name = node_ptr->getLocationCell();
  std::string rack_name = node_ptr->getLocationRack();
  
  // Find the cell or create it if it doesn't exist
  std::shared_ptr<Cell> cell_ptr;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto cell_iter = cell_map_.find(cell_name);
    
    if (cell_iter == cell_map_.end()) {
      // Cell doesn't exist, create it
      cell_ptr = std::make_shared<Cell>(cell_name);
      cell_map_.insert(std::make_pair(cell_name, cell_ptr));
    } else {
      cell_ptr = cell_iter->second;
    }
  }

  // Find the rack or create it if it doesn't exist
  std::shared_ptr<Rack> rack_ptr = cell_ptr->getRack(rack_name);
  if (!rack_ptr) {
    rack_ptr = std::make_shared<Rack>(rack_name);
    cell_ptr->addRack(rack_ptr);
  }

  // Add the node to the rack
  return rack_ptr->addNode(node_ptr);
}

int Dc::dropNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }
  
  std::string cell_name = node_ptr->getLocationCell();
  std::shared_ptr<Cell> cell_ptr;
  
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto cell_iter = cell_map_.find(cell_name);
    
    if (cell_iter == cell_map_.end()) {
      return -1; // Cell doesn't exist
    }
    
    cell_ptr = cell_iter->second;
  }
  
  return cell_ptr->dropNode(node_ptr);
}

int Dc::addCell(const std::shared_ptr<Cell>& cell_ptr) {
  if (!cell_ptr) {
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  return cell_map_.insert(std::make_pair(cell_ptr->cellName(), cell_ptr)).second ? 0 : -1;
}

int Dc::dropCell(const std::shared_ptr<Cell>& cell_ptr) {
  if (!cell_ptr) {
    return -1;
  }
  
  std::lock_guard<std::mutex> lock(mutex_);
  return cell_map_.erase(cell_ptr->cellName()) ? 0 : -1;
}

std::shared_ptr<Cell> Dc::getCell(const std::string& cell_name) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = cell_map_.find(cell_name);
  if (it == cell_map_.end()) {
    return nullptr;
  }
  return it->second;
}

int Dc::addRack(const std::shared_ptr<Rack>& rack_ptr, const std::string& cell_name) {
  if (!rack_ptr) {
    return -1;
  }
  
  // Find the cell or create it if it doesn't exist
  std::shared_ptr<Cell> cell_ptr;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto cell_iter = cell_map_.find(cell_name);
    
    if (cell_iter == cell_map_.end()) {
      // Cell doesn't exist, create it
      cell_ptr = std::make_shared<Cell>(cell_name);
      cell_map_.insert(std::make_pair(cell_name, cell_ptr));
    } else {
      cell_ptr = cell_iter->second;
    }
  }
  
  // Add the rack to the cell
  return cell_ptr->addRack(rack_ptr);
}

std::shared_ptr<Rack> Dc::getRack(const std::string& rack_name, const std::string& cell_name) {
  std::shared_ptr<Cell> cell_ptr;
  
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto cell_iter = cell_map_.find(cell_name);
    if (cell_iter == cell_map_.end()) {
      return nullptr; // Cell doesn't exist
    }
    cell_ptr = cell_iter->second;
  }
  
  return cell_ptr->getRack(rack_name);
}

// ClusterManager implementations
ClusterManager::ClusterManager(RootServer* root_server, const std::string& cluster_uuid)
    : root_server_(root_server), cluster_uuid_(cluster_uuid), running_(false) {
  // Initialize the cluster proto
  cluster_pb_ = std::make_shared<proto::ClusterProto>();
}

void ClusterManager::Start() {
  running_ = true;
  LOG(INFO) << "cfs cluster manager start, load cluster metadata from kvstore";
  std::unique_ptr<RSMetaReader> reader = std::make_unique<RSMetaReader>(root_server_);
  auto ret = reader->GetCluster(FLAGS_cfs_cluster_uuid, cluster_pb_.get());
  if (UNLIKELY(ret == bds::proto::Status::ERROR_RS_KV_STORE_KEY_NOT_FOUND)) {
    // TODO write cluster info
  } else if (UNLIKELY(ret != bds::proto::Status::SUCCESS)) {
    LOG(ERROR) << "load cluster metadata from kvstore failed, start ClusterManager fail";
    return;
  }
  LOG(INFO) << "ClusterManager '" << cluster_uuid_ << "' started success";
}

void ClusterManager::Stop() {
  running_ = false;
  std::cout << "ClusterManager '" << cluster_uuid_ << "' stopping..." << std::endl;
  // Add actual stop logic here
  std::cout << "ClusterManager '" << cluster_uuid_ << "' stopped." << std::endl;
}

std::string ClusterManager::GetClusterUuid() const {
  return cluster_uuid_;
}

std::shared_ptr<ClusterProto> ClusterManager::GetClusterProto() const {
  return cluster_pb_;
}

int ClusterManager::addDc(const std::shared_ptr<Dc>& dc_ptr) {
  if (!dc_ptr) {
    return -1;
  }

  std::lock_guard<std::mutex> lock(mutex_);
  return dc_map_.insert(std::make_pair(dc_ptr->dcName(), dc_ptr)).second ? 0 : -1;
}

int ClusterManager::dropDc(const std::shared_ptr<Dc>& dc_ptr) {
  if (!dc_ptr) {
    return -1;
  }

  std::lock_guard<std::mutex> lock(mutex_);
  return dc_map_.erase(dc_ptr->dcName()) ? 0 : -1;
}

std::shared_ptr<Dc> ClusterManager::getDc(const std::string& dc_name) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = dc_map_.find(dc_name);
  if (it == dc_map_.end()) {
    return nullptr;
  }
  return it->second;
}

void ClusterManager::getDcList(DcVector* dc_list) {
  if (!dc_list) {
    return;
  }

  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& dc_pair : dc_map_) {
    dc_list->push_back(dc_pair.second);
  }
}

int ClusterManager::addNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }

  // Get the DC name using the const method
  std::string dc_name = node_ptr->getLocationDc();

  // Find the DC or create it if it doesn't exist
  std::shared_ptr<Dc> dc_ptr;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto dc_iter = dc_map_.find(dc_name);

    if (dc_iter == dc_map_.end()) {
      // DC doesn't exist, create it
      dc_ptr = std::make_shared<Dc>(dc_name);
      dc_map_.insert(std::make_pair(dc_name, dc_ptr));
    } else {
      dc_ptr = dc_iter->second;
    }
  }

  // Add the node to the DC
  return dc_ptr->addNode(node_ptr);
}

int ClusterManager::dropNode(const std::shared_ptr<Node>& node_ptr) {
  if (!node_ptr) {
    return -1;
  }

  // Use the const method to get DC name
  std::string dc_name = node_ptr->getLocationDc();
  
  std::shared_ptr<Dc> dc_ptr;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto dc_iter = dc_map_.find(dc_name);

    if (dc_iter == dc_map_.end()) {
      return -1; // DC doesn't exist
    }

    dc_ptr = dc_iter->second;
  }

  return dc_ptr->dropNode(node_ptr);
}

void ClusterManager::getNodeList(NodeVector* node_list) {
  if (!node_list) {
    return;
  }

  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& dc_pair : dc_map_) {
    dc_pair.second->getNodeList(node_list);
  }
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds