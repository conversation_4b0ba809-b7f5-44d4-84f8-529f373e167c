#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include <memory>

#include <byterpc/util/endpoint.h>
#include <byte/concurrent/lite_lock.h>
#include <byte/include/macros.h>

#include "metadata/meta/group_replica.h"
// #include "metadata/meta/space.h"
#include "rootserver.pb.h"

namespace bds {
namespace rootserver {
namespace cfs {

class BlockGroup {
public:
    explicit BlockGroup(const GroupProto& group_pb);

    ~BlockGroup();

    bool UpdateGroupReplica(const GroupReplicaProto& group_rep_pb,
                                  GroupReplicaPtr* add_rep,
                                  GroupReplicaPtr* remove_rep);

    uint32_t GroupId() const {
        return group_id_;
    }

    uint32_t SpaceId() const {
        return space_id_;
    }

    GroupStatusProto Status();

    uint32_t Version();

    void GetGroupProto(GroupProto* pb);

    void ListReplicas(std::vector<GroupReplicaPtr>* reps, uint32_t* version);

    void GetReplicas(std::map<uint32_t, GroupReplicaPtr>* replicas, uint32_t* version);

    bool GetReplica(uint32_t index, GroupReplicaPtr* replica);

    void ListAllReplicas(std::vector<GroupReplicaPtr>* reps, uint32_t* version);

    void GetHistoryReplicas(std::map<uint32_t, GroupReplicaPtr>* history_replicas, uint32_t* version);
    bool GetHistoryReplica(uint32_t index, GroupReplicaPtr* history_replica);
    void ListHistoryReplicas(std::vector<GroupReplicaPtr>* reps, uint32_t* version);

private:
    const uint32_t group_id_{0};
    const uint32_t space_id_{0};
    byte::LiteRWLock mutex_;
    GroupProto pb_;
};

typedef std::shared_ptr<BlockGroup> BlockGroupPtr;

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds