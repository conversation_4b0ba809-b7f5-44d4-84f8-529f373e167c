/**
 * @file datanode.h
 * @brief Definition of DataNode structure for ByteDance Storage system
 */
#pragma once

#include <byterpc/util/endpoint.h>
#include <byte/concurrent/lite_lock.h>
#include <byte/include/macros.h>
#include "rootserver.pb.h"

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "metadata/meta/disk.h"

namespace bds {
namespace rootserver {
namespace cfs {

using MutexLock = byte::LiteRWLock;
using RwLock = byte::LiteRWLock;

using DanceDNStatusProto = ::bds::proto::DanceDNStatusProto;
using DanceDNInfoProto = ::bds::proto::DanceDNInfoProto;

/**
 * @struct DataNodeOptions
 * @brief Configuration options for creating a DataNode
 */
struct DataNodeOptions {
    byterpc::util::EndPoint addr;
    std::string location_dc;
    std::string location_cell;
    std::string location_rack;
    DanceDNStatusProto state = DanceDNStatusProto::DN_STATUS_PROTO_NORMAL;
    uint64_t create_time = 0;
};

/**
 * @class Node
 * @brief Represents a data node in the distributed storage system
 * 
 * DataNode manages storage information, disk statistics, and location
 * information within the cluster hierarchy.
 */
class Node {
public:

    explicit Node(const DataNodeOptions& options);
    ~Node() = default;
    

    byterpc::util::EndPoint getAddr() const;


    void setAddr(const byterpc::util::EndPoint& endpoint);
    

    uint64_t getCreateTime() const;
    

    std::string getLocationDc() const;

    void setLocationDc(const std::string& location_dc);

    std::string getLocationCell() const;

    void setLocationCell(const std::string& location_cell);

    std::string getLocationRack() const;

    void setLocationRack(const std::string& location_rack);

    uint64_t getOfflineTime();

    void setOfflineTime(uint64_t time);

    uint64_t getOnlineTime();

    void setOnlineTime(uint64_t time);

    void setState(DanceDNStatusProto state);
    
    /**
     * @brief Get the node state
     * 
     * @return DanceDNStatusProto Current state
     */
    DanceDNStatusProto getState() const;
    
    /**
     * @brief Get maximum IO utilization across all disks
     * 
     * @return uint64_t Maximum IO utilization
     */
    uint64_t getMaxIoutil() const;
    
    /**
     * @brief Get the disk ID with maximum IO utilization
     * 
     * @return int Disk ID
     */
    int getMaxIoutilDiskId() const;
    
    /**
     * @brief Calculate the minimum disk usage percentage
     * 
     * @return double Minimum disk usage percentage
     */
    double getMinDiskUsage();
    
    /**
     * @brief Calculate overall disk usage percentage
     * 
     * @return double Overall disk usage percentage
     */
    double getUsage();
    
    /**
     * @brief Calculate free usage percentage
     * 
     * @return double Free usage percentage
     */
    double getFreeUsage();
    
    /**
     * @brief Get free space in bytes
     * 
     * @return uint64_t Free space
     */
    uint64_t getFreeSpace();
    
    /**
     * @brief Check if disk is in safemode
     * 
     * @return true If in safemode
     * @return false If not in safemode
     */
    bool isDiskSafemode();
    
    /**
     * @brief Get disk safemode uptime
     * 
     * @return uint64_t Safemode uptime
     */
    uint64_t getDiskSafemodeUptime();
    
    /**
     * @brief Set disk safemode status
     * 
     * @param is_safemode Whether to enable safemode
     * @param time Timestamp for the change
     */
    void setDiskSafemode(bool is_safemode, uint64_t time);
    
    /**
     * @brief Get used storage size
     * 
     * @return uint64_t Used size in bytes
     */
    uint64_t getUsedSize() const;
    
    /**
     * @brief Get total storage size
     * 
     * @return uint64_t Total size in bytes
     */
    uint64_t getTotalSize() const;
    
    /**
     * @brief Get free storage size
     * 
     * @return uint64_t Free size in bytes
     */
    uint64_t getFreeSize() const;
    
    /**
     * @brief Get total disk size
     * 
     * @return uint64_t Total disk size in bytes
     */
    uint64_t getDiskTotalSize() const;
    
    /**
     * @brief Get last heartbeat time
     * 
     * @return uint64_t Heartbeat timestamp
     */
    uint64_t getLastHeartbeatTime() const;
    
    /**
     * @brief Set last heartbeat time
     * 
     * @param time Heartbeat timestamp
     */
    void setLastHeartbeatTime(uint64_t time);
    
    /**
     * @brief Set data node ID
     * 
     * @param id Data node ID
     */
    void setDnId(const std::string& id);
    
    /**
     * @brief Get data node ID
     * 
     * @return std::string Data node ID
     */
    std::string getDnId() const;

    void updateDiskStat(const std::vector<DiskStat>& disk_stat_vector);

    std::shared_ptr<Disk> getDisk(int disk_id);

    void getDiskList(DiskVector* disk_list);

    int addDisk(const std::shared_ptr<Disk>& disk_ptr);

    int dropDisk(int disk_id);
    
    /**
     * @brief Get list of existing group replicas
     * 
     * @param[out] group_replicas Output vector for group replicas
     */
    void getExistGroupReplicas(GroupReplicaVector* group_replicas);
    
    /**
     * @brief Get list of all group replicas
     * 
     * @param[out] group_replicas Output vector for group replicas
     */
    void getGroupReplicaList(GroupReplicaVector* group_replicas);
    
    /**
     * @brief Get number of group replicas
     * 
     * @return size_t Number of group replicas
     */
    size_t getGroupReplicaNum();
    
    /**
     * @brief Get group replica by volume ID
     * 
     * @param volume_id Volume ID
     * @return std::shared_ptr<GroupReplica> Pointer to group replica or nullptr
     */
    std::shared_ptr<GroupReplica> getGroupReplica(uint64_t volume_id);

private:
    /** Node information proto */
    DanceDNInfoProto node_info_;
    
    /** Overall usage percentage */
    double usage_ = 1.0;
    
    /** Map of disk ID to disk object */
    DiskMap disk_map_;
    
    /** Mutex for thread safety */
    mutable RwLock mutex_;
    
    /** Timestamp when node went offline */
    uint64_t offline_time_ = 0;
    
    /** Timestamp when node came online */
    uint64_t online_time_ = 0;
    
    /** Flag indicating if disks are in safemode */
    bool is_disk_safemode_ = false;
    
    /** Timestamp when disk entered safemode */
    uint64_t disk_safemode_uptime_ = 0;
    
    /** Total disk size in bytes */
    uint64_t disk_total_size_ = 0;
    
    /** Free disk size in bytes */
    uint64_t disk_free_size_ = 0;
    
    /** Used size reported by data node */
    uint64_t report_used_size_ = 0;
    
    /** Used size calculated from disk usage */
    uint64_t calc_used_size_ = 0;
    
    /** ID of disk with maximum IO utilization */
    int max_ioutil_disk_id_ = -1;
    
    /** Maximum IO utilization value [0-100] */
    uint64_t max_ioutil_ = 0;
};

/** Type alias for map of node IDs to node objects */
using DataNodeMap = std::unordered_map<std::string, std::shared_ptr<Node>>;

/** Type alias for vector of node pointers */
using DataNodeVector = std::vector<std::shared_ptr<Node>>;

} // namespace cfs
} // namespace rootserver
} // namespace bds