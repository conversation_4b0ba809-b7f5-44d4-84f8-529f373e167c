// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once
#include <gflags/gflags.h>

namespace byterpc {

DECLARE_bool(byterpc_reuse_port);
DECLARE_bool(byterpc_reuse_addr);
DECLARE_bool(byterpc_preempt_uds_path);
DECLARE_bool(byterpc_enable_time_profiler);
DECLARE_bool(byterpc_enable_loop_metrics);
DECLARE_int32(byterpc_report_loop_metrics_interval_us);
DECLARE_int32(byterpc_slow_loop_us);
DECLARE_bool(byterpc_enable_poller_metrics);
DECLARE_int32(byterpc_rpc_timeout_ms);
DECLARE_int32(byterpc_connect_timeout_ms);
DECLARE_int32(byterpc_channel_map_check_interval_ms);
DECLARE_bool(byterpc_enable_iobuf_stats_monitor);
DECLARE_int32(byterpc_iobuf_stats_mon_interval_ms);
DECLARE_uint32(byterpc_channel_defer_close_second);
DECLARE_int64(byterpc_socket_max_unwritten_bytes);
DECLARE_uint64(byterpc_max_body_size);
DECLARE_bool(byterpc_immutable_flags);
DECLARE_int32(byterpc_timeout_event_hashmap_bucket_num);
DECLARE_bool(byterpc_enable_block_pool);
DECLARE_bool(byterpc_enable_iobuf_multi_thread);
DECLARE_bool(byterpc_enable_unregister_memory_dynamicly);

// memory pool
DECLARE_bool(byterpc_enable_numa_aware);
DECLARE_bool(byterpc_byte_express_memory_pool_use_hugepage);
DECLARE_uint64(byterpc_memory_pool_region_size);
DECLARE_uint32(byterpc_byte_express_memory_pool_max_regions);
DECLARE_uint64(byterpc_min_resident_capacity_in_MB_to_expand_memory);
// - only numa-aware enabled
DECLARE_uint32(byterpc_numa0_memory_pool_init_regions);
DECLARE_uint32(byterpc_numa1_memory_pool_init_regions);
DECLARE_string(byterpc_memory_pool_numa_init_region_num);
DECLARE_string(byterpc_memory_pool_numa_max_hugepage_region_num);
// - only numa-aware disabled
DECLARE_uint32(byterpc_byte_express_memory_pool_init_regions);
DECLARE_uint32(byterpc_byte_express_memory_pool_max_hugepage_regions);
// - only bemalloc
DECLARE_uint32(byterpc_byte_express_memory_pool_garbage_collection_in_conn_mgr_interval_ms);
DECLARE_bool(byterpc_init_bemalloc_monitor_task);
DECLARE_bool(byterpc_enable_api_level_mem_statistics);

// watcher thread
// - slow polling detect task
DECLARE_bool(byterpc_init_slow_polling_detect_task);
DECLARE_bool(byterpc_enable_dump_stacktrace);
DECLARE_uint32(byterpc_slow_polling_oneshot_times_limit);
DECLARE_uint32(byterpc_slow_polling_stack_skip_depth);
DECLARE_uint32(byterpc_slow_polling_stack_max_print_depth);

// log
DECLARE_int32(byterpc_log_buf_level);
DECLARE_int32(byterpc_log_min_level);

// built-in service
DECLARE_bool(byterpc_enable_collecting_connection_info);
DECLARE_uint32(byterpc_collecting_period);

// metrics
DECLARE_bool(byterpc_metric_enable_log);
DECLARE_int32(byterpc_metric_window_size_s);
DECLARE_int32(byterpc_metric_report_interval_s);
DECLARE_string(byterpc_metric_log_file);
DECLARE_int32(byterpc_metric_log_max_file_size_mb);
DECLARE_int32(byterpc_metric_log_max_file_num);
DECLARE_int32(byterpc_metric_log_level);

// fallback channel
DECLARE_uint64(byterpc_fallback_health_check_interval_s);
DECLARE_int32(byterpc_fallback_transport_switch_window_ms);
DECLARE_uint64(byterpc_fallback_dedup_failure_window_us);
DECLARE_uint32(byterpc_fallback_risky_max_failed_count);
DECLARE_int32(byterpc_fallback_health_check_continuous_succ_time);
DECLARE_uint64(byterpc_fallback_risky_conn_gc_interval_s);

// byte express
DECLARE_uint32(byterpc_byte_express_accept_schedule_policy);
DECLARE_uint32(byterpc_byte_express_max_wc_at_once);
DECLARE_int32(byterpc_byte_express_listen_backlog);
DECLARE_uint32(byterpc_byte_express_dispatcher_num);
DECLARE_bool(byterpc_byte_express_use_res_allocator);
DECLARE_uint32(byterpc_byte_express_flow_control_timeout_ms);
DECLARE_uint32(byterpc_byte_express_slow_connect_us);
DECLARE_uint32(byterpc_byte_express_max_inflight_connectors);
DECLARE_uint32(byterpc_byte_express_keepalive_interval_ms);
DECLARE_uint32(byterpc_byte_express_qp_max_send_wr);
DECLARE_uint32(byterpc_byte_express_qp_max_recv_wr);
DECLARE_uint32(byterpc_byte_express_recv_buffer_size);
DECLARE_uint32(byterpc_byte_express_qp_retry_cnt);
DECLARE_uint32(byterpc_byte_express_qp_timeout);
DECLARE_uint32(byterpc_byte_express_qp_mtu);
DECLARE_bool(byterpc_byte_express_use_infiniband);

// tarzan
DECLARE_int32(byterpc_tarzan_worker_num);
DECLARE_int32(byterpc_tarzan_max_events_at_once);
DECLARE_uint32(byterpc_tarzan_slow_connect_us);

// kernel tcp
DECLARE_bool(byterpc_prevent_tcp_self_connect);
DECLARE_uint32(byterpc_tcp_slow_connect_us);
DECLARE_int32(byterpc_socket_send_buffer_size);
DECLARE_int32(byterpc_socket_recv_buffer_size);
DECLARE_int32(byterpc_socket_tcp_user_timeout_ms);
DECLARE_int32(byterpc_tcp_max_events_at_once);

// http
DECLARE_bool(byterpc_pb_enum_as_number);

#ifndef NDEBUG
DECLARE_bool(byterpc_disable_rdma_traffic);
DECLARE_bool(byterpc_disable_tarzan_traffic);
#endif

DECLARE_bool(byterpc_enable_remote_task_metrics);
DECLARE_uint64(byterpc_process_remote_task_slow_us);
DECLARE_uint64(byterpc_report_remote_task_metrics_interval_us);
DECLARE_uint64(byterpc_inner_task_step);

// NAPI
DECLARE_uint32(byterpc_napi_event_to_polling_estimate_valid_cpu_utilization_interval_us);
DECLARE_uint32(byterpc_napi_event_to_polling_valid_cpu_utilization_threshold);
DECLARE_uint32(byterpc_napi_polling_to_event_estimate_valid_cpu_utilization_interval_us);
DECLARE_uint32(byterpc_napi_polling_to_event_valid_cpu_utilization_threshold);

}  // namespace byterpc
