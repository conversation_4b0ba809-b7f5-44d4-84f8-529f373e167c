// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <sys/uio.h>

#include <vector>

#include "byterpc/mem/mem_page_info.h"
#include "byterpc/memory_id.h"

namespace byterpc {

// Must call ExecCtx::Init() before use any API below.

// TODO(dpw): whether to support numa
// void* Allocate(size_t size, uint64_t numa_id = -1);

// Allocate memory from byterpc memory pool. The memory size must be in range of [0, 4M-128B].
void* Allocate(size_t size);

void Deallocate(void* addr);

// Register external memory to all transports.
// MemPageInfo's hold_type fill UserHuge if user alloc with HugePage, others fill HugeNormal;
// return 0 on success, -1 otherwise
int ExtmemRegister(const MemPageInfo& pages);

// Unregister external memory from all already inited transport.
// return 0 on success, -1 otherwise
// NOTE: Users must make sure that they do not unregister any memory which is still in use. If
// `byterpc_enable_unregister_memory_dynamicly` flag is false, users must not call this until the
// process is about to exit.
int ExtmemUnregister(const MemPageInfo& pages);

// get all hugepage list
void GetHugepageList(std::vector<iovec>* mem_vec);

// get all MemPageInfo registered in byterpc, including: byterpc MemoryPool-allocated mem
// (HolderType == Unify), tarzan-allocated mem (HolderType == Tarzan), user-registered mem
// (registered by byterpc::ExtmemRegister)
std::vector<MemPageInfo> GetAllRegisteredMem();

// Get current allocated region num of memory pool on the `numa_id` node.
// If numa-aware is disabled, `numa_id` is useless and will always return the total region number.
// If numa-aware is enabled and `numa_id` < 0, return the total region number of all numa nodes.
// If numa-aware is enabled and `numa_id` >= 0, return the region number of the correspond node.
uint32_t GetMemoryPoolRegionNum(int numa_id = -1);

}  // namespace byterpc
