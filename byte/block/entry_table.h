// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <deque>
#include <memory>
#include <utility>
#include "byte/block/entry.h"
#include "byte/block/range.h"
#include "byte/concurrent/lite_lock.h"
#include "byte/container/concurrent_hash_table.h"

namespace byte {

class EntryIterator {
public:
    virtual ~EntryIterator() {}

    virtual bool Valid() const = 0;
    virtual void Next() = 0;
    virtual std::pair<EntryId, BlockEntry*> Current() const = 0;
};

class BlockEntryIterator;

class BlockEntryTable {
public:
    BlockEntryTable(
        const uint32_t num_buckets,
        const uint32_t max_entry_size);

    ~BlockEntryTable();

    void Initialize(BlockEntryTable* base_table);

    bool AddEntry(
        const EntryId& entry_id,
        BlockEntry* added_entry);

    bool AddEntryMeta(
        const EntryId& entry_id,
        const BlockEntry* added_entry);

    bool RemoveEntry(const EntryId& entry_id);

    bool LookupEntry(
        const EntryId& entry_id,
        BlockEntry* entry = nullptr) const;

    bool UpdateEntryMeta(
        const EntryId& entry_id,
        const EntryMeta& meta);

    bool LookupRanges(
        const EntryId& entry_id,
        const Range32& range,
        std::deque<Range32>* bases,
        std::deque<RangePosition>* updates) const;

    bool UpdateRange(
        const EntryId& entry_id,
        const RangePosition& rangePos);

    void SetMinLogOffset(uint64_t min_log_offset) {
        min_log_offset_ = min_log_offset;
    }

    uint64_t GetMinLogOffset() const { return min_log_offset_; }

    EntryIterator* NewEntryIterator() const;

    EntryIterator* NewEntryIterator(const EntryId& entry_id) const;

    const MemoryPoolLiteAllocator<std::pair<const uint32_t, uint64_t>>& GetAllocator() {
        return allocator_;
    }

    void SwitchBaseTable(BlockEntryTable* base_table);

protected:
    friend class BlockEntryIterator;
    uint32_t num_buckets_;
    std::unique_ptr<ConcurrentHashTable<EntryId, BlockEntry*>> entry_contexts_;

private:
    enum TriState {
        NOT_FOUND = 0,
        FOUND = 1,
        NOT_SURE = 2,
    };

    TriState LookupEntryInternal(
        const EntryId& entry_id,
        BlockEntry** entry = nullptr) const;

    void AddEntryInternal(const EntryId& entry_id, BlockEntry* added_entry);

    TriState LookupRangesInternal(
        const EntryId& entry_id,
        const Range32& range,
        std::deque<Range32>* bases,
        std::deque<RangePosition>* updates) const;

    void UpdateRangeInternal(
        const RangePosition& rangePos,
        BlockEntry* entry);

    uint32_t max_entry_size_;
    BlockEntryTable* base_table_;
    mutable LiteRWLock locker_;
    uint64_t min_log_offset_;
    std::unique_ptr<MemoryPoolLite> mem_pool_;
    MemoryPoolLiteAllocator<std::pair<const uint32_t, uint64_t>> allocator_;
};

}  // namespace byte
