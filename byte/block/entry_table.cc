// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include <algorithm>
#include "byte/block/entry_table.h"
#include "byte/container/btree/btree_map.h"

namespace byte {

class BlockEntryIterator : public EntryIterator {
public:
    explicit BlockEntryIterator(const BlockEntryTable* table);
    BlockEntryIterator(const BlockEntryTable* table, const EntryId& entry_id);

    virtual ~BlockEntryIterator() {}

    bool Valid() const override;
    void Next() override;
    std::pair<EntryId, BlockEntry*> Current() const override;

private:
    void Forward();

    uint32_t num_buckets_;
    uint32_t bucket_index_;
    btree::btree_map<EntryId, BlockEntry*> bucket_cache_;
    btree::btree_map<EntryId, BlockEntry*>::iterator iter_;
    const BlockEntryTable* table_;

    DISALLOW_COPY_AND_ASSIGN(BlockEntryIterator);
};

BlockEntryIterator::BlockEntryIterator(const BlockEntryTable* table)
    : table_(table) {
    bucket_index_ = 0;
    num_buckets_ = table->num_buckets_;
    Forward();
}

BlockEntryIterator::BlockEntryIterator(const BlockEntryTable* table, const EntryId& entry_id)
    : table_(table) {
    num_buckets_ = table->num_buckets_;
    bucket_index_ = ConcurrentHashTable<EntryId, BlockEntry*>::Hash(entry_id, num_buckets_);
    table_->entry_contexts_->GetBucket(bucket_index_, &bucket_cache_);
    if (!bucket_cache_.empty()) {
        iter_ = bucket_cache_.find(entry_id);
    }
    if (bucket_cache_.empty() || iter_ == bucket_cache_.end() || ++iter_ == bucket_cache_.end()) {
        ++bucket_index_;
        Forward();
    }
}

bool BlockEntryIterator::Valid() const {
    return iter_ != bucket_cache_.end();
}

void BlockEntryIterator::Next() {
    BYTE_ASSERT(Valid());
    if (++iter_ == bucket_cache_.end()) {
        bucket_cache_.clear();
        ++bucket_index_;
        Forward();
    }
}

std::pair<EntryId, BlockEntry*> BlockEntryIterator::Current() const {
    BYTE_ASSERT(Valid());
    return std::make_pair(iter_->first, iter_->second);
}

void BlockEntryIterator::Forward() {
    for (; bucket_index_ < num_buckets_; ++bucket_index_) {
        table_->entry_contexts_->GetBucket(bucket_index_, &bucket_cache_);
        if (!bucket_cache_.empty()) {
            break;
        }
    }
    iter_ = bucket_cache_.begin();
}

BlockEntryTable::BlockEntryTable(
    const uint32_t num_buckets,
    const uint32_t max_entry_size)
    : num_buckets_(num_buckets),
      max_entry_size_(max_entry_size) {
    entry_contexts_.reset(
        new ConcurrentHashTable<EntryId, BlockEntry*>(num_buckets_));
    mem_pool_.reset(new MemoryPoolLite());
    allocator_ =
        MemoryPoolLiteAllocator<std::pair<const uint32_t, uint64_t>>(
            mem_pool_.get());
    base_table_ = nullptr;
}

void BlockEntryTable::Initialize(BlockEntryTable* base_table) {
    base_table_ = base_table;
    if (base_table != nullptr) {
        min_log_offset_ = base_table->GetMinLogOffset();
    }
}

BlockEntryTable::~BlockEntryTable() {
    entry_contexts_->ClearValues();
}

bool BlockEntryTable::AddEntry(
    const EntryId& entry_id,
    BlockEntry* added_entry) {
    if (LookupEntry(entry_id)) {
        // Entry has existed
        return false;
    }
    AddEntryInternal(entry_id, added_entry);
    return true;
}

bool BlockEntryTable::AddEntryMeta(
    const EntryId& entry_id,
    const BlockEntry* added_entry) {
    if (LookupEntry(entry_id)) {
        // Entry has existed
        return false;
    }
    BlockEntry* entry = new BlockEntry(max_entry_size_, &allocator_);
    entry->CopyFrom(added_entry);
    AddEntryInternal(entry_id, entry);
    return true;
}

bool BlockEntryTable::RemoveEntry(
    const EntryId& entry_id) {
    BlockEntry* found_entry = nullptr;
    TriState state = LookupEntryInternal(entry_id, &found_entry);
    if (state == FOUND) {
        found_entry->SetExistent(false);
        return true;
    } else if (state == NOT_FOUND) {
        // Entry not found
        return false;
    }
    if (base_table_ == nullptr) {
        return false;
    }
    std::unique_ptr<BlockEntry> entry(new BlockEntry(max_entry_size_, &allocator_));
    RETURN_FALSE_IF_(!base_table_->LookupEntry(entry_id, entry.get()));
    entry->SetExistent(false);
    AddEntryInternal(entry_id, entry.release());
    return true;
}

bool BlockEntryTable::LookupEntry(
    const EntryId& entry_id,
    BlockEntry* entry) const {
    BlockEntry* found_entry = nullptr;
    TriState state = LookupEntryInternal(entry_id, &found_entry);
    if (state == FOUND) {
        if (entry != nullptr) {
            entry->CopyFrom(found_entry);
        }
        return true;
    } else if (state == NOT_FOUND) {
        // Entry not found
        return false;
    }
    if (base_table_ == nullptr) {
        return false;
    } else {
        return base_table_->LookupEntry(entry_id, entry);
    }
}

bool BlockEntryTable::UpdateEntryMeta(
    const EntryId& entry_id,
    const EntryMeta& meta) {
    BlockEntry* found_entry = nullptr;
    TriState state = LookupEntryInternal(entry_id, &found_entry);
    if (state == FOUND) {
        found_entry->meta_.min_log_offset_ = meta.min_log_offset_;
        found_entry->meta_.logical_length_ = meta.logical_length_;
        return true;
    } else if (state == NOT_FOUND) {
        // Entry not found
        return false;
    }
    if (base_table_ == nullptr) {
        return false;
    }
    std::unique_ptr<BlockEntry> entry(new BlockEntry(max_entry_size_, &allocator_));
    RETURN_FALSE_IF_(!base_table_->LookupEntry(entry_id, entry.get()));
    // TODO(dongchengyu): Refuse smaller log id?
    entry->meta_.min_log_offset_ = meta.min_log_offset_;
    entry->meta_.logical_length_ = meta.logical_length_;
    AddEntryInternal(entry_id, entry.release());
    return true;
}

bool BlockEntryTable::LookupRanges(
    const EntryId& entry_id,
    const Range32& range,
    std::deque<Range32>* bases,
    std::deque<RangePosition>* updates) const {
    std::deque<Range32> bases_in_base_table;
    std::deque<RangePosition> updates_in_base_table;
    TriState state = LookupRangesInternal(entry_id, range, bases, updates);
    if (state == FOUND) {
        if (base_table_ == nullptr) {
            return true;
        }
        if (!base_table_->LookupRanges(entry_id,
                                       range,
                                       bases ? &bases_in_base_table : nullptr,
                                       updates ? &updates_in_base_table : nullptr)) {
            return true;
        }
    } else if (state == NOT_FOUND) {
        // Entry not found
        return false;
    } else {
        if (base_table_ == nullptr) {
            return false;
        }
        if (!base_table_->LookupRanges(entry_id, range, bases, updates)) {
            // Entry not found
            return false;
        }
    }
    std::deque<Range32> bases_in_update_table;
    std::deque<RangePosition> updates_in_update_table;
    if (bases != nullptr) {
        bases->swap(bases_in_update_table);
        MergeRanges(&bases_in_base_table, &bases_in_update_table, bases);
    }
    if (updates != nullptr) {
        updates->swap(updates_in_update_table);
        MergeRangePositions(&updates_in_base_table, &updates_in_update_table, updates);
    }
    return true;
}

bool BlockEntryTable::UpdateRange(
    const EntryId& entry_id,
    const RangePosition& range_pos) {
    BlockEntry* found_entry = nullptr;
    TriState state = LookupEntryInternal(entry_id, &found_entry);
    if (state == FOUND) {
        UpdateRangeInternal(range_pos, found_entry);
        return true;
    } else if (state == NOT_FOUND) {
        // Entry not found
        return false;
    }
    std::unique_ptr<BlockEntry> entry(new BlockEntry(max_entry_size_, &allocator_));
    if (base_table_ == nullptr) {
        return false;
    }
    RETURN_FALSE_IF_(!base_table_->LookupEntry(entry_id, entry.get()));
    UpdateRangeInternal(range_pos, entry.get());
    AddEntryInternal(entry_id, entry.release());
    return true;
}

void BlockEntryTable::UpdateRangeInternal(
    const RangePosition& range_pos,
    BlockEntry* entry) {
    entry->AddRange(range_pos);
    entry->meta_.min_log_offset_ = std::max<uint64_t>(entry->meta_.min_log_offset_,
                                                      range_pos.log_offset_);
    entry->meta_.logical_length_ = std::max<uint32_t>(entry->meta_.logical_length_,
                                                      range_pos.entry_offset_ + range_pos.length_);
}

BlockEntryTable::TriState BlockEntryTable::LookupEntryInternal(
    const EntryId& entry_id,
    BlockEntry** entry) const {
    BlockEntry* found_entry = nullptr;
    if (!entry_contexts_->Find(entry_id, &found_entry)) {
        return NOT_SURE;
    }
    if (!found_entry->GetExistent()) {
        return NOT_FOUND;
    }
    if (entry != nullptr) {
        *entry = found_entry;
    }
    return FOUND;
}

void BlockEntryTable::AddEntryInternal(const EntryId& entry_id,
                                       BlockEntry* added_entry) {
    BYTE_ASSERT(entry_contexts_->Insert(std::make_pair(entry_id, added_entry)));
}

BlockEntryTable::TriState BlockEntryTable::LookupRangesInternal(
    const EntryId& entry_id,
    const Range32& range,
    std::deque<Range32>* bases,
    std::deque<RangePosition>* updates) const {
    BlockEntry* entry = nullptr;
    TriState state = LookupEntryInternal(entry_id, &entry);
    if (state == FOUND) {
        entry->GetRanges(range, bases, updates);
        return FOUND;
    }
    return state;
}

EntryIterator* BlockEntryTable::NewEntryIterator() const {
    return new BlockEntryIterator(this);
}

EntryIterator* BlockEntryTable::NewEntryIterator(const EntryId& entry_id) const {
    return new BlockEntryIterator(this, entry_id);
}

void BlockEntryTable::SwitchBaseTable(BlockEntryTable* base_table) {
    base_table_ = base_table;
}

}  // namespace byte
