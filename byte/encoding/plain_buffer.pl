#!/usr/bin/perl  -w

# this perl script is used to generate plain buffer

$kTotalArgs = 20;
sub PrintHead
{
    print "// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.\n\n";
    print "#pragma once\n";
    print "\n/////////////////////////////////////////////////////\n";
    print "// DO NOT EDIT!!!\n";
    print "// This header file is auto generated by perl script\n";
    print "/////////////////////////////////////////////////////\n\n";
    print "// GLOBAL_NOLINT(whitespace/line_length)\n";
    print "// GLOBAL_NOLINT(runtime/explicit)\n\n";
    print "#include <stdint.h>\n";
    print "#include <vector>\n";
    print "#include \"byte/algorithm/checksum.h\"\n";
    print "#include \"byte/encoding/variant_int.h\"\n";
    print "#include \"byte/include/assert.h\"\n";
    print "#include \"byte/string/string_piece.h\"\n\n";
    print "#define PLAIN_BUFFER_MAGIC 0x803c9e17\n\n";
}

sub PrintPlainBufferBase
{
    print "namespace byte {\n";
    print "class PlainBufferBase {\n";
    print "public:\n";
    print "    PlainBufferBase() {}\n";
    print "    virtual ~PlainBufferBase() {}\n";
    print "    virtual bool serialize(char* buffer, uint32_t maxsize, uint32_t* size) const = 0;\n";
    print "    virtual bool deserialize(const char* buffer, uint32_t size, uint32_t* ret) = 0;\n";
    print "};\n\n";

    print "template <size_t N>\n";
    print "class VariantPlainBufferBase : public byte::PlainBufferBase {\n";
    print "public:\n";
    print "    VariantPlainBufferBase() : num_members_(N) {}\n";
    print "    virtual ~VariantPlainBufferBase() {}\n";
    print "    size_t ByteSize() const {\n";
    print "        size_t size = sizeof(num_members_);\n";
    print "        for (uint32_t i = 0; i < num_members_; ++i) {\n";
    print "            size += (sizeof(uint32_t) + data_[i].size());\n";
    print "        }\n";
    print "        return size;\n";
    print "    }\n";
    print "    bool serialize(char* buffer, uint32_t maxsize, uint32_t* size) const override {\n";
    print "        if (maxsize < sizeof(num_members_)) {\n";
    print "            return false;\n";
    print "        }\n";
    print "        size_t offset = 0;\n";
    print "        offset = sizeof(num_members_);\n";
    print "        byte::FixedInt::Encode<uint32_t>(buffer, num_members_);\n";
    print "        if (maxsize < sizeof(num_members_) + num_members_ * sizeof(uint32_t)) {\n";
    print "            return false;\n";
    print "        }\n";
    print "        for (uint32_t i = 0; i < num_members_; ++i) {\n";
    print "            byte::FixedInt::Encode<uint32_t>(buffer + offset, data_[i].size());\n";
    print "            offset += sizeof(uint32_t);\n";
    print "            const byte::StringPiece& data = data_[i];\n";
    print "            if (maxsize < offset + data.size()) {\n";
    print "                return false;\n";
    print "            }\n";
    print "            memcpy(buffer + offset, data.data(), data.size());\n";
    print "            offset += data.size();\n";
    print "        }\n";
    print "        *size = offset;\n";
    print "        return true;\n";
    print "    }\n";
    print "    bool deserialize(const char* buffer, uint32_t size, uint32_t* ret) override {\n";
    print "        if (size < sizeof(size)) { return false; }\n";
    print "        uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer);\n";
    print "        size_t offset = sizeof(num_members);\n";
    print "        uint32_t sz = 0;\n";
    print "        if (num_members > num_members_) { num_members = num_members_; }\n";
    print "        for (uint32_t i = 0; i < num_members; ++i) {\n";
    print "            if (offset + sizeof(sz) > size) { return false; }\n";
    print "            sz = byte::FixedInt::Decode<uint32_t>(buffer + offset);\n";
    print "            offset += sizeof(sz);\n";
    print "            if (offset + sz > size) { return false; }\n";
    print "            data_[i].set(buffer + offset, sz);\n";
    print "            offset += sz;\n";
    print "        }\n";
    print "        *ret = offset;\n";
    print "        return true;\n";
    print "    }\n\n";
    print "protected:\n";
    print "    byte::StringPiece data_[N];\n";
    print "    uint32_t num_members_;\n";
    print "};\n\n";
}

sub PrintPlainBuffer
{
    for($i = 0; $i <= $kTotalArgs; $i++)
    {
        print "\n// for $i argument(s)\n";
        print "#define CLASS_FIXED_PLAIN_BUFFER_$i(name";
        for($j = 1; $j <= $i; ++$j)
        {
            print ", type$j, var$j";
        }
        print ") \\\n";
        print "    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \\\n";
        print "    public: \\\n";
        print "        FixedPlainBuffer_##name() : bitmap_(0) {} \\\n";
        print "        virtual ~FixedPlainBuffer_##name() {} \\\n";
        for($j = 1; $j <= $i; ++$j)
        {
            $t = $j - 1;
            $mask = 1 << $t;
            print "        type$j var$j() const { return var$j\_; } \\\n";
            print "        type$j* mutable_##var$j() { \\\n";
            print "            bitmap_ |= $mask; \\\n";
            print "            return &var$j\_; \\\n";
            print "        } \\\n";
            print "        void set_##var$j(const type$j& var) { \\\n";
            print "            var$j\_ = var; \\\n";
            print "            bitmap_ |= $mask; \\\n";
            print "        } \\\n";
            print "        void clear_##var$j() { bitmap_ &= ~$mask; } \\\n";
            print "        bool has_##var$j() const { return (bitmap_ & $mask) != 0; } \\\n";
        }
        print "        size_t ByteSize() const { \\\n";
        print "            size_t size = sizeof(num_members_) + sizeof(bitmap_); \\\n";
        for($j = 1; $j <= $i; ++$j)
        {
            print "            size += sizeof(var$j\_);\\\n";
        }
        print "            return size; \\\n";
        print "        } \\\n";
        print "        bool serialize(char* buffer, uint32_t maxsize, \\\n";
        print "                       uint32_t* size) const override { \\\n";
        print "            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \\\n";
        print "                return false; \\\n";
        print "            } \\\n";
        print "            size_t offset = 0; \\\n";
        print "            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \\\n";
        print "            byte::FixedInt::Encode<uint32_t>( \\\n";
        print "                buffer + sizeof(num_members_), bitmap_); \\\n";
        print "            offset = sizeof(num_members_) + sizeof(bitmap_); \\\n";
        for($j = 1; $j <= $i; ++$j)
        {
            print "            if (maxsize < offset + sizeof(var$j\_)) { return false; } \\\n";
            print "            byte::FixedInt::Encode<type$j>(buffer + offset, var$j\_); \\\n";
            print "            offset += sizeof(var$j\_); \\\n";
        }
        print "            *size = offset; \\\n";
        print "            return true; \\\n";
        print "        } \\\n";
        print "        bool deserialize(const char* buffer, \\\n";
        print "                         uint32_t size, \\\n";
        print "                         uint32_t* ret) override { \\\n";
        print "            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \\\n";
        print "                return false; \\\n";
        print "            } \\\n";
        print "            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \\\n";
        print "            size_t offset = sizeof(num_members_); \\\n";
        print "            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \\\n";
        print "            offset += sizeof(bitmap_); \\\n";
        print "            *ret = offset; \\\n";
        print "            if (num_members-- == 0) { return true; } \\\n";
        for($j = 1; $j <= $i; ++$j)
        {
            print "            if (offset + sizeof(var$j\_) > size) { return true; } \\\n";
            print "            var$j\_ = byte::FixedInt::Decode<type$j>(buffer + offset); \\\n";
            print "            offset += sizeof(var$j\_); \\\n";
            print "            *ret = offset; \\\n";
            print "            if (num_members-- == 0) { return true; } \\\n";
        }
        print "            return true; \\\n";
        print "        } \\\n    \\\n";
        print "    private: \\\n";
        for($j = 1; $j <= $i; ++$j)
        {
            print "        type$j var$j\_ = {}; \\\n";
        }
        print "        uint32_t bitmap_; \\\n";
        print "        const uint32_t num_members_ = $i; \\\n";
        print "    }\n";

        print "\n// for $i argument(s)\n";
        print "#define CLASS_VARIANT_PLAIN_BUFFER_$i(name";
        for($j = 1; $j <= $i; ++$j)
        {
            print ", var$j";
        }
        print ") \\\n";
        print "    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<$i> { \\\n";
        print "    public: \\\n";
        print "        VariantPlainBuffer_##name() {} \\\n";
        print "        virtual ~VariantPlainBuffer_##name() {} \\\n";
        for($j = 1; $j <= $i; ++$j)
        {
            $t = $j - 1;
            print "        byte::StringPiece var$j() const { return data_[$t]; } \\\n";
            print "        byte::StringPiece* mutable_##var$j() { return &data_[$t]; } \\\n";
            print "        void set_##var$j(const char* name, size_t size) { \\\n";
            print "            data_[$t].set(name, size); \\\n";
            print "        } \\\n";
            print "        void clear_##var$j() { data_[$t].clear(); } \\\n";
            print "        bool has_##var$j() const { return !data_[$t].empty(); } \\\n";
        }
        print "    }\n";
    }
}

sub PrintPlainBufferMessage
{
    print "\n\n";
    print "struct PlainBufferHeader {\n";
    print "    uint32_t magic_;\n";
    print "    uint32_t checksum_length_;  // 0 representing old version or no checksum enabled\n";
    print "    uint32_t checksum_;\n";
    print "\n";
    print "    PlainBufferHeader() : magic_(PLAIN_BUFFER_MAGIC), checksum_length_(0), checksum_(0) {}\n";
    print "};\n";
    print "\n";
    print "static_assert(sizeof(PlainBufferHeader) == 12, \"sizeof(PlainBufferHeader) should bbe 12\");\n";
    print "\n";
    print "template <typename Fixed, typename Variant>\n";
    print "class PlainBufferMessage {\n";
    print "public:\n";
    print "    PlainBufferMessage() : enable_checksum_(false) {}\n";
    print "\n";
    print "    ~PlainBufferMessage() {}\n";
    print "\n";
    print "    uint32_t ByteSize() const {\n";
    print "        return sizeof(PlainBufferHeader) + sizeof(uint32_t) * 2 +\n";
    print "            fixed_buf_.ByteSize() + variant_buf_.ByteSize();\n";
    print "    }\n";
    print "\n";
    print "    bool serialize(char* buffer, uint32_t max_size, uint32_t* buf_size, int* err = nullptr) const {\n";
    print "        PlainBufferHeader header;\n";
    print "        uint32_t byte_size = 0;\n";
    print "        if (max_size < sizeof(header) + 2 * sizeof(byte_size)) {\n";
    print "            PTR_SET_VALUE(err, -1);\n";
    print "            return false;\n";
    print "        }\n";
    print "        memcpy(buffer, &header, sizeof(header));\n";
    print "        uint32_t offset = sizeof(header);\n";
    print "        max_size -= sizeof(header);\n";
    print "        byte_size = fixed_buf_.ByteSize();\n";
    print "        memcpy(buffer + offset, &byte_size, sizeof(byte_size));\n";
    print "        offset += sizeof(byte_size);\n";
    print "        max_size -= sizeof(byte_size);\n";
    print "        uint32_t size = 0;\n";
    print "        if (!fixed_buf_.serialize(buffer + offset, max_size, &size)) {\n";
    print "            PTR_SET_VALUE(err, -2);\n";
    print "            return false;\n";
    print "        }\n";
    print "        offset += size;\n";
    print "        BYTE_ASSERT_GE(max_size, size);\n";
    print "        max_size -= size;\n";
    print "        byte_size = variant_buf_.ByteSize();\n";
    print "        memcpy(buffer + offset, &byte_size, sizeof(byte_size));\n";
    print "        offset += sizeof(byte_size);\n";
    print "        max_size -= sizeof(byte_size);\n";
    print "        if (!variant_buf_.serialize(buffer + offset, max_size, &size)) {\n";
    print "            PTR_SET_VALUE(err, -3);\n";
    print "            return false;\n";
    print "        }\n";
    print "        offset += size;\n";
    print "        if (enable_checksum_) {\n";
    print "            (reinterpret_cast<PlainBufferHeader*>(buffer))->checksum_ =\n";
    print "                byte::Checksum32(buffer + sizeof(header), offset - sizeof(header));\n";
    print "            (reinterpret_cast<PlainBufferHeader*>(buffer))->checksum_length_ =\n";
    print "                offset - sizeof(header);\n";
    print "        }\n";
    print "        BYTE_ASSERT_GE(max_size, size);\n";
    print "        *buf_size = offset;\n";
    print "        return true;\n";
    print "    }\n";
    print "\n";
    print "    bool deserialize(const char* buffer, uint32_t size, int* err = nullptr) {\n";
    print "        PlainBufferHeader header;\n";
    print "        if (size < sizeof(header)) {\n";
    print "            PTR_SET_VALUE(err, -101);\n";
    print "            return false;\n";
    print "        }\n";
    print "        if ((reinterpret_cast<const PlainBufferHeader*>(buffer))->magic_ != kMagic) {\n";
    print "            PTR_SET_VALUE(err, -102);\n";
    print "            return false;\n";
    print "        }\n";
    print "        const uint32_t checksum_length =\n";
    print "            (reinterpret_cast<const PlainBufferHeader*>(buffer))->checksum_length_;\n";
    print "        const bool enable_checksum = (checksum_length == 0) ? false : enable_checksum_;\n";
    print "        if (enable_checksum &&\n";
    print "            (reinterpret_cast<const PlainBufferHeader*>(buffer))->checksum_ !=\n";
    print "            byte::Checksum32(buffer + sizeof(header), checksum_length)) {\n";
    print "            PTR_SET_VALUE(err, -103);\n";
    print "            return false;\n";
    print "        }\n";
    print "        uint32_t offset = sizeof(header);\n";
    print "        size -= sizeof(header);\n";
    print "        uint32_t byte_size = *(reinterpret_cast<const uint32_t*>(buffer + offset));\n";
    print "        uint32_t ret = 0;\n";
    print "        offset += sizeof(byte_size);\n";
    print "        if (!fixed_buf_.deserialize(buffer + offset, size, &ret)) {\n";
    print "            PTR_SET_VALUE(err, -104);\n";
    print "            return false;\n";
    print "        }\n";
    print "        offset += byte_size;\n";
    print "        size -= byte_size;\n";
    print "        offset += sizeof(byte_size);\n";
    print "        size -= sizeof(byte_size);\n";
    print "        if (!variant_buf_.deserialize(buffer + offset, size, &ret)) {\n";
    print "            PTR_SET_VALUE(err, -105);\n";
    print "            return false;\n";
    print "        }\n";
    print "        return true;\n";
    print "    }\n";
    print "\n";
    print "public:\n";
    print "    static const uint32_t kMagic = PLAIN_BUFFER_MAGIC;\n";
    print "\n";
    print "    Fixed fixed_buf_;\n";
    print "    Variant variant_buf_;\n";
    print "    bool enable_checksum_;\n";
    print "};\n";
}

sub PrintTail 
{
    print "\n";
    print "}  // namespace byte\n";
}

PrintHead();
PrintPlainBufferBase();
PrintPlainBuffer();
PrintPlainBufferMessage();
PrintTail()
