// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#pragma once

/////////////////////////////////////////////////////
// DO NOT EDIT!!!
// This header file is auto generated by perl script
/////////////////////////////////////////////////////

// GLOBAL_NOLINT(whitespace/line_length)
// GLOBAL_NOLINT(runtime/explicit)

#include <stdint.h>
#include <vector>
#include "byte/algorithm/checksum.h"
#include "byte/encoding/variant_int.h"
#include "byte/include/assert.h"
#include "byte/string/string_piece.h"

#define PLAIN_BUFFER_MAGIC 0x803c9e17

namespace byte {
class PlainBufferBase {
public:
    PlainBufferBase() {}
    virtual ~PlainBufferBase() {}
    virtual bool serialize(char* buffer, uint32_t maxsize, uint32_t* size) const = 0;
    virtual bool deserialize(const char* buffer, uint32_t size, uint32_t* ret) = 0;
};

template <size_t N>
class VariantPlainBufferBase : public byte::PlainBufferBase {
public:
    VariantPlainBufferBase() : num_members_(N) {}
    virtual ~VariantPlainBufferBase() {}
    size_t ByteSize() const {
        size_t size = sizeof(num_members_);
        for (uint32_t i = 0; i < num_members_; ++i) {
            size += (sizeof(uint32_t) + data_[i].size());
        }
        return size;
    }
    bool serialize(char* buffer, uint32_t maxsize, uint32_t* size) const override {
        if (maxsize < sizeof(num_members_)) {
            return false;
        }
        size_t offset = 0;
        offset = sizeof(num_members_);
        byte::FixedInt::Encode<uint32_t>(buffer, num_members_);
        if (maxsize < sizeof(num_members_) + num_members_ * sizeof(uint32_t)) {
            return false;
        }
        for (uint32_t i = 0; i < num_members_; ++i) {
            byte::FixedInt::Encode<uint32_t>(buffer + offset, data_[i].size());
            offset += sizeof(uint32_t);
            const byte::StringPiece& data = data_[i];
            if (maxsize < offset + data.size()) {
                return false;
            }
            memcpy(buffer + offset, data.data(), data.size());
            offset += data.size();
        }
        *size = offset;
        return true;
    }
    bool deserialize(const char* buffer, uint32_t size, uint32_t* ret) override {
        if (size < sizeof(size)) { return false; }
        uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer);
        size_t offset = sizeof(num_members);
        uint32_t sz = 0;
        if (num_members > num_members_) { num_members = num_members_; }
        for (uint32_t i = 0; i < num_members; ++i) {
            if (offset + sizeof(sz) > size) { return false; }
            sz = byte::FixedInt::Decode<uint32_t>(buffer + offset);
            offset += sizeof(sz);
            if (offset + sz > size) { return false; }
            data_[i].set(buffer + offset, sz);
            offset += sz;
        }
        *ret = offset;
        return true;
    }

protected:
    byte::StringPiece data_[N];
    uint32_t num_members_;
};


// for 0 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_0(name) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 0; \
    }

// for 0 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_0(name) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<0> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
    }

// for 1 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_1(name, type1, var1) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 1; \
    }

// for 1 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_1(name, var1) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<1> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
    }

// for 2 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_2(name, type1, var1, type2, var2) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 2; \
    }

// for 2 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_2(name, var1, var2) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<2> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
    }

// for 3 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_3(name, type1, var1, type2, var2, type3, var3) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 3; \
    }

// for 3 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_3(name, var1, var2, var3) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<3> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
    }

// for 4 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_4(name, type1, var1, type2, var2, type3, var3, type4, var4) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 4; \
    }

// for 4 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_4(name, var1, var2, var3, var4) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<4> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
    }

// for 5 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_5(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 5; \
    }

// for 5 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_5(name, var1, var2, var3, var4, var5) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<5> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
    }

// for 6 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_6(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 6; \
    }

// for 6 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_6(name, var1, var2, var3, var4, var5, var6) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<6> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
    }

// for 7 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_7(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 7; \
    }

// for 7 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_7(name, var1, var2, var3, var4, var5, var6, var7) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<7> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
    }

// for 8 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_8(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 8; \
    }

// for 8 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_8(name, var1, var2, var3, var4, var5, var6, var7, var8) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<8> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
    }

// for 9 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_9(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 9; \
    }

// for 9 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_9(name, var1, var2, var3, var4, var5, var6, var7, var8, var9) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<9> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
    }

// for 10 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_10(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 10; \
    }

// for 10 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_10(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<10> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
    }

// for 11 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_11(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 11; \
    }

// for 11 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_11(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<11> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
    }

// for 12 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_12(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 12; \
    }

// for 12 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_12(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<12> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
    }

// for 13 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_13(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 13; \
    }

// for 13 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_13(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<13> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
    }

// for 14 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_14(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 14; \
    }

// for 14 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_14(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<14> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
    }

// for 15 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_15(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14, type15, var15) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        type15 var15() const { return var15_; } \
        type15* mutable_##var15() { \
            bitmap_ |= 16384; \
            return &var15_; \
        } \
        void set_##var15(const type15& var) { \
            var15_ = var; \
            bitmap_ |= 16384; \
        } \
        void clear_##var15() { bitmap_ &= ~16384; } \
        bool has_##var15() const { return (bitmap_ & 16384) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            size += sizeof(var15_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            if (maxsize < offset + sizeof(var15_)) { return false; } \
            byte::FixedInt::Encode<type15>(buffer + offset, var15_); \
            offset += sizeof(var15_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var15_) > size) { return true; } \
            var15_ = byte::FixedInt::Decode<type15>(buffer + offset); \
            offset += sizeof(var15_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        type15 var15_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 15; \
    }

// for 15 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_15(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14, var15) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<15> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
        byte::StringPiece var15() const { return data_[14]; } \
        byte::StringPiece* mutable_##var15() { return &data_[14]; } \
        void set_##var15(const char* name, size_t size) { \
            data_[14].set(name, size); \
        } \
        void clear_##var15() { data_[14].clear(); } \
        bool has_##var15() const { return !data_[14].empty(); } \
    }

// for 16 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_16(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14, type15, var15, type16, var16) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        type15 var15() const { return var15_; } \
        type15* mutable_##var15() { \
            bitmap_ |= 16384; \
            return &var15_; \
        } \
        void set_##var15(const type15& var) { \
            var15_ = var; \
            bitmap_ |= 16384; \
        } \
        void clear_##var15() { bitmap_ &= ~16384; } \
        bool has_##var15() const { return (bitmap_ & 16384) != 0; } \
        type16 var16() const { return var16_; } \
        type16* mutable_##var16() { \
            bitmap_ |= 32768; \
            return &var16_; \
        } \
        void set_##var16(const type16& var) { \
            var16_ = var; \
            bitmap_ |= 32768; \
        } \
        void clear_##var16() { bitmap_ &= ~32768; } \
        bool has_##var16() const { return (bitmap_ & 32768) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            size += sizeof(var15_);\
            size += sizeof(var16_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            if (maxsize < offset + sizeof(var15_)) { return false; } \
            byte::FixedInt::Encode<type15>(buffer + offset, var15_); \
            offset += sizeof(var15_); \
            if (maxsize < offset + sizeof(var16_)) { return false; } \
            byte::FixedInt::Encode<type16>(buffer + offset, var16_); \
            offset += sizeof(var16_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var15_) > size) { return true; } \
            var15_ = byte::FixedInt::Decode<type15>(buffer + offset); \
            offset += sizeof(var15_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var16_) > size) { return true; } \
            var16_ = byte::FixedInt::Decode<type16>(buffer + offset); \
            offset += sizeof(var16_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        type15 var15_ = {}; \
        type16 var16_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 16; \
    }

// for 16 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_16(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14, var15, var16) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<16> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
        byte::StringPiece var15() const { return data_[14]; } \
        byte::StringPiece* mutable_##var15() { return &data_[14]; } \
        void set_##var15(const char* name, size_t size) { \
            data_[14].set(name, size); \
        } \
        void clear_##var15() { data_[14].clear(); } \
        bool has_##var15() const { return !data_[14].empty(); } \
        byte::StringPiece var16() const { return data_[15]; } \
        byte::StringPiece* mutable_##var16() { return &data_[15]; } \
        void set_##var16(const char* name, size_t size) { \
            data_[15].set(name, size); \
        } \
        void clear_##var16() { data_[15].clear(); } \
        bool has_##var16() const { return !data_[15].empty(); } \
    }

// for 17 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_17(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14, type15, var15, type16, var16, type17, var17) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        type15 var15() const { return var15_; } \
        type15* mutable_##var15() { \
            bitmap_ |= 16384; \
            return &var15_; \
        } \
        void set_##var15(const type15& var) { \
            var15_ = var; \
            bitmap_ |= 16384; \
        } \
        void clear_##var15() { bitmap_ &= ~16384; } \
        bool has_##var15() const { return (bitmap_ & 16384) != 0; } \
        type16 var16() const { return var16_; } \
        type16* mutable_##var16() { \
            bitmap_ |= 32768; \
            return &var16_; \
        } \
        void set_##var16(const type16& var) { \
            var16_ = var; \
            bitmap_ |= 32768; \
        } \
        void clear_##var16() { bitmap_ &= ~32768; } \
        bool has_##var16() const { return (bitmap_ & 32768) != 0; } \
        type17 var17() const { return var17_; } \
        type17* mutable_##var17() { \
            bitmap_ |= 65536; \
            return &var17_; \
        } \
        void set_##var17(const type17& var) { \
            var17_ = var; \
            bitmap_ |= 65536; \
        } \
        void clear_##var17() { bitmap_ &= ~65536; } \
        bool has_##var17() const { return (bitmap_ & 65536) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            size += sizeof(var15_);\
            size += sizeof(var16_);\
            size += sizeof(var17_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            if (maxsize < offset + sizeof(var15_)) { return false; } \
            byte::FixedInt::Encode<type15>(buffer + offset, var15_); \
            offset += sizeof(var15_); \
            if (maxsize < offset + sizeof(var16_)) { return false; } \
            byte::FixedInt::Encode<type16>(buffer + offset, var16_); \
            offset += sizeof(var16_); \
            if (maxsize < offset + sizeof(var17_)) { return false; } \
            byte::FixedInt::Encode<type17>(buffer + offset, var17_); \
            offset += sizeof(var17_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var15_) > size) { return true; } \
            var15_ = byte::FixedInt::Decode<type15>(buffer + offset); \
            offset += sizeof(var15_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var16_) > size) { return true; } \
            var16_ = byte::FixedInt::Decode<type16>(buffer + offset); \
            offset += sizeof(var16_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var17_) > size) { return true; } \
            var17_ = byte::FixedInt::Decode<type17>(buffer + offset); \
            offset += sizeof(var17_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        type15 var15_ = {}; \
        type16 var16_ = {}; \
        type17 var17_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 17; \
    }

// for 17 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_17(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14, var15, var16, var17) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<17> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
        byte::StringPiece var15() const { return data_[14]; } \
        byte::StringPiece* mutable_##var15() { return &data_[14]; } \
        void set_##var15(const char* name, size_t size) { \
            data_[14].set(name, size); \
        } \
        void clear_##var15() { data_[14].clear(); } \
        bool has_##var15() const { return !data_[14].empty(); } \
        byte::StringPiece var16() const { return data_[15]; } \
        byte::StringPiece* mutable_##var16() { return &data_[15]; } \
        void set_##var16(const char* name, size_t size) { \
            data_[15].set(name, size); \
        } \
        void clear_##var16() { data_[15].clear(); } \
        bool has_##var16() const { return !data_[15].empty(); } \
        byte::StringPiece var17() const { return data_[16]; } \
        byte::StringPiece* mutable_##var17() { return &data_[16]; } \
        void set_##var17(const char* name, size_t size) { \
            data_[16].set(name, size); \
        } \
        void clear_##var17() { data_[16].clear(); } \
        bool has_##var17() const { return !data_[16].empty(); } \
    }

// for 18 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_18(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14, type15, var15, type16, var16, type17, var17, type18, var18) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        type15 var15() const { return var15_; } \
        type15* mutable_##var15() { \
            bitmap_ |= 16384; \
            return &var15_; \
        } \
        void set_##var15(const type15& var) { \
            var15_ = var; \
            bitmap_ |= 16384; \
        } \
        void clear_##var15() { bitmap_ &= ~16384; } \
        bool has_##var15() const { return (bitmap_ & 16384) != 0; } \
        type16 var16() const { return var16_; } \
        type16* mutable_##var16() { \
            bitmap_ |= 32768; \
            return &var16_; \
        } \
        void set_##var16(const type16& var) { \
            var16_ = var; \
            bitmap_ |= 32768; \
        } \
        void clear_##var16() { bitmap_ &= ~32768; } \
        bool has_##var16() const { return (bitmap_ & 32768) != 0; } \
        type17 var17() const { return var17_; } \
        type17* mutable_##var17() { \
            bitmap_ |= 65536; \
            return &var17_; \
        } \
        void set_##var17(const type17& var) { \
            var17_ = var; \
            bitmap_ |= 65536; \
        } \
        void clear_##var17() { bitmap_ &= ~65536; } \
        bool has_##var17() const { return (bitmap_ & 65536) != 0; } \
        type18 var18() const { return var18_; } \
        type18* mutable_##var18() { \
            bitmap_ |= 131072; \
            return &var18_; \
        } \
        void set_##var18(const type18& var) { \
            var18_ = var; \
            bitmap_ |= 131072; \
        } \
        void clear_##var18() { bitmap_ &= ~131072; } \
        bool has_##var18() const { return (bitmap_ & 131072) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            size += sizeof(var15_);\
            size += sizeof(var16_);\
            size += sizeof(var17_);\
            size += sizeof(var18_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            if (maxsize < offset + sizeof(var15_)) { return false; } \
            byte::FixedInt::Encode<type15>(buffer + offset, var15_); \
            offset += sizeof(var15_); \
            if (maxsize < offset + sizeof(var16_)) { return false; } \
            byte::FixedInt::Encode<type16>(buffer + offset, var16_); \
            offset += sizeof(var16_); \
            if (maxsize < offset + sizeof(var17_)) { return false; } \
            byte::FixedInt::Encode<type17>(buffer + offset, var17_); \
            offset += sizeof(var17_); \
            if (maxsize < offset + sizeof(var18_)) { return false; } \
            byte::FixedInt::Encode<type18>(buffer + offset, var18_); \
            offset += sizeof(var18_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var15_) > size) { return true; } \
            var15_ = byte::FixedInt::Decode<type15>(buffer + offset); \
            offset += sizeof(var15_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var16_) > size) { return true; } \
            var16_ = byte::FixedInt::Decode<type16>(buffer + offset); \
            offset += sizeof(var16_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var17_) > size) { return true; } \
            var17_ = byte::FixedInt::Decode<type17>(buffer + offset); \
            offset += sizeof(var17_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var18_) > size) { return true; } \
            var18_ = byte::FixedInt::Decode<type18>(buffer + offset); \
            offset += sizeof(var18_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        type15 var15_ = {}; \
        type16 var16_ = {}; \
        type17 var17_ = {}; \
        type18 var18_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 18; \
    }

// for 18 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_18(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14, var15, var16, var17, var18) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<18> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
        byte::StringPiece var15() const { return data_[14]; } \
        byte::StringPiece* mutable_##var15() { return &data_[14]; } \
        void set_##var15(const char* name, size_t size) { \
            data_[14].set(name, size); \
        } \
        void clear_##var15() { data_[14].clear(); } \
        bool has_##var15() const { return !data_[14].empty(); } \
        byte::StringPiece var16() const { return data_[15]; } \
        byte::StringPiece* mutable_##var16() { return &data_[15]; } \
        void set_##var16(const char* name, size_t size) { \
            data_[15].set(name, size); \
        } \
        void clear_##var16() { data_[15].clear(); } \
        bool has_##var16() const { return !data_[15].empty(); } \
        byte::StringPiece var17() const { return data_[16]; } \
        byte::StringPiece* mutable_##var17() { return &data_[16]; } \
        void set_##var17(const char* name, size_t size) { \
            data_[16].set(name, size); \
        } \
        void clear_##var17() { data_[16].clear(); } \
        bool has_##var17() const { return !data_[16].empty(); } \
        byte::StringPiece var18() const { return data_[17]; } \
        byte::StringPiece* mutable_##var18() { return &data_[17]; } \
        void set_##var18(const char* name, size_t size) { \
            data_[17].set(name, size); \
        } \
        void clear_##var18() { data_[17].clear(); } \
        bool has_##var18() const { return !data_[17].empty(); } \
    }

// for 19 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_19(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14, type15, var15, type16, var16, type17, var17, type18, var18, type19, var19) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        type15 var15() const { return var15_; } \
        type15* mutable_##var15() { \
            bitmap_ |= 16384; \
            return &var15_; \
        } \
        void set_##var15(const type15& var) { \
            var15_ = var; \
            bitmap_ |= 16384; \
        } \
        void clear_##var15() { bitmap_ &= ~16384; } \
        bool has_##var15() const { return (bitmap_ & 16384) != 0; } \
        type16 var16() const { return var16_; } \
        type16* mutable_##var16() { \
            bitmap_ |= 32768; \
            return &var16_; \
        } \
        void set_##var16(const type16& var) { \
            var16_ = var; \
            bitmap_ |= 32768; \
        } \
        void clear_##var16() { bitmap_ &= ~32768; } \
        bool has_##var16() const { return (bitmap_ & 32768) != 0; } \
        type17 var17() const { return var17_; } \
        type17* mutable_##var17() { \
            bitmap_ |= 65536; \
            return &var17_; \
        } \
        void set_##var17(const type17& var) { \
            var17_ = var; \
            bitmap_ |= 65536; \
        } \
        void clear_##var17() { bitmap_ &= ~65536; } \
        bool has_##var17() const { return (bitmap_ & 65536) != 0; } \
        type18 var18() const { return var18_; } \
        type18* mutable_##var18() { \
            bitmap_ |= 131072; \
            return &var18_; \
        } \
        void set_##var18(const type18& var) { \
            var18_ = var; \
            bitmap_ |= 131072; \
        } \
        void clear_##var18() { bitmap_ &= ~131072; } \
        bool has_##var18() const { return (bitmap_ & 131072) != 0; } \
        type19 var19() const { return var19_; } \
        type19* mutable_##var19() { \
            bitmap_ |= 262144; \
            return &var19_; \
        } \
        void set_##var19(const type19& var) { \
            var19_ = var; \
            bitmap_ |= 262144; \
        } \
        void clear_##var19() { bitmap_ &= ~262144; } \
        bool has_##var19() const { return (bitmap_ & 262144) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            size += sizeof(var15_);\
            size += sizeof(var16_);\
            size += sizeof(var17_);\
            size += sizeof(var18_);\
            size += sizeof(var19_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            if (maxsize < offset + sizeof(var15_)) { return false; } \
            byte::FixedInt::Encode<type15>(buffer + offset, var15_); \
            offset += sizeof(var15_); \
            if (maxsize < offset + sizeof(var16_)) { return false; } \
            byte::FixedInt::Encode<type16>(buffer + offset, var16_); \
            offset += sizeof(var16_); \
            if (maxsize < offset + sizeof(var17_)) { return false; } \
            byte::FixedInt::Encode<type17>(buffer + offset, var17_); \
            offset += sizeof(var17_); \
            if (maxsize < offset + sizeof(var18_)) { return false; } \
            byte::FixedInt::Encode<type18>(buffer + offset, var18_); \
            offset += sizeof(var18_); \
            if (maxsize < offset + sizeof(var19_)) { return false; } \
            byte::FixedInt::Encode<type19>(buffer + offset, var19_); \
            offset += sizeof(var19_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var15_) > size) { return true; } \
            var15_ = byte::FixedInt::Decode<type15>(buffer + offset); \
            offset += sizeof(var15_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var16_) > size) { return true; } \
            var16_ = byte::FixedInt::Decode<type16>(buffer + offset); \
            offset += sizeof(var16_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var17_) > size) { return true; } \
            var17_ = byte::FixedInt::Decode<type17>(buffer + offset); \
            offset += sizeof(var17_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var18_) > size) { return true; } \
            var18_ = byte::FixedInt::Decode<type18>(buffer + offset); \
            offset += sizeof(var18_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var19_) > size) { return true; } \
            var19_ = byte::FixedInt::Decode<type19>(buffer + offset); \
            offset += sizeof(var19_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        type15 var15_ = {}; \
        type16 var16_ = {}; \
        type17 var17_ = {}; \
        type18 var18_ = {}; \
        type19 var19_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 19; \
    }

// for 19 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_19(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14, var15, var16, var17, var18, var19) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<19> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
        byte::StringPiece var15() const { return data_[14]; } \
        byte::StringPiece* mutable_##var15() { return &data_[14]; } \
        void set_##var15(const char* name, size_t size) { \
            data_[14].set(name, size); \
        } \
        void clear_##var15() { data_[14].clear(); } \
        bool has_##var15() const { return !data_[14].empty(); } \
        byte::StringPiece var16() const { return data_[15]; } \
        byte::StringPiece* mutable_##var16() { return &data_[15]; } \
        void set_##var16(const char* name, size_t size) { \
            data_[15].set(name, size); \
        } \
        void clear_##var16() { data_[15].clear(); } \
        bool has_##var16() const { return !data_[15].empty(); } \
        byte::StringPiece var17() const { return data_[16]; } \
        byte::StringPiece* mutable_##var17() { return &data_[16]; } \
        void set_##var17(const char* name, size_t size) { \
            data_[16].set(name, size); \
        } \
        void clear_##var17() { data_[16].clear(); } \
        bool has_##var17() const { return !data_[16].empty(); } \
        byte::StringPiece var18() const { return data_[17]; } \
        byte::StringPiece* mutable_##var18() { return &data_[17]; } \
        void set_##var18(const char* name, size_t size) { \
            data_[17].set(name, size); \
        } \
        void clear_##var18() { data_[17].clear(); } \
        bool has_##var18() const { return !data_[17].empty(); } \
        byte::StringPiece var19() const { return data_[18]; } \
        byte::StringPiece* mutable_##var19() { return &data_[18]; } \
        void set_##var19(const char* name, size_t size) { \
            data_[18].set(name, size); \
        } \
        void clear_##var19() { data_[18].clear(); } \
        bool has_##var19() const { return !data_[18].empty(); } \
    }

// for 20 argument(s)
#define CLASS_FIXED_PLAIN_BUFFER_20(name, type1, var1, type2, var2, type3, var3, type4, var4, type5, var5, type6, var6, type7, var7, type8, var8, type9, var9, type10, var10, type11, var11, type12, var12, type13, var13, type14, var14, type15, var15, type16, var16, type17, var17, type18, var18, type19, var19, type20, var20) \
    class FixedPlainBuffer_##name : public byte::PlainBufferBase { \
    public: \
        FixedPlainBuffer_##name() : bitmap_(0) {} \
        virtual ~FixedPlainBuffer_##name() {} \
        type1 var1() const { return var1_; } \
        type1* mutable_##var1() { \
            bitmap_ |= 1; \
            return &var1_; \
        } \
        void set_##var1(const type1& var) { \
            var1_ = var; \
            bitmap_ |= 1; \
        } \
        void clear_##var1() { bitmap_ &= ~1; } \
        bool has_##var1() const { return (bitmap_ & 1) != 0; } \
        type2 var2() const { return var2_; } \
        type2* mutable_##var2() { \
            bitmap_ |= 2; \
            return &var2_; \
        } \
        void set_##var2(const type2& var) { \
            var2_ = var; \
            bitmap_ |= 2; \
        } \
        void clear_##var2() { bitmap_ &= ~2; } \
        bool has_##var2() const { return (bitmap_ & 2) != 0; } \
        type3 var3() const { return var3_; } \
        type3* mutable_##var3() { \
            bitmap_ |= 4; \
            return &var3_; \
        } \
        void set_##var3(const type3& var) { \
            var3_ = var; \
            bitmap_ |= 4; \
        } \
        void clear_##var3() { bitmap_ &= ~4; } \
        bool has_##var3() const { return (bitmap_ & 4) != 0; } \
        type4 var4() const { return var4_; } \
        type4* mutable_##var4() { \
            bitmap_ |= 8; \
            return &var4_; \
        } \
        void set_##var4(const type4& var) { \
            var4_ = var; \
            bitmap_ |= 8; \
        } \
        void clear_##var4() { bitmap_ &= ~8; } \
        bool has_##var4() const { return (bitmap_ & 8) != 0; } \
        type5 var5() const { return var5_; } \
        type5* mutable_##var5() { \
            bitmap_ |= 16; \
            return &var5_; \
        } \
        void set_##var5(const type5& var) { \
            var5_ = var; \
            bitmap_ |= 16; \
        } \
        void clear_##var5() { bitmap_ &= ~16; } \
        bool has_##var5() const { return (bitmap_ & 16) != 0; } \
        type6 var6() const { return var6_; } \
        type6* mutable_##var6() { \
            bitmap_ |= 32; \
            return &var6_; \
        } \
        void set_##var6(const type6& var) { \
            var6_ = var; \
            bitmap_ |= 32; \
        } \
        void clear_##var6() { bitmap_ &= ~32; } \
        bool has_##var6() const { return (bitmap_ & 32) != 0; } \
        type7 var7() const { return var7_; } \
        type7* mutable_##var7() { \
            bitmap_ |= 64; \
            return &var7_; \
        } \
        void set_##var7(const type7& var) { \
            var7_ = var; \
            bitmap_ |= 64; \
        } \
        void clear_##var7() { bitmap_ &= ~64; } \
        bool has_##var7() const { return (bitmap_ & 64) != 0; } \
        type8 var8() const { return var8_; } \
        type8* mutable_##var8() { \
            bitmap_ |= 128; \
            return &var8_; \
        } \
        void set_##var8(const type8& var) { \
            var8_ = var; \
            bitmap_ |= 128; \
        } \
        void clear_##var8() { bitmap_ &= ~128; } \
        bool has_##var8() const { return (bitmap_ & 128) != 0; } \
        type9 var9() const { return var9_; } \
        type9* mutable_##var9() { \
            bitmap_ |= 256; \
            return &var9_; \
        } \
        void set_##var9(const type9& var) { \
            var9_ = var; \
            bitmap_ |= 256; \
        } \
        void clear_##var9() { bitmap_ &= ~256; } \
        bool has_##var9() const { return (bitmap_ & 256) != 0; } \
        type10 var10() const { return var10_; } \
        type10* mutable_##var10() { \
            bitmap_ |= 512; \
            return &var10_; \
        } \
        void set_##var10(const type10& var) { \
            var10_ = var; \
            bitmap_ |= 512; \
        } \
        void clear_##var10() { bitmap_ &= ~512; } \
        bool has_##var10() const { return (bitmap_ & 512) != 0; } \
        type11 var11() const { return var11_; } \
        type11* mutable_##var11() { \
            bitmap_ |= 1024; \
            return &var11_; \
        } \
        void set_##var11(const type11& var) { \
            var11_ = var; \
            bitmap_ |= 1024; \
        } \
        void clear_##var11() { bitmap_ &= ~1024; } \
        bool has_##var11() const { return (bitmap_ & 1024) != 0; } \
        type12 var12() const { return var12_; } \
        type12* mutable_##var12() { \
            bitmap_ |= 2048; \
            return &var12_; \
        } \
        void set_##var12(const type12& var) { \
            var12_ = var; \
            bitmap_ |= 2048; \
        } \
        void clear_##var12() { bitmap_ &= ~2048; } \
        bool has_##var12() const { return (bitmap_ & 2048) != 0; } \
        type13 var13() const { return var13_; } \
        type13* mutable_##var13() { \
            bitmap_ |= 4096; \
            return &var13_; \
        } \
        void set_##var13(const type13& var) { \
            var13_ = var; \
            bitmap_ |= 4096; \
        } \
        void clear_##var13() { bitmap_ &= ~4096; } \
        bool has_##var13() const { return (bitmap_ & 4096) != 0; } \
        type14 var14() const { return var14_; } \
        type14* mutable_##var14() { \
            bitmap_ |= 8192; \
            return &var14_; \
        } \
        void set_##var14(const type14& var) { \
            var14_ = var; \
            bitmap_ |= 8192; \
        } \
        void clear_##var14() { bitmap_ &= ~8192; } \
        bool has_##var14() const { return (bitmap_ & 8192) != 0; } \
        type15 var15() const { return var15_; } \
        type15* mutable_##var15() { \
            bitmap_ |= 16384; \
            return &var15_; \
        } \
        void set_##var15(const type15& var) { \
            var15_ = var; \
            bitmap_ |= 16384; \
        } \
        void clear_##var15() { bitmap_ &= ~16384; } \
        bool has_##var15() const { return (bitmap_ & 16384) != 0; } \
        type16 var16() const { return var16_; } \
        type16* mutable_##var16() { \
            bitmap_ |= 32768; \
            return &var16_; \
        } \
        void set_##var16(const type16& var) { \
            var16_ = var; \
            bitmap_ |= 32768; \
        } \
        void clear_##var16() { bitmap_ &= ~32768; } \
        bool has_##var16() const { return (bitmap_ & 32768) != 0; } \
        type17 var17() const { return var17_; } \
        type17* mutable_##var17() { \
            bitmap_ |= 65536; \
            return &var17_; \
        } \
        void set_##var17(const type17& var) { \
            var17_ = var; \
            bitmap_ |= 65536; \
        } \
        void clear_##var17() { bitmap_ &= ~65536; } \
        bool has_##var17() const { return (bitmap_ & 65536) != 0; } \
        type18 var18() const { return var18_; } \
        type18* mutable_##var18() { \
            bitmap_ |= 131072; \
            return &var18_; \
        } \
        void set_##var18(const type18& var) { \
            var18_ = var; \
            bitmap_ |= 131072; \
        } \
        void clear_##var18() { bitmap_ &= ~131072; } \
        bool has_##var18() const { return (bitmap_ & 131072) != 0; } \
        type19 var19() const { return var19_; } \
        type19* mutable_##var19() { \
            bitmap_ |= 262144; \
            return &var19_; \
        } \
        void set_##var19(const type19& var) { \
            var19_ = var; \
            bitmap_ |= 262144; \
        } \
        void clear_##var19() { bitmap_ &= ~262144; } \
        bool has_##var19() const { return (bitmap_ & 262144) != 0; } \
        type20 var20() const { return var20_; } \
        type20* mutable_##var20() { \
            bitmap_ |= 524288; \
            return &var20_; \
        } \
        void set_##var20(const type20& var) { \
            var20_ = var; \
            bitmap_ |= 524288; \
        } \
        void clear_##var20() { bitmap_ &= ~524288; } \
        bool has_##var20() const { return (bitmap_ & 524288) != 0; } \
        size_t ByteSize() const { \
            size_t size = sizeof(num_members_) + sizeof(bitmap_); \
            size += sizeof(var1_);\
            size += sizeof(var2_);\
            size += sizeof(var3_);\
            size += sizeof(var4_);\
            size += sizeof(var5_);\
            size += sizeof(var6_);\
            size += sizeof(var7_);\
            size += sizeof(var8_);\
            size += sizeof(var9_);\
            size += sizeof(var10_);\
            size += sizeof(var11_);\
            size += sizeof(var12_);\
            size += sizeof(var13_);\
            size += sizeof(var14_);\
            size += sizeof(var15_);\
            size += sizeof(var16_);\
            size += sizeof(var17_);\
            size += sizeof(var18_);\
            size += sizeof(var19_);\
            size += sizeof(var20_);\
            return size; \
        } \
        bool serialize(char* buffer, uint32_t maxsize, \
                       uint32_t* size) const override { \
            if (maxsize < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            size_t offset = 0; \
            byte::FixedInt::Encode<uint32_t>(buffer, num_members_); \
            byte::FixedInt::Encode<uint32_t>( \
                buffer + sizeof(num_members_), bitmap_); \
            offset = sizeof(num_members_) + sizeof(bitmap_); \
            if (maxsize < offset + sizeof(var1_)) { return false; } \
            byte::FixedInt::Encode<type1>(buffer + offset, var1_); \
            offset += sizeof(var1_); \
            if (maxsize < offset + sizeof(var2_)) { return false; } \
            byte::FixedInt::Encode<type2>(buffer + offset, var2_); \
            offset += sizeof(var2_); \
            if (maxsize < offset + sizeof(var3_)) { return false; } \
            byte::FixedInt::Encode<type3>(buffer + offset, var3_); \
            offset += sizeof(var3_); \
            if (maxsize < offset + sizeof(var4_)) { return false; } \
            byte::FixedInt::Encode<type4>(buffer + offset, var4_); \
            offset += sizeof(var4_); \
            if (maxsize < offset + sizeof(var5_)) { return false; } \
            byte::FixedInt::Encode<type5>(buffer + offset, var5_); \
            offset += sizeof(var5_); \
            if (maxsize < offset + sizeof(var6_)) { return false; } \
            byte::FixedInt::Encode<type6>(buffer + offset, var6_); \
            offset += sizeof(var6_); \
            if (maxsize < offset + sizeof(var7_)) { return false; } \
            byte::FixedInt::Encode<type7>(buffer + offset, var7_); \
            offset += sizeof(var7_); \
            if (maxsize < offset + sizeof(var8_)) { return false; } \
            byte::FixedInt::Encode<type8>(buffer + offset, var8_); \
            offset += sizeof(var8_); \
            if (maxsize < offset + sizeof(var9_)) { return false; } \
            byte::FixedInt::Encode<type9>(buffer + offset, var9_); \
            offset += sizeof(var9_); \
            if (maxsize < offset + sizeof(var10_)) { return false; } \
            byte::FixedInt::Encode<type10>(buffer + offset, var10_); \
            offset += sizeof(var10_); \
            if (maxsize < offset + sizeof(var11_)) { return false; } \
            byte::FixedInt::Encode<type11>(buffer + offset, var11_); \
            offset += sizeof(var11_); \
            if (maxsize < offset + sizeof(var12_)) { return false; } \
            byte::FixedInt::Encode<type12>(buffer + offset, var12_); \
            offset += sizeof(var12_); \
            if (maxsize < offset + sizeof(var13_)) { return false; } \
            byte::FixedInt::Encode<type13>(buffer + offset, var13_); \
            offset += sizeof(var13_); \
            if (maxsize < offset + sizeof(var14_)) { return false; } \
            byte::FixedInt::Encode<type14>(buffer + offset, var14_); \
            offset += sizeof(var14_); \
            if (maxsize < offset + sizeof(var15_)) { return false; } \
            byte::FixedInt::Encode<type15>(buffer + offset, var15_); \
            offset += sizeof(var15_); \
            if (maxsize < offset + sizeof(var16_)) { return false; } \
            byte::FixedInt::Encode<type16>(buffer + offset, var16_); \
            offset += sizeof(var16_); \
            if (maxsize < offset + sizeof(var17_)) { return false; } \
            byte::FixedInt::Encode<type17>(buffer + offset, var17_); \
            offset += sizeof(var17_); \
            if (maxsize < offset + sizeof(var18_)) { return false; } \
            byte::FixedInt::Encode<type18>(buffer + offset, var18_); \
            offset += sizeof(var18_); \
            if (maxsize < offset + sizeof(var19_)) { return false; } \
            byte::FixedInt::Encode<type19>(buffer + offset, var19_); \
            offset += sizeof(var19_); \
            if (maxsize < offset + sizeof(var20_)) { return false; } \
            byte::FixedInt::Encode<type20>(buffer + offset, var20_); \
            offset += sizeof(var20_); \
            *size = offset; \
            return true; \
        } \
        bool deserialize(const char* buffer, \
                         uint32_t size, \
                         uint32_t* ret) override { \
            if (size < sizeof(num_members_) + sizeof(bitmap_)) { \
                return false; \
            } \
            uint32_t num_members = byte::FixedInt::Decode<uint32_t>(buffer); \
            size_t offset = sizeof(num_members_); \
            bitmap_ = byte::FixedInt::Decode<uint32_t>(buffer + offset); \
            offset += sizeof(bitmap_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var1_) > size) { return true; } \
            var1_ = byte::FixedInt::Decode<type1>(buffer + offset); \
            offset += sizeof(var1_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var2_) > size) { return true; } \
            var2_ = byte::FixedInt::Decode<type2>(buffer + offset); \
            offset += sizeof(var2_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var3_) > size) { return true; } \
            var3_ = byte::FixedInt::Decode<type3>(buffer + offset); \
            offset += sizeof(var3_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var4_) > size) { return true; } \
            var4_ = byte::FixedInt::Decode<type4>(buffer + offset); \
            offset += sizeof(var4_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var5_) > size) { return true; } \
            var5_ = byte::FixedInt::Decode<type5>(buffer + offset); \
            offset += sizeof(var5_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var6_) > size) { return true; } \
            var6_ = byte::FixedInt::Decode<type6>(buffer + offset); \
            offset += sizeof(var6_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var7_) > size) { return true; } \
            var7_ = byte::FixedInt::Decode<type7>(buffer + offset); \
            offset += sizeof(var7_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var8_) > size) { return true; } \
            var8_ = byte::FixedInt::Decode<type8>(buffer + offset); \
            offset += sizeof(var8_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var9_) > size) { return true; } \
            var9_ = byte::FixedInt::Decode<type9>(buffer + offset); \
            offset += sizeof(var9_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var10_) > size) { return true; } \
            var10_ = byte::FixedInt::Decode<type10>(buffer + offset); \
            offset += sizeof(var10_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var11_) > size) { return true; } \
            var11_ = byte::FixedInt::Decode<type11>(buffer + offset); \
            offset += sizeof(var11_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var12_) > size) { return true; } \
            var12_ = byte::FixedInt::Decode<type12>(buffer + offset); \
            offset += sizeof(var12_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var13_) > size) { return true; } \
            var13_ = byte::FixedInt::Decode<type13>(buffer + offset); \
            offset += sizeof(var13_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var14_) > size) { return true; } \
            var14_ = byte::FixedInt::Decode<type14>(buffer + offset); \
            offset += sizeof(var14_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var15_) > size) { return true; } \
            var15_ = byte::FixedInt::Decode<type15>(buffer + offset); \
            offset += sizeof(var15_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var16_) > size) { return true; } \
            var16_ = byte::FixedInt::Decode<type16>(buffer + offset); \
            offset += sizeof(var16_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var17_) > size) { return true; } \
            var17_ = byte::FixedInt::Decode<type17>(buffer + offset); \
            offset += sizeof(var17_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var18_) > size) { return true; } \
            var18_ = byte::FixedInt::Decode<type18>(buffer + offset); \
            offset += sizeof(var18_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var19_) > size) { return true; } \
            var19_ = byte::FixedInt::Decode<type19>(buffer + offset); \
            offset += sizeof(var19_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            if (offset + sizeof(var20_) > size) { return true; } \
            var20_ = byte::FixedInt::Decode<type20>(buffer + offset); \
            offset += sizeof(var20_); \
            *ret = offset; \
            if (num_members-- == 0) { return true; } \
            return true; \
        } \
    \
    private: \
        type1 var1_ = {}; \
        type2 var2_ = {}; \
        type3 var3_ = {}; \
        type4 var4_ = {}; \
        type5 var5_ = {}; \
        type6 var6_ = {}; \
        type7 var7_ = {}; \
        type8 var8_ = {}; \
        type9 var9_ = {}; \
        type10 var10_ = {}; \
        type11 var11_ = {}; \
        type12 var12_ = {}; \
        type13 var13_ = {}; \
        type14 var14_ = {}; \
        type15 var15_ = {}; \
        type16 var16_ = {}; \
        type17 var17_ = {}; \
        type18 var18_ = {}; \
        type19 var19_ = {}; \
        type20 var20_ = {}; \
        uint32_t bitmap_; \
        const uint32_t num_members_ = 20; \
    }

// for 20 argument(s)
#define CLASS_VARIANT_PLAIN_BUFFER_20(name, var1, var2, var3, var4, var5, var6, var7, var8, var9, var10, var11, var12, var13, var14, var15, var16, var17, var18, var19, var20) \
    class VariantPlainBuffer_##name : public byte::VariantPlainBufferBase<20> { \
    public: \
        VariantPlainBuffer_##name() {} \
        virtual ~VariantPlainBuffer_##name() {} \
        byte::StringPiece var1() const { return data_[0]; } \
        byte::StringPiece* mutable_##var1() { return &data_[0]; } \
        void set_##var1(const char* name, size_t size) { \
            data_[0].set(name, size); \
        } \
        void clear_##var1() { data_[0].clear(); } \
        bool has_##var1() const { return !data_[0].empty(); } \
        byte::StringPiece var2() const { return data_[1]; } \
        byte::StringPiece* mutable_##var2() { return &data_[1]; } \
        void set_##var2(const char* name, size_t size) { \
            data_[1].set(name, size); \
        } \
        void clear_##var2() { data_[1].clear(); } \
        bool has_##var2() const { return !data_[1].empty(); } \
        byte::StringPiece var3() const { return data_[2]; } \
        byte::StringPiece* mutable_##var3() { return &data_[2]; } \
        void set_##var3(const char* name, size_t size) { \
            data_[2].set(name, size); \
        } \
        void clear_##var3() { data_[2].clear(); } \
        bool has_##var3() const { return !data_[2].empty(); } \
        byte::StringPiece var4() const { return data_[3]; } \
        byte::StringPiece* mutable_##var4() { return &data_[3]; } \
        void set_##var4(const char* name, size_t size) { \
            data_[3].set(name, size); \
        } \
        void clear_##var4() { data_[3].clear(); } \
        bool has_##var4() const { return !data_[3].empty(); } \
        byte::StringPiece var5() const { return data_[4]; } \
        byte::StringPiece* mutable_##var5() { return &data_[4]; } \
        void set_##var5(const char* name, size_t size) { \
            data_[4].set(name, size); \
        } \
        void clear_##var5() { data_[4].clear(); } \
        bool has_##var5() const { return !data_[4].empty(); } \
        byte::StringPiece var6() const { return data_[5]; } \
        byte::StringPiece* mutable_##var6() { return &data_[5]; } \
        void set_##var6(const char* name, size_t size) { \
            data_[5].set(name, size); \
        } \
        void clear_##var6() { data_[5].clear(); } \
        bool has_##var6() const { return !data_[5].empty(); } \
        byte::StringPiece var7() const { return data_[6]; } \
        byte::StringPiece* mutable_##var7() { return &data_[6]; } \
        void set_##var7(const char* name, size_t size) { \
            data_[6].set(name, size); \
        } \
        void clear_##var7() { data_[6].clear(); } \
        bool has_##var7() const { return !data_[6].empty(); } \
        byte::StringPiece var8() const { return data_[7]; } \
        byte::StringPiece* mutable_##var8() { return &data_[7]; } \
        void set_##var8(const char* name, size_t size) { \
            data_[7].set(name, size); \
        } \
        void clear_##var8() { data_[7].clear(); } \
        bool has_##var8() const { return !data_[7].empty(); } \
        byte::StringPiece var9() const { return data_[8]; } \
        byte::StringPiece* mutable_##var9() { return &data_[8]; } \
        void set_##var9(const char* name, size_t size) { \
            data_[8].set(name, size); \
        } \
        void clear_##var9() { data_[8].clear(); } \
        bool has_##var9() const { return !data_[8].empty(); } \
        byte::StringPiece var10() const { return data_[9]; } \
        byte::StringPiece* mutable_##var10() { return &data_[9]; } \
        void set_##var10(const char* name, size_t size) { \
            data_[9].set(name, size); \
        } \
        void clear_##var10() { data_[9].clear(); } \
        bool has_##var10() const { return !data_[9].empty(); } \
        byte::StringPiece var11() const { return data_[10]; } \
        byte::StringPiece* mutable_##var11() { return &data_[10]; } \
        void set_##var11(const char* name, size_t size) { \
            data_[10].set(name, size); \
        } \
        void clear_##var11() { data_[10].clear(); } \
        bool has_##var11() const { return !data_[10].empty(); } \
        byte::StringPiece var12() const { return data_[11]; } \
        byte::StringPiece* mutable_##var12() { return &data_[11]; } \
        void set_##var12(const char* name, size_t size) { \
            data_[11].set(name, size); \
        } \
        void clear_##var12() { data_[11].clear(); } \
        bool has_##var12() const { return !data_[11].empty(); } \
        byte::StringPiece var13() const { return data_[12]; } \
        byte::StringPiece* mutable_##var13() { return &data_[12]; } \
        void set_##var13(const char* name, size_t size) { \
            data_[12].set(name, size); \
        } \
        void clear_##var13() { data_[12].clear(); } \
        bool has_##var13() const { return !data_[12].empty(); } \
        byte::StringPiece var14() const { return data_[13]; } \
        byte::StringPiece* mutable_##var14() { return &data_[13]; } \
        void set_##var14(const char* name, size_t size) { \
            data_[13].set(name, size); \
        } \
        void clear_##var14() { data_[13].clear(); } \
        bool has_##var14() const { return !data_[13].empty(); } \
        byte::StringPiece var15() const { return data_[14]; } \
        byte::StringPiece* mutable_##var15() { return &data_[14]; } \
        void set_##var15(const char* name, size_t size) { \
            data_[14].set(name, size); \
        } \
        void clear_##var15() { data_[14].clear(); } \
        bool has_##var15() const { return !data_[14].empty(); } \
        byte::StringPiece var16() const { return data_[15]; } \
        byte::StringPiece* mutable_##var16() { return &data_[15]; } \
        void set_##var16(const char* name, size_t size) { \
            data_[15].set(name, size); \
        } \
        void clear_##var16() { data_[15].clear(); } \
        bool has_##var16() const { return !data_[15].empty(); } \
        byte::StringPiece var17() const { return data_[16]; } \
        byte::StringPiece* mutable_##var17() { return &data_[16]; } \
        void set_##var17(const char* name, size_t size) { \
            data_[16].set(name, size); \
        } \
        void clear_##var17() { data_[16].clear(); } \
        bool has_##var17() const { return !data_[16].empty(); } \
        byte::StringPiece var18() const { return data_[17]; } \
        byte::StringPiece* mutable_##var18() { return &data_[17]; } \
        void set_##var18(const char* name, size_t size) { \
            data_[17].set(name, size); \
        } \
        void clear_##var18() { data_[17].clear(); } \
        bool has_##var18() const { return !data_[17].empty(); } \
        byte::StringPiece var19() const { return data_[18]; } \
        byte::StringPiece* mutable_##var19() { return &data_[18]; } \
        void set_##var19(const char* name, size_t size) { \
            data_[18].set(name, size); \
        } \
        void clear_##var19() { data_[18].clear(); } \
        bool has_##var19() const { return !data_[18].empty(); } \
        byte::StringPiece var20() const { return data_[19]; } \
        byte::StringPiece* mutable_##var20() { return &data_[19]; } \
        void set_##var20(const char* name, size_t size) { \
            data_[19].set(name, size); \
        } \
        void clear_##var20() { data_[19].clear(); } \
        bool has_##var20() const { return !data_[19].empty(); } \
    }


struct PlainBufferHeader {
    uint32_t magic_;
    uint32_t checksum_length_;  // 0 representing old version or no checksum enabled
    uint32_t checksum_;

    PlainBufferHeader() : magic_(PLAIN_BUFFER_MAGIC), checksum_length_(0), checksum_(0) {}
};

static_assert(sizeof(PlainBufferHeader) == 12, "sizeof(PlainBufferHeader) should bbe 12");

template <typename Fixed, typename Variant>
class PlainBufferMessage {
public:
    PlainBufferMessage() : enable_checksum_(false) {}

    ~PlainBufferMessage() {}

    uint32_t ByteSize() const {
        return sizeof(PlainBufferHeader) + sizeof(uint32_t) * 2 +
            fixed_buf_.ByteSize() + variant_buf_.ByteSize();
    }

    bool serialize(char* buffer, uint32_t max_size, uint32_t* buf_size, int* err = nullptr) const {
        PlainBufferHeader header;
        uint32_t byte_size = 0;
        if (max_size < sizeof(header) + 2 * sizeof(byte_size)) {
            PTR_SET_VALUE(err, -1);
            return false;
        }
        memcpy(buffer, &header, sizeof(header));
        uint32_t offset = sizeof(header);
        max_size -= sizeof(header);
        byte_size = fixed_buf_.ByteSize();
        memcpy(buffer + offset, &byte_size, sizeof(byte_size));
        offset += sizeof(byte_size);
        max_size -= sizeof(byte_size);
        uint32_t size = 0;
        if (!fixed_buf_.serialize(buffer + offset, max_size, &size)) {
            PTR_SET_VALUE(err, -2);
            return false;
        }
        offset += size;
        BYTE_ASSERT_GE(max_size, size);
        max_size -= size;
        byte_size = variant_buf_.ByteSize();
        memcpy(buffer + offset, &byte_size, sizeof(byte_size));
        offset += sizeof(byte_size);
        max_size -= sizeof(byte_size);
        if (!variant_buf_.serialize(buffer + offset, max_size, &size)) {
            PTR_SET_VALUE(err, -3);
            return false;
        }
        offset += size;
        if (enable_checksum_) {
            (reinterpret_cast<PlainBufferHeader*>(buffer))->checksum_ =
                byte::Checksum32(buffer + sizeof(header), offset - sizeof(header));
            (reinterpret_cast<PlainBufferHeader*>(buffer))->checksum_length_ =
                offset - sizeof(header);
        }
        BYTE_ASSERT_GE(max_size, size);
        *buf_size = offset;
        return true;
    }

    bool deserialize(const char* buffer, uint32_t size, int* err = nullptr) {
        PlainBufferHeader header;
        if (size < sizeof(header)) {
            PTR_SET_VALUE(err, -101);
            return false;
        }
        if ((reinterpret_cast<const PlainBufferHeader*>(buffer))->magic_ != kMagic) {
            PTR_SET_VALUE(err, -102);
            return false;
        }
        const uint32_t checksum_length =
            (reinterpret_cast<const PlainBufferHeader*>(buffer))->checksum_length_;
        const bool enable_checksum = (checksum_length == 0) ? false : enable_checksum_;
        if (enable_checksum &&
            (reinterpret_cast<const PlainBufferHeader*>(buffer))->checksum_ !=
            byte::Checksum32(buffer + sizeof(header), checksum_length)) {
            PTR_SET_VALUE(err, -103);
            return false;
        }
        uint32_t offset = sizeof(header);
        size -= sizeof(header);
        uint32_t byte_size = *(reinterpret_cast<const uint32_t*>(buffer + offset));
        uint32_t ret = 0;
        offset += sizeof(byte_size);
        if (!fixed_buf_.deserialize(buffer + offset, size, &ret)) {
            PTR_SET_VALUE(err, -104);
            return false;
        }
        offset += byte_size;
        size -= byte_size;
        offset += sizeof(byte_size);
        size -= sizeof(byte_size);
        if (!variant_buf_.deserialize(buffer + offset, size, &ret)) {
            PTR_SET_VALUE(err, -105);
            return false;
        }
        return true;
    }

public:
    static const uint32_t kMagic = PLAIN_BUFFER_MAGIC;

    Fixed fixed_buf_;
    Variant variant_buf_;
    bool enable_checksum_;
};

}  // namespace byte
