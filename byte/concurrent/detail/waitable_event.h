// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <inttypes.h>
#include "byte/include/macros.h"

namespace byte {

class WaitableEvent {
public:
    explicit WaitableEvent(bool auto_reset = true);
    ~WaitableEvent();

    // NOTE: delay_in_ms == 0: return true immediately if setted, else return false
    // timeout > 0: Wait with timeout(ms), return true if setted, false if timeout.
    // timeout < 0: Wait wihout timeout, return true if setted.
    bool Wait(int64_t delay_in_ms = -1);
    void Reset();
    void Set();

private:
    class Impl;
    Impl* impl_;

    DISALLOW_COPY_AND_ASSIGN(WaitableEvent);
};

}  // namespace byte
