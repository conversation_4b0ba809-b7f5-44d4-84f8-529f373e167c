// Copyright (c) 2011, The Toft Authors. All rights reserved.
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>
// Created: 2010-06-18

#include "byte/concurrent/event.h"

namespace byte {

AutoResetEvent::AutoResetEvent(bool init_state) :
    mutex_(),
    cond_(&mutex_),
    signaled_(init_state) {}

AutoResetEvent::~AutoResetEvent() {}

void AutoResetEvent::Wait() {
    MutexLocker locker(&mutex_);
    while (!signaled_) {
        cond_.Wait();
    }
    signaled_ = false;
}

bool AutoResetEvent::TimedWait(int64_t timeout) {
    MutexLocker locker(&mutex_);
    if (!signaled_) {
        cond_.TimedWait(timeout);
    }
    if (!signaled_) {
        return false;
    }
    signaled_ = false;
    return true;
}

bool AutoResetEvent::TryWait() {
    return TimedWait(0);
}

void AutoResetEvent::Set() {
    MutexLocker locker(&mutex_);
    signaled_ = true;
    cond_.Signal();
}

void AutoResetEvent::Reset() {
    MutexLocker locker(&mutex_);
    signaled_ = false;
}

// ManualResetEvent

ManualResetEvent::ManualResetEvent(bool init_state) :
    mutex_(),
    cond_(&mutex_),
    signaled_(init_state) {}

ManualResetEvent::~ManualResetEvent() {}

void ManualResetEvent::Wait() {
    MutexLocker locker(&mutex_);
    while (!signaled_) {
        cond_.Wait();
    }
}

bool ManualResetEvent::TimedWait(int64_t timeout) {
    MutexLocker locker(&mutex_);
    if (!signaled_) {
        cond_.TimedWait(timeout);
    }
    return signaled_;
}

bool ManualResetEvent::TryWait() {
    return TimedWait(0);
}

void ManualResetEvent::Set() {
    MutexLocker locker(&mutex_);
    signaled_ = true;
    cond_.Broadcast();
}

void ManualResetEvent::Reset() {
    MutexLocker locker(&mutex_);
    signaled_ = false;
}

}  // namespace byte
