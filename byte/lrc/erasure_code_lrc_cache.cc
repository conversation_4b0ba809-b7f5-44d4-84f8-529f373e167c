/*
 * Copyright (C) 2019 Bytedance.Inc
 *
 */



#include <iostream>
#include <cstring>
#include <cstdio>
#include <cstdlib>
#include "byte/include/assert.h"
#include "byte/lrc/erasure_code_lrc_cache.h"
#include "byte/concurrent/mutex.h"

namespace InnovationCenter {
ErasureCodeLrcTableCache::~ErasureCodeLrcTableCache() {
    byte::MutexLocker locker(&mutex);
    if (decoding_tables) {
        for (auto i : *decoding_tables) {
            CachePtr ptr = (i.second.second);
            delete[] ptr.ptr;
        }
        delete decoding_tables;
    }
    if (decoding_tables_lru) {
        delete decoding_tables_lru;
    }
    for (uint32_t k = 0; k < LRC_MAX_K; ++k)
        for (uint32_t l = 0; l < LRC_MAX_L; ++l)
            for (uint32_t g = 0; g < LRC_MAX_G; ++g) {
                if (encoding_table[k][l][g]) {
                    delete[] encoding_table[k][l][g];
                }
                if (encoding_coefficient[k][l][g]) {
                    delete[] encoding_coefficient[k][l][g];
                }
            }
}

unsigned char* ErasureCodeLrcTableCache::getEncodingTable(
    uint32_t k,
    uint32_t l,
    uint32_t g) {
    BYTE_ASSERT(k < LRC_MAX_K
                && l < LRC_MAX_L
                && g < LRC_MAX_G);
    byte::MutexLocker locker(&mutex);
    return  getEncodingTableNoLock(k, l, g);
}

unsigned char* ErasureCodeLrcTableCache::getEncodingTableNoLock(
    uint32_t k,
    uint32_t l,
    uint32_t g) {
    return encoding_table[k][l][g];
}

unsigned char* ErasureCodeLrcTableCache::getEncodingCoefficient(
    uint32_t k,
    uint32_t l,
    uint32_t g) {
    BYTE_ASSERT(k < LRC_MAX_K
                && l < LRC_MAX_L
                && g < LRC_MAX_G);
    byte::MutexLocker locker(&mutex);
    return  getEncodingCoefficientNoLock(k, l, g);
}

unsigned char* ErasureCodeLrcTableCache::getEncodingCoefficientNoLock(
    uint32_t k,
    uint32_t l,
    uint32_t g) {
    return encoding_coefficient[k][l][g];
}

unsigned char* ErasureCodeLrcTableCache::setEncodingTable(
    uint32_t k,
    uint32_t l,
    uint32_t g,
    unsigned char* ec_in_table) {
    BYTE_ASSERT(k < LRC_MAX_K
                && l < LRC_MAX_L
                && g < LRC_MAX_G);
    byte::MutexLocker locker(&mutex);
    unsigned char* ec_out_table = getEncodingTableNoLock(k, l, g);
    if (ec_out_table) {
        delete[] ec_in_table;
        return ec_out_table;
    } else {
        encoding_table[k][l][g] = ec_in_table;
        return ec_in_table;
    }
}

unsigned char* ErasureCodeLrcTableCache::setEncodingCoefficient(
    uint32_t k,
    uint32_t l,
    uint32_t g,
    unsigned char* ec_in_coeff) {
    BYTE_ASSERT(k < LRC_MAX_K
                && l < LRC_MAX_L
                && g < LRC_MAX_G);
    byte::MutexLocker locker(&mutex);
    unsigned char* ec_out_coeff = getEncodingCoefficientNoLock(k, l, g);
    if (ec_out_coeff) {
        delete[] ec_in_coeff;
        return  ec_out_coeff;
    } else {
        encoding_coefficient[k][l][g] = ec_in_coeff;
        return ec_in_coeff;
    }
}

ErasureCodeLrcTableCache::lru_map_t* ErasureCodeLrcTableCache::getDecodingTables() {
    return decoding_tables;
}

ErasureCodeLrcTableCache::lru_list_t* ErasureCodeLrcTableCache::getDecodingTablesLru() {
    return decoding_tables_lru;
}

bool  ErasureCodeLrcTableCache::getDecodingTableFromCache(
    const std::string &signature,
    unsigned char* table,
    std::set<uint32_t>* to_use_index) {
    bool found = false;
    byte::MutexLocker locker(&mutex);
    lru_map_t* decode_tbls_map = getDecodingTables();
    lru_list_t* decode_tbls_lru = getDecodingTablesLru();
    if (decode_tbls_map->count(signature)) {
        CachePtr ptr = (*decode_tbls_map)[signature].second;
        memcpy(table, ptr.ptr, ptr.length);
        *to_use_index = ptr.indexes;
        decode_tbls_lru->splice((decode_tbls_lru->begin()),
                                *decode_tbls_lru,
                                (*decode_tbls_map)[signature].first);
        found = true;
    }
    return found;
}

void ErasureCodeLrcTableCache::putDecodingTableToCache(
    std::string& signature,
    unsigned char* table,
    uint32_t size,
    std::set<uint32_t>* to_use_index) {
    unsigned char* cachetable;
    byte::MutexLocker locker(&mutex);
    lru_map_t* decode_tbls_map = getDecodingTables();
    lru_list_t* decode_tbls_lru = getDecodingTablesLru();
    if (static_cast<uint32_t>(decode_tbls_lru->size())
                     >= ErasureCodeLrcTableCache::decoding_tables_lru_length) {
        cachetable = (*decode_tbls_map)[decode_tbls_lru->back()].second.ptr;
        if ((*decode_tbls_map)[decode_tbls_lru->back()].second.length != size) {
            delete[] cachetable;
            cachetable = new unsigned char[size];
        }
        decode_tbls_map->erase(decode_tbls_lru->back());
        decode_tbls_lru->pop_back();
        decode_tbls_lru->push_front(signature);
        (*decode_tbls_map)[signature] = std::make_pair(decode_tbls_lru->begin(),
                                                       CachePtr(cachetable, size,
                                                       *to_use_index));
    } else {
        cachetable = new unsigned char[size];
        decode_tbls_lru->push_front(signature);
        (*decode_tbls_map)[signature] = std::make_pair(decode_tbls_lru->begin(),
                                                       CachePtr(cachetable, size,
                                                       *to_use_index));
    }
    memcpy(cachetable, table, size);
}
}  // namespace InnovationCenter
