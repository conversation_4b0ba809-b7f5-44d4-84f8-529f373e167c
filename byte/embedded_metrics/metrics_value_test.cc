// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "byte/embedded_metrics/metrics_registry.h"
#include "gtest/gtest.h"
#include "spdlog/sinks/stdout_color_sinks.h"

namespace byte {
namespace embedded_metrics {

namespace {
class MetricsValueTest : public ::testing::Test,
                         public ::testing::WithParamInterface<MetricsMode> {};

#ifdef BYTE_ENABLE_METRICS2
TEST(MetricsValueTest, EmitValuePerSecond) {
    MetricsOptions options;
    options.mode = MetricsMode::kPush2;
    options.enable_logging = true;
    options.counter_window_size = 1;
    options.report_interval_secs = 1;
    Tags tags;
    std::string name = "EmitValuePerSecond";
    std::shared_ptr<spdlog::logger> logger = spdlog::get(name);
    if (!logger) {
        logger = spdlog::stdout_color_mt(name);
    }
    logger->set_level(spdlog::level::debug);
    std::unique_ptr<MetricsRegistry> m_r(new MetricsRegistry());
    bool ok = m_r->Initialize("byte_test", tags, options, logger);
    ASSERT_TRUE(ok);

    ASSERT_TRUE(
        m_r->RegisterMetricName(name, MetricType::kTlsHistogram, /*tag_keys=*/{}, /*fields=*/{}));
    auto t1 = m_r->GetByteMetric(name, MetricType::kTlsHistogram, tags);
    ASSERT_NE(t1, nullptr);
    std::shared_ptr<TlsHistogram> histogram = std::dynamic_pointer_cast<TlsHistogram>(t1);

    for (int i = 1; i < 5; i++) {
        histogram->Set(i);
        sleep(1);
    }
}

TEST(MetricsValueTest, MultipleRegistriesEmitValuePerSecond) {
    MetricsOptions options;
    options.mode = MetricsMode::kPush2;
    options.enable_logging = true;
    options.counter_window_size = 1;
    options.report_interval_secs = 1;
    Tags tags;
    std::string name = "MultipleRegistriesEmitValuePerSecond1";
    std::shared_ptr<spdlog::logger> logger = spdlog::get(name);
    if (!logger) {
        logger = spdlog::stdout_color_mt(name);
    }
    logger->set_level(spdlog::level::debug);
    std::unique_ptr<MetricsRegistry> m_r(new MetricsRegistry());
    bool ok = m_r->Initialize(name, tags, options, logger);
    ASSERT_TRUE(ok);
    std::string name2 = "MultipleRegistriesEmitValuePerSecond2";
    std::unique_ptr<MetricsRegistry> m_r2(new MetricsRegistry());
    bool ok2 = m_r2->Initialize(name2, tags, options, logger);
    ASSERT_TRUE(ok2);

    MetricsOptions options3;
    options3.mode = MetricsMode::kPush2;
    options3.enable_logging = true;
    options3.counter_window_size = 2;
    options3.report_interval_secs = 2;
    std::string name3 = "MultipleRegistriesEmitValuePerSecond3";
    std::unique_ptr<MetricsRegistry> m_r3(new MetricsRegistry());
    bool ok3 = m_r3->Initialize(name3, tags, options3, logger);
    ASSERT_TRUE(ok3);

    ASSERT_TRUE(
        m_r->RegisterMetricName(name, MetricType::kTlsHistogram, /*tag_keys=*/{}, /*fields=*/{}));
    auto t1 = m_r->GetByteMetric(name, MetricType::kTlsHistogram, tags);
    std::shared_ptr<TlsHistogram> histogram = std::dynamic_pointer_cast<TlsHistogram>(t1);

    for (int i = 1; i < 5; i++) {
        histogram->Set(i);
        sleep(1);
    }
    ASSERT_TRUE(
        m_r2->RegisterMetricName(name, MetricType::kTlsHistogram, /*tag_keys=*/{}, /*fields=*/{}));
    auto t2 = m_r2->GetByteMetric(name, MetricType::kTlsHistogram, tags);
    std::shared_ptr<TlsHistogram> histogram2 = std::dynamic_pointer_cast<TlsHistogram>(t2);

    for (int i = 1; i < 5; i++) {
        histogram2->Set(i);
        sleep(1);
    }

    ASSERT_TRUE(
        m_r3->RegisterMetricName(name, MetricType::kTlsHistogram, /*tag_keys=*/{}, /*fields=*/{}));
    auto t3 = m_r->GetByteMetric(name, MetricType::kTlsHistogram, tags);
    std::shared_ptr<TlsHistogram> histogram3 = std::dynamic_pointer_cast<TlsHistogram>(t3);

    for (int i = 1; i < 5; i++) {
        histogram3->Set(i);
        sleep(1);
    }
}
#endif

void CheckValid(double value, double lower, double higher, double allowance) {
    EXPECT_LT(lower / allowance, value);
    EXPECT_LT(value, higher * allowance);
}

TEST_P(MetricsValueTest, TlsHistogram) {
    MetricsOptions options;
    options.mode = GetParam();
    if (options.mode == MetricsMode::kPull) {
        return;
    }
    options.enable_logging = false;
    options.counter_window_size = 4;
    options.report_interval_secs = 3600;  // in effect disable periodical report
    Tags tags;
    std::shared_ptr<spdlog::logger> logger = nullptr;
    std::unique_ptr<MetricsRegistry> m_r(new MetricsRegistry());
    bool ok = m_r->Initialize("test", tags, options, logger);
    ASSERT_TRUE(ok);

#ifdef BYTE_ENABLE_METRICS2
    m_r->RegisterMetricName("test.metrics1", MetricType::kTlsHistogram, /*tag_keys=*/{});
    auto t1 = m_r->GetByteMetric("test.metrics1", MetricType::kTlsHistogram, tags);
#else
    auto t1 = m_r->Register("test.metrics1", tags, MetricType::kTlsHistogram);
#endif
    std::shared_ptr<TlsHistogram> histogram = std::dynamic_pointer_cast<TlsHistogram>(t1);

    // 9000 points, pct50 ~ (5999, 6000)
    for (int i = 1000; i < 10000; i++) {
        histogram->Set(i);
    }
    // 900 points, pct99 ~ (99900, 100000)
    for (int i = 10000; i < 100000; i += 100) {
        histogram->Set(i);
    }
    // 99 points, pct999 ~ (990000, 1000000)
    for (int i = 100000; i < 1090000; i += 10000) {
        histogram->Set(i);
    }
    // 1 point
    histogram->Set(1000000);

    m_r->report();
    double val1 = histogram->GetPct(kPct50);
    double val2 = histogram->GetPct(kPct99);
    double val3 = histogram->GetPct(kPct999);

    // the ratio of adjacent bucket values in direct mapping is ~ 1.171934
    // the margin of error shouldn't exceed 1.172
    double allowance = 1.172;
    CheckValid(val1, 5999, 6000, allowance);
    CheckValid(val2, 99900, 100000, allowance);
    CheckValid(val3, 990000, 1000000, allowance);
}

TEST_P(MetricsValueTest, IntGauge) {
    MetricsOptions options;
    options.enable_logging = false;
    options.counter_window_size = 4;
    options.report_interval_secs = 3600;  // in effect disable periodical report
    options.mode = GetParam();
    Tags tags;
    std::shared_ptr<spdlog::logger> logger = nullptr;
    std::unique_ptr<MetricsRegistry> m_r(new MetricsRegistry());
    bool ok = m_r->Initialize("test_int_gauge", tags, options, logger);
    ASSERT_TRUE(ok);

#ifdef BYTE_ENABLE_METRICS2
    m_r->RegisterMetricName("test.metrics2", MetricType::kIntGauge, /*tag_keys=*/{});
    auto t1 = m_r->GetByteMetric("test.metrics2", MetricType::kIntGauge, tags);
#else
    auto t1 = m_r->Register("test.metrics2", tags, MetricType::kIntGauge);
#endif
    std::shared_ptr<IntGauge> int_gauge = std::dynamic_pointer_cast<IntGauge>(t1);
    int_gauge->SetValue(0);
    m_r->report();
    EXPECT_EQ(int_gauge->GetValue(), 0);
    for (int i = 0; i < 10; i++) {
        int_gauge->Increment();
    }
    m_r->report();
    EXPECT_EQ(int_gauge->GetValue(), 10);

    for (int i = 0; i < 10; i++) {
        int_gauge->Decrement();
    }
    m_r->report();
    EXPECT_EQ(int_gauge->GetValue(), 0);

    int_gauge->Add(100);
    m_r->report();
    EXPECT_EQ(int_gauge->GetValue(), 100);

    int_gauge->Minus(100);
    m_r->report();
    EXPECT_EQ(int_gauge->GetValue(), 0);

    int_gauge->SetValue(1000);
    m_r->report();
    EXPECT_EQ(int_gauge->GetValue(), 1000);
}

#ifdef BYTE_WITH_BRPC
TEST_P(MetricsValueTest, Counter) {
    MetricsOptions options;
    options.enable_logging = false;
    options.counter_window_size = 4;
    options.report_interval_secs = 3600;  // in effect disable periodical report
    options.mode = GetParam();
    Tags tags;
    std::shared_ptr<spdlog::logger> logger = nullptr;
    std::unique_ptr<MetricsRegistry> m_r(new MetricsRegistry());
    bool ok = m_r->Initialize("test_counter", tags, options, logger);
    ASSERT_TRUE(ok);

#ifdef BYTE_ENABLE_METRICS2
    m_r->RegisterMetricName("test.metrics3", MetricType::kCounter, /*tag_keys=*/{});
    auto t1 = m_r->GetByteMetric("test.metrics3", MetricType::kCounter, tags);
#else
    auto t1 = m_r->Register("test.metrics3", tags, MetricType::kCounter);
#endif
    std::shared_ptr<Counter> counter = std::dynamic_pointer_cast<Counter>(t1);
    counter->SetRateInterval(0);
    double init_value = counter->GetValue();
    for (int i = 0; i < 10; i++) {
        counter->Increment();
    }
    m_r->report();
    EXPECT_EQ(counter->GetValue(), init_value + 10);
    for (int i = 0; i < 10; i++) {
        counter->Decrement();
    }
    m_r->report();
    EXPECT_EQ(counter->GetValue(), init_value);

    counter->Add(100);
    m_r->report();
    EXPECT_EQ(counter->GetValue(), init_value + 100);
}
#endif

INSTANTIATE_TEST_CASE_P(MetricsValueTestInstantiation, MetricsValueTest,
                        ::testing::Values(
#ifdef BYTE_ENABLE_METRICS2
                            MetricsMode::kPush, MetricsMode::kPush2, MetricsMode::kPull
#else
                            MetricsMode::kPush, MetricsMode::kPull
#endif
                            ));  // NOLINT
}  // namespace
}  // namespace embedded_metrics
}  // namespace byte
