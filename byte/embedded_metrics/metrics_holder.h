// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

// For metrics sdk 1, BYTE_ENABLE_METRICS2 = false and use
// byte/embedded_metrics/metrics_holder.h.
// For metrics sdk 2, BYTE_ENABLE_METRICS2 = true and use
// byte/embedded_metrics/metrics_holder_v2.h.
#ifndef BYTE_ENABLE_METRICS2

#include <map>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "byte/embedded_metrics/metrics.h"
#include "byte/embedded_metrics/metrics_registry.h"

namespace byte {
namespace embedded_metrics {

template <typename MetricClass>
class MetricHolder {
public:
    MetricHolder() = default;
    MetricHolder(const byte::StringPiece& name, const Tags& tags) { Initialize(name, tags); }
    MetricHolder(MetricHolder&& another) { std::swap(metric_, another.metric_); }
    ~MetricHolder() { Clear(); }
    MetricHolder& operator=(MetricHolder&& another) { std::swap(metric_, another.metric_); }

    void Initialize(const byte::StringPiece& name, const Tags& tags) {
        Clear();
        std::shared_ptr<MetricBase> m =
            MetricsRegistry::Registry().Register(name, tags, MetricClass::Type());
        metric_ = static_cast<MetricClass*>(m.get());
    }
    void Clear() {
        if (metric_ != nullptr) {
            std::string name = metric_->UniqName();
            MetricsRegistry::Registry().Unregister(name);
            metric_ = nullptr;
        }
    }
    MetricClass* get() const { return metric_; }
    MetricClass* operator->() const { return metric_; }

private:
    MetricClass* metric_{nullptr};
    DISALLOW_COPY_AND_ASSIGN(MetricHolder);
};

template <class MetricClass>
class MetricsGroup {
public:
    typedef MetricHolder<MetricClass> MetricHolderT;

public:
    MetricsGroup(const byte::StringPiece& name, const Tags& shared_tags,
                 const byte::StringPiece& uniq_tag_key,
                 const std::vector<std::string>& uniq_tag_value)
        : MetricsGroup(name, shared_tags, uniq_tag_key, uniq_tag_value, "") {}
    MetricsGroup(const byte::StringPiece& name, const Tags& shared_tags,
                 const byte::StringPiece& uniq_tag_key,
                 const std::vector<std::string>& uniq_tag_value,
                 const byte::StringPiece& except_uniq) {
        for (const auto& v : uniq_tag_value) {
            Tags t = shared_tags;
            t.emplace_back(uniq_tag_key.as_string(), v);
            v_metrics_.emplace_back(name, t);
            BYTE_ASSERT(metrics_.find(v) == metrics_.end());
            metrics_[v] = v_metrics_.size() - 1;
        }

        if (except_uniq.size() > 0) {
            Tags t = shared_tags;
            t.emplace_back(uniq_tag_key.as_string(), except_uniq.as_string());
            except_uniq_.Initialize(name, t);
        }
    }

    MetricClass* GetMetric(const std::string& tag_value) {
        auto iter = metrics_.find(tag_value);
        if (iter != metrics_.end()) {
            return v_metrics_[iter->second].get();
        } else if (except_uniq_.get() != nullptr) {
            return except_uniq_.get();
        } else {
            return nullptr;
        }
    }

    MetricClass* GetMetricById(unsigned id) {
        BYTE_ASSERT(id < v_metrics_.size()) << id << " vs " << v_metrics_.size();
        return v_metrics_[id].get();
    }

    unsigned MetricsCount() const { return v_metrics_.size(); }

private:
    std::map<std::string, unsigned int> metrics_;
    std::vector<MetricHolderT> v_metrics_;
    MetricHolderT except_uniq_;
};

template <typename MetricClass>
class MetricsPool {
public:
    typedef MetricHolder<MetricClass> MetricHolderT;

    explicit MetricsPool(const byte::StringPiece& name) : name_(name.as_string()) {}
    MetricsPool(const byte::StringPiece& name, const Tags& shared_tags)
        : name_(name.as_string()), shared_tags_(shared_tags) {}

    MetricClass* GetMetric(const Tags& tags) {
        std::string key = MetricBase::GetKey(MetricBase::DedupAndSortTags(tags), "");
        {
            absl::ReaderMutexLock l(&mu_);
            auto iter = metrics_.find(key);
            if (iter != metrics_.end()) {
                return iter->second.get();
            }
        }
        {
            absl::MutexLock l(&mu_);
            auto iter = metrics_.find(key);
            if (iter != metrics_.end()) {
                return iter->second.get();
            }

            Tags result_tags = shared_tags_;
            result_tags.insert(result_tags.end(), tags.begin(), tags.end());
            MetricHolderT h(name_, result_tags);
            MetricClass* result = h.get();
            metrics_.emplace(key, std::move(h));
            return result;
        }
    }

    void Foreach(const std::function<void(const std::string& name, MetricClass*)>& visitor) {
        absl::ReaderMutexLock l(&mu_);
        for (const auto& kv : metrics_) {
            visitor(kv.first, kv.second.get());
        }
    }

    void Clear() {
        absl::MutexLock l(&mu_);
        metrics_.clear();
    }

private:
    absl::Mutex mu_;
    std::string name_;
    Tags shared_tags_;
    std::map<std::string, MetricHolderT> metrics_;
};

class ElapseMicros final {
public:
    ElapseMicros() { start_tick_us_ = absl::ToUnixMicros(absl::Now()); }
    void AddStage() { ticks_.push_back(absl::ToUnixMicros(absl::Now())); }
    uint32_t Count() const { return ticks_.size(); }
    uint64_t GetStage(uint32_t index) {
        BYTE_ASSERT_LT(index, ticks_.size());
        if (index == 0) {
            return ticks_[index] - start_tick_us_;
        }
        return ticks_[index] - ticks_[index - 1];
    }
    uint64_t GetStartTick() const { return start_tick_us_; }
    uint64_t GetTickAt(unsigned int i) const {
        BYTE_ASSERT_LT(i, ticks_.size());
        return ticks_[i];
    }

private:
    uint64_t start_tick_us_;
    std::vector<uint64_t> ticks_;
};

class PeriodGuard final {
public:
    explicit PeriodGuard(std::function<void(uint64_t)>&& elapse) : elapse_(std::move(elapse)) {
        start_tick_us_ = absl::ToUnixMicros(absl::Now());
    }
    ~PeriodGuard() { elapse_(absl::ToUnixMicros(absl::Now()) - start_tick_us_); }

private:
    uint64_t start_tick_us_;
    std::function<void(uint64_t)> elapse_;
};

class OneLatencyGuard final {
public:
    explicit OneLatencyGuard(MetricHolder<Histogram>* metric) : metric_(metric) {
        start_tick_us_ = absl::ToUnixMicros(absl::Now());
    }
    ~OneLatencyGuard() { metric_->get()->Set(absl::ToUnixMicros(absl::Now()) - start_tick_us_); }

private:
    MetricHolder<Histogram>* metric_;
    uint64_t start_tick_us_;
};

class LatencyMetricsGuard final {
public:
    explicit LatencyMetricsGuard(MetricsGroup<Histogram>* group)
        : group_(group), has_end_stage_(false) {}
    void EnableEndStage() { has_end_stage_ = true; }
    void AddStage() { elapse_.AddStage(); }
    uint64_t GetStage(unsigned int stage_index) { return elapse_.GetStage(stage_index); }
    const ElapseMicros& GetTickRecorder() const { return elapse_; }
    ~LatencyMetricsGuard() {
        if (has_end_stage_) {
            AddStage();
        }
        for (unsigned i = 0; i < elapse_.Count(); ++i) {
            group_->GetMetricById(i)->Set(elapse_.GetStage(i));
        }
    }

private:
    MetricsGroup<Histogram>* group_;
    ElapseMicros elapse_;
    bool has_end_stage_;
};

class LatencyPoolMetricsGuard final {
public:
    explicit LatencyPoolMetricsGuard(MetricsPool<Histogram>* pool)
        : pool_(pool), has_end_stage_(false) {}
    void EnableEndStage(const byte::StringPiece& end_stage) {
        has_end_stage_ = true;
        end_stage_ = end_stage.as_string();
    }
    void AddStage(const byte::StringPiece& stage) {
        elapse_.AddStage();
        stages_.emplace_back(stage.as_string());
    }
    uint64_t GetStage(unsigned int stage_index) { return elapse_.GetStage(stage_index); }
    ~LatencyPoolMetricsGuard() {
        if (has_end_stage_) {
            AddStage(end_stage_);
        }
        for (unsigned i = 0; i < elapse_.Count(); ++i) {
            pool_->GetMetric({{"stage", stages_[i]}})->Set(elapse_.GetStage(i));
        }
    }

private:
    MetricsPool<Histogram>* pool_;
    ElapseMicros elapse_;
    std::vector<std::string> stages_;
    bool has_end_stage_;
    std::string end_stage_;
};

}  // namespace embedded_metrics
}  // namespace byte
#endif
