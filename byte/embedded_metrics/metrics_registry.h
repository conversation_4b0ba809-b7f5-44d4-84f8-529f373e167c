// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <iostream>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "byte/concurrent/mutex.h"
#include "byte/embedded_metrics/metrics.h"
#include "byte/include/assert.h"
#include "byte/include/macros.h"
#include "byte/include/status.h"
#include "byte/thread/timer_thread.h"
#include "byte/util/process_utils.h"
#include "nlohmann/json.hpp"

namespace spdlog {
class logger;
}

#if defined(BYTE_ENABLE_METRICS2)
#if __cplusplus >= 201402L  // C++14 and above
#define BYTE_METRICS1_ONLY(msg) [[deprecated(msg)]]
#elif __cplusplus >= 201103L  // C++11
#define BYTE_METRICS1_ONLY(msg) __attribute__((deprecated(msg)))
#else
#define BYTE_METRICS1_ONLY(msg)
#endif
#else
#define BYTE_METRICS1_ONLY(msg)
#endif

namespace byte {

constexpr char kDefaultTenant[8] = "default";

namespace embedded_metrics {

constexpr char kMetricsServerIp[] = "127.0.0.1";
const uint32_t kMetricsServerPort = 9123;

// Enum representing MetricsMode with three types: kPush, kPull, and kPush2.
// For metric agent data emission, it's suggested to use metrics 2.0.
// kPush(metrics 1.0): one global static MetricsRegistry pushes metrics to metric agent
//  at regular intervals: 30s. The reporter talking to metric agent is in byte/metrics.
// kPush2(metrics 2.0): one global static MetricsRegistry instance can emit
//  metrics at regular intervals: 30s. Client can also set up tenant and emit metircs
//  with specific interval using customized metirc registry instance.
//  The reporter talking to metric agent is in cpp_util/metrics_sdk.
// kPull: All the metrics are stored in the memory, no reporter.
//   User can fetch all serialized metrics using the PullAllMetrics method.
enum class MetricsMode {
    kPush,  // default
    kPull,
    kPush2,  // metrics with tenant.
};

enum class MetricsFormat {
    kPrometheus
};

class MetricsOptions {
public:
    MetricsOptions() = default;
    bool Parse(const nlohmann::json& j);

    std::string server_ip{kMetricsServerIp};
    uint16_t server_port{kMetricsServerPort};
    bool enable_logging{true};
    uint32_t counter_window_size{30};
    // Note:
    // 1. In MetricsMode::kPush2, report_interval_secs sets the report interval
    //    for default tenant; report interval eventually picks up the lower interval from
    //    {1, 5, 10, 15, 30, 60}.
    // 2. In MetricsMode::kPush, report_interval_secs defines how frequently metric SDK
    //    sends metrics to the agent, but the agent sample interval is always 30s.
    // 3. In MetricsMode::kPull, report_interval_secs sets the agent sample interval as well.
    uint32_t report_interval_secs{10};
    size_t send_batch_size{1};
    // whether to increase batch size automatically in certain cases
    int auto_batch{0};
    // whether enable retry logic to metrics 1.0 (kPush mode) client.
    int metrics1_retry{0};
#ifdef BYTE_ENABLE_METRICS2
    // Note: default support kPush2 mode when BYTE_ENABLE_METRICS2 is on.
    MetricsMode mode{MetricsMode::kPush2};
#else
    MetricsMode mode{MetricsMode::kPush};
#endif
    // Note:
    // In MetricsMode::kPush2 mode, tenant should be set properly.
    // All the metrics that are created by default will
    // be reported to the metric agent per report_interval_secs.
    // Note that these tenants are not effective in kPush/kPull mode.
    std::string tenant = kDefaultTenant;
    bool disable_builtin_metrics = false;
};

using Metrics2TagKeys = std::unordered_set<std::string>;
using Metrics2Fields = std::unordered_set<std::string>;

#ifdef BYTE_ENABLE_METRICS2
/**
 * Not thread safe. Call this before any MetricsRegistry Initialize functions.
 *
 * @brief Set the maximum number of native metrics per registry.
 *
 * @param native_metrics_num The number of native metrics to set.
 *
 * @return true if either:
 *         - The native_metrics_num is the same as the current maximum native metrics number per
 *           registry, or
 *         - The set operation is successful because it is the first time being set.
 *
 *         false if the native metrics number is not correctly set.
 */
bool SetGlobalMaxNativeMetricsNumPerRegistry(size_t native_metrics_num);
size_t GetGlobalMaxNativeMetricsNumPerRegistry();
#endif

namespace internal {

struct NativeMetricConfig {
    Metrics2TagKeys tag_keys;
    Metrics2Fields fields;
    MetricType type;
};

// To decouple the class MetricsRegistry and the static functions from metrics 1.0 and metrics 2.0
class MetricsReporter {
public:
    MetricsReporter() {}
    // This function is only used by kPush mode
    BYTE_METRICS1_ONLY("Use FlushAndStop instead") virtual int Flush() = 0;
    // This function is used in metrics 1.0 (kPush) and metrics 2.0 (kPush2) mode.
    // metrics 2.0 sdk client will quit after the function call.
    virtual int FlushAndStop() = 0;
    virtual int Init(const std::string& prefix, const MetricsOptions& opts) = 0;
    virtual std::unique_ptr<NativeMetric> CreateNativeMetric(
        const byte::StringPiece& metric_name, const internal::NativeMetricConfig& config,
        const Tags& sorted_tags, const std::string& field) = 0;
    virtual ~MetricsReporter() {}
};
}  // namespace internal

class MetricsRegistry {
public:
    // WARNING: Each tenant needs to create its own registry for MetricsMode::kPush2.
    MetricsRegistry();
    ~MetricsRegistry();

#ifdef BYTE_ENABLE_METRICS2
    // Retrieves the primary MetricsRegistry instance.
    // Recommended for default tenant usage.
    // NOTE: Call Initialize() first before use.
    static MetricsRegistry& GetPrimaryRegistry() {
        static MetricsRegistry* primary_reg = new MetricsRegistry();
        return *primary_reg;
    }

    // Retrieves the secondary MetricsRegistry instance.
    // Recommended for high precision tenant usage.
    // This registry is optional and should be used when metrics need to be emitted at high
    // frequency (e.g., per second). When using this registry, ensure that
    // MetricsOptions::disable_builtin_metrics is set to true. NOTE: Call
    // Initialize() first before use.
    static MetricsRegistry& GetSecondaryRegistry() {
        static MetricsRegistry* second_reg = new MetricsRegistry();
        return *second_reg;
    }

    /*
     *
     * Registers a metric name with the specified tag keys and optional fields.
     * This will has constrains on multiple byte metrics with the same metric name.
     * Use GetByteMetric later to create a byte metric. The byte metric is a
     * metric series with fixed tags and field(optional).
     *
     * Tag Rules for kPush2 mode:
     * - Tag Key: Non-empty, < 255 characters, containing "a-zA-Z0-9._-:/%" (no spaces or returns).
     * - Tag Value: Same as Tag Key, plus " ,[]".
     * Invalid tags are ignored; valid tags are parsed. Tags are used in all modes.
     *
     * Multi-fields metric in kPush2 mode:
     * - Field: Same as Tag Key rule.
     * - Fields Info: List of field strings, must include the field.
     *   Invalid fields are ignored. At least one valid field is required to create the metric.
     * - Field and Fields Info are only used in kPush2 mode.
     *
     * Returns false if the metric name is registered before.
     */
    bool RegisterMetricName(const std::string& name, MetricType type,
                            const Metrics2TagKeys& tag_keys, const Metrics2Fields& fields = {});

    /*
     * This function will return a byte metric
     * The byte metric is a metric series with fixed tags and field(opitonal).
     *
     * Example for multi-tags metrics:
     * RegisterMetricName("name1", kTlsHistogram, {"tag_key1", "tag_key2"});
     * GetByteMetric("name1", kTlsHistogram, {{"tag_key1", "v1"}});
     * GetByteMetric("name1", kTlsHistogram, {{"tag_key2", "v1"}});
     * GetByteMetric("name1", kTlsHistogram, {{"tag_key1", "v1"},
     * {"tag_key2", "v2"}});
     *
     * Example for multi-fields metrics:
     * RegisterMetricName("name2", kTlsHistogram, {"key"}, {"field1", "field2", "field3"});
     * GetByteMetric("name2", kTlsHistogram, {{"key", "val"}}, "field1");
     * GetByteMetric("name2", kTlsHistogram, {{"key", "val"}}, "field2");
     * GetByteMetric("name2", kTlsHistogram, {{"key", "val"}}, "field3");
     *
     * NOTE: If there are duplicate tag keys in the tags, the tag value will be the last one
     * specified.
     */
    std::shared_ptr<MetricBase> GetByteMetric(const std::string& name, MetricType type,
                                              const Tags& tags, const std::string& field = "");

    bool IsInitalized() { return initialized_; }
#else  // BYTE_ENABLE_METRICS2 = OFF
    // WARNING: If you plan to use MetricsMode::kPush2, enable BYTE_ENABLE_METRICS2 and use
    // GetPrimaryRegistry for default tenant and GetSeMetricsRegistry for high precision tenant.
    static MetricsRegistry& Registry() {
        static MetricsRegistry reg;
        return reg;
    }

    /*
     * Registers a single metric for kPush and kPull modes when BYTE_ENABLE_METRICS2 is OFF.
     * If a metric with the same name and tags already exists, returns the existing metric.
     * If the existing metric has a different type, an assertion error is triggered.
     * Otherwise, creates a new byte metric and returns it.
     *
     * NOTE: If there are duplicate tag keys in the tags, the tag value will be the last one
     * specified.
     */
    std::shared_ptr<MetricBase> Register(const byte::StringPiece& name, const Tags& tags,
                                         MetricType t);
#endif

    // NOTE: Call SetGlobalMaxNativeMetricsCntPerRegistry before All Initialize function if
    // customer needs more than 1024 native metrics per registry.
    /*
     * common_tags Rules for kPush2 mode:
     * - Tag Key: Non-empty, < 255 characters, containing "a-zA-Z0-9._-:/%" (no spaces or returns).
     * - Tag Value: Same as Tag Key, plus " ,[]".
     * Invalid tags are ignored; valid tags are parsed.
     */
    bool Initialize(const std::string& prefix, const Tags& common_tags, const MetricsOptions& opts,
                    std::shared_ptr<spdlog::logger> logger);
    // thread_classfier classify the threads into thread groups.
    // arg(string) : thread name
    // return(string) : thread_group name
    bool Initialize(const std::string& prefix, const Tags& common_tags, const MetricsOptions& opts,
                    std::shared_ptr<spdlog::logger> loggger,
                    std::function<std::string(std::string)> thread_classifier);

    void Unregister(const std::string& uniq_name);
    void SetTags(const Tags& common_tags);
    void JoinTags(const Tags& common_tags);

    // Flushes metrics to the agent (only in kPush mode).
    BYTE_METRICS1_ONLY("Use FlushAndStop instead") void Flush();
    // Flushes metrics to the agent and halts further reporting (kPush and kPush2 modes).
    // No subsequent metrics can be reported after this call.
    void FlushAndStop();

    void GetSnapshot(std::vector<std::shared_ptr<MetricBase>>* snapshot, Tags* common_tags);
    // return all metrics with given format
    Status PullAllMetrics(MetricsFormat format, std::string* serialized);

private:
    void logMetric(bool enable_logging, const std::string& metric_uniqname, double val,
                   const char* type);
    void report();
    std::shared_ptr<MetricBase> GetOrCreateMetric(const byte::StringPiece& name,
                                                  const Tags& sorted_tags, const std::string& field,
                                                  internal::NativeMetricConfig config);
    std::shared_ptr<MetricBase> createMetric(const byte::StringPiece& name,
                                             const std::string& uniq_name, const Tags& sorted_tags,
                                             const std::string& field,
                                             internal::NativeMetricConfig config);
    void createBuiltinMetrics();
    void updateBuiltinMetrics();
    void updateMemoryMetrics(util::ProcessStat* stat);
    void updateCpuMetrics(util::ProcessStat* stat);
    void stopReporterLoop();

protected:
    std::atomic<bool> initialized_{false};
    byte::TimerThread reporter_;
    uint64_t task_id_;
    std::unique_ptr<internal::MetricsReporter> metrics_reporter_;

private:
    util::HostStat host_stat_;

    std::string prefix_;
    Tags common_tags_;
    MetricsOptions opts_;
    std::shared_ptr<spdlog::logger> logger_;
    std::function<std::string(std::string)> thread_classifier_;

    std::vector<std::shared_ptr<MetricBase>> memory_metrics_;
    std::shared_ptr<MetricBase> total_cpu_usage_;
    int64_t last_total_jiffies_{0};
    std::map<std::string, std::shared_ptr<MetricBase>> threadpool_cpu_usage_;
    std::map<std::string, int64_t> last_threadpool_jiffies_;
    uint64_t last_report_nanos_;

    absl::Mutex mu_;
    struct MetricRef {
        std::shared_ptr<MetricBase> metric;
        uint32_t user_ref;
    };
    std::unordered_map<std::string, MetricRef> metrics_ ABSL_GUARDED_BY(mu_);

    absl::Mutex config_mu_;
    // ASAN might detect a heap-buffer-overflow error when
    // name_to_configs_ is an object and allocated in the heap for MetricsRegistry.
    // Reallocate name_to_configs_ to ensure safe heap allocation.
    std::unique_ptr<absl::flat_hash_map<std::string, internal::NativeMetricConfig>> name_to_configs_
        ABSL_GUARDED_BY(config_mu_);

#ifdef BYTE_ENABLE_METRICS2
    size_t native_metrics_cnt_ = 0;
#endif

    DISALLOW_COPY_AND_ASSIGN(MetricsRegistry);
};

}  // namespace embedded_metrics
}  // namespace byte
