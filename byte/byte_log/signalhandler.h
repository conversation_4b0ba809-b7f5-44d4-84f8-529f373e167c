// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

namespace byte {

void DumpStackTrace(int skip_count, int max_depth = -1);

void DumpStackTraceAndExit();

bool IsFailureSignalHandlerInstalled();

void InstallFailureSignalHandler();

void InstallDumpStackWriter(void (*writer)(const char* data, int size));

struct StackInfo {
    void* stack[32] = {nullptr};
    int depth = 0;
    std::string user_hint;
};

StackInfo GetStackTraceInfo(int skip_count, int max_depth = -1);

// NOTE: user should care about the StackInfo lifespan management
//       e.g., cross-thread scenarios
void SymbolizeStackTraceInfo(const StackInfo& info);

}  // namespace byte
