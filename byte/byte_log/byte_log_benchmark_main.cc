// Copyright (c) 2023, ByteDance Inc. All rights reserved.
#include "benchmark/benchmark.h"
#include "byte/include/byte_log.h"

static void BMByteLogWithCompression(benchmark::State& state) {  // NOLINT(runtime/references)
    byte::EnableByteLogCompression();
    // Perform setup here
    for (auto _ : state) {
        std::chrono::duration<double> elapsed_seconds = std::chrono::duration<double>();
        for (uint32_t i = 0; i < 100000; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            LOG(INFO) << "The quick brown fox jumps over a lazy dog. "
                      << "Pack my box with five dozen liquor jugs.";
            elapsed_seconds += (std::chrono::high_resolution_clock::now() - start);
        }
        state.SetIterationTime(elapsed_seconds.count());
    }
    byte::DisableByteLogCompression();
}

static void BMByteLogWithoutCompression(benchmark::State& state) {  // NOLINT(runtime/references)
    // Perform setup here
    for (auto _ : state) {
        std::chrono::duration<double> elapsed_seconds = std::chrono::duration<double>();
        for (uint32_t i = 0; i < 100000; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            LOG(INFO) << "The quick brown fox jumps over a lazy dog. "
                      << "Pack my box with five dozen liquor jugs.";
            elapsed_seconds += (std::chrono::high_resolution_clock::now() - start);
        }
        state.SetIterationTime(elapsed_seconds.count());
    }
}

// Register the function as a benchmark
BENCHMARK(BMByteLogWithoutCompression)->ThreadRange(1, 64)->UseManualTime();
BENCHMARK(BMByteLogWithCompression)->ThreadRange(1, 64)->UseManualTime();
//  Run the benchmark
BENCHMARK_MAIN();
