// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <sys/uio.h>

#include "byte/container/lru_cache.h"
#include "byte/container/radix_tree.h"
#include "byte/algorithm/crc32.h"

namespace byte {

class BlockCache {
public:
    BlockCache(size_t total_capacity, size_t block_capacity);

    BlockCache(size_t total_capacity, size_t block_capacity, bool enable_crc);

    ~BlockCache();

    void Release(void* ref);

    Status Lookup(const Slice& key, uint32_t hash, uint32_t pos, Slice* value, void** ref);

    Status Insert(const Slice& key, uint32_t hash, uint32_t pos, const Slice& value);

    // The iov_count is the number of elements in vec, and the sum of all elements should be
    // matched with the parameter length.
    Status Insert(const Slice& key, uint32_t hash, uint32_t pos, uint32_t length,
                  iovec* vec, uint32_t iov_count);

private:
    LRUCacheShard* cache_;
    size_t total_capacity_;
    size_t block_capacity_;
    bool enable_crc_;
};

}  // namespace byte
