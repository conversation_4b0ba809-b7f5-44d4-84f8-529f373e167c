// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <map>
#include <utility>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/base/hash.h"
#include "byte/container/btree/btree_map.h"
#include "byte/include/assert.h"
#include "byte/include/macros.h"

namespace byte {

// A fixed size lock free concurrent hash table implementation.
template<typename Key, typename Value = void*>
class ConcurrentHashTable {
public:
    ConcurrentHashTable() : mNumBuckets(0) {}

    explicit ConcurrentHashTable(uint32_t numBuckets);

    ~ConcurrentHashTable();

    // Given a key, find its value.
    // Returns true iff `key' exists, and `value' points to real value.
    bool Find(Key key, Value* value = NULL) const;

    // Insert a pair of <key, value>.
    // Returns false iff insert failed.
    bool Insert(const std::pair<Key, Value>& pair);

    // Replace a pair of <key, value>.
    void Replace(const std::pair<Key, Value>& pair);

    // Get all the <key, value> pairs from the given bucket.
    void GetBucket(const uint32_t index, btree::btree_map<Key, Value>* bucket) const;

    void GetBucket(const uint32_t index, std::map<Key, Value>* bucket) const;

    void ClearValues();

    static inline uint32_t Hash(Key key, uint32_t maxSize) {
        return XXHash(reinterpret_cast<const char*>(&key),
                      sizeof(key)) %  maxSize;
    }

private:
    struct Node {
        Node() : mNext(NULL) {}
        Node(Node* next, Key key, Value value)
            : mNext(next), mKey(key), mValue(value) {}
        Node* mNext;
        Key mKey;
        Value mValue;
    };

    void clear();
    bool findNode(const uint32_t index, Key key, Node** foundNode) const;
    bool insert(Key key, Value value, bool force);

    std::vector<Node*> mBuckets;
    uint32_t mNumBuckets;

    friend class Iterator;

public:
    // A *Forward Only* iterator over ConcurentHashTable
    class Iterator {
    public:
        // Initialize an iterator over the specified ConcurrentHashTable.
        // The returned iterator is not valid.
        explicit Iterator(const ConcurrentHashTable* htable);

        // Returns true iff the iterator is positioned at a valid node.
        bool Valid() const;

        // Advances to next position
        // @Requires: Valid()
        void Next();

        // Returns the key at current position
        // @Requires: Valid()
        const Key& key();

        // Returns the Value at current position
        // @Requires: Valid()
        const Value& value();

        // Point to the first entry in ConcurrentHashTable
        void SeekToFirst();

        // Point to the entry equal or bigger than key
        void Seek(const Key& key);

    private:
        // Try to find first valid node start from mBucketIndex.
        // Returns NULL iff none valid node found
        Node* findFirstValidNode();

        const ConcurrentHashTable* mTable;
        size_t mBucketIndex;
        Node* mNode;
    };
};

template<typename Key, typename Value>
ConcurrentHashTable<Key, Value>::ConcurrentHashTable(uint32_t numBuckets) {
    mBuckets.assign(numBuckets, NULL);
    mNumBuckets = numBuckets;
}

template<typename Key, typename Value>
ConcurrentHashTable<Key, Value>::~ConcurrentHashTable() {
    clear();
}

template<typename Key, typename Value>
bool ConcurrentHashTable<Key, Value>::Find(Key key, Value* value) const {
    const uint32_t index = Hash(key, mNumBuckets);
    Node* node;
    if (!findNode(index, key, &node)) {
        return false;
    }
    if (value != NULL) {
        *value = node->mValue;
    }
    return true;
}

template<typename Key, typename Value>
bool ConcurrentHashTable<Key, Value>::Insert(const std::pair<Key, Value>& pair) {
    return insert(pair.first, pair.second, false);
}

template<typename Key, typename Value>
void ConcurrentHashTable<Key, Value>::Replace(const std::pair<Key, Value>& pair) {
    insert(pair.first, pair.second, true);
}

template<typename Key, typename Value>
void ConcurrentHashTable<Key, Value>::GetBucket(const uint32_t index,
    btree::btree_map<Key, Value>* bucket) const {
    Node* node = AtomicGet(&(mBuckets[index]));
    bucket->clear();
    while (node != NULL) {
        bucket->insert(std::make_pair(node->mKey, node->mValue));
        node = AtomicGet(&(node->mNext));
    }
}

template<typename Key, typename Value>
void ConcurrentHashTable<Key, Value>::GetBucket(const uint32_t index,
    std::map<Key, Value>* bucket) const {
    Node* node = AtomicGet(&(mBuckets[index]));
    bucket->clear();
    while (node != NULL) {
        bucket->insert(std::make_pair(node->mKey, node->mValue));
        node = AtomicGet(&(node->mNext));
    }
}

template<typename Key, typename Value>
void ConcurrentHashTable<Key, Value>::ClearValues() {
    for (uint32_t i = 0; i < mNumBuckets; ++i) {
        Node* node = mBuckets[i];
        while (node != NULL) {
            Node* next = node->mNext;
            delete node->mValue;
            node = next;
        }
    }
}

template<typename Key, typename Value>
void ConcurrentHashTable<Key, Value>::clear() {
    for (uint32_t i = 0; i < mNumBuckets; ++i) {
        Node* node = mBuckets[i];
        while (node != NULL) {
            Node* next = node->mNext;
            delete node;
            node = next;
        }
    }
}

template<typename Key, typename Value>
bool ConcurrentHashTable<Key, Value>::findNode(const uint32_t index, Key key,
    Node** foundNode) const {
    Node* node = AtomicGet(&(mBuckets[index]));
    while (node != NULL) {
        Node* next = AtomicGet(&(node->mNext));
        if (node->mKey == key) {
            if (foundNode != NULL) {
                *foundNode = node;
            }
            return true;
        }
        node = next;
    }
    return false;
}

template<typename Key, typename Value>
bool ConcurrentHashTable<Key, Value>::insert(Key key, Value value, bool force) {
    const uint32_t index = Hash(key, mNumBuckets);
    while (true) {
        Node* node = NULL;
        if (!findNode(index, key, &node)) {
            // Create a new entry and insert it.
            Node* next = AtomicGet(&(mBuckets[index]));
            Node* newNode = new Node(next, key, value);
            if (LIKELY(AtomicCompareExchange(
                    &(mBuckets[index]),
                    next,
                    newNode))) {
                return true;
            }
            // Failed to insert. Retry again.
            delete newNode;
            continue;
        }
        // Found an entry.
        if (force) {
            AtomicSet(&(node->mValue), value);
            return true;
        } else {
            break;
        }
    }
    return false;
}

template<typename Key, typename Value>
ConcurrentHashTable<Key, Value>::Iterator::Iterator(
    const ConcurrentHashTable* htable) {
    mTable = htable;
    mBucketIndex = 0;
    mNode = NULL;
    SeekToFirst();
}

template<typename Key, typename Value>
inline bool ConcurrentHashTable<Key, Value>::Iterator::Valid() const {
    return mNode != NULL;
}

template<typename Key, typename Value>
inline void ConcurrentHashTable<Key, Value>::Iterator::Next() {
    BYTE_ASSERT(Valid());
    mNode = mNode->mNext;
    if (mNode) return;

    mBucketIndex++;
    mNode = findFirstValidNode();
}

template<typename Key, typename Value>
inline const Key& ConcurrentHashTable<Key, Value>::Iterator::key() {
    BYTE_ASSERT(Valid());
    return mNode->mKey;
}

template<typename Key, typename Value>
inline const Value& ConcurrentHashTable<Key, Value>::Iterator::value() {
    BYTE_ASSERT(Valid());
    return mNode->mValue;
}

template<typename Key, typename Value>
inline void ConcurrentHashTable<Key, Value>::Iterator::SeekToFirst() {
    mBucketIndex = 0;
    mNode = findFirstValidNode();
}

template<typename Key, typename Value>
typename ConcurrentHashTable<Key, Value>::Node*
    ConcurrentHashTable<Key, Value>::Iterator::findFirstValidNode() {
    for (; mBucketIndex != mTable->mBuckets.size(); ++mBucketIndex) {
        Node* node = AtomicGet(&(mTable->mBuckets[mBucketIndex]));
        if (node) {
            return node;
        }
    }
    return NULL;
}

}  // namespace byte
