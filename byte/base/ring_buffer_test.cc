// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "byte/base/ring_buffer.h"
#include "gtest/gtest.h"

namespace byte {

TEST(RingBuffer, Basic) {
    RingBuffer<int> ring_buffer(4096);

    EXPECT_TRUE(ring_buffer.push(1));
    EXPECT_TRUE(ring_buffer.push(2));
    EXPECT_TRUE(ring_buffer.push(3));
    EXPECT_TRUE(ring_buffer.push(4));
    int v = 0;
    EXPECT_TRUE(ring_buffer.pop(&v));
    EXPECT_EQ(1, v);
    EXPECT_EQ(3, ring_buffer.size());
    EXPECT_FALSE(ring_buffer.empty());
    EXPECT_EQ(2, ring_buffer.front());
    EXPECT_EQ(4, ring_buffer.back());
    RingBuffer<int>::iterator i = ring_buffer.begin();
    int j = 2;
    while (i != ring_buffer.end()) {
        EXPECT_EQ(j, *i);
        ++i;
        ++j;
    }
    EXPECT_TRUE(ring_buffer.pop(&v));
    EXPECT_TRUE(ring_buffer.pop(&v));
    EXPECT_TRUE(ring_buffer.pop(&v));
    EXPECT_TRUE(ring_buffer.empty());
    EXPECT_FALSE(ring_buffer.pop(&v));
    // Test resize
    RingBuffer<int> ring_buffer2(4);
    EXPECT_TRUE(ring_buffer2.push(1));
    EXPECT_TRUE(ring_buffer2.push(2));
    EXPECT_TRUE(ring_buffer2.push(3));
    EXPECT_TRUE(ring_buffer2.pop(&v));
    EXPECT_TRUE(ring_buffer2.push(4));
    EXPECT_EQ(4, ring_buffer2.capacity());
    EXPECT_EQ(3, ring_buffer2.size());
    ring_buffer2.resize(6);
    EXPECT_TRUE(ring_buffer2.push(5));
    EXPECT_TRUE(ring_buffer2.push(6));
    EXPECT_EQ(5, ring_buffer2.size());
    EXPECT_TRUE(ring_buffer2.push(7));
    EXPECT_EQ(6, ring_buffer2.capacity());
    EXPECT_EQ(6, ring_buffer2.size());
    EXPECT_FALSE(ring_buffer2.push(8));
    ring_buffer2.force_push(8);
    EXPECT_EQ(6, ring_buffer2.size());
    EXPECT_TRUE(ring_buffer2.pop(&v));
    EXPECT_EQ(5, ring_buffer2.size());
    EXPECT_EQ(6, ring_buffer2.capacity());
}

}  // namespace byte
