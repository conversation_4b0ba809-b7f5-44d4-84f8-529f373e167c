// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
// Modified from facebook folly.

/*
 * Copyright 2015 Facebook, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Improved thread local storage for non-trivial types (similar speed as
 * pthread_getspecific but only consumes a single pthread_key_t, and 4x faster
 * than boost::thread_specific_ptr).
 *
 * Also includes an accessor interface to walk all the thread local child
 * objects of a parent.  AccessAllThreads() initializes an accessor which holds
 * a global lock *that blocks all creation and destruction of ThreadLocal
 * objects and can be used as an iterable container.
 *
 * Intended use is for frequent write, infrequent read data access patterns such
 * as counters.
 *
 * There are two classes here - ThreadLocal and ThreadLocalPtr.  ThreadLocalPtr
 * has semantics similar to boost::thread_specific_ptr. ThreadLocal is a thin
 * wrapper around ThreadLocalPtr that manages allocation automatically.
 *
 * <AUTHOR> Ahrens (sahrens)
 */

#pragma once

#include <stddef.h>
#include <stdint.h>
#include <vector>
#include "byte/concurrent/mutex.h"
#include "byte/container/list.h"
#include "byte/include/assert.h"
#include "byte/include/macros.h"

namespace byte {

enum TLPDestructionMode {
  TLP_DESTRUCTION_THIS_THREAD,
  TLP_DESTRUCTION_ALL_THREADS
};

namespace threadlocal_detail {

/**
 * Base class for deleters.
 */
class DeleterBase {
public:
  virtual ~DeleterBase() {}
  virtual void dispose(void* ptr, TLPDestructionMode mode) const = 0;
};

/**
 * Simple deleter class that calls delete on the passed-in pointer.
 */
template <class Ptr>
class SimpleDeleter : public DeleterBase {
public:
  virtual void dispose(void* ptr, TLPDestructionMode mode) const {
    delete static_cast<Ptr>(ptr);
  }
};

/**
 * Custom deleter that calls a given callable.
 */
template <class Ptr, class Deleter>
class CustomDeleter : public DeleterBase {
public:
  explicit CustomDeleter(Deleter d) : deleter_(d) { }
  virtual void dispose(void* ptr, TLPDestructionMode mode) const {
    deleter_(static_cast<Ptr>(ptr), mode);
  }
private:
  Deleter deleter_;
};


/**
 * POD wrapper around an element (a void*) and an associated deleter.
 * This must be POD, as we memset() it to 0 and memcpy() it around.
 */
struct ElementWrapper {
  bool dispose(TLPDestructionMode mode);
  void* release();
  // clean up customized deleter if owned.
  void cleanup();

  template <class Ptr>
  void set(Ptr p) {
    BYTE_ASSERT_DEBUG(ptr == NULL);
    BYTE_ASSERT_DEBUG(deleter == NULL);

    if (p) {
      // We leak a single object here but that is ok.  If we used an
      // object directly, there is a chance that the destructor will be
      // called on that static object before any of the ElementWrappers
      // are disposed and that isn't so nice.
      static SimpleDeleter<Ptr>* d = new SimpleDeleter<Ptr>();
      ptr = p;
      deleter = d;
      ownsDeleter = false;
    }
  }

  template <class Ptr, class Deleter>
  void set(Ptr p, Deleter d) {
    BYTE_ASSERT_DEBUG(ptr == NULL);
    BYTE_ASSERT_DEBUG(deleter == NULL);
    if (p) {
      ptr = p;
      deleter = new CustomDeleter<Ptr, Deleter>(d);
      ownsDeleter = true;
    }
  }

  void* ptr;
  DeleterBase* deleter;
  bool ownsDeleter;
};

/**
 * Per-thread entry.  Each thread using a StaticMeta object has one.
 * This is written from the owning thread only (under the lock), read
 * from the owning thread (no lock necessary), and read from other threads
 * (under the lock).
 */
struct ThreadEntry {
  ElementWrapper* elements;
  size_t elementsCapacity;
  struct list_head node;
};

// Held in a singleton to track our global instances.
//
// Creating and destroying ThreadLocalPtr objects, as well as thread exit
// for threads that use ThreadLocalPtr objects collide on a lock inside
// StaticMeta; you can specify multiple Tag types to break that lock.
class StaticMeta {
public:
  static StaticMeta* instance();

  uint32_t nextId_;
  std::vector<uint32_t> freeIds_;
  Mutex lock_;
  pthread_key_t pthreadKey_;
  struct list_head head_;

  void push_back(ThreadEntry* t);
  void erase(ThreadEntry* t);

  static __thread ThreadEntry threadEntry_;
  static StaticMeta* inst_;

  StaticMeta();
  ~StaticMeta();

  static ThreadEntry* getThreadEntry() {
    return &threadEntry_;
  }

  static void onThreadExit(void* ptr);
  static uint32_t create();
  static void destroy(uint32_t id);
  static void reserve(uint32_t id);
  static inline ElementWrapper& get(uint32_t id);

private:
  DISALLOW_COPY_AND_ASSIGN(StaticMeta);
};

}  // namespace threadlocal_detail

template<class T> class ThreadLocalPtr;

// Use Cases for Thread-Local Storage:
// https://www.open-std.org/jtc1/sc22/wg21/docs/papers/2014/n4324.html
template<class T>
class ThreadLocal {
public:
  typedef typename ThreadLocalPtr<T>::Accessor Accessor;
  typedef typename ThreadLocalPtr<T>::Iterator Iterator;

  ThreadLocal() {}

  T* Get() const {
    T* ptr = tlp_.Get();
    if (LIKELY(ptr != NULL)) {
      return ptr;
    }

    // separated new item creation out to speed up the fast path.
    return makeTlp();
  }

  T* operator->() const { return Get(); }

  T& operator*() const { return *Get(); }

  void Reset(T* newPtr = NULL) { tlp_.reset(newPtr); }

  void AccessAllThreads(Accessor* accessor) const {
    tlp_.AccessAllThreads(accessor);
  }

private:
  T* makeTlp() const {
    T* ptr = new T();
    tlp_.Reset(ptr);
    return ptr;
  }

  mutable ThreadLocalPtr<T> tlp_;

  DISALLOW_COPY_AND_ASSIGN(ThreadLocal);
};

/*
 * The idea here is that __thread is faster than pthread_getspecific, so we
 * keep a __thread array of pointers to objects (ThreadEntry::elements) where
 * each array has an index for each unique instance of the ThreadLocalPtr
 * object.  Each ThreadLocalPtr object has a unique id that is an index into
 * these arrays so we can fetch the correct object from thread local storage
 * very efficiently.
 *
 * In order to prevent unbounded growth of the id space and thus huge
 * ThreadEntry::elements, arrays, for example due to continuous creation and
 * destruction of ThreadLocalPtr objects, we keep a set of all active
 * instances.  When an instance is destroyed we remove it from the active
 * set and insert the id into freeIds_ for reuse.  These operations require a
 * global mutex, but only happen at construction and destruction time.
 *
 * We use a single global pthread_key_t manage object destruction and
 * memory cleanup upon thread exit because there is a finite number of
 * pthread_key_t's available per machine.
 *
 * NOTE: Apple platforms don't support the same semantics for __thread that
 *       Linux does (and it's only supported at all on i386). For these, use
 *       pthread_setspecific()/pthread_getspecific() for the per-thread
 *       storage.  Windows (MSVC and GCC) does support the same semantics
 *       with __declspec(thread)
 */

template<class T>
class ThreadLocalPtr {
public:
  class Accessor;
  class Iterator;

  ThreadLocalPtr() : id_(threadlocal_detail::StaticMeta::create()) {}

  ~ThreadLocalPtr() { destroy(); }

  T* Get() const {
    return static_cast<T*>(threadlocal_detail::StaticMeta::get(id_).ptr);
  }

  T* operator->() const { return Get(); }

  T& operator*() const { return *Get(); }

  T* Release() {
    threadlocal_detail::ElementWrapper& w =
      threadlocal_detail::StaticMeta::get(id_);

    return static_cast<T*>(w.release());
  }

  void Reset(T* newPtr = NULL) {
    threadlocal_detail::ElementWrapper& w =
      threadlocal_detail::StaticMeta::get(id_);
    if (w.ptr != newPtr) {
      w.dispose(TLP_DESTRUCTION_THIS_THREAD);
      w.set(newPtr);
    }
  }

  operator bool() const { return Get() != NULL; }

  /**
   * reset() with a custom deleter:
   * deleter(T* ptr, TLPDestructionMode mode)
   * "mode" is ALL_THREADS if we're destructing this ThreadLocalPtr (and thus
   * deleting pointers for all threads), and THIS_THREAD if we're only deleting
   * the member for one thread (because of thread exit or reset())
   */
  template <class Deleter>
  void Reset(T* newPtr, Deleter deleter) {
    threadlocal_detail::ElementWrapper& w =
      threadlocal_detail::StaticMeta::get(id_);
    if (w.ptr != newPtr) {
      w.dispose(TLP_DESTRUCTION_THIS_THREAD);
      w.set(newPtr, deleter);
    }
  }

  // accessor allows a client to iterate through all thread local child
  // elements of this ThreadLocal instance.  Holds a global lock for each <Tag>
  void AccessAllThreads(Accessor* accessor) const {
    // static_assert(!std::is_same<Tag, void>::value,
    //               "Must use a unique Tag to use the accessAllThreads feature");
    accessor->reset(id_);
  }

private:
  void destroy() {
    if (id_) {
      threadlocal_detail::StaticMeta::destroy(id_);
    }
  }

  uint32_t id_;  // every instantiation has a unique id

  DISALLOW_COPY_AND_ASSIGN(ThreadLocalPtr);
};

// Holds a global lock for iteration through all thread local child objects.
// Can be used as an iterable container.
// Use accessAllThreads() to obtain one.
template <typename T>
class ThreadLocalPtr<T>::Accessor {
  friend class ThreadLocalPtr<T>;

  threadlocal_detail::StaticMeta* meta_;
  Mutex* lock_;
  uint32_t id_;

public:
  friend class ThreadLocalPtr<T>::Iterator;

  ~Accessor() { release(); }

  Iterator begin() const { return ++Iterator(this); }

  Iterator end() const { return Iterator(this); }

  Accessor()
    : meta_(threadlocal_detail::StaticMeta::instance()),
      lock_(NULL),
      id_(0) {
  }

private:
  void reset(uint32_t id) {
    release();
    meta_ = threadlocal_detail::StaticMeta::instance(),
    lock_ = &meta_->lock_;
    id_ = id;
    lock_->Lock();
  }

  void release() {
    if (lock_) {
      lock_->Unlock();
      id_ = 0;
      lock_ = NULL;
    }
  }

  DISALLOW_COPY_AND_ASSIGN(Accessor);
};

// The iterators obtained from Accessor are bidirectional iterators.
template <typename T>
class ThreadLocalPtr<T>::Iterator {
public:
  typedef Iterator self;
  typedef T value_type;
  typedef ptrdiff_t difference_type;
  typedef T* pointer;
  typedef T& reference;
  typedef std::bidirectional_iterator_tag iterator_category;

  Iterator() : accessor_(NULL), node_(NULL) {}

  bool operator==(const Iterator& that) { return equal(that); }

  bool operator!=(const Iterator& that) { return !equal(that); }

  self& operator++() { increment(); return *this; }

  self operator++(int) {
      self tmp(*this);
      ++*this;
      return tmp;
  }

  self& operator--() { decrement(); return *this; }

  self operator--(int) {
      self tmp(*this);
      --*this;
      return tmp;
  }

  pointer operator->() { return &dereference(); }

  reference operator*() { return dereference(); }

private:
  explicit Iterator(const Accessor* accessor)
    : accessor_(accessor),
      node_(&accessor_->meta_->head_) {
  }

  void increment() {
    node_ = node_->next;
    while (node_ != &accessor_->meta_->head_ && !valid()) {
      node_ = node_->next;
    }
  }

  void decrement() {
    node_ = node_->prev;
    while (node_ != &accessor_->meta_->head_ && !valid()) {
      node_ = node_->prev;
    }
  }

  T& dereference() const {
    threadlocal_detail::ThreadEntry* e =
        container_of(node_, threadlocal_detail::ThreadEntry, node);
    return *static_cast<T*>(e->elements[accessor_->id_].ptr);
  }

  bool equal(const Iterator& other) const {
    return (accessor_->id_ == other.accessor_->id_ && node_ == other.node_);
  }

  bool valid() const {
    threadlocal_detail::ThreadEntry* e =
        container_of(node_, threadlocal_detail::ThreadEntry, node);
    return (e->elements &&
            accessor_->id_ < e->elementsCapacity &&
            e->elements[accessor_->id_].ptr);
  }

  friend class Accessor;
  const Accessor* accessor_;
  struct list_head* node_;
};

namespace threadlocal_detail {
// IMPLEMENTATION OF StaticMeta
ElementWrapper& StaticMeta::get(uint32_t id) {
  ThreadEntry* threadEntry = getThreadEntry();
  if (UNLIKELY(threadEntry->elementsCapacity <= id)) {
    reserve(id);
    BYTE_ASSERT_DEBUG(threadEntry->elementsCapacity > id);
  }
  return threadEntry->elements[id];
}
}  // namespace threadlocal_detail

}  // namespace byte
