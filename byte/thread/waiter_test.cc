// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/thread/waiter.h"

#include "byte/include/status.h"
#include "gtest/gtest.h"

using byte::Waiter;

class WaiterTest : public testing::Test {
protected:
    WaiterTest() { tw_ = new Waiter(10); }
    ~WaiterTest() { delete tw_; }
    void SetUp() override {}
    void TearDown() override {}

    Waiter w_;
    Waiter* tw_;
};

TEST_F(WaiterTest, WaitCompleted) {
    w_.Signal();
    ASSERT_TRUE(w_.Wait().ok());
}

TEST_F(WaiterTest, WaitTimeout) {
    ASSERT_TRUE(tw_->Wait().IsTimeout());
    ASSERT_TRUE(w_.Wait(10).IsTimeout());
}

TEST_F(WaiterTest, Signal) {
    tw_->Signal();
    EXPECT_EQ(tw_->Wait().ok(), true);
}

TEST_F(WaiterTest, SignalStatus) {
    tw_->Signal(byte::Status::IOError("test"));
    EXPECT_EQ(tw_->Wait().IsIOError(), true);
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
