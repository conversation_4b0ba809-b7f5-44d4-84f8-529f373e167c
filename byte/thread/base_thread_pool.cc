// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
// Copyright (C) 2011, The Toft Authors.
// Authors: <AUTHORS>
//          CHEN Feng <<EMAIL>>
// Created: 2010-06-18
//
// Description:

#include "byte/base/hash.h"
#include "byte/container/intrusive_list.h"
#include "byte/include/assert.h"
#include "byte/include/scoped_ptr.h"
#include "byte/string/concat.h"
#include "byte/system/sys_info.h"
#include "byte/system/timestamp.h"
#include "byte/thread/base_thread_pool.h"
#include "byte/thread/this_thread.h"

namespace byte {

struct BaseThreadPool::Task {
    Task(Closure<void>* cb, const std::function<void()>& f) :
        callback(cb), function(f) {}
    intrusive_list_node link;
    Closure<void>* callback;
    std::function<void()> function;
};

struct BaseThreadPool::ThreadContext {
    typedef intrusive_list<BaseThreadPool::Task> TaskList;
    ThreadContext() : cond_(&mutex_), exit_(false) {}
    scoped_ptr<BaseThread> thread_;
    mutable Mutex mutex_;
    ConditionVariable cond_;
    TaskList pending_tasks_;  // __attribute__((aligned(64)));
    TaskList free_tasks_;  // __attribute__((aligned(64)));
    size_t thread_index_;
    bool exit_ __attribute__((aligned(64)));

    bool GetPendingTask(TaskList* tasks);
} __attribute__((aligned(64)));  // Make cache alignment.

// Return whether should continue (i.e. not exit). Note even if return false, the task may be not
// empty because of remaining tasks before exit was set.
bool BaseThreadPool::ThreadContext::GetPendingTask(TaskList* tasks) {
    MutexLocker locker(&mutex_);
    free_tasks_.splice(tasks);
    while (pending_tasks_.empty() && !exit_) {
        cond_.Wait();
    }
    tasks->splice(&pending_tasks_);
    return !exit_;
}

BaseThreadPool::BaseThreadPool(int num_threads, const std::string& name):
    num_threads_(0), num_alive_threads_(0), exit_cond_(&exit_lock_), exit_(false) {
    if (num_threads < 0)
        num_threads_ = GetLogicalCpuNumber();
    else if (num_threads == 0)
        num_threads_ = 1;
    else
        num_threads_ = num_threads;

    thread_contexts_ = new ThreadContext[num_threads_];
    ThreadAttributes attr;
    thread_alive_bitmap_.reserve(num_threads_);
    for (size_t i = 0; i < num_threads_; ++i) {
        attr.SetName(StringConcat(name, "_", i));
        BaseThread* thread = new BaseThread(attr,
                           std::bind(&BaseThreadPool::WorkRoutine, this, &thread_contexts_[i]));
        base_thread_set_.insert(thread);
        thread_contexts_[i].thread_.reset(thread);
        thread_contexts_[i].thread_index_ = i;
        thread_alive_bitmap_.push_back(false);
    }
    num_alive_threads_ = num_threads_;
    num_busy_threads_ = 0;
}

BaseThreadPool::~BaseThreadPool() {
    Terminate();
}

void BaseThreadPool::AddTaskInternal(
    Closure<void>* callback,
    const std::function<void()>& function,
    int dispatch_key,
    bool fixed_thread) {
    BYTE_ASSERT(!exit_);
    int index = dispatch_key % num_threads_;
    bool all_busy = false;
    if (!fixed_thread) {
        if (num_busy_threads_.Value() < num_threads_) {
            size_t count = 0;
            while (!thread_alive_bitmap_[index].CompareExchange(false, true)) {
                index = (index + 1) % num_threads_;
                if (++count == num_threads_) {
                    // All of threads are busy now.
                    all_busy = true;
                    break;
                }
            }
        } else {
            all_busy = true;
        }
    }
    if (all_busy) {
        index = dispatch_key % num_threads_;
    }
    ThreadContext& context = thread_contexts_[index];
    BaseThreadPool::Task* task = nullptr;
    MutexLocker locker(&context.mutex_);
    if (!context.free_tasks_.empty()) {
        task = &context.free_tasks_.front();
        context.free_tasks_.pop_front();
        task->callback = callback;
        task->function = function;
    } else {
        task = new Task(callback, function);
    }
    context.pending_tasks_.push_back(task);
    context.cond_.Signal();
}

bool BaseThreadPool::AddTask(Closure<void>* callback) {
    // The memory address is random enough for load balance, but need
    // remove low alignment part. (The lowest 5 bits of allocated object
    // address are always 0 for 64 bit system).
    unsigned int key = reinterpret_cast<uintptr_t>(callback) / 32;
    unsigned int dispatch_key = MurmurHash(reinterpret_cast<const char*>(&key), sizeof(key));
    AddTaskInternal(callback, nullptr, dispatch_key, false);
    return true;
}

void BaseThreadPool::AddTask(Closure<void>* callback, int dispatch_key) {
    AddTaskInternal(callback, nullptr, dispatch_key, true);
}

bool BaseThreadPool::AddTask(const std::function<void()>& callback) {
    AddTaskInternal(nullptr, callback, GetCurrentTimeInNs() & 0xffffffffL, false);
    return true;
}

void BaseThreadPool::AddTask(const std::function<void()>& callback, int dispatch_key) {
    AddTaskInternal(nullptr, callback, dispatch_key, true);
}

void BaseThreadPool::WorkRoutine(ThreadContext* context) {
    ThreadContext::TaskList tasks;
    for (;;) {
        bool continued = context->GetPendingTask(&tasks);
        thread_alive_bitmap_[context->thread_index_] = true;
        ++num_busy_threads_;
        ThreadContext::TaskList::iterator i;
        for (i = tasks.begin(); i != tasks.end(); ++i) {
            if (i->callback) {
                i->callback->Run();
                i->callback = nullptr;
            } else {
                i->function();
                i->function = nullptr;
            }
        }
        --num_busy_threads_;
        thread_alive_bitmap_[context->thread_index_] = false;
        if (!continued)
            break;
    }

    // Clear free_tasks list
    // Exiting, so no other context will operate this list.
    // TODO(M-8864556) do we need a lock for context.free_tasks_ here?
    context->free_tasks_.splice(&tasks);
    while (!context->free_tasks_.empty()) {
        Task* task = &context->free_tasks_.front();
        context->free_tasks_.pop_front();
        delete task;
    }
    MutexLocker locker(&exit_lock_);
    if (--num_alive_threads_ == 0)
        exit_cond_.Signal();
}

bool BaseThreadPool::AnyTaskPending() const {
    for (size_t i = 0; i < num_threads_; ++i) {
        MutexLocker locker(&thread_contexts_[i].mutex_);
        if (!thread_contexts_[i].pending_tasks_.empty())
            return true;
    }
    return false;
}

bool BaseThreadPool::AnyThreadRunning() const {
    for (size_t i = 0; i < num_threads_; ++i) {
        if (thread_contexts_[i].thread_->IsAlive())
            return true;
    }
    return false;
}

void BaseThreadPool::Terminate() {
    MutexLocker lock(&exit_lock_);
    if (exit_)
        return;

    exit_ = true;
    for (size_t i = 0; i < num_threads_; ++i) {
        MutexLocker locker(&thread_contexts_[i].mutex_);
        thread_contexts_[i].exit_ = true;
        thread_contexts_[i].cond_.Signal();
    }
    while (num_alive_threads_ > 0)
        exit_cond_.Wait();

    for (size_t i = 0; i < num_threads_; ++i)
        thread_contexts_[i].thread_->Join();

    base_thread_set_.clear();
    delete[] thread_contexts_;
    thread_contexts_ = nullptr;
    num_threads_ = 0;
}

void BaseThreadPool::WaitForIdle() {
    BYTE_ASSERT(!exit_);
    while (AnyTaskPending()) {
        ThisThread::Sleep(1);
    }
}

std::vector<int> BaseThreadPool::GetAllThreadIds() {
    std::vector<int> tid_list;
    if (!exit_) {
        tid_list.reserve(num_threads_);
        for (size_t i = 0; i < num_threads_; ++i) {
            int id = thread_contexts_[i].thread_->GetId();
            tid_list.push_back(id);
        }
    }
    return tid_list;
}

// If pool contains this thread, function returns true, otherwise, return false.
bool BaseThreadPool::HasBaseThread(BaseThread* thread) {
    return base_thread_set_.find(thread) != base_thread_set_.end();
}

}  // namespace byte
