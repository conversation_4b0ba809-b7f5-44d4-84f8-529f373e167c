// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <sys/syscall.h>
#include <unistd.h>

#include "byte/include/assert.h"
#include "byte/thread/thread.h"

namespace byte {
namespace this_thread {

pid_t ThreadId() { return syscall(SYS_gettid); }

void Yield() { sched_yield(); }

void Exit(void* retval) { pthread_exit(retval); }

}  // namespace this_thread

Thread::Thread(std::function<void()> thread_routine)
    : thread_routine_(thread_routine) {
    pthread_create(&thread_id_, nullptr,
                   [](void* arg) -> void* {
                       static_cast<Thread*>(arg)->thread_routine_();
                       return nullptr;
                   },
                   this);
}

int Thread::Join() {
    return pthread_join(thread_id_, nullptr);
}

pthread_t Thread::ID() {
    return thread_id_;
}

}  // namespace byte
