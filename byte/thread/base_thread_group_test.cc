// Copyright (c) 2013, The Toft Authors.
// All rights reserved.
//
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>

#include "byte/thread/base_thread_group.h"

#include "byte/concurrent/mutex.h"
#include "gtest/gtest.h"

namespace byte {

class BaseThreadGroupTest : public testing::Test {
public:
    static const int  kCount = 100000;
public:
    BaseThreadGroupTest() : n(0) {}

    void TestThread() {
        for (;;) {
            Mutex::Locker locker(&mutex);
            if (++n >= kCount)
                return;
        }
    }

protected:
    int n;
    Mutex mutex;
};

const int BaseThreadGroupTest::kCount;

TEST_F(BaseThreadGroupTest, CtorWithTask) {
    BaseThreadGroup thread_group(std::bind(&BaseThreadGroupTest::TestThread, this), 4);
    thread_group.Join();
    EXPECT_GE(n, kCount);
    EXPECT_EQ(4U, thread_group.Size());
}

TEST_F(BaseThreadGroupTest, AddTasks) {
    BaseThreadGroup thread_group;
    thread_group.Add(std::bind(&BaseThreadGroupTest::TestThread, this), 3);
    EXPECT_EQ(3U, thread_group.Size());
    thread_group.Add(std::bind(&BaseThreadGroupTest::TestThread, this));
    EXPECT_EQ(4U, thread_group.Size());
    thread_group.Join();
    EXPECT_GE(n, kCount);
    EXPECT_EQ(4U, thread_group.Size());
}

}  // namespace byte
