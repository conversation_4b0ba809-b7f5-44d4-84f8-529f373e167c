// Copyright (c) 2023, ByteDance Inc. All rights reserved.

#include "byte/thread/polling_consume_thread.h"

#include "byte/concurrent/count_down_latch.h"
#include "gtest/gtest.h"

namespace {

void StopPollingConsumeThread(byte::PollingConsumeThread* th) {
    th->Stop();
}

void CallbackDone(byte::CountDownLatch* latch) {
    latch->CountDown();
}

}  // namespace

namespace byte {

TEST(PollingConsumeThreadTest, StartAndStop) {
    PollingConsumeThread th;
    PollingConsumeThreadOptions options("test", 1, 4);
    th.Init(options);
    EXPECT_TRUE(th.Start());

    EXPECT_TRUE(th.Stop());
    EXPECT_TRUE(th.Join());
}

TEST(PollingConsumeThreadTest, StopInPollingConsumeThread) {
    PollingConsumeThread th;
    PollingConsumeThreadOptions options("test", 1, 4);
    th.Init(options);
    EXPECT_TRUE(th.Start());

    Closure<void>* callback = NewClosure(&StopPollingConsumeThread, &th);
    th.Invoke(callback, 0);

    EXPECT_TRUE(th.Join());
}

TEST(PollingConsumeThreadTest, InvokeTask) {
    PollingConsumeThread th;
    PollingConsumeThreadOptions options("test", 1, 4);
    th.Init(options);
    EXPECT_TRUE(th.Start());

    // Invoke invalid queue_index
    size_t k_size = 100;
    byte::CountDownLatch latch(k_size * 4);
    Closure<void>* callback = NewClosure(&CallbackDone, &latch);
    EXPECT_FALSE(th.Invoke(callback, 4));
    delete callback;

    for (size_t i = 0; i < k_size; ++i) {
        for (size_t j = 0; j < 4; ++j) {
            Closure<void>* callback = NewClosure(&CallbackDone, &latch);
            EXPECT_TRUE(th.Invoke(callback, j));
        }
    }
    latch.Wait();

    EXPECT_TRUE(th.Stop());
    EXPECT_TRUE(th.Join());
}

}  // namespace byte
