// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "byte/metrics/metric_collector_conf.h"

namespace byte {
namespace metrics2 {

// 所有define_*函数是为了check_*函数服务的，
// 测试条件下可通过METRICS_CHECK宏控制开启验证
class MetricCollector {
public:
    typedef std::vector<std::pair<std::string, std::string> > TagkvList;

    MetricCollector() = default;
    ~MetricCollector() = default;

    // 接口向前兼容，实际上 init 应该通过 Metrics 的 static 函数调用，
    // 单例类中的 collector 不会调用本 init 函数。但是，为了兼容旧代码中
    // 对每个 collector 分别调用以设置不同 prefix 的用法，在这里进行了兼容。
    // 如果调用了实例的 init 函数，则会采用实例中设置的 prefix。
    // 实例的 init 也会用来初始化全局的 MetricClient，以先调用为准。
    int init(const MetricCollectorConf& conf);

    // 这里实际上 T = const cpputil::program::Conf，模板函数是为了解耦依赖
    // 接口向前兼容，实际上 init 应该通过 Metrics 的 static 函数调用
    // 下面的type traits是为了防止MetricCollectorConf左值调用到这个函数
    template <typename T>
    typename std::enable_if<
        !std::is_same<MetricCollectorConf, typename std::remove_cv<typename std::remove_reference<
                                               T>::type>::type>::value,
        int>::type
    init(const T& conf) {
        MetricCollectorConf tmp_conf;
        tmp_conf.namespace_prefix = conf.get("metrics_namespace_prefix");
        tmp_conf.sock_path = conf.get("sock_path", "/tmp/metric.sock");
        tmp_conf.send_batch_size =
            static_cast<size_t>(atoi(conf.get("send_batch_size", "1").c_str()));
        tmp_conf.auto_batch = atoi(conf.get("auto_batch", "0").c_str());
        tmp_conf.metrics1_retry = atoi(conf.get("metrics1_retry", "0").c_str());
        // 优先使用psm字段,psm为空使用rpc.psm字段,rpc.psm也为空使用环境变量
        char* tce_psm = std::getenv("TCE_PSM");
        if (tce_psm) {
            tmp_conf.psm = std::string(tce_psm);
        }
        if (tmp_conf.psm.empty()) {
            tmp_conf.psm = conf.get("psm", "");
        }
        if (tmp_conf.psm.empty()) {
            tmp_conf.psm = conf.get("rpc.psm", "");
        }
        if (tmp_conf.psm.empty()) {
            char* c_psm = std::getenv("LOAD_SERVICE_PSM");
            if (c_psm) {
                tmp_conf.psm = std::string(c_psm);
            } else {
                tmp_conf.psm = "data.default.alert";
            }
        }
        return init(tmp_conf);
    }

    /**
     * 定义本进程要输出的tagkv
     *  tagk: tag名字
     */
    int define_tagk(const std::string& tagk) {
        auto it = tagk_set_.find(tagk);
        if (it == tagk_set_.end()) {
            tagk_set_.insert(tagk);
        }
        return 0;
    }

    /**
     * 定义本进程要输出的tagkv
     *  tagk: tag名字
     *  tagv_list: tag允许的取值
     */
    // deprecated
    int define_tagkv(const std::string& tagk, const std::vector<std::string>& tagv_list) {
        (void)tagv_list;
        return define_tagk(tagk);
    }

    /**
     * 定义本进程要输出的metric
     *  name: metric名字。拼接在配置文件中指定的prefix后作为真实的metric name.
     *  units: metric原始单位
     */
    int define_counter(const std::string& name) {
        auto it = metric_map_.find(name);
        if (it != metric_map_.end()) {
            return 1;
        }
        metric_map_.emplace(name, kMetricTypeCounter);
        return 0;
    }

    int define_counter(const std::string& name, const std::string& /*units*/) {
        return define_counter(name);
    }

    int define_timer(const std::string& name) {
        auto it = metric_map_.find(name);
        if (it != metric_map_.end()) {
            return 1;
        }
        metric_map_.emplace(name, kMetricTypeTimer);
        return 0;
    }

    int define_timer(const std::string& name, const std::string& /*units*/) {
        return define_timer(name);
    }

    int define_store(const std::string& name) {
        auto it = metric_map_.find(name);
        if (it != metric_map_.end()) {
            return 1;
        }
        metric_map_.emplace(name, kMetricTypeStore);
        return 0;
    }

    int define_store(const std::string& name, const std::string& /*units*/) {
        return define_store(name);
    }

    /**
     * 向指定metric输出数据
     */
    int emit_counter(const std::string& name, double value) const {
        return emit_message(kMetricTypeCounter, name, value);
    }

    int emit_counter(const std::string& name, double value, std::string tagkv) const {
        return emit_message(kMetricTypeCounter, name, value, std::move(tagkv));
    }

    int emit_counter(const std::string& name, double value, const TagkvList& tagkv_list) const {
        return emit_message(kMetricTypeCounter, name, value, tagkv_list);
    }

    int emit_timer(const std::string& name, double value) const {
        return emit_message(kMetricTypeTimer, name, value);
    }

    int emit_timer(const std::string& name, double value, std::string tagkv) const {
        return emit_message(kMetricTypeTimer, name, value, std::move(tagkv));
    }

    int emit_timer(const std::string& name, double value, const TagkvList& tagkv_list) const {
        return emit_message(kMetricTypeTimer, name, value, tagkv_list);
    }

    int emit_store(const std::string& name, double value) const {
        return emit_message(kMetricTypeStore, name, value);
    }

    int emit_store(const std::string& name, double value, std::string tagkv) const {
        return emit_message(kMetricTypeStore, name, value, std::move(tagkv));
    }

    int emit_store(const std::string& name, double value, const TagkvList& tagkv_list,
                   bool metrics1_retry = false) const {
        return emit_message(kMetricTypeStore, name, value, tagkv_list, metrics1_retry);
    }

    /**
     * 重置metric
     */
    int reset_counter(const std::string& name) const {
        return reset_message(kMetricTypeCounter, name);
    }

    int reset_counter(const std::string& name, std::string tagkv) const {
        return reset_message(kMetricTypeCounter, name, std::move(tagkv));
    }

    int reset_counter(const std::string& name, const TagkvList& tagkv_list) const {
        return reset_message(kMetricTypeCounter, name, tagkv_list);
    }

    int reset_timer(const std::string& name) const { return reset_message(kMetricTypeTimer, name); }

    int reset_timer(const std::string& name, std::string tagkv) const {
        return reset_message(kMetricTypeTimer, name, std::move(tagkv));
    }

    int reset_timer(const std::string& name, const TagkvList& tagkv_list) const {
        return reset_message(kMetricTypeTimer, name, tagkv_list);
    }

    int reset_store(const std::string& name) const { return reset_message(kMetricTypeStore, name); }

    int reset_store(const std::string& name, std::string tagkv) const {
        return reset_message(kMetricTypeStore, name, std::move(tagkv));
    }

    int reset_store(const std::string& name, const TagkvList& tagkv_list) const {
        return reset_message(kMetricTypeStore, name, tagkv_list);
    }

    static std::string make_tagkv(const TagkvList& tagkv_list);

    // deprecated
    static int start_flush_thread() { return 1; }

    // deprecated
    static int start_listening_thread() { return 1; }

protected:
    bool check_metric_type(const std::string& type, const std::string& name) const {
        auto it = metric_map_.find(name);
        return it != metric_map_.end() && it->second == type;
    }

    // 先解析字符串再判断是否已定义
    bool check_tagkv(const std::string& tagkv) const;

    bool check_tagkv(const TagkvList& tagkv_list) const {
        for (auto kv : tagkv_list) {
            if (!check_single_tagk(kv.first)) {
                return false;
            }
        }
        return true;
    }

    bool check_single_tagk(const std::string& tagk) const {
        auto it = tagk_set_.find(tagk);
        return it != tagk_set_.end();
    }

    int emit_message(const std::string& type, const std::string& name, double value) const {
#ifdef METRICS_CHECK
        if (!check_metric_type(type, name)) {
            return 1;
        }
#endif
        return send_emit_message(kMessageTypeEmit, type, name, value, "");
    }

    int emit_message(const std::string& type, const std::string& name, double value,
                     std::string tagkv) const {
#ifdef METRICS_CHECK
        if (!check_metric_type(type, name) || !check_tagkv(tagkv)) {
            return 1;
        }
#endif
        return send_emit_message(kMessageTypeEmit, type, name, value, std::move(tagkv));
    }

    int emit_message(const std::string& type, const std::string& name, double value,
                     const TagkvList& tagkv_list, bool metrics1_retry = false) const {
#ifdef METRICS_CHECK
        if (!check_metric_type(type, name) || !check_tagkv(tagkv_list)) {
            return 1;
        }
#endif
        return send_emit_message(kMessageTypeEmit, type, name, value, make_tagkv(tagkv_list),
                                 metrics1_retry);
    }

    int reset_message(const std::string& type, const std::string& name) const {
#ifdef METRICS_CHECK
        if (!check_metric_type(type, name)) {
            return 1;
        }
#endif
        return send_reset_message(kMessageTypeReset, type, name, 0.0, "");
    }

    int reset_message(const std::string& type, const std::string& name, std::string tagkv) const {
#ifdef METRICS_CHECK
        if (!check_metric_type(type, name) || !check_tagkv(tagkv)) {
            return 1;
        }
#endif
        return send_reset_message(kMessageTypeReset, type, name, 0.0, std::move(tagkv));
    }

    int reset_message(const std::string& type, const std::string& name,
                      const TagkvList& tagkv_list) const {
#ifdef METRICS_CHECK
        if (!check_metric_type(type, name) || !check_tagkv(tagkv_list)) {
            return 1;
        }
#endif
        return send_reset_message(kMessageTypeReset, type, name, 0.0, make_tagkv(tagkv_list));
    }

    // 这里并不清楚为什么需要两个函数，实现里面差一个field，影响不确定。如果没有影响应该合二为一。
    int send_emit_message(const std::string& action, const std::string& type,
                          const std::string& name, double value, std::string tagkv,
                          bool metrics1_retry = false) const;

    // 这里并不清楚为什么需要两个函数，实现里面差一个field，影响不确定。如果没有影响应该合二为一。
    int send_reset_message(const std::string& action, const std::string& type,
                           const std::string& name, double value, std::string tagkv) const;

    std::string get_canonical_metric_name(const std::string& name) const {
        // 冒号开头的，一律忽略前缀
        if (!name.empty() && name[0] == ':') {
            return name.substr(1);
        }
        // 本地设置了前缀，则用本地前缀
        if (has_namespace_prefix_) {
            return namespace_prefix_ + name;
        }
        // 否则，使用全局前缀
        return name;
    }

    bool use_global_namespace_prefix(const std::string& name) const {
        return !(!name.empty() && name[0] == ':') && !has_namespace_prefix_;
    }

public:
    static const char kMetricTypeCounter[];
    static const char kMetricTypeTimer[];
    static const char kMetricTypeStore[];

    static const char kMessageTypeEmit[];
    static const char kMessageTypeReset[];

protected:
    std::unordered_map<std::string, std::string> metric_map_;
    std::unordered_set<std::string> tagk_set_;
    // 兼容性设置：如果 collector 的 init 函数被调用，则将保留 collector 自身设置的 prefix
    std::string namespace_prefix_;
    bool has_namespace_prefix_ = false;
    std::string _psm;
};

} /* namespace metrics2 */
} /* namespace byte */
