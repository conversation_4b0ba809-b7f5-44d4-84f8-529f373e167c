// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include <thread>  // NOLINT
#include <vector>

#include "byte/fiber/fiber.h"
#include "byte/fiber/synchronization.h"
#include "byte/system/timestamp.h"
#include "byte/util/defer.h"
#include "gtest/gtest.h"

namespace byte {
namespace fiber {

namespace {
struct CountArg {
    int* counter = nullptr;
    int times = 0;

    SpinLock* spinlock{nullptr};
};
}  // namespace

void* AddCounterUnderSpinlock(void* arg) {
    CountArg* param = static_cast<CountArg*>(arg);
    for (int i = 0; i < param->times; ++i) {
        LockGuard<SpinLock> lg(*param->spinlock);
        EXPECT_FALSE(param->spinlock->TryLock());
        ++(*param->counter);
    }
    return nullptr;
}

TEST(Synchronization, SpinlockAcrossPthread) {
    std::vector<std::thread> threads;

    int globalVar = 0;
    const int perThreadAddTimes = 10000;
    const int pthreadNum = 5;
    SpinLock spinLock;
    for (int i = 0; i < pthreadNum; i++) {
        threads.emplace_back([&]() {
            ThreadInitOption th_opt;
            EXPECT_EQ(0, ThreadInit(th_opt));

            CountArg args;
            args.times = perThreadAddTimes;
            args.counter = &globalVar;
            args.spinlock = &spinLock;

            CreateOpt fiber_opt;
            fiber_opt.joinable_ = true;
            Fiber* fiber = fiber::Create(fiber_opt, AddCounterUnderSpinlock, &args);
            EXPECT_NE(fiber, nullptr);

            EXPECT_EQ(0, Join(fiber));
            ThreadFini();
        });
    }

    for (auto&& th : threads) {
        th.join();
    }

    EXPECT_EQ(pthreadNum * perThreadAddTimes, globalVar);
}

}  // namespace fiber
}  // namespace byte

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);

    byte::fiber::GlobalInitOption opt;
    byte::fiber::GlobalInit(opt);

    auto global_fini_guard = byte::defer(byte::fiber::GlobalFini);

    return RUN_ALL_TESTS();
}
