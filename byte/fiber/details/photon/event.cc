// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include "byte/fiber/event.h"

#include "byte/fiber/errno.h"
#include "byte/fiber/fiber.h"
#include "byte/include/macros.h"
#include "photon/io/fd-events.h"

namespace byte {
namespace fiber {

static const uint32_t event_map[3] = {photon::EVENT_READ, photon::EVENT_WRITE, photon::EVENT_ERROR};

int WaitForEvent(int fd, EventType event, int timeout_us) {
    if (UNLIKELY(!GetThreadCtx())) {
        return EUNINIT;
    }

    if (UNLIKELY(event >= NUM_EVENT_TYPE)) {
        return EINVAL;
    }

    uint64_t timeout = timeout_us < 0 ? -1 : timeout_us;
    int ret = photon::get_vcpu()->master_event_engine->wait_for_fd(fd, event_map[event], timeout);

    if (UNLIKELY(ret != 0)) {
        return errno;
    }

    return 0;
}

}  // namespace fiber
}  // namespace byte
