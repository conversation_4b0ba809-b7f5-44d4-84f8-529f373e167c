// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <map>
#include <memory>
#include <set>
#include <string>

#include "byte/base/atomic.h"
#include "byte/concurrent/mutex.h"
#include "byte/keeper/keeper_client.h"
#include "byte/keeper/keeper_config.h"
#if __has_include(<zookeeper/zookeeper.h>)
#include <zookeeper/zookeeper.h>
#else
#include <zookeeper-client-c/include/zookeeper.h>
#endif

namespace byte {

std::string ZKStateToString(int state);
std::string ZKTypeToString(int type);

// Declare here to access ZKClient's private members for testing its public functions.
class ZKClientFriendTest;

namespace internal {
/**
 * @class IZooKeeperAPIWrapper
 * @brief Interface class for the ZooKeeper API wrapper, providing an abstraction for convenient
 * interaction with the ZooKeeper C API.
 */
class IZooKeeperAPIWrapper {
public:
    virtual ~IZooKeeperAPIWrapper() {}

    virtual int WGet(zhandle_t* zh, const char* path, watcher_fn watcher, void* watcher_ctx,
                     char* buffer, int* buffer_len, struct Stat* stat) = 0;

    virtual zhandle_t* Init(const char* host, watcher_fn fn, int recv_timeout,
                            const clientid_t* clientid, void* context, int flags,
                            log_callback_fn log_callback) = 0;

    virtual int Close(zhandle_t* zh) = 0;

    virtual int Create(zhandle_t* zh, const char* path, const char* value, int valuelen,
                       const struct ACL_vector* acl, int mode, char* path_buffer,
                       int path_buffer_len) = 0;

    virtual int Set(zhandle_t* zh, const char* path, const char* buffer, int buflen,
                    int version) = 0;

    virtual int Set2(zhandle_t* zh, const char* path, const char* buffer, int buflen, int version,
                     struct Stat* stat) = 0;

    virtual int Delete(zhandle_t* zh, const char* path, int version) = 0;

    virtual int Exists(zhandle_t* zh, const char* path, int watch, struct Stat* stat) = 0;

    virtual int AddAuth(zhandle_t* zh, const char* scheme, const char* cert, int certLen,
                        void_completion_t completion, const void* data) = 0;

    virtual int SetAcl(zhandle_t* zh, const char* path, int version,
                       const struct ACL_vector* acl) = 0;

    virtual int GetChildren(zhandle_t* zh, const char* path, int watch,
                            struct String_vector* strings, watcher_fn watcher,
                            void* watcher_ctx) = 0;

    virtual int SetServers(zhandle_t* zh, const char* hosts) = 0;
};

/**
 * @class ZooKeeperAPIWrapper
 * @brief A class that implements IZooKeeperAPIWrapper, providing an object-oriented way to interact
 * with the ZooKeeper C API.
 *
 * This class wraps the native C functions of the ZooKeeper API and provides methods that directly
 * map to corresponding API calls. The class can be used to easily interact with ZooKeeper without
 * needing to handle raw C pointers and structures.
 *  - WGet: Corresponds to zoo_wget()
 *  - Init: Corresponds to zookeeper_init()
 *  - Close: Corresponds to zookeeper_close()
 *  - Create: Corresponds to zoo_create()
 *  - Set: Corresponds to zoo_set()
 *  - Set2: Corresponds to zoo_set2()
 *  - Delete: Corresponds to zoo_delete()
 *  - Exists: Corresponds to zoo_exists()
 *  - AddAuth: Corresponds to zoo_add_auth()
 *  - SetAcl: Corresponds to zoo_set_acl()
 *  - GetChildren: Corresponds to zoo_get_children() and zoo_wget_children()
 *  - SetServers: Corresponds to zoo_set_servers()
 */
class ZooKeeperAPIWrapper : public IZooKeeperAPIWrapper {
public:
    ZooKeeperAPIWrapper() {}
    ~ZooKeeperAPIWrapper() {}

    int WGet(zhandle_t* zh, const char* path, watcher_fn watcher, void* watcher_ctx, char* buffer,
             int* buffer_len, struct Stat* stat) {
        return zoo_wget(zh, path, watcher, watcher_ctx, buffer, buffer_len, stat);
    }

    zhandle_t* Init(const char* host, watcher_fn fn, int recv_timeout, const clientid_t* clientid,
                    void* context, int flags, log_callback_fn log_callback) {
        return zookeeper_init2(host, fn, recv_timeout, clientid, context, flags, log_callback);
    }

    int Close(zhandle_t* zh) { return zookeeper_close(zh); }

    int Create(zhandle_t* zh, const char* path, const char* value, int valuelen,
               const struct ACL_vector* acl, int mode, char* path_buffer, int path_buffer_len) {
        return zoo_create(zh, path, value, valuelen, acl, mode, path_buffer, path_buffer_len);
    }

    int Set(zhandle_t* zh, const char* path, const char* buffer, int buflen, int version) {
        return zoo_set(zh, path, buffer, buflen, version);
    }

    int Set2(zhandle_t* zh, const char* path, const char* buffer, int buflen, int version,
             struct Stat* stat) {
        return zoo_set2(zh, path, buffer, buflen, version, stat);
    }

    int Delete(zhandle_t* zh, const char* path, int version) {
        return zoo_delete(zh, path, version);
    }

    int Exists(zhandle_t* zh, const char* path, int watch, struct Stat* stat) {
        return zoo_exists(zh, path, watch, stat);
    }

    int AddAuth(zhandle_t* zh, const char* scheme, const char* cert, int certLen,
                void_completion_t completion, const void* data) {
        return zoo_add_auth(zh, scheme, cert, certLen, completion, data);
    }

    int SetAcl(zhandle_t* zh, const char* path, int version, const struct ACL_vector* acl) {
        return zoo_set_acl(zh, path, version, acl);
    }

    int GetChildren(zhandle_t* zh, const char* path, int watch, struct String_vector* strings,
                    watcher_fn watcher, void* watcher_ctx) {
        if (watcher == nullptr) {
            return zoo_get_children(zh, path, watch, strings);
        } else {
            return zoo_wget_children(zh, path, watcher, watcher_ctx, strings);
        }
    }
    int SetServers(zhandle_t* zh, const char* hosts) { return zoo_set_servers(zh, hosts); }
};

// Virtual base class
class IZKRefHolder {
public:
    virtual ~IZKRefHolder() = default;
};

}  // namespace internal

// ZKClient inherits from KeeperClient(pure virtual class);
//
// This class provides the C++ wrapping interfaces over zk's C interface,
// also manages zk handler, log file handler and acl auth info etc.
class ZKClient : public KeeperClient {
public:
    explicit ZKClient(const KeeperClientConfig* config = nullptr);

    ~ZKClient();

    /**
     * Init zookeeper
     */
    ZK_ERR_Code init(const std::string& keeper_addr_list, watcher_fn watcher, void* context,
                     log_callback_fn log_callback) override;

    /**
     * Destory zookeeper environment
     */
    void close() override;

    /**
      * Add auth
      */
    ZK_ERR_Code add_auth(const std::string& region_path) override;

    /**
      * set acl
      */
    ZK_ERR_Code node_set_acl(const std::string& node) override;

    /**
     * create node
     */
    ZK_ERR_Code node_create(const std::string& node, const std::string& value) override;

    /**
     * Set node value, add if not exist
     */
    ZK_ERR_Code node_set(const std::string& node, const std::string& value,
                       int* version, bool force) override;

    /**
     * Delete node
     */
    ZK_ERR_Code node_delete(const std::string& node, int version, bool recursively) override;

    /**
     *  Get node value
     *  buf should have a reasonable length or is an empty length string;
     *  if buf size is too small, it can easily trigger two zoo_wget calls.
     */
    ZK_ERR_Code node_get(const std::string& node, std::string* buf,
                       watcher_fn watcher, void* watcher_ctx,
                       struct Stat* stat) override;

    /**
     *  Node exist or not
     */
    ZK_ERR_Code node_exist(const std::string& node, int watch, Stat* stat) override;

    /**
     * List all children nodes
     */
    ZK_ERR_Code list_children(const std::string& node, std::set<std::string>* children,
                              watcher_fn watcher, void* watcher_ctx) override;

    /**
     * List all children nodes together with their values
     */
    ZK_ERR_Code list_children_with_value(
            const std::string& node,
            std::map<std::string, std::string>* children) override;

    bool try_update_server(const std::string& address_list) override;

private:
    ZK_ERR_Code zk_create(const std::string& path, const std::string& value);

    ZK_ERR_Code zk_modify(const std::string& path, const std::string& value,
                        int* version, bool force);

    // Value should have a reasonable length or is an empty length string;
    // if value size is too small, it can easily trigger two zoo_wget calls.
    ZK_ERR_Code zk_get(const std::string& path, std::string* value, watcher_fn watcher,
                       void* watcher_ctx, struct Stat* stat);

    ZK_ERR_Code zk_delete(const std::string& path, int version, bool recursively);

    ZK_ERR_Code zk_exist(const std::string& path, int watch, Stat* stat);

    std::string parent_node(const std::string& path);

    zhandle_t* zh_;   //  modify
    FILE *zkLog_;
    byte::Atomic<bool> auth_;
    EPHEMERAL_CHECKER ephemeral_checker_;
    SEQUENCE_CHECKER  sequence_checker_;
    PATH_CONVERT      path_convert_;
    ACL_GETTER acl_getter_;
    ACL_CERT  acl_cert_;
    std::unique_ptr<internal::IZKRefHolder> ref_holder_;
    bool is_closed_;
    std::unique_ptr<internal::IZooKeeperAPIWrapper> zookeeper_api_wrapper_;

    friend class ZKClientFriendTest;

    DISALLOW_COPY_AND_ASSIGN(ZKClient);
};

class MockInitNotConnectedZKClient : public ZKClient {
public:
    using ZKClient::ZKClient;
    ~MockInitNotConnectedZKClient() override;
    /**
     * Init zookeeper
     */
    ZK_ERR_Code init(const std::string& keeper_addr_list, watcher_fn watcher, void* context,
                     log_callback_fn log_callback) override;
    pthread_t GetCompletion() { return completion_; }
    void SetCompletion(pthread_t completion) { completion_ = completion; }

private:
    pthread_t completion_ = 0;
};

class MockInitConnectedZKClient : public ZKClient {
public:
    using ZKClient::ZKClient;
    ~MockInitConnectedZKClient() override;
    /**
     * Init zookeeper
     */
    ZK_ERR_Code init(const std::string& keeper_addr_list, watcher_fn watcher, void* context,
                     log_callback_fn log_callback) override;
    pthread_t GetCompletion() { return completion_; }
    void SetCompletion(pthread_t completion) { completion_ = completion; }

private:
    pthread_t completion_ = 0;
};

class MockInitConnectingZKClient : public MockInitNotConnectedZKClient {
public:
    using MockInitNotConnectedZKClient::MockInitNotConnectedZKClient;
    /**
     * Init zookeeper
     */
    ZK_ERR_Code init(const std::string& keeper_addr_list, watcher_fn watcher, void* context,
                     log_callback_fn log_callback) override;
};

}  // namespace byte
