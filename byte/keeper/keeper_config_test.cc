// Copyright (c) 2023, ByteDance Inc. All rights reserved.

#include "byte/keeper/keeper_config.h"

#include <map>
#include <set>

#include "byte/keeper/keeper_client.h"
#include "byte/keeper/keeper_internal.h"
#include "gtest/gtest.h"

namespace byte {
namespace {

TEST(GlobalKeeperConfigTestTest, SetGetDomainAliasCorrectly) {
    std::string region = "test_region";
    std::string domain = "test_domain";
    std::string new_domain = "test_domain_new";
    internal::GlobalKeeperConfig::Singleton()->SetDomainAlias(region, domain);
    ASSERT_EQ(domain, internal::GlobalKeeperConfig::Singleton()->GetDomainAlias(region));
    ASSERT_EQ(domain, internal::GetDomainAlias(region));

    internal::GlobalKeeperConfig::Singleton()->SetDomainAlias(region, new_domain);
    ASSERT_EQ(new_domain, internal::GlobalKeeperConfig::Singleton()->GetDomainAlias(region));
    ASSERT_EQ(new_domain, internal::GetDomainAlias(region));

    ASSERT_EQ("", internal::GlobalKeeperConfig::Singleton()->GetDomainAlias("no_region"));
    ASSERT_EQ("", internal::GetDomainAlias("no_region"));
}

TEST(GlobalKeeperConfigTest, SetGetSockListCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    std::vector<std::string> expected_socket_list = {"socket1", "socket2"};
    config->SetHardCodeSockList("region1", expected_socket_list);

    std::vector<std::string> result_socket_list;
    EXPECT_TRUE(config->GetHardCodeSockList("region1", &result_socket_list));
    EXPECT_EQ(result_socket_list, expected_socket_list);

    std::vector<std::string> non_existent_socket_list;
    EXPECT_FALSE(config->GetHardCodeSockList("non_existent_region", &non_existent_socket_list));
}

TEST(GlobalKeeperConfigTest, SetGetZKSessionExpiredTimeoutMSCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    uint64_t timeout_ms = 7000;
    config->SetZKSessionExpiredTimeoutMS(timeout_ms);
    EXPECT_EQ(config->GetZKSessionExpiredTimeoutMS(), timeout_ms);
}

TEST(GlobalKeeperConfigTest, SetGetLogFilePathCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    std::string log_path = "./new_zk_log";
    config->SetLogFilePath(log_path);
    EXPECT_EQ(config->GetLogFilePath(), log_path);
}

TEST(GlobalKeeperConfigTest, SetGetDNSSuffixCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    std::string new_dns_suffix = ".test2.org";
    config->SetDNSSuffix(new_dns_suffix);
    EXPECT_EQ(config->GetDNSSuffix(), new_dns_suffix);
    EXPECT_EQ(internal::GetDNSSuffix(), new_dns_suffix);
}

TEST(GlobalKeeperConfigTest, SetGetPortCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    uint16_t new_port = 2182;
    config->SetPort(new_port);
    EXPECT_EQ(config->GetPort(), new_port);
}

TEST(GlobalKeeperConfigTest, SetGetDNSRefreshTimeIntervalSecondsCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    uint64_t interval = 2;
    config->SetDNSRefreshTimeIntervalSeconds(interval);
    EXPECT_EQ(config->GetDNSRefreshTimeIntervalSeconds(), interval);
    EXPECT_EQ(internal::GetDNSRefreshTimeIntervalSeconds(), interval);
}

TEST(GlobalKeeperConfigTest, SetGetDNSMaxRefreshIntervalSecondsCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    uint64_t max_interval = 7 * 60;
    config->SetDNSMaxRefreshIntervalSeconds(max_interval);
    EXPECT_EQ(config->GetDNSMaxRefreshIntervalSeconds(), max_interval);
    EXPECT_EQ(internal::GetDNSMaxRefreshIntervalSeconds(), max_interval);
}

TEST(GlobalKeeperConfigTest, SetGetZkInitWaitingBaseIntervalMsCorrectly) {
    internal::GlobalKeeperConfig* config = internal::GlobalKeeperConfig::Singleton();
    uint64_t init_interval = 5;
    config->SetZkInitWaitingBaseIntervalMs(init_interval);
    EXPECT_EQ(config->GetZkInitWaitingBaseIntervalMs(), init_interval);
    EXPECT_EQ(internal::GetZkInitWaitingBaseIntervalMs(), init_interval);
}

TEST(ZKERRCodeTest, OutputStreamCorrectly) {
    std::stringstream buffer;

    // Define a map to store expected results
    std::map<ZK_ERR_Code, std::string> expected_result_map = {
        {ZK_ERR_Code::ZK_OK, "code: 1"},
        {ZK_ERR_Code::ZK_ERR_PARAM, "code: 2"},
        {ZK_ERR_Code::ZK_ERR_MEM, "code: 3"},
        {ZK_ERR_Code::ZK_ERR_OTHER, "code: 4"},
        {ZK_ERR_Code::ZK_ERR_OUT_OF_RANGE, "code: 5"},
        {ZK_ERR_Code::ZK_ERR_NOT_NUMBER, "code: 6"},
        {ZK_ERR_Code::ZK_ERR_OTHRE_CHARACTER, "code: 7"},
        {ZK_ERR_Code::ZK_ERR_ZOO_FAILED, "code: 8"},
        {ZK_ERR_Code::ZK_ERR_ZOO_NOTEMPTY, "code: 9"},
        {ZK_ERR_Code::ZK_ERR_ZOO_NOT_EXIST, "code: 10"},
        {ZK_ERR_Code::ZK_ERR_ZOO_ALREADY_EXIST, "code: 11"},
        {ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED, "code: 12"},
        {ZK_ERR_Code::ZK_ERR_NODE_TYPE, "code: 13"},
        {ZK_ERR_Code::ZK_ERR_CONFIG_FILE_INEXIST, "code: 14"},
        {ZK_ERR_Code::ZK_ERR_BAD_VERSION, "code: 15"},
        {ZK_ERR_Code::ZK_ERR_CLIENT_NOT_READY, "code: 16"},
        {ZK_ERR_Code::ZK_ERR_NOT_INIT, "code: 17"},
        {ZK_ERR_Code::ZK_ERR_INIT_NOT_CONNECTED, "code: 18"}};

    // Test output for all enum values in the map
    for (const auto& keyValue : expected_result_map) {
        buffer.str("");  // Reset the buffer content
        buffer << keyValue.first;
        EXPECT_EQ(buffer.str(), keyValue.second);
    }
}
bool EqualsId(const Id& a, const Id& b) {
    return strcmp(a.scheme, b.scheme) == 0 && strcmp(a.id, b.id) == 0;
}

bool EqualsAcl(const ACL& a, const ACL& b) { return a.perms == b.perms && EqualsId(a.id, b.id); }

void CheckAssertEqual(const std::vector<ACL>& a, const std::vector<ACL>& b) {
    ASSERT_EQ(a.size(), b.size());
    for (size_t i = 0; i < a.size(); ++i) {
        ASSERT_TRUE(EqualsAcl(a[i], b[i]));
    }
}

TEST(DefaultAclGetterTest, SetGetAclCorrectly) {
    std::vector<struct ACL> acl_vec;
    byte::default_acl_getter("some_path", &acl_vec);

    std::vector<struct ACL> expected_acls = {{ZOO_PERM_ALL, ZOO_AUTH_IDS},
                                             {ZOO_PERM_READ, ZOO_ANYONE_ID_UNSAFE},
                                             {ZOO_PERM_CREATE, ZOO_ANYONE_ID_UNSAFE}};
    CheckAssertEqual(acl_vec, expected_acls);
}

TEST(BytestoreAclCertTest, SetGetBytestoreAclCorrectly) {
    std::string path = "some_path";
    Stat stat;
    stat.ctime = 1234567890;
    std::string bytestore_acl = byte::bytestore_acl_cert(path, stat);
    std::string expected_bytestore_acl = "bytestore:1234567890";
    EXPECT_EQ(bytestore_acl, expected_bytestore_acl);
}

TEST(DefaultMockClientTest, SetGetFlagCorrectly) {
    std::shared_ptr<BaseMockKeeperClient> client_ptr;
    bool result_flag = default_mock_client("some_path", &client_ptr);
    bool expected_flag = false;
    EXPECT_EQ(result_flag, expected_flag);
}

TEST(BaseMockKeeperClientTest, BasicFunctionalities) {
    BaseMockKeeperClient client;
    std::string keeper_addr_list = "127.0.0.1:1234,192.168.1.1:5678";
    ZK_ERR_Code result = client.init(keeper_addr_list, nullptr, nullptr, nullptr);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    result = client.add_auth("some_region_path");
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    result = client.node_set_acl("some_node");
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    result = client.node_create("some_node", "some_value");
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    int version = 0;
    result = client.node_set("some_node", "some_value", &version, false);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    result = client.node_delete("some_node", 1, false);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    std::string buf;
    result = client.node_get("some_node", &buf, nullptr, nullptr, nullptr);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    result = client.node_exist("some_node", 0, nullptr);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    std::set<std::string> children;
    result =
        client.list_children("some_node", &children, /*watcher=*/nullptr, /*watcher_ctx=*/nullptr);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    std::map<std::string, std::string> children_with_value;
    result = client.list_children_with_value("some_node", &children_with_value);
    ASSERT_EQ(result, ZK_ERR_Code::ZK_OK);

    bool update_result = client.try_update_server("some_address_list");
    ASSERT_TRUE(update_result);
}

}  // namespace

}  // namespace byte
