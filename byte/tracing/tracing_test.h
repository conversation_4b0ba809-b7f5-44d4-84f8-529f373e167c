// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdarg.h>
#include <stdlib.h>
#include <map>
#include <memory>
#include <string>
#include <utility>
#include "byte/base/atomic.h"
#include "byte/byte_log/byte_log_impl.h"
#include "byte/concurrent/lite_lock.h"
#include "byte/include/byte_log.h"
#include "byte/include/macros.h"
#include "byte/string/concat.h"
#include "byte/string/format.h"
#include "byte/system/process_info.h"
#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"
#include "byte/thread/thread_local.h"
#include "byte/tracing/tracing_collector.h"
#include "byte/tracing/tracing.h"

#define TRACING_APPEND_STR_INTERNAL_NODIC(span, str, len, c) \
    do { \
        if (len == 0) { \
            break; \
        } \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (len + ((c == '\0') ? 0 : 2)); \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
            static const char temp = c; \
            if (temp != '\0') { \
                s->span_message_[msg_size] = c; \
                memcpy(s->span_message_ + msg_size + 1, str, len); \
                s->span_message_[s->message_size_ - 1] = '\0'; \
            } else { \
                memcpy(s->span_message_ + msg_size, str, len); \
            } \
        } else { \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STR_INTERNAL_NO_STREND_NODIC(span, str, len, c) \
    do { \
        if (len == 0) { \
            break; \
        } \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (len + ((c == '\0') ? 0 : 1)); \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
            static const char temp = c; \
            if (temp != '\0') { \
                s->span_message_[msg_size] = c; \
                memcpy(s->span_message_ + msg_size + 1, str, len); \
            } else { \
                memcpy(s->span_message_ + msg_size, str, len); \
            } \
        } else { \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STREND_NODIC(span) \
    do { \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += 1; \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
                s->span_message_[msg_size] = '\0'; \
        } else { \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STR_RAW_NODIC(span, str, len) \
    TRACING_APPEND_STR_INTERNAL_NODIC(span, str, len, '\0')

#define TRACING_APPEND_STR_NODIC(span, str, len, c) \
    TRACING_APPEND_STR_INTERNAL_NODIC(span, str, len, c)

#define TRACING_APPEND_NAME_NODIC(span, str, len) \
    do { \
        if (len == 0) { \
            break; \
        } \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t name_size = s->name_size_; \
        s->name_size_ += len; \
        if (LIKELY(s->name_size_ < k_max_tracing_name_size)) { \
            memcpy(s->name_ + name_size, str, len); \
            s->name_[s->name_size_] = '\0'; \
        } else { \
            s->name_size_ = name_size; \
        } \
    } while (false)

#define TRACING_NAME_FIND_LAST_OF_NODIC(span, c, ret) \
    do { \
         if (UNLIKELY(span->name_size_ == 0)) { \
            ret = -1; \
            break; \
        } \
        const char* p = span->name_ + span->name_size_; \
        while ((span->name_size_ >0) && (--p != span->name_) && (*p != c)) {} \
        if (*p == c) { \
            ret = p - span->name_; \
        } else { \
            ret = -1; \
        } \
    } while (false)

#define TRACING_APPEND_LATENCY_INTERNAL_NODIC(span, latency, current_ns, penalty, cmp) \
    do { \
        const int64_t lat = (latency < cmp) ? cmp : latency; \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (sizeof(lat) + sizeof(current_ns) + sizeof(penalty) + 1); \
        if (s->message_size_ < k_max_tracing_msg_size) { \
            s->span_message_[msg_size] = '#'; \
            memcpy(s->span_message_ + msg_size + 1, &lat, sizeof(lat)); \
            int64_t tp_ns = current_ns;\
            memcpy(s->span_message_ + msg_size + 1 + sizeof(lat), &tp_ns, sizeof(tp_ns)); \
            tp_ns = penalty;\
            memcpy(s->span_message_ + msg_size + 1 + sizeof(lat) + sizeof(current_ns), \
                   &tp_ns, sizeof(penalty));\
        } else { \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_LATENCY_NODIC(span, latency, current_ns, penalty) \
    TRACING_APPEND_LATENCY_INTERNAL_NODIC(span, latency, current_ns, penalty, 0)

#define DO_TRACE_RECORD_INTERNAL_NODIC(span, CODE) \
    do { \
        if ((span)->active_) { \
            int64_t now = (span)->start_time_; \
            const int64_t interval = now - (span)->parent_trace_point_timestamp_; \
            CODE; \
            (span)->parent_trace_point_timestamp_ = now; \
            (span)->first_tracing_ = false; \
        } \
    } while (false)

#define DO_TRACE_RECORD_WITH_TRACE_POINT_NODIC(span, trace_point, parent_trace, parent_trace_size) \
    DO_TRACE_RECORD_INTERNAL_NODIC(span, \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        (TRACEPOINT_##trace_point).Set(k_file, __LINE__).Add( \
            (span)->annotation_id_, 0, interval); \
        static const uint32_t len = strlen(#trace_point); \
        if (!(span)->first_tracing_) { \
            TRACING_APPEND_STR_RAW_NODIC(span, ",", 1); \
        } \
        TRACING_APPEND_STR_NODIC(span, #trace_point, len, '|'); \
        TRACING_APPEND_STR_NODIC(span, parent_trace, parent_trace_size, '@'); \
        TRACING_APPEND_LATENCY_NODIC(span, interval, byte::GetCurrentTimeInNs(), span->penalty_);)

#define DO_TRACE_RECORD_NODIC(span) \
    DO_TRACE_RECORD_INTERNAL_NODIC(span, \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        static byte::TracePoint trace_point(k_file, __LINE__); \
        trace_point.Add((span)->annotation_id_, 0, interval); \
        if (!(span)->first_tracing_) { \
            TRACING_APPEND_STR_RAW_NODIC(span, ",", 1); \
        } \
        static const std::string& k_trace_point = \
            std::string(k_file) + ":" + std::to_string(__LINE__); \
        TRACING_APPEND_STR_NODIC(span, k_trace_point.c_str(), k_trace_point.size(), '|'); \
        TRACING_APPEND_LATENCY_NODIC(span, interval, byte::GetCurrentTimeInNs(), span->penalty_);)

#define UPDATE_SPAN_NODIC(name) \
    do { \
        if (TRACE_##name.Get() == nullptr) { \
            TRACE_##name.Reset(new byte::TraceSpan()); \
        } \
    } while (false)

// API macro
#define TRACING_NODIC(name) \
    do { \
        UPDATE_SPAN_NODIC(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            span->start_time_ = byte::GetCurrentTimeInNs(); \
            DO_TRACE_RECORD_NODIC(span); \
        } \
    } while (false)

// API macro
#define TRACEPOINT_NODIC(span_name, trace_point_name) \
    do { \
        static const char* k_dummy_str = ""; \
        UPDATE_SPAN_NODIC(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            span->start_time_ = byte::GetCurrentTimeInNs(); \
        } \
        DO_TRACE_RECORD_WITH_TRACE_POINT_NODIC(span, trace_point_name, k_dummy_str, 0); \
    } while (false)

// API macro
#define TRACEPOINT_START_NODIC(span_name, trace_point_name) \
    do { \
        UPDATE_SPAN_NODIC(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            span->start_time_ = start_time; \
            int pos = 0; \
            TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
            ++pos; \
            DO_TRACE_RECORD_WITH_TRACE_POINT_NODIC(span, trace_point_name ## _START, \
                                             span->name_ + pos, span->name_size_ - pos); \
            TRACING_APPEND_NAME_NODIC(span, "/", 1); \
            static const uint32_t len = strlen(#trace_point_name); \
            TRACING_APPEND_NAME_NODIC(span, #trace_point_name, len); \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            span->penalty_ = end_time - start_time; \
        } \
    } while (false)

// API macro
#define TRACEPOINT_END_NODIC(span_name, trace_point_name) \
    do { \
        UPDATE_SPAN_NODIC(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            TRACEPOINT_NODIC(span_name, trace_point_name ## _END); \
            int pos = 0; \
            TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
            span->name_size_ = (pos == -1) ? 0 : pos; \
            span->name_[span->name_size_] = '\0'; \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            span->penalty_ = end_time - start_time; \
        } \
    } while (false)

// API macro
#define SCOPED_TRACEPOINT_NODIC(span_name, trace_point_name) \
    TRACEPOINT_START_NODIC(span_name, trace_point_name); \
    BYTE_DEFER(TRACEPOINT_END_NODIC(span_name, trace_point_name));

// API macro
#define START_TRACING_NODIC(annot, name, n) \
    do { \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        static byte::TracePoint trace_point(k_file, __LINE__); \
        static const uint32_t len = strlen(#name); \
        byte::Tracing_GetAnnotation_##annot()->Start(&TRACE_##name, #name, len, \
                                                     trace_point.GetId(), (n)); \
    } while (false)

// API macro
#define FINISH_TRACING_NODIC(LOGLEVEL, annot, name, latency_us) \
    do { \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        static byte::TracePoint trace_point(k_file, __LINE__); \
        byte::Tracing_GetAnnotation_##annot()->Finish(&TRACE_##name, k_file, __LINE__, \
                                                      #name, trace_point.GetId(), latency_us); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            const int64_t cost = byte::GetCurrentTimeInNs()  - \
                TRACE_##name.Get()->trace_start_timestamp_; \
            trace_point.Add(span->annotation_id_, 0, cost); \
        } \
        TRACE_##name->Init(); \
    } while (false)

#define TRACING_SWITCH(name, span) \
    do { \
        byte::TraceSpan* trace_span = TRACE_##name.Get(); \
        byte::TraceSpan temp(std::move(*span)); \
        *span = std::move(*trace_span); \
        *trace_span = std::move(temp); \
    } while (false)

#define TRACING_SWITCH_TO_NODIC(name, span, CODE) \
    do { \
        UPDATE_SPAN_NODIC(name); \
        if (TRACE_##name.Get()->active_) { \
            byte::TraceSpan* trace_span = TRACE_##name.Get(); \
            trace_span->start_time_ = byte::GetCurrentTimeInNs(); \
            DO_TRACE_RECORD_NODIC(trace_span); \
            span->Init(trace_span); \
            CODE; \
        } else if (span != nullptr) { \
            span->active_ = false; \
        } \
        TRACE_##name->Init(); \
    } while (false)

#define TRACING_SWITCH_FROM_NODIC(name, span, CODE) \
    do { \
        if (span != nullptr) { \
            if (span->active_) { \
                span->start_time_ = byte::GetCurrentTimeInNs(); \
                CODE; \
                UPDATE_SPAN_NODIC(name); \
                byte::TraceSpan* trace_span = TRACE_##name.Get(); \
                trace_span->Init(span); \
                DO_TRACE_RECORD_NODIC(trace_span); \
            } \
        } else { \
             UPDATE_SPAN_NODIC(name); \
             TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_SPAN_BY_NAMES_NODIC(name1, name2) \
    do { \
        UPDATE_SPAN_NODIC(name1); \
        UPDATE_SPAN_NODIC(name2); \
        if (TRACE_##name1.Get()->active_) { \
            byte::TraceSpan* span_origin = TRACE_##name1.Get(); \
            byte::TraceSpan* span = TRACE_##name2.Get(); \
            span->Init(span_origin); \
            DO_TRACE_RECORD_NODIC(span); \
            TRACING_APPEND_STR_RAW_NODIC(span, "]},", 3); \
            static const uint32_t k_len = strlen(#name2); \
            TRACING_APPEND_STR_INTERNAL_NO_STREND_NODIC(span, #name2, k_len, '%'); \
            static const uint32_t ip_addr_len = strlen(span->ipv4_addr_); \
            TRACING_APPEND_STR_RAW_NODIC(span, span->ipv4_addr_, ip_addr_len); \
            TRACING_APPEND_STREND_NODIC(span); \
            int pos = 0; \
            const char* parent = "-"; \
            uint32_t parent_size = 1; \
            if (span->name_size_ > 1) { \
                TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
                ++pos; \
                parent = span->name_ + pos; \
                parent_size = span->name_size_ - pos; \
            } \
            TRACING_APPEND_STR_NODIC(span, parent, parent_size, '&'); \
            span->first_tracing_ = true; \
        } else { \
            TRACE_##name2.Get()->active_ = false;\
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_TO_SPAN_BY_NAMES_NODIC(name1, name2) /* to name2 */ \
    do { \
        byte::TraceSpan* span1 = TRACE_##name1.Get(); \
        if (span1 != nullptr && span1->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            TRACING_SWITCH_SPAN_BY_NAMES(name1, name2); \
            byte::TraceSpan* span2 = TRACE_##name2.Get(); \
            TRACING_APPEND_NAME_NODIC(span2, "/", 1); \
            static const uint32_t len = strlen(#name2); \
            TRACING_APPEND_NAME_NODIC(span2, #name2, len); \
            TRACING_APPEND_NAME_NODIC(span2, span2->ipv4_addr_, \
                                strlen(span2->ipv4_addr_)); \
            span1->Init(); \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            span2->penalty_ = end_time - start_time; \
        } else { \
            UPDATE_SPAN_NODIC(name2); \
            TRACE_##name2.Get()->active_ = false; \
        }\
    } while (false)

// API macro
#define TRACING_SWITCH_FROM_SPAN_BY_NAMES_NODIC(name1, name2) /* from name2 */ \
    do { \
        UPDATE_SPAN_NODIC(name1); \
        UPDATE_SPAN_NODIC(name2); \
        byte::TraceSpan* span = TRACE_##name2.Get(); \
        if (span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            /* double resize */ \
            int pos = 0; \
            TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
            span->name_size_ = (pos == -1) ? 0 : pos; \
            span->name_[span->name_size_] = '\0'; \
            TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
            span->name_size_ = (pos == -1) ? 0 : pos; \
            span->name_[span->name_size_] = '\0'; \
            TRACING_SWITCH_SPAN_BY_NAMES(name2, name1); \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name1->penalty_ = end_time - start_time; \
        } else { \
            TRACE_##name1.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_TO_NODIC(name, span) \
    do { \
        UPDATE_SPAN_NODIC(name); \
        if (TRACE_##name->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            span = new byte::TraceSpan(); \
            TRACING_SWITCH_TO_NODIC(name, span, /**/); \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            span->penalty_ = end_time - start_time; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_FROM_NODIC(name, span) \
    do { \
        if (span != nullptr && span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            UPDATE_SPAN_NODIC(name); \
            TRACING_SWITCH_FROM_NODIC(name, span, /**/); \
            delete span; \
            span = nullptr; \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name->penalty_ = end_time - start_time; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_TO_EX_NODIC(name, span) \
    TRACING_SWITCH_TO_NODIC(name, span, /**/)

// API macro
#define TRACING_SWITCH_THREAD_FROM_EX_NODIC(name, span) \
    TRACING_SWITCH_FROM_NODIC(name, span, /**/) \


#define TRACING_SWITCH_SPAN_TO_INTERNAL_NODIC(name, span, penalty) \
    UPDATE_SPAN_NODIC(name); \
    if (TRACE_##name.Get()->active_) { \
        TRACING_SWITCH_TO_NODIC(name, span, \
            static const char* k_file_to = byte::BaseNameOf(__FILE__); \
            static byte::TracePoint trace_point_to(k_file_to, __LINE__); \
            const int64_t cost = start_time - \
                span->span_start_timestamp_; \
            trace_point_to.Add(span->annotation_id_, 0, cost); \
            span->first_tracing_ = true; \
            TRACING_APPEND_STR_RAW_NODIC(span, "]},", 3)); \
        if (penalty) { \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            span->penalty_ = end_time - start_time; \
        } \
    } else if (span != nullptr) { \
        span->active_ = false; \
    }

// API macro
#define TRACING_SWITCH_SPAN_TO_NODIC(name, span) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        TRACING_SWITCH_SPAN_TO_INTERNAL_NODIC(name, span, true); \
    } while (false)

#define TRACING_SWITCH_SPAN_FROM_INTERNAL_NODIC(name, span, penalty) \
    if (span != nullptr && span->active_) { \
        TRACING_SWITCH_FROM_NODIC(name, span, \
            static const char* k_file = byte::BaseNameOf(__FILE__); \
            static byte::TracePoint trace_point_from(k_file, __LINE__); \
            int64_t now = start_time; \
            int pos = 0; \
            TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
            std::string lastname = span->name_ + pos + 1; \
            std::string pad_name; \
            if (span->state_) { \
                span->name_size_ = (pos == -1) ? 0 : pos; \
                span->name_[span->name_size_] = '\0'; \
                pad_name = std::string("RPC_") + lastname; \
            } \
            static const uint32_t k_len_1 = strlen("RPC_" #name); \
            const char* temp_name = span->state_ ? pad_name.c_str() : "RPC_" #name; \
            const uint32_t temp_name_size = span->state_ ? pad_name.size(): k_len_1; \
            TRACING_APPEND_STR_INTERNAL_NO_STREND_NODIC(span, temp_name, temp_name_size, '%'); \
            static const uint32_t ip_addr_len = strlen(span->ipv4_addr_); \
            TRACING_APPEND_STR_RAW_NODIC(span, span->ipv4_addr_, ip_addr_len); \
            TRACING_APPEND_STREND_NODIC(span); \
            static const uint32_t k_len_2 = strlen(#name); \
            const char* parent_name = span->state_ ? "-" : span->name_; \
            uint32_t parent_name_size = span->state_ ? 1 : span->name_size_; \
            TRACING_APPEND_STR_NODIC(span, (parent_name[0] == 0) ? parent_name + 1 : parent_name, \
                (parent_name[0] == 0) ? parent_name_size-1 : parent_name_size, '&'); \
            static const std::string& k_trace_point = \
                std::string(k_file) + ":" + std::to_string(__LINE__); \
            TRACING_APPEND_STR_NODIC(span, k_trace_point.c_str(), k_trace_point.size(), '|'); \
            const int64_t temp_latency = now - span->parent_trace_point_timestamp_; \
            TRACING_APPEND_LATENCY_INTERNAL_NODIC(span, temp_latency, span->start_time_, \
                                            span->penalty_, INT32_MIN); \
            TRACING_APPEND_STR_RAW_NODIC(span, "]},", 3); \
            TRACING_APPEND_STR_INTERNAL_NO_STREND_NODIC(span, #name, k_len_2, '%'); \
            TRACING_APPEND_STR_RAW_NODIC(span, span->ipv4_addr_, ip_addr_len); \
            TRACING_APPEND_STREND_NODIC(span); /*add '\0'*/ \
            parent_name = span->state_ ? "-" : "RPC_" #name; \
            parent_name_size = span->state_ ? 1 : k_len_1; \
            TRACING_APPEND_STR_NODIC(span, parent_name, parent_name_size, '&'); \
            span->parent_trace_point_timestamp_ = now; \
            if (!span->state_) { \
                TRACING_APPEND_NAME_NODIC(span, "/", 1); \
                static const uint32_t len = strlen(#name); \
                TRACING_APPEND_NAME_NODIC(span, #name, len); \
                TRACING_APPEND_NAME_NODIC(span, span->ipv4_addr_, \
                                    strlen(span->ipv4_addr_)); \
            } \
            span->span_start_timestamp_ = now); \
        if (penalty) { \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name.Get()->penalty_ = end_time - start_time; \
        } \
    } else { \
        UPDATE_SPAN_NODIC(name); \
        TRACE_##name.Get()->active_ = false; \
    }\

// API macro
#define TRACING_SWITCH_SPAN_FROM_NODIC(name, span) \
    do { \
        int64_t start_time = byte::GetCurrentTimeInNs(); \
        TRACING_SWITCH_SPAN_FROM_INTERNAL_NODIC(name, span, true); \
    } while (false)

// For Group Commit:

// API macro
#define TRACING_SPAN_TO_NODIC(span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && ctx->active_) { \
            ctx->start_time_ = byte::GetCurrentTimeInNs();\
            DO_TRACE_RECORD_NODIC(ctx); \
        } \
    } while (false)

// API macro
#define TRACING_SPAN_FROM_NODIC(span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && ctx->active_) { \
            ctx->start_time_ = byte::GetCurrentTimeInNs();\
            DO_TRACE_RECORD_NODIC(ctx); \
        } \
    } while (false)

// API macro
#define TRACING_EXCHANGE_SPAN_NODIC(name, span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr) { \
            TRACING_APPEND_STR_RAW_NODIC(ctx, "]},", 3); \
            static const uint32_t k_len = strlen(#name); \
            TRACING_APPEND_STR_NO_STREND(ctx, #name, k_len, '%'); \
            static const uint32_t ip_addr_len = strlen(span->ipv4_addr_); \
            TRACING_APPEND_STR_RAW_NODIC(ctx, span->ipv4_addr_, ip_addr_len);  \
            TRACING_APPEND_STREND_NODIC(ctx); \
            int pos = 0; \
            TRACING_NAME_FIND_LAST_OF_NODIC(ctx, '/', pos); \
            ++pos; \
            const char* parent = ctx->name_ + pos; \
            uint32_t parent_size = ctx->name_size_ - pos; \
            TRACING_APPEND_STR_NODIC(ctx, parent, parent_size, '&'); \
            ctx->first_tracing_ = true; \
            ctx->name_size_ = pos; \
            ctx->name_[ctx->name_size_] = '\0'; \
            TRACING_APPEND_NAME_NODIC(ctx, #name, k_len); \
            TRACING_APPEND_NAME_NODIC(ctx, ctx->ipv4_addr_, strlen(ctx->ipv4_addr_)); \
            UPDATE_SPAN_NODIC(name); \
            TRACING_SWITCH(name, ctx); \
        } else { \
            UPDATE_SPAN_NODIC(name); \
            TRACE_##name.Get()->active_ = false; \
        }\
    } while (false)

#define TRACING_EXCHANGE_SPAN_INTERNAL_NODIC(name, span) \
    do { \
        DO_TRACE_RECORD_NODIC(span); \
        TRACING_APPEND_STR_RAW_NODIC(span, "]},", 3); \
        static const uint32_t k_len = strlen(#name); \
        TRACING_APPEND_STR_NODIC(span, #name, k_len, '%'); \
        int pos = 0; \
        const char* parent = "-"; \
        uint32_t parent_size = 1; \
        if (span->name_size_ > 1) { \
            TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', pos); \
            ++pos; \
            parent = span->name_ + pos; \
            parent_size = span->name_size_ - pos; \
        } \
        TRACING_APPEND_STR_NODIC(span, parent, parent_size, '&'); \
        span->first_tracing_ = true; \
    } while (false)

// API macro
#define TRACING_EXCHANGE_SPAN_TO_NODIC(name, span, name2) \
    do { \
        UPDATE_SPAN_NODIC(name); \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && TRACE_##name.Get()->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name.Get()->start_time_ = start_time; \
            ctx->Init(TRACE_##name.Get()); \
            TRACING_EXCHANGE_SPAN_INTERNAL_NODIC(name2, ctx); \
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            ctx->penalty_ = end_time - start_time; \
        } else if (ctx != nullptr) { \
            ctx->active_ = false; \
        }\
    } while (false)

// API macro
#define TRACING_EXCHANGE_SPAN_FROM_NODIC(name, span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && ctx->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            ctx->start_time_ = start_time; \
            TRACING_EXCHANGE_SPAN_INTERNAL_NODIC(name, ctx); \
            int pos = 0; \
            TRACING_NAME_FIND_LAST_OF_NODIC(ctx, '/', pos); \
            ctx->name_size_ = (pos == -1) ? 0 : pos; \
            ctx->name_[ctx->name_size_] = '\0'; \
            TRACING_NAME_FIND_LAST_OF_NODIC(ctx, '/', pos); \
            ctx->name_size_ = (pos == -1) ? 0 : pos; \
            ctx->name_[ctx->name_size_] = '\0'; \
            TRACING_APPEND_NAME_NODIC(ctx, "/", 1); \
            static const uint32_t len = strlen(#name); \
            TRACING_APPEND_NAME_NODIC(ctx, #name, len); \
            TRACING_APPEND_NAME_NODIC(ctx, ctx->ipv4_addr_, strlen(ctx->ipv4_addr_)); \
            UPDATE_SPAN_NODIC(name); \
            TRACE_##name.Get()->Init(ctx);\
            const int64_t end_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name.Get()->penalty_ = end_time - start_time; \
        } else { \
            UPDATE_SPAN_NODIC(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_PREPARE_REQUEST_TO_NODIC(name, message, CODE) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        byte::TraceSpan trace_span; \
        byte::TraceSpan* span = &trace_span; \
        TRACING_SWITCH_SPAN_TO_INTERNAL_NODIC(name, span, false); \
        message = std::string(span->span_message_, span->message_size_); \
        span->span_message_[0] = '\0'; \
        span->message_size_ = 0; \
        int ret = 0; \
        TRACING_NAME_FIND_LAST_OF_NODIC(span, '/', ret); \
        ++ret; \
        memmove(span->name_, span->name_ + ret, span->name_size_ - ret); \
        span->name_size_ = span->name_size_ - ret; \
        const int64_t end_time = byte::GetCurrentTimeInNs(); \
        span->penalty_ = end_time - start_time; \
        CODE; \
    } while (false)

// API macro
#define TRACING_PREPARE_REQUEST_FROM_NODIC(name, CODE) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        byte::TraceSpan trace_span; \
        byte::TraceSpan* span = &trace_span; \
        CODE; \
        span->state_ = false; \
        TRACING_SWITCH_SPAN_FROM_INTERNAL_NODIC(name, span, false); \
        UPDATE_SPAN_NODIC(name); \
        const int64_t end_time = byte::GetCurrentTimeInNs(); \
        TRACE_##name->penalty_ = end_time - start_time;\
    } while (false)

// API macro
#define TRACING_PREPARE_RESPONSE_FROM_NODIC(name, CODE) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        byte::TraceSpan trace_span; \
        byte::TraceSpan* span = &trace_span; \
        CODE; \
        span->state_ = true; \
        TRACING_SWITCH_SPAN_FROM_INTERNAL_NODIC(name, span, false); \
        const int64_t end_time = byte::GetCurrentTimeInNs(); \
        UPDATE_SPAN_NODIC(name); \
        TRACE_##name->penalty_ = end_time - start_time; \
    } while (false)

// API macro
#define TRACING_PREPARE_RESPONSE_TO_NODIC(name, CODE) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        byte::TraceSpan trace_span; \
        byte::TraceSpan* span = &trace_span; \
        TRACING_SWITCH_SPAN_TO_INTERNAL_NODIC(name, span, false); \
        const int64_t end_time = byte::GetCurrentTimeInNs(); \
        span->penalty_ = end_time - start_time; \
        CODE; \
    } while (false)

// API macro
#define TRACE_PRINTF_NODIC(name, severity, format ...) \
    do { \
        UPDATE_SPAN_NODIC(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            span->start_time_ = byte::GetCurrentTimeInNs();\
        } \
        DO_TRACE_RECORD_NODIC(span); \
        if (span->active_ && byte::LOG_LEVEL_ ## severity >= byte::GetMinLogLevel()) { \
            byte::TracePoint* last_trace_point = byte::TracePoint::LastTRACEPOINT_NODIC(); \
            char* buffer = byte::GetLoggingSystem()->GetArena()->New(512 * sizeof(char)); \
            LOG(severity) \
                << byte::TracingFormat(buffer, 512, last_trace_point->GetId(), ## format); \
            byte::GetLoggingSystem()->GetArena()->Dispose(buffer); \
        } \
    } while (false)

namespace byte {

static uint64_t s_global_annot_id = 0;

class AnnotationNodic {
public:
    AnnotationNodic(uint32_t* occurrences, const char* name);

    ~AnnotationNodic() {}

    void Start(ThreadLocalPtr<TraceSpan>* span,
               const char* name,
               uint32_t len,
               uint64_t parent_trace_point_id,
               uint32_t n);

    void Finish(ThreadLocalPtr<TraceSpan>* span,
                const char* file,
                int line,
                const char* name,
                uint64_t parent_trace_point_id,
                int64_t latency_threshold_us);

    void Format(TraceSpan* span);

    void WriteBinaryBuffer2File(const char* buf,
                                uint32_t buf_len,
                                const char *file_path,
                                bool append);

private:
    uint32_t* occurrences_;
    std::string name_;
};

char* TracingFormat(char* buffer, uint32_t size, uint64_t trace_point_id, const char* format, ...);

void EnableTraceStat();
void DisableTraceStat();
bool IsTraceStatEnable();

void EnableTraceWriteToCollector();

void DisableTraceWriteToCollector();

bool IsDataWritetoCollectorEnable();

#define DECLARE_ANNOTATION_NODIC(annot) \
    namespace byte { \
    extern byte::AnnotationNodic* Tracing_GetAnnotation_##annot(); \
    extern ThreadLocalPtr<AnnotationNodic> t_annotation_##annot; \
    } \
    using byte::t_annotation_##annot

#define DEFINE_ANNOTATION_NODIC(annot) \
namespace byte { \
uint32_t g_occurrences_##annot = 0; \
ThreadLocalPtr<AnnotationNodic> t_annotation_##annot; \
AnnotationNodic* Tracing_GetAnnotation_##annot() { \
    if (t_annotation_##annot.Get() == nullptr) { \
        t_annotation_##annot.Reset(new AnnotationNodic(&g_occurrences_##annot, #annot)); \
    } \
    return t_annotation_##annot.Get(); \
} \
}  /* namespace byte */ \
using byte::t_annotation_##annot

AnnotationNodic::AnnotationNodic(uint32_t* occurrences, const char* name) {
    occurrences_ = occurrences;
    name_ = name;
}

void AnnotationNodic::Start(
    ThreadLocalPtr<TraceSpan>* span,
    const char* name,
    uint32_t len,
    uint64_t parent_trace_point_id,
    const uint32_t n) {

    uint32_t occurrences = AtomicIncrement(occurrences_);
    while (occurrences >= n) {
        if (!LIKELY(AtomicCompareExchange(occurrences_, occurrences, occurrences - n))) {
            occurrences = AtomicGet(occurrences_);
        } else {
            occurrences -= n;
        }
    }
    if (span->Get() == nullptr) {
        span->Reset(new byte::TraceSpan());
    }
    TraceSpan* trace_span = span->Get();
    if (occurrences == 0) {
        trace_span->Init();
        trace_span->annotation_id_ = AtomicIncrement(&s_global_annot_id);
        static uint64_t k_pid_id = static_cast<uint64_t>(GetPId()) & ((1 << 24) - 1);
        trace_span->annotation_id_ = (trace_span->annotation_id_ << 24) | k_pid_id;
        int64_t now = byte::GetCurrentTimeInNs();

        trace_span->active_ = true;
        trace_span->parent_trace_point_timestamp_ = now;

        trace_span->trace_start_timestamp_ = now;
        trace_span->span_start_timestamp_ = now;
        trace_span->name_[0] = '/';
        trace_span->name_[1] = '/';

        len = strlen(name);
        len = (len + 2 >= k_max_tracing_name_size) ? (k_max_tracing_name_size - 3) : len;
        memcpy(trace_span->name_ + 2, name, len);
        trace_span->name_[trace_span->name_size_] = '\0';

        int32_t len2 = strlen(trace_span->ipv4_addr_);
        len2 = (len + len2 + 2 >= k_max_tracing_name_size) ? (k_max_tracing_name_size - 3) : len2;
        memcpy(trace_span->name_ + 2 + len, trace_span->ipv4_addr_, len2);
        trace_span->name_[trace_span->name_size_] = '\0';
        trace_span->name_size_ = (2 + len + len2);
        // add prefix
        char head[256] = {0};
        char sign[] = "$$$";
        snprintf(head, sizeof(head), "%s0123{\"id\":\"%lu\",\"date\":\"%ld\",\"traces\":[%%%s%s,"
                          "\"parent\":\"/\",\"phases\":[",
                          sign, trace_span->annotation_id_, now, name,
                          trace_span->ipv4_addr_);

        int32_t buf_len = strlen(head);
        TRACING_APPEND_STR_RAW(trace_span, head, buf_len);
    } else {
        trace_span->active_ = false;
    }
}

void AnnotationNodic::Finish(
    ThreadLocalPtr<TraceSpan>* span,
    const char* file,
    int line,
    const char* name,
    uint64_t parent_trace_point_id,
    int64_t latency_threshold_us) {

    if (span->Get() == nullptr) {
        span->Reset(new byte::TraceSpan());
    }
    TraceSpan* trace_span = span->Get();
    BYTE_CHECK_NOTNULL(trace_span);
    if (trace_span->active_) {
        int64_t now = byte::GetCurrentTimeInNs();

        std::string name_with_ip = name;
        name_with_ip += trace_span->ipv4_addr_;
        name = name_with_ip.c_str();

        const int64_t duration = (now < trace_span->trace_start_timestamp_) ? 0 :
            now - trace_span->trace_start_timestamp_;

        if (duration < latency_threshold_us * TRACE_NS_TO_US) {
            return;
        }

        char tail[256] = {0};
        char sign[] = "$$$";

        snprintf(tail, sizeof(tail), "]}],\"latency_ns\":\"%ld\"}%s", duration, sign);

        TRACING_APPEND_STR_RAW(trace_span, tail, strlen(tail));

        uint32_t buf_len = trace_span->message_size_ - (2 * strlen(sign) + 4);
        // update buffer len
        memcpy(trace_span->span_message_ + 3, &buf_len, sizeof(buf_len));

        if (LIKELY(IsDataWritetoCollectorEnable())) {
            // copy trace info to mem
            TracingDataBuffer::GetInstance(TRACING_BUFFER_WHOLE)->
                InsertTracingRec2Buf(trace_span->span_message_, trace_span->message_size_);
        }
    }
}

}  // namespace byte
