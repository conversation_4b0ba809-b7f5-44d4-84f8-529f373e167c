// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include "gtest/gtest.h"
#include "byte/tracing/ioprof.h"

namespace byte {
static void local_read_example() {
  IOProfiler::Scope _scope_("LocalReadExample");

  for (int i = 0; i < 10; i++) {
    int len = 0;
    FILE *f = fopen("ioprof.txt", "wb");
    if (f) {
      char buf[1025];
      for (int j = 0; j < 1024; ++j) {
        buf[j] = 'a' + j % 26;
      }
      buf[1024] = '\0';
      len = fwrite(buf, 1, sizeof(buf), f);
      fclose(f);
    }
    printf("write %d bytes\n", len);
  }
}

static void remote_read_emulation() {
  IOProfiler::Scope _scope_("RemoteReadEmulation");

  usleep(100 /* ms */ * 1000);
  local_read_example();
}

TEST(<PERSON>OP<PERSON><PERSON><PERSON>, Basic) {
  remote_read_emulation();
}
}  // namespace byte

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
