// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdarg.h>
#include <stdlib.h>
#include <map>
#include <memory>
#include <string>
#include "byte/concurrent/spinlock.h"
#include "byte/concurrent/timer_manager.h"
#include "byte/concurrent/event.h"
#include "byte/base/singleton.h"

namespace byte {

#define TRACING_DICTIONARY_MAX_ITEM_CNT  65536
#define HASH_BUCKET_NUM  65536

typedef struct tagTracingDictionaryItem {
    tagTracingDictionaryItem() {
        item_name = "";
        item_val = 0;
    }
    std::string item_name;  // string desp, such as span name;
    uint64_t item_val;  // val, such as pointer to this item;
}TracingDictionaryItem;

class TracingDictionary : public byte::SingletonBase<TracingDictionary> {
public:
    TracingDictionary();
    ~TracingDictionary();
    // static TracingDictionary* GetInstance();
    // static void Release();
    friend class byte::SingletonBase<TracingDictionary>;
    static TracingDictionary* GetInstance() {
        return byte::SingletonBase<TracingDictionary>::Instance();
    }
    // register a dictionary item, such as span_name, trace_point;
    // ret val: -1 if failed;
    int32_t RegisterDicItem(const char* item_name);
    int32_t RegisterDicItemFastInMem(const char* item_name);
    void    UnRegisterDicItem(ushort dic_id, const char* item_name);
    void    UnRegisterDicItemFastInMem(ushort dic_id, const char* item_name);
    void ReBuildDicFileIfNeed();
    std::string FindItemNameById(int32_t item_id);
    bool  ConsturctFromCurDicFile();
    int32_t GetDicItemCnt();

private:
    void  UpdateIPAddr();
    void  SyncToFile();

    TracingDictionaryItem arr_items[HASH_BUCKET_NUM];
    uint64_t  item_cnt;
    std::string ipv4_addr;
    byte::SpinLock syncfile_lock;
    byte::SpinLock reconstruct_lock;
    std::string  proc_name;
    char dic_file_dir[128];
    char file_path[256];
};

}  // namespace byte

